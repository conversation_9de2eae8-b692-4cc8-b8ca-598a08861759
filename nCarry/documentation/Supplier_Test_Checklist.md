# 🔍 Supplier Module Test Checklist

## 📋 Test Durumu Legend
- ✅ **Başarılı** - Test tamamlandı, çalışıyor
- ❌ **Hata** - Test başarısız, hata var
- ⚠️ **Geliştirilebilir** - Çal<PERSON><PERSON><PERSON><PERSON>r ama iyileştirme gerekiyor
- ⏳ **Beklemede** - Test edilmedi

---

## 🏗️ **1. <PERSON><PERSON> ve Dosya Analizi**
- [ ] ⏳ Controller dosyası kontrolü (SupplierController.cs)
- [ ] ⏳ Model dosyaları kontrolü (Supplier.cs, SupplierAddress.cs, vb.)
- [ ] ⏳ View dosyaları kontrolü (Index, Create, Edit, Details, Delete)
- [ ] ⏳ ViewModel dosyaları kontrolü
- [ ] ⏳ Database context kontrolü

---

## 🗄️ **2. Database ve Data Model**
- [ ] ⏳ Supplier tablosu yapısı kontrolü
- [ ] ⏳ Foreign key ilişkileri kontrolü
- [ ] ⏳ Validation kuralları kontrolü
- [ ] ⏳ Sample data var mı kontrolü

---

## 🌐 **3. Web Uygulaması Çalıştırma**
- [ ] ⏳ Uygulama başlatma (http://localhost:5298/)
- [ ] ⏳ Authentication sistemi kontrolü
- [ ] ⏳ Supplier menüsüne erişim kontrolü

---

## 📝 **4. Supplier Index Sayfası Testleri**
- [ ] ⏳ Supplier listesi yüklenme kontrolü
- [ ] ⏳ DataTable işlevselliği (sıralama, arama, pagination)
- [ ] ⏳ "Create New Supplier" butonu kontrolü
- [ ] ⏳ Action butonları kontrolü (Details, Edit, Delete)
- [ ] ⏳ Status gösterimleri (Active/Inactive,