# Product Requirements Document (PRD)

## Proje Adı:

Wholesale Management Web Application

## Amaç:

İngiltere'de toptancı olarak faaliyet gösteren şirketin müşteri, stok, satış, satın alma, faturalama ve ödemeler gibi iş süreçlerini dijital ortamda etkin ve entegre biçimde yönetmesini sağlayacak kapsamlı bir web uygulamasıdır.

## Hedef <PERSON>:

* Şirket içi çalışanlar (satış, depo, satın alma, finans ve yönetim departmanları)
* Yöneticiler (detaylı raporlama ve analiz ihtiyaçları)
* Tedarikçiler ve Müşteriler (temel bilgi ve belge paylaşımları)

## Temel Özellikler:

### 1. Kullanıcı Yönetimi

* Kullanıcı rolleri ve yetkileri
* Kimlik doğrulama ve erişim yönetimi

### 2. Müşteri Yönetimi

* Müşteri profilleri
* Çoklu iletişim bilgileri (adres, telefon, e-posta)
* Referans kodu ve vergi bilgileri

### 3. Tedarikçi Yönetimi

* Tedarikçi kayıt ve takibi
* İletişim bilgileri yönetimi
* Tedarikçi sipariş ve alacak yönetimi

### 4. Stok ve Depo Yönetimi

* Ürün ve kategori yönetimi
* Depo ve raf yerleşimi takibi
* Barkod ve SKU bazlı ürün yönetimi
* Ürün varyantları yönetimi

### 5. Satış ve Teklif Yönetimi

* Teklif hazırlama ve takibi
* Satış siparişi oluşturma ve takip
* Otomatik fatura oluşturma
* Sipariş olmadan doğrudan satış faturası oluşturabilme

### 6. Satın Alma Yönetimi

* Satın alma siparişleri ve tedarikçi yönetimi
* Otomatik stok güncellemeleri
* Sipariş olmadan doğrudan satın alma faturası oluşturabilme

### 7. Faturalama ve Ödeme

* Elektronik fatura düzenleme ve yönetimi
* Alacak dekontu (Credit Note) oluşturma
* Çoklu ödeme yöntemi desteği ve takip sistemi

### 8. Gider Yönetimi

* Gider kalemlerinin takibi
* Ödeme yöntemleri ve onay süreçleri

### 9. Raporlama ve Analitik

* Satış, stok ve finansal raporlar
* Yönetici panelleri ve KPI izleme
* Özelleştirilebilir raporlama aracı

## Teknoloji Tercihleri:

* Backend: C# ASP.NET Core MVC
* Frontend: HTML, CSS, JavaScript
* Database: MS SQL Server
* Deployment: Azure/AWS (Cloud tabanlı hosting)

## Başarı Kriterleri:

* İş süreçlerinin hızında ve verimliliğinde %30 artış
* Hata ve manuel işlem oranının düşürülmesi (%20'nin altında)
* Kullanıcı memnuniyet oranının en az %90 olması

## Proje Aşamaları:

1. Analiz ve Tasarım
2. Veritabanı oluşturma ve Backend Geliştirme
3. Frontend Geliştirme
4. Test ve Kalite Kontrol
5. Kullanıcı Eğitimi ve Dokümantasyon
6. Yayına alma ve Destek

## Varsayımlar ve Bağımlılıklar:

* Kullanıcı eğitimleri ve destek hizmetleri sağlanacaktır.
* Şirket içi ekip ile sıkı işbirliği gerekmektedir.
* Yasal mevzuata ve GDPR'a tam uyum sağlanacaktır.
