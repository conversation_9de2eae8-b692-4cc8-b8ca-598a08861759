  nCarry Product Flow: Purchase to Sale Process

     1. Product Setup (Prerequisites)

     - Create product in Product Management
     - Set up product variants if needed
     - Configure Units of Measure (UOM)
     - Set up barcodes
     - Define pricing tiers
     - Configure warehouse locations

     2. Purchase Process

     2.1 Create Purchase Order

     - Select supplier
     - Add products with quantities
     - Set delivery warehouse
     - Configure payment terms
     - Submit for approval

     2.2 Goods Receipt

     - Receive shipment at warehouse
     - Create Goods Receipt from PO
     - Quality check if required
     - Update inventory automatically via InventoryTransaction

     2.3 Supplier Invoice & Payment

     - Create Supplier Invoice
     - Match with Goods Receipt (3-way matching)
     - Process payment to supplier
     - Allocate payment to invoice

     3. Inventory Management

     3.1 Stock Updates

     - Automatic inventory increase on goods receipt
     - Track by warehouse and location (Bay/Level)
     - Monitor stock levels
     - Set reorder points

     3.2 Stock Transfers (if needed)

     - Transfer between warehouses
     - Transfer between locations
     - Update inventory transactions

     4. Sales Process

     4.1 Create Sales Order

     - Select customer
     - Apply customer-specific pricing
     - Add products from inventory
     - Calculate taxes and discounts
     - Check credit limits

     4.2 Order Processing

     - Create picking list
     - Assign to warehouse staff
     - Pick items from locations
     - Update picked quantities

     4.3 Packing

     - Create packing slip
     - Pack items into boxes
     - Record serial/batch numbers
     - Calculate package weight/volume

     5. Shipping & Delivery

     5.1 Create Shipment

     - Select carrier and service
     - Generate tracking number
     - Print shipping labels
     - Update order status

     5.2 Delivery Tracking

     - Track shipment status
     - Update delivery information
     - Record proof of delivery
     - Complete order fulfillment

     6. Financial Completion

     6.1 Sales Invoice

     - Generate from sales order
     - Apply payment terms
     - Send to customer

     6.2 Payment Collection

     - Record customer payment
     - Allocate to invoices
     - Update AR balance
     - Complete transaction cycle

     7. Reporting & Analysis

     - Stock valuation reports
     - Sales analysis
     - Profit margin calculation
     - Inventory turnover
     - Customer profitability

     This complete cycle ensures full traceability from purchase to sale with proper inventory management and financial tracking.