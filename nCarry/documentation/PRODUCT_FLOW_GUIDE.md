# nCarry Product Flow Guide: From Purchase to Sale

## Overview
This guide provides a detailed walkthrough of the complete product lifecycle in the nCarry system, from initial purchase to final sale and payment collection.

## Table of Contents
1. [Product Setup](#1-product-setup)
2. [Purchase Process](#2-purchase-process)
3. [Inventory Management](#3-inventory-management)
4. [Sales Process](#4-sales-process)
5. [Shipping & Delivery](#5-shipping--delivery)
6. [Financial Completion](#6-financial-completion)
7. [System Integration Points](#7-system-integration-points)
8. [Best Practices](#8-best-practices)

## 1. Product Setup

### 1.1 Create Product Master Data
Before any purchase or sale, products must be properly configured:

```sql
-- Product creation involves these key tables:
-- Product, ProductCategory, UnitOfMeasure, ProductVariant, Barcode
```

**Required Steps:**
1. **Create Product Category** (if not exists)
   - Navigate to: Products > Categories
   - Define category hierarchy
   - Set category code and name

2. **Set Up Units of Measure**
   - Define base units (EA, KG, L, M)
   - Configure conversion factors
   - Set decimal places for accuracy

3. **Create Product**
   - Navigate to: Products > New Product
   - Required fields:
     - Product Code (unique)
     - Product Name
     - Category
     - Inventory UOM
     - Standard Cost
     - Sell Price
   - Optional settings:
     - Track inventory (Yes/No)
     - Requires batch/serial tracking
     - Min/Max stock levels
     - Reorder point

4. **Configure Product Variants** (if applicable)
   - Size variations (S, M, L, XL)
   - Color variations
   - Material variations
   - Each variant gets unique code

5. **Set Up Barcodes**
   - Primary barcode for each product
   - Additional barcodes for variants
   - Barcode types: EAN13, UPC, Code128

### 1.2 Configure Pricing
```
Product Pricing Structure:
- Standard Cost: What you pay
- List Price: Recommended retail
- Sell Price: Default selling price
- Customer-specific pricing (via CustomerPriceList)
- Volume-based pricing tiers
```

## 2. Purchase Process

### 2.1 Create Purchase Order
**Navigation:** Purchases > Purchase Orders > New

**Workflow:**
```
1. Select Supplier
   ↓
2. Add Products (with quantities)
   ↓
3. Set Delivery Details
   ↓
4. Review & Submit
   ↓
5. Approval (if required)
```

**Key Fields:**
- PurchaseOrderNumber: Auto-generated (PO-2024-00001)
- SupplierID: Select from supplier list
- OrderDate: Today's date
- RequiredDate: When you need the goods
- DeliveryWarehouseID: Where to receive
- Currency & ExchangeRate: For international purchases

**Purchase Order Items:**
- ProductID
- Quantity
- UnitPrice
- DiscountPercent
- TaxCodeID

### 2.2 Goods Receipt Process
**Navigation:** Warehouse > Goods Receipts > New

**Steps:**
1. **Create Goods Receipt**
   - Link to Purchase Order
   - Verify supplier delivery note
   - Select receiving warehouse

2. **Receive Items**
   ```
   For each item:
   - Verify product matches PO
   - Check quantity received
   - Inspect quality
   - Record batch/serial numbers
   - Note any damages/shortages
   ```

3. **Quality Check** (if required)
   - QualityCheckStatus: Pending → Passed/Failed
   - QualityCheckByUserID: Inspector
   - QualityCheckDate: Timestamp

4. **Post Receipt**
   - Status: Draft → Posted
   - Creates InventoryTransaction records
   - Updates Inventory levels

**Automatic Inventory Update:**
```sql
-- System creates InventoryTransaction:
TransactionType: 'GoodsReceipt'
Quantity: [Received Quantity]
ReferenceType: 'PurchaseOrder'
ReferenceID: [PO ID]

-- Updates Inventory table:
QuantityOnHand: + [Received Quantity]
LastReceiptDate: [Receipt Date]
```

### 2.3 Supplier Invoice Processing
**Navigation:** Purchases > Supplier Invoices > New

**Three-Way Matching:**
1. Purchase Order (what we ordered)
2. Goods Receipt (what we received)
3. Supplier Invoice (what they're charging)

**Invoice Creation:**
- Link to Goods Receipt(s)
- Verify prices match PO
- Check quantities match receipt
- Apply any agreed discounts
- Calculate total with tax

### 2.4 Supplier Payment
**Navigation:** Finance > Payments > New Payment

**Payment Process:**
```
1. Select Supplier
2. Enter Payment Details:
   - PaymentMethod (Bank Transfer, Check, etc.)
   - Amount
   - Reference Number
3. Allocate to Invoices:
   - Select open invoices
   - Apply payment amounts
   - Handle partial payments
4. Post Payment
```

## 3. Inventory Management

### 3.1 Inventory Structure
```
Warehouse
  └── WarehouseLocation (Bay, Level)
      └── Inventory
          └── Product + Variant
              └── Batch/Serial Numbers
```

### 3.2 Stock Tracking
**Real-time Updates via InventoryTransaction:**
- **Increase Stock:**
  - Goods Receipt
  - Stock Transfer In
  - Stock Adjustment (+)
  - Customer Return

- **Decrease Stock:**
  - Sales Order Shipment
  - Stock Transfer Out
  - Stock Adjustment (-)
  - Damage/Loss

### 3.3 Stock Levels Monitoring
**Key Metrics:**
- QuantityOnHand: Current stock
- QuantityReserved: Allocated to orders
- QuantityAvailable: OnHand - Reserved
- ReorderPoint: When to reorder
- MinStockLevel: Safety stock
- MaxStockLevel: Storage limit

**Alerts:**
- Low stock warning
- Reorder point reached
- Overstock situation
- Expiry date approaching

## 4. Sales Process

### 4.1 Create Sales Order
**Navigation:** Sales > Sales Orders > New

**Order Creation Flow:**
```
1. Customer Selection
   ├── Check credit limit
   ├── Load payment terms
   └── Apply customer pricing

2. Add Products
   ├── Check availability
   ├── Apply discounts
   └── Calculate taxes

3. Delivery Details
   ├── Shipping address
   ├── Delivery instructions
   └── Preferred date

4. Review & Confirm
   └── Reserve inventory
```

**Pricing Logic:**
1. Check CustomerPriceList for specific pricing
2. Check volume-based pricing tiers
3. Apply promotional pricing if active
4. Fall back to product sell price
5. Apply customer group discounts

**Credit Check:**
```
IF (Customer.CreditLimit > 0) THEN
  AvailableCredit = CreditLimit - CurrentBalance - OrderTotal
  IF (AvailableCredit < 0) THEN
    Block order or require approval
  END IF
END IF
```

### 4.2 Order Fulfillment

#### 4.2.1 Create Picking List
**Navigation:** Warehouse > Picking Lists > Generate

**Picking List Generation:**
- Select orders to pick
- Group by warehouse zone
- Optimize picking route
- Assign to picker

**Picking Process:**
```
For each item:
1. Go to location (Bay/Level)
2. Scan product barcode
3. Pick quantity
4. Record batch/serial
5. Mark as picked
```

#### 4.2.2 Packing Process
**Navigation:** Warehouse > Packing > New Packing Slip

**Packing Steps:**
1. **Create Packing Slip**
   - Link to Sales Order
   - Link to Picking List

2. **Pack Items**
   ```
   Box 1:
   - Product A: 5 units
   - Product B: 3 units
   - Weight: 2.5 kg
   
   Box 2:
   - Product C: 10 units
   - Weight: 5.0 kg
   ```

3. **Record Package Details**
   - Number of boxes
   - Total weight
   - Dimensions (L×W×H)
   - Special handling notes

## 5. Shipping & Delivery

### 5.1 Create Shipment
**Navigation:** Logistics > Shipments > New

**Shipment Creation:**
```
1. Select Carrier & Service
   ├── Compare rates
   ├── Check transit time
   └── Verify service area

2. Generate Shipping Documents
   ├── Shipping label
   ├── Packing list
   ├── Commercial invoice
   └── Customs forms (if international)

3. Record Tracking Info
   └── Carrier tracking number
```

**Carrier Integration:**
- Real-time rate quotes
- Label printing
- Tracking updates
- Proof of delivery

### 5.2 Delivery Tracking
**Automatic Updates:**
```
ShipmentTracking entries:
- Picked up from warehouse
- In transit
- Out for delivery
- Delivered
- Exception/Failed delivery
```

**Customer Notifications:**
- Order shipped email
- Tracking link
- Delivery confirmation
- E-signature capture

## 6. Financial Completion

### 6.1 Sales Invoice Generation
**Navigation:** Sales > Invoices > Generate from Order

**Invoice Creation:**
- Auto-generate from shipped orders
- Include all shipped items
- Apply agreed payment terms
- Calculate due date

**Invoice Details:**
```
Invoice Number: INV-2024-00001
Order Reference: SO-2024-00100
Shipped Items:
- Product A: 5 @ £10.00 = £50.00
- Product B: 3 @ £15.00 = £45.00
Subtotal: £95.00
VAT (20%): £19.00
Total: £114.00
Due Date: 30 days from invoice date
```

### 6.2 Payment Collection
**Navigation:** Finance > Customer Payments > New

**Payment Recording:**
1. **Receive Payment**
   - Payment method
   - Reference/Check number
   - Amount received

2. **Allocate to Invoices**
   ```
   Payment: £500.00
   Allocate to:
   - INV-2024-00001: £114.00
   - INV-2024-00002: £386.00
   ```

3. **Handle Partial Payments**
   - Update invoice balance
   - Track payment history
   - Age remaining balance

**Automatic Updates:**
- SalesOrder.PaidAmount
- SalesOrder.BalanceAmount
- Customer.CurrentBalance
- Aged receivables report

## 7. System Integration Points

### 7.1 Database Relationships
```
PurchaseOrder → GoodsReceipt → InventoryTransaction → Inventory
                     ↓
              SupplierInvoice → Payment

Product → Inventory → SalesOrderItem → SalesOrder
             ↓              ↓
      InventoryReservation  PickingList → PackingSlip → Shipment
                                 ↓
                           SalesInvoice → Payment
```

### 7.2 Transaction Flow
1. **Purchase Side:**
   - PO creates commitment
   - GR updates inventory
   - Invoice creates payable
   - Payment clears payable

2. **Sales Side:**
   - SO reserves inventory
   - Picking allocates stock
   - Shipment reduces inventory
   - Invoice creates receivable
   - Payment clears receivable

### 7.3 Audit Trail
Every transaction records:
- CreatedDate & CreatedByUserID
- UpdatedDate & UpdatedByUserID
- Status changes with timestamps
- Reference links between documents

## 8. Best Practices

### 8.1 Data Entry
1. **Always verify before posting:**
   - Check quantities
   - Verify prices
   - Confirm addresses

2. **Use barcode scanning:**
   - Faster data entry
   - Fewer errors
   - Better tracking

3. **Regular reconciliation:**
   - Physical counts vs system
   - Bank reconciliation
   - Supplier statements

### 8.2 Process Optimization
1. **Batch Processing:**
   - Group similar orders
   - Bulk picking lists
   - Consolidated shipments

2. **Automation Rules:**
   - Auto-generate invoices
   - Scheduled reports
   - Low stock alerts

3. **Performance Monitoring:**
   - Order cycle time
   - Picking accuracy
   - Delivery performance
   - Payment collection days

### 8.3 Security & Controls
1. **Approval Workflows:**
   - Purchase orders over limit
   - Credit limit overrides
   - Manual price changes

2. **Segregation of Duties:**
   - Order entry ≠ Shipping
   - Receiving ≠ Payment
   - Inventory count ≠ Adjustment

3. **Regular Audits:**
   - Inventory accuracy
   - Pricing compliance
   - Discount authorization

## Troubleshooting Common Issues

### Issue: Stock Mismatch
**Solution:**
1. Run inventory reconciliation report
2. Check pending transactions
3. Verify all receipts posted
4. Review recent adjustments

### Issue: Order Won't Ship
**Possible Causes:**
- Insufficient stock
- Credit limit exceeded
- Missing shipping address
- Items not picked

### Issue: Payment Not Allocating
**Check:**
- Currency matches invoice
- Customer ID correct
- Invoice not already paid
- Amount within tolerance

## Reporting & Analytics

### Key Reports:
1. **Inventory Reports:**
   - Stock valuation
   - Movement analysis
   - Aging report
   - Reorder report

2. **Sales Reports:**
   - Order status
   - Shipping performance
   - Invoice aging
   - Customer profitability

3. **Purchase Reports:**
   - Open POs
   - Receiving log
   - Supplier performance
   - Price variance

### KPIs to Monitor:
- Inventory turnover ratio
- Order fill rate
- Days sales outstanding (DSO)
- Days payable outstanding (DPO)
- Gross margin by product/customer

---

## Quick Reference

### Status Workflows
**Purchase Order:** Draft → Submitted → Approved → Partial → Completed → Closed
**Sales Order:** Pending → Confirmed → Picking → Packed → Shipped → Delivered → Invoiced → Paid
**Inventory:** Available → Reserved → Allocated → Shipped

### Common Transaction Types
- **Inventory In:** GoodsReceipt, TransferIn, Return, Adjustment+
- **Inventory Out:** Sales, TransferOut, Damage, Adjustment-

### Document Numbering
- Purchase Orders: PO-YYYY-NNNNN
- Sales Orders: SO-YYYY-NNNNN
- Invoices: INV-YYYY-NNNNN
- Shipments: SH-YYYY-NNNNN

---

*This guide covers the complete product flow in nCarry. For specific module details, refer to individual module documentation.*