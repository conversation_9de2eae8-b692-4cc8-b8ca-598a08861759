# nCarry Database Schema Fix - Professional Guide

## 📋 Overview

This guide provides a comprehensive, professional approach to fixing all database schema mismatches in the nCarry application. The solution maintains architectural integrity while resolving all model-database inconsistencies.

## 🎯 Problem Statement

The nCarry application experiences SQL errors due to missing columns in database tables that don't match the Entity Framework models:

### Quote Table Issues:
- Missing: Status, ContactID, CurrencyCode, DeletedByUserID, DeletedDate, ExpiryDate, IsDeleted, PaymentTermDays, ReferenceNumber, SalesRepUserID

### Product Table Issues:
- Missing: Attribute1Name, Attribute1Value, Attribute2Name, Attribute2Value, Attribute3Name, Attribute3Value, ImageURL, CreatedByUserID, UpdatedByUserID

### ProductVariant Table Issues:
- Missing: BarcodeValue, UOMID

## 🛠️ Solution Components

### 1. Schema Analysis Script (`schema_analysis.sql`)
- Comprehensive analysis of all table schemas
- Identifies missing columns and tables
- Provides detailed reports of issues
- Safe read-only analysis

### 2. Comprehensive Fix Script (`comprehensive_schema_fix.sql`)
- Fixes all identified schema issues
- Creates missing tables and columns
- Maintains data integrity
- Includes proper error handling

### 3. Professional PowerShell Wrapper (`professional_schema_fix.ps1`)
- Automated execution with safety checks
- Connection validation
- Backup recommendations
- Detailed logging and verification

## 🚀 Execution Instructions

### Option 1: Automated PowerShell Execution (Recommended)

```powershell
# Navigate to the nCarry directory
cd C:\source\nCarry

# Run analysis only (safe, no changes)
.\professional_schema_fix.ps1 -AnalysisOnly -Verbose

# Apply comprehensive fixes
.\professional_schema_fix.ps1 -Verbose

# Custom connection parameters
.\professional_schema_fix.ps1 -Server "your-server" -Database "your-db" -Username "your-user" -Password "your-password"
```

### Option 2: Manual SQL Execution

```sql
-- Step 1: Run analysis (optional but recommended)
-- Execute schema_analysis.sql in SQL Server Management Studio

-- Step 2: Apply fixes
-- Execute comprehensive_schema_fix.sql in SQL Server Management Studio
```

### Option 3: Quick Batch Execution

```batch
# Navigate to directory and run
cd C:\source\nCarry
.\professional_schema_fix.ps1
```

## 📊 What Gets Fixed

### Quote Table
- ✅ Creates complete Quote table if missing
- ✅ Adds all missing columns with proper data types
- ✅ Sets appropriate defaults and constraints
- ✅ Maintains referential integrity

### QuoteItem Table
- ✅ Creates QuoteItem table if missing
- ✅ Establishes foreign key relationships
- ✅ Proper decimal precision for financial data

### Product Table
- ✅ Adds extended attribute columns
- ✅ Adds audit trail columns (CreatedByUserID, UpdatedByUserID)
- ✅ Adds ImageURL for product images

### ProductVariant Table
- ✅ Adds BarcodeValue for variant-specific barcodes
- ✅ Adds UOMID for unit of measure linking

### User Table
- ✅ Adds IsActive column for user management

## 🔍 Verification Steps

After running the fix:

1. **Restart Application**: Stop and restart your nCarry web application
2. **Test SalesOrder/Create**: Navigate to http://localhost:5298/SalesOrder/Create
3. **Verify Dropdowns**: Ensure Quote and SalesRep dropdowns load without errors
4. **Test CRUD Operations**: Create, read, update, and delete sales orders
5. **Check Product Pages**: Verify product-related pages work correctly

## 🛡️ Safety Measures

### Built-in Protections:
- ✅ Connection validation before execution
- ✅ Backup recommendation prompts
- ✅ Comprehensive error handling
- ✅ Rollback-safe operations (only ADD operations, no data loss)
- ✅ Idempotent scripts (safe to run multiple times)

### Backup Recommendations:
```sql
-- Create backup before running fixes
BACKUP DATABASE nCarryDB 
TO DISK = 'C:\Backup\nCarryDB_BeforeSchemaFix.bak'
WITH FORMAT, INIT;
```

## 📈 Expected Results

### Before Fix:
- ❌ SalesOrder/Create throws SQL exceptions
- ❌ Quote-related features fail
- ❌ Product attribute fields cause errors

### After Fix:
- ✅ All pages load without schema errors
- ✅ Quote management fully functional
- ✅ Product attributes properly stored
- ✅ Complete CRUD operations work
- ✅ Application runs smoothly

## 🔧 Troubleshooting

### Common Issues:

**Permission Errors:**
```
Error: 'ALTER permission denied on object'
```
**Solution:** Ensure your database user has `db_ddladmin` or `db_owner` permissions.

**Connection Timeout:**
```
Error: 'Timeout expired'
```
**Solution:** Increase timeout: `.\professional_schema_fix.ps1 -TimeoutSeconds 600`

**Table Lock Errors:**
```
Error: 'Table is being used by another process'
```
**Solution:** Stop the web application before running fixes.

### Rollback Procedure:
If issues occur, restore from backup:
```sql
RESTORE DATABASE nCarryDB 
FROM DISK = 'C:\Backup\nCarryDB_BeforeSchemaFix.bak'
WITH REPLACE;
```

## 📞 Support

### Pre-Execution Checklist:
- [ ] Database backup created
- [ ] Web application stopped
- [ ] Database permissions verified
- [ ] Connection string tested

### Post-Execution Checklist:
- [ ] Schema fix completed successfully
- [ ] Application restarted
- [ ] SalesOrder/Create page tested
- [ ] No console errors in browser
- [ ] All dropdowns populated correctly

## 🎉 Success Indicators

When the fix is successful, you should see:
- No more SQL column errors in application logs
- Quote dropdown in SalesOrder/Create populates correctly
- SalesRep dropdown loads user data
- Product pages display all attribute fields
- Full application functionality restored

---

**Professional Development Standards:**
- ✅ Comprehensive error handling
- ✅ Detailed logging and reporting
- ✅ Safe, idempotent operations
- ✅ Architectural integrity maintained
- ✅ Full documentation provided
- ✅ Rollback procedures documented
