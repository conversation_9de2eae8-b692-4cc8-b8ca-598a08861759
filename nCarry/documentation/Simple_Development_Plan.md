# 🎯 nCarry - Basitleştirilmiş Geliştirme Planı

## 📋 Özet

**Yaklaşım:** Monolitik MVC Uygulama (Başlangıç için)  
**Teknoloji:** ASP.NET Core MVC + Entity Framework  
**Süre:** 3-4 Ay  
**Hedef:** <PERSON><PERSON>zlı ve çalışan bir MVP (Minimum Viable Product)

---

## 🏗️ Basit Proje Yapısı

```
nCarry/
├── nCarry.Web/
│   ├── Controllers/        # MVC Controllers
│   ├── Models/            # Entity modeller
│   ├── ViewModels/        # Görünüm modelleri
│   ├── Services/          # İş mantığı
│   ├── Data/              # DbContext ve Migrations
│   ├── Views/             # Razor views
│   └── wwwroot/           # CSS, JS, Images
└── nCarry.Tests/          # Unit testler (opsiyonel)
```

**NOT:** Tek proje ile başlıyoruz, ihtiyaç oldukça ayırırız!

---

## 📅 Basitleştirilmiş Fazlar

### 🚀 Faz 1: <PERSON><PERSON> (2 Hafta)

#### Hafta 1: Proje <PERSON>
```csharp
// 1. Basit DbContext oluştur
public class AppDbContext : DbContext
{
    public DbSet<Customer> Customers { get; set; }
    public DbSet<Product> Products { get; set; }
    // ... diğer tablolar
}

// 2. Basit Repository (opsiyonel)
public class Repository<T> where T : class
{
    private readonly AppDbContext _context;
    // Basit CRUD metodları
}
```

- [ ] ASP.NET Core MVC projesi oluştur
- [ ] Entity Framework ekle
- [ ] Veritabanı modellerini oluştur (Entity'ler)
- [ ] Migration'ları çalıştır
- [ ] Basit login sistemi (Identity)

#### Hafta 2: Admin Paneli
- [ ] Layout tasarımı (Bootstrap tema)
- [ ] Kullanıcı yönetimi sayfaları
- [ ] Rol yönetimi
- [ ] Basit dashboard

---

### 🚀 Faz 2: Temel İşlemler (3 Hafta)

#### Hafta 3: Müşteri ve Tedarikçi
- [ ] Müşteri CRUD sayfaları
- [ ] Tedarikçi CRUD sayfaları
- [ ] Adres yönetimi
- [ ] Basit arama ve listeleme

#### Hafta 4: Ürün Yönetimi
- [ ] Kategori ağacı
- [ ] Ürün ekleme/düzenleme
- [ ] Barkod yazdırma
- [ ] Stok durumu görüntüleme

#### Hafta 5: Basit Stok İşlemleri
- [ ] Stok girişi
- [ ] Stok çıkışı
- [ ] Stok listesi
- [ ] Basit stok uyarıları

---

### 🚀 Faz 3: Satış Modülü (3 Hafta)

#### Hafta 6: Teklif ve Sipariş
- [ ] Teklif oluşturma (basit form)
- [ ] Sipariş girişi
- [ ] Stok kontrolü
- [ ] PDF çıktı

#### Hafta 7: Faturalama
- [ ] Fatura oluşturma
- [ ] Fatura yazdırma
- [ ] Basit ödeme takibi
- [ ] İade işlemleri

#### Hafta 8: Basit Raporlar
- [ ] Satış raporu
- [ ] Stok raporu
- [ ] Müşteri ekstreleri
- [ ] Excel export

---

### 🚀 Faz 4: Satınalma ve Temel Finans (3 Hafta)

#### Hafta 9: Satınalma
- [ ] Satınalma siparişi
- [ ] Mal kabul
- [ ] Satınalma faturaları

#### Hafta 10: Basit Finans
- [ ] Tahsilat girişi
- [ ] Ödeme girişi
- [ ] Kasa/Banka takibi
- [ ] Basit gider yönetimi

#### Hafta 11: Kullanıcı İyileştirmeleri
- [ ] Dashboard geliştirmeleri
- [ ] Hızlı işlem menüleri
- [ ] Arama iyileştirmeleri
- [ ] Performans optimizasyonu

---

## 🛠️ Teknoloji Kararları (Basit)

### Backend
```csharp
// Örnek Controller - Karmaşık pattern'ler yok!
public class CustomerController : Controller
{
    private readonly AppDbContext _context;
    
    public async Task<IActionResult> Index()
    {
        var customers = await _context.Customers.ToListAsync();
        return View(customers);
    }
    
    [HttpPost]
    public async Task<IActionResult> Create(Customer customer)
    {
        if (ModelState.IsValid)
        {
            _context.Add(customer);
            await _context.SaveChangesAsync();
            return RedirectToAction(nameof(Index));
        }
        return View(customer);
    }
}
```

### Frontend
- **Bootstrap 5** - Hazır tema kullan
- **jQuery** - Basit interaktivite
- **DataTables** - Tablo işlemleri
- **Select2** - Gelişmiş dropdown
- **SweetAlert2** - Güzel uyarılar
- **Chart.js** - Basit grafikler

### Araçlar
- **Visual Studio** - IDE
- **SQL Server Express** - Ücretsiz DB
- **Git** - Versiyon kontrolü

---

## 📝 Geliştirme İpuçları

### ✅ YAPILACAKLAR
1. **KISS Prensibi** - Keep It Simple, Stupid!
2. **Çalışan kod öncelikli** - Önce çalıştır, sonra güzelleştir
3. **Kullanıcı geri bildirimi** - Sık sık göster, feedback al
4. **Incremental geliştirme** - Küçük adımlarla ilerle

### ❌ YAPILMAYACAKLAR (Başlangıçta)
1. **Microservice** - Tek uygulama yeterli
2. **Complex patterns** - Repository, UoW gereksiz karmaşa
3. **Over-engineering** - İhtiyaç yoksa yapma
4. **Premature optimization** - Önce çalışsın

---

## 🎯 MVP Özellikleri

### Faz 1 Sonunda (1 Ay)
- [ ] Kullanıcı girişi yapılabiliyor
- [ ] Müşteri/Tedarikçi eklenebiliyor
- [ ] Ürün tanımlanabiliyor
- [ ] Basit stok takibi var

### Faz 2 Sonunda (2 Ay)
- [ ] Sipariş alınabiliyor
- [ ] Fatura kesilebiliyor
- [ ] Stok kontrolü yapılıyor
- [ ] Basit raporlar alınabiliyor

### Faz 3 Sonunda (3 Ay)
- [ ] Satınalma yapılabiliyor
- [ ] Tahsilat/Ödeme takibi var
- [ ] Temel finansal kontrol var
- [ ] Günlük operasyonlar yapılabiliyor

---

## 💡 Pratik Öneriler

### 1. Hazır Çözümler Kullan
- **Admin Template:** AdminLTE, CoreUI (ücretsiz)
- **Authentication:** ASP.NET Core Identity
- **PDF:** Rotativa veya PuppeteerSharp
- **Excel:** ClosedXML

### 2. Basit Tutulacak Alanlar
- **Raporlama:** Basit liste ve toplamlar
- **Yetkilendirme:** Role-based yeterli
- **API:** Şimdilik gerek yok
- **Mobile:** Responsive web yeterli

### 3. Sonraya Bırakılacaklar
- ❌ Karmaşık onay süreçleri
- ❌ Entegrasyonlar
- ❌ Advanced analytics
- ❌ Multi-company

---

## 📊 Basit Veritabanı Kullanımı

```csharp
// Entity örneği - Fazla detaya girme!
public class Customer
{
    public int CustomerID { get; set; }
    public string CustomerName { get; set; }
    public string Email { get; set; }
    public string Phone { get; set; }
    public decimal CreditLimit { get; set; }
    public bool IsActive { get; set; }
    
    // Navigation properties
    public virtual ICollection<SalesOrder> Orders { get; set; }
}

// Basit servis örneği
public class CustomerService
{
    private readonly AppDbContext _context;
    
    public async Task<List<Customer>> GetActiveCustomers()
    {
        return await _context.Customers
            .Where(c => c.IsActive)
            .OrderBy(c => c.CustomerName)
            .ToListAsync();
    }
}
```

---

## 🚀 Hemen Başlayalım!

### Bu Hafta Yapılacaklar:
1. [ ] Visual Studio'da yeni proje oluştur
2. [ ] NuGet paketlerini yükle
3. [ ] İlk migration'ı oluştur
4. [ ] Basit login ekranı yap
5. [ ] İlk CRUD sayfasını tamamla

### Gerekli NuGet Paketleri:
```xml
<PackageReference Include="Microsoft.EntityFrameworkCore.SqlServer" Version="8.0.0" />
<PackageReference Include="Microsoft.EntityFrameworkCore.Tools" Version="8.0.0" />
<PackageReference Include="Microsoft.AspNetCore.Identity.EntityFrameworkCore" Version="8.0.0" />
<PackageReference Include="Microsoft.AspNetCore.Identity.UI" Version="8.0.0" />
```

---

## 💰 Gerçekçi Bütçe

- **Geliştirme:** 3 ay x 1-2 developer = £30,000 - £60,000
- **Lisanslar:** SQL Server Express (ücretsiz)
- **Hosting:** Azure/AWS = £100-200/ay
- **Toplam:** £35,000 - £65,000

---

**Özet:** Karmaşık mimariler yerine, çalışan basit bir sistem ile başlayıp, ihtiyaç oldukça geliştirmek daha mantıklı! 🎯