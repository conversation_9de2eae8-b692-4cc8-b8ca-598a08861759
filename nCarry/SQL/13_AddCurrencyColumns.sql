-- Add Currency columns to various tables
-- This script adds Currency field to tables that process financial transactions

-- 1. Add Currency to Product table
IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[Product]') AND name = 'Currency')
BEGIN
    ALTER TABLE [dbo].[Product]
    ADD [Currency] NVARCHAR(3) NOT NULL DEFAULT 'GBP';
END
GO

-- 2. Add Currency to SalesOrderItem table
IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[SalesOrderItem]') AND name = 'Currency')
BEGIN
    ALTER TABLE [dbo].[SalesOrderItem]
    ADD [Currency] NVARCHAR(3) NOT NULL DEFAULT 'GBP';
END
GO

-- 3. Add Currency to SalesInvoiceItem table
IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[SalesInvoiceItem]') AND name = 'Currency')
BEGIN
    ALTER TABLE [dbo].[SalesInvoiceItem]
    ADD [Currency] NVARCHAR(3) NOT NULL DEFAULT 'GBP';
END
GO

-- 4. Add Currency to PurchaseOrderItem table
IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[PurchaseOrderItem]') AND name = 'Currency')
BEGIN
    ALTER TABLE [dbo].[PurchaseOrderItem]
    ADD [Currency] NVARCHAR(3) NOT NULL DEFAULT 'GBP';
END
GO

-- 5. Add Currency to QuoteItem table
IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[QuoteItem]') AND name = 'Currency')
BEGIN
    ALTER TABLE [dbo].[QuoteItem]
    ADD [Currency] NVARCHAR(3) NOT NULL DEFAULT 'GBP';
END
GO

-- 6. Add Currency to GoodsReceipt table
IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[GoodsReceipt]') AND name = 'Currency')
BEGIN
    ALTER TABLE [dbo].[GoodsReceipt]
    ADD [Currency] NVARCHAR(3) NOT NULL DEFAULT 'GBP';
END
GO

-- 7. Add Currency to Warehouse table
IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[Warehouse]') AND name = 'Currency')
BEGIN
    ALTER TABLE [dbo].[Warehouse]
    ADD [Currency] NVARCHAR(3) NOT NULL DEFAULT 'GBP';
END
GO

-- Note: The following tables already have Currency fields:
-- - SalesOrder (has Currency)
-- - SalesInvoice (has Currency - standardized from CurrencyCode)
-- - Quote (has Currency - standardized from CurrencyCode)
-- - PurchaseOrder (has CurrencyCode)
-- - PurchaseInvoice/SupplierInvoice (has Currency)

-- Create indexes for Currency columns where appropriate
CREATE NONCLUSTERED INDEX IX_Product_Currency ON [dbo].[Product] ([Currency]);
CREATE NONCLUSTERED INDEX IX_SalesOrderItem_Currency ON [dbo].[SalesOrderItem] ([Currency]);
CREATE NONCLUSTERED INDEX IX_SalesInvoiceItem_Currency ON [dbo].[SalesInvoiceItem] ([Currency]);
CREATE NONCLUSTERED INDEX IX_PurchaseOrderItem_Currency ON [dbo].[PurchaseOrderItem] ([Currency]);
CREATE NONCLUSTERED INDEX IX_QuoteItem_Currency ON [dbo].[QuoteItem] ([Currency]);
CREATE NONCLUSTERED INDEX IX_GoodsReceipt_Currency ON [dbo].[GoodsReceipt] ([Currency]);
CREATE NONCLUSTERED INDEX IX_Warehouse_Currency ON [dbo].[Warehouse] ([Currency]);
GO

PRINT 'Currency columns added successfully to all required tables.';