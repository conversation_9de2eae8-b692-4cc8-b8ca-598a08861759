-- Financial Management Tables Creation Script
-- Purpose: Comprehensive financial tracking including payments, banking, and accounting
-- Supports multi-currency, multiple payment methods, and financial reporting

-- Currency table
CREATE TABLE [dbo].[Currency] (
    [CurrencyID] INT IDENTITY(1,1) NOT NULL,
    [CurrencyCode] NVARCHAR(3) UNIQUE NOT NULL,
    [CurrencyName] NVARCHAR(50) NOT NULL,
    [Symbol] NVARCHAR(10) NULL,
    [DecimalPlaces] INT DEFAULT 2 NOT NULL,
    [IsBaseCurrency] BIT DEFAULT 0 NOT NULL,
    [IsActive] BIT DEFAULT 1 NOT NULL,
    [CreatedDate] DATETIME2 DEFAULT GETDATE() NOT NULL,
    [UpdatedDate] DATETIME2 DEFAULT GETDATE() NOT NULL,
    CONSTRAINT [PK_Currency] PRIMARY KEY CLUSTERED ([CurrencyID] ASC)
);

-- Exchange Rate table
CREATE TABLE [dbo].[ExchangeRate] (
    [ExchangeRateID] INT IDENTITY(1,1) NOT NULL,
    [FromCurrencyID] INT NOT NULL,
    [ToCurrencyID] INT NOT NULL,
    [RateDate] DATE NOT NULL,
    [ExchangeRate] DECIMAL(18,6) NOT NULL,
    [Source] NVARCHAR(50) NULL, -- Manual, Bank, API
    [IsActive] BIT DEFAULT 1 NOT NULL,
    [CreatedDate] DATETIME2 DEFAULT GETDATE() NOT NULL,
    [CreatedByUserID] INT NULL,
    CONSTRAINT [PK_ExchangeRate] PRIMARY KEY CLUSTERED ([ExchangeRateID] ASC),
    CONSTRAINT [FK_ExchangeRate_FromCurrency] FOREIGN KEY ([FromCurrencyID]) REFERENCES [dbo].[Currency] ([CurrencyID]),
    CONSTRAINT [FK_ExchangeRate_ToCurrency] FOREIGN KEY ([ToCurrencyID]) REFERENCES [dbo].[Currency] ([CurrencyID]),
    CONSTRAINT [UQ_ExchangeRate] UNIQUE ([FromCurrencyID], [ToCurrencyID], [RateDate])
);

-- Payment Method table
CREATE TABLE [dbo].[PaymentMethod] (
    [PaymentMethodID] INT IDENTITY(1,1) NOT NULL,
    [MethodCode] NVARCHAR(20) UNIQUE NOT NULL,
    [MethodName] NVARCHAR(50) NOT NULL,
    [MethodType] NVARCHAR(20) NOT NULL, -- Cash, Check, CreditCard, BankTransfer, DirectDebit, Other
    [IsCustomerPayment] BIT DEFAULT 1 NOT NULL,
    [IsSupplierPayment] BIT DEFAULT 1 NOT NULL,
    [RequiresBankDetails] BIT DEFAULT 0 NOT NULL,
    [ProcessingDays] INT DEFAULT 0 NOT NULL,
    [IsActive] BIT DEFAULT 1 NOT NULL,
    [CreatedDate] DATETIME2 DEFAULT GETDATE() NOT NULL,
    [UpdatedDate] DATETIME2 DEFAULT GETDATE() NOT NULL,
    CONSTRAINT [PK_PaymentMethod] PRIMARY KEY CLUSTERED ([PaymentMethodID] ASC)
);

-- Bank table
CREATE TABLE [dbo].[Bank] (
    [BankID] INT IDENTITY(1,1) NOT NULL,
    [BankCode] NVARCHAR(20) UNIQUE NOT NULL,
    [BankName] NVARCHAR(100) NOT NULL,
    [BranchName] NVARCHAR(100) NULL,
    [AccountName] NVARCHAR(100) NOT NULL,
    [AccountNumber] NVARCHAR(50) NOT NULL,
    [SortCode] NVARCHAR(20) NULL,
    [IBAN] NVARCHAR(50) NULL,
    [SWIFT] NVARCHAR(20) NULL,
    [CurrencyID] INT NOT NULL,
    [AccountType] NVARCHAR(20) NOT NULL, -- Current, Savings, Credit, Merchant
    [CurrentBalance] DECIMAL(18,2) DEFAULT 0 NOT NULL,
    [AvailableBalance] DECIMAL(18,2) DEFAULT 0 NOT NULL,
    [CreditLimit] DECIMAL(18,2) DEFAULT 0 NULL,
    [IsCompanyAccount] BIT DEFAULT 1 NOT NULL,
    [IsActive] BIT DEFAULT 1 NOT NULL,
    [LastReconcileDate] DATETIME2 NULL,
    [Notes] NVARCHAR(MAX) NULL,
    [CreatedDate] DATETIME2 DEFAULT GETDATE() NOT NULL,
    [CreatedByUserID] INT NULL,
    [UpdatedDate] DATETIME2 DEFAULT GETDATE() NOT NULL,
    [UpdatedByUserID] INT NULL,
    CONSTRAINT [PK_Bank] PRIMARY KEY CLUSTERED ([BankID] ASC),
    CONSTRAINT [FK_Bank_Currency] FOREIGN KEY ([CurrencyID]) REFERENCES [dbo].[Currency] ([CurrencyID])
);

-- Payment table for all payment transactions
CREATE TABLE [dbo].[Payment] (
    [PaymentID] INT IDENTITY(1,1) NOT NULL,
    [PaymentNumber] NVARCHAR(50) UNIQUE NOT NULL,
    [PaymentDate] DATETIME2 DEFAULT GETDATE() NOT NULL,
    [PaymentType] NVARCHAR(20) NOT NULL, -- CustomerPayment, SupplierPayment, Refund, Transfer
    [PaymentMethodID] INT NOT NULL,
    [PaymentStatus] NVARCHAR(20) DEFAULT 'Pending' NOT NULL, -- Pending, Processing, Completed, Failed, Cancelled, Reversed
    
    -- Party Information
    [CustomerID] INT NULL,
    [SupplierID] INT NULL,
    
    -- Amount Information
    [Amount] DECIMAL(18,2) NOT NULL,
    [CurrencyID] INT NOT NULL,
    [ExchangeRate] DECIMAL(18,6) DEFAULT 1 NOT NULL,
    [BaseAmount] DECIMAL(18,2) NOT NULL, -- Amount in base currency
    
    -- Bank Information
    [BankID] INT NULL,
    [CheckNumber] NVARCHAR(50) NULL,
    [TransactionReference] NVARCHAR(100) NULL,
    
    -- Processing Information
    [ProcessingDate] DATETIME2 NULL,
    [ClearanceDate] DATETIME2 NULL,
    [ProcessingFee] DECIMAL(18,2) DEFAULT 0 NULL,
    
    -- Additional Information
    [PaymentReference] NVARCHAR(100) NULL,
    [Notes] NVARCHAR(MAX) NULL,
    [InternalNotes] NVARCHAR(MAX) NULL,
    
    -- System fields
    [CreatedDate] DATETIME2 DEFAULT GETDATE() NOT NULL,
    [CreatedByUserID] INT NULL,
    [UpdatedDate] DATETIME2 DEFAULT GETDATE() NOT NULL,
    [UpdatedByUserID] INT NULL,
    [IsDeleted] BIT DEFAULT 0 NOT NULL,
    [DeletedDate] DATETIME2 NULL,
    [DeletedByUserID] INT NULL,
    
    CONSTRAINT [PK_Payment] PRIMARY KEY CLUSTERED ([PaymentID] ASC),
    CONSTRAINT [FK_Payment_PaymentMethod] FOREIGN KEY ([PaymentMethodID]) REFERENCES [dbo].[PaymentMethod] ([PaymentMethodID]),
    CONSTRAINT [FK_Payment_Customer] FOREIGN KEY ([CustomerID]) REFERENCES [dbo].[Customer] ([CustomerID]),
    CONSTRAINT [FK_Payment_Supplier] FOREIGN KEY ([SupplierID]) REFERENCES [dbo].[Supplier] ([SupplierID]),
    CONSTRAINT [FK_Payment_Currency] FOREIGN KEY ([CurrencyID]) REFERENCES [dbo].[Currency] ([CurrencyID]),
    CONSTRAINT [FK_Payment_Bank] FOREIGN KEY ([BankID]) REFERENCES [dbo].[Bank] ([BankID])
);

-- Payment Allocation table for linking payments to invoices
CREATE TABLE [dbo].[PaymentAllocation] (
    [AllocationID] INT IDENTITY(1,1) NOT NULL,
    [PaymentID] INT NOT NULL,
    [InvoiceType] NVARCHAR(20) NOT NULL, -- Sales, Purchase
    [InvoiceID] INT NOT NULL,
    [AllocatedAmount] DECIMAL(18,2) NOT NULL,
    [AllocationDate] DATETIME2 DEFAULT GETDATE() NOT NULL,
    [Notes] NVARCHAR(500) NULL,
    [CreatedDate] DATETIME2 DEFAULT GETDATE() NOT NULL,
    [CreatedByUserID] INT NULL,
    CONSTRAINT [PK_PaymentAllocation] PRIMARY KEY CLUSTERED ([AllocationID] ASC),
    CONSTRAINT [FK_PaymentAllocation_Payment] FOREIGN KEY ([PaymentID]) REFERENCES [dbo].[Payment] ([PaymentID])
);

-- Bank Transaction table
CREATE TABLE [dbo].[BankTransaction] (
    [TransactionID] INT IDENTITY(1,1) NOT NULL,
    [BankID] INT NOT NULL,
    [TransactionDate] DATETIME2 NOT NULL,
    [ValueDate] DATETIME2 NOT NULL,
    [TransactionType] NVARCHAR(20) NOT NULL, -- Debit, Credit
    [TransactionCategory] NVARCHAR(50) NULL, -- Payment, Receipt, Fee, Interest, Transfer
    [Description] NVARCHAR(500) NOT NULL,
    [Reference] NVARCHAR(100) NULL,
    [DebitAmount] DECIMAL(18,2) DEFAULT 0 NOT NULL,
    [CreditAmount] DECIMAL(18,2) DEFAULT 0 NOT NULL,
    [Balance] DECIMAL(18,2) NOT NULL,
    [PaymentID] INT NULL,
    [ReconcileStatus] NVARCHAR(20) DEFAULT 'Unreconciled' NOT NULL, -- Unreconciled, Reconciled, Excluded
    [ReconcileDate] DATETIME2 NULL,
    [ReconcileByUserID] INT NULL,
    [ImportBatch] NVARCHAR(50) NULL,
    [CreatedDate] DATETIME2 DEFAULT GETDATE() NOT NULL,
    [CreatedByUserID] INT NULL,
    CONSTRAINT [PK_BankTransaction] PRIMARY KEY CLUSTERED ([TransactionID] ASC),
    CONSTRAINT [FK_BankTransaction_Bank] FOREIGN KEY ([BankID]) REFERENCES [dbo].[Bank] ([BankID]),
    CONSTRAINT [FK_BankTransaction_Payment] FOREIGN KEY ([PaymentID]) REFERENCES [dbo].[Payment] ([PaymentID])
);

-- Tax Code table
CREATE TABLE [dbo].[TaxCode] (
    [TaxCodeID] INT IDENTITY(1,1) NOT NULL,
    [TaxCode] NVARCHAR(20) UNIQUE NOT NULL,
    [TaxName] NVARCHAR(100) NOT NULL,
    [TaxType] NVARCHAR(20) NOT NULL, -- VAT, GST, Sales, Other
    [TaxRate] DECIMAL(5,2) NOT NULL,
    [IsCompound] BIT DEFAULT 0 NOT NULL,
    [IsRecoverable] BIT DEFAULT 1 NOT NULL,
    [AccountCode] NVARCHAR(20) NULL,
    [IsActive] BIT DEFAULT 1 NOT NULL,
    [EffectiveFrom] DATETIME2 NOT NULL,
    [EffectiveTo] DATETIME2 NULL,
    [CreatedDate] DATETIME2 DEFAULT GETDATE() NOT NULL,
    [UpdatedDate] DATETIME2 DEFAULT GETDATE() NOT NULL,
    CONSTRAINT [PK_TaxCode] PRIMARY KEY CLUSTERED ([TaxCodeID] ASC)
);

-- Tax Transaction table
CREATE TABLE [dbo].[TaxTransaction] (
    [TaxTransactionID] INT IDENTITY(1,1) NOT NULL,
    [TransactionDate] DATETIME2 NOT NULL,
    [TaxCodeID] INT NOT NULL,
    [TransactionType] NVARCHAR(50) NOT NULL, -- SalesInvoice, PurchaseInvoice, CreditNote, etc.
    [TransactionID] INT NOT NULL,
    [TaxableAmount] DECIMAL(18,2) NOT NULL,
    [TaxAmount] DECIMAL(18,2) NOT NULL,
    [TaxRate] DECIMAL(5,2) NOT NULL,
    [ReportingPeriod] NVARCHAR(20) NOT NULL, -- YYYY-MM or YYYY-Q1
    [IsReported] BIT DEFAULT 0 NOT NULL,
    [ReportedDate] DATETIME2 NULL,
    [CreatedDate] DATETIME2 DEFAULT GETDATE() NOT NULL,
    CONSTRAINT [PK_TaxTransaction] PRIMARY KEY CLUSTERED ([TaxTransactionID] ASC),
    CONSTRAINT [FK_TaxTransaction_TaxCode] FOREIGN KEY ([TaxCodeID]) REFERENCES [dbo].[TaxCode] ([TaxCodeID])
);

-- Chart of Accounts table
CREATE TABLE [dbo].[ChartOfAccounts] (
    [AccountID] INT IDENTITY(1,1) NOT NULL,
    [AccountCode] NVARCHAR(20) UNIQUE NOT NULL,
    [AccountName] NVARCHAR(100) NOT NULL,
    [AccountType] NVARCHAR(20) NOT NULL, -- Asset, Liability, Equity, Revenue, Expense
    [AccountSubType] NVARCHAR(50) NULL,
    [ParentAccountID] INT NULL,
    [Level] INT DEFAULT 1 NOT NULL,
    [IsHeader] BIT DEFAULT 0 NOT NULL,
    [IsActive] BIT DEFAULT 1 NOT NULL,
    [IsCashAccount] BIT DEFAULT 0 NOT NULL,
    [CurrencyID] INT NULL,
    [Description] NVARCHAR(500) NULL,
    [CreatedDate] DATETIME2 DEFAULT GETDATE() NOT NULL,
    [CreatedByUserID] INT NULL,
    [UpdatedDate] DATETIME2 DEFAULT GETDATE() NOT NULL,
    [UpdatedByUserID] INT NULL,
    CONSTRAINT [PK_ChartOfAccounts] PRIMARY KEY CLUSTERED ([AccountID] ASC),
    CONSTRAINT [FK_ChartOfAccounts_Parent] FOREIGN KEY ([ParentAccountID]) REFERENCES [dbo].[ChartOfAccounts] ([AccountID]),
    CONSTRAINT [FK_ChartOfAccounts_Currency] FOREIGN KEY ([CurrencyID]) REFERENCES [dbo].[Currency] ([CurrencyID])
);

-- General Ledger table
CREATE TABLE [dbo].[GeneralLedger] (
    [GLID] BIGINT IDENTITY(1,1) NOT NULL,
    [PostingDate] DATETIME2 NOT NULL,
    [AccountID] INT NOT NULL,
    [TransactionType] NVARCHAR(50) NOT NULL,
    [TransactionID] INT NOT NULL,
    [Description] NVARCHAR(500) NOT NULL,
    [Reference] NVARCHAR(100) NULL,
    [DebitAmount] DECIMAL(18,2) DEFAULT 0 NOT NULL,
    [CreditAmount] DECIMAL(18,2) DEFAULT 0 NOT NULL,
    [CurrencyID] INT NOT NULL,
    [ExchangeRate] DECIMAL(18,6) DEFAULT 1 NOT NULL,
    [BaseDebitAmount] DECIMAL(18,2) DEFAULT 0 NOT NULL,
    [BaseCreditAmount] DECIMAL(18,2) DEFAULT 0 NOT NULL,
    [CostCenterID] INT NULL,
    [ProjectID] INT NULL,
    [IsPosted] BIT DEFAULT 1 NOT NULL,
    [IsReversed] BIT DEFAULT 0 NOT NULL,
    [ReversalGLID] BIGINT NULL,
    [CreatedDate] DATETIME2 DEFAULT GETDATE() NOT NULL,
    [CreatedByUserID] INT NULL,
    CONSTRAINT [PK_GeneralLedger] PRIMARY KEY CLUSTERED ([GLID] ASC),
    CONSTRAINT [FK_GeneralLedger_Account] FOREIGN KEY ([AccountID]) REFERENCES [dbo].[ChartOfAccounts] ([AccountID]),
    CONSTRAINT [FK_GeneralLedger_Currency] FOREIGN KEY ([CurrencyID]) REFERENCES [dbo].[Currency] ([CurrencyID]),
    CONSTRAINT [FK_GeneralLedger_Reversal] FOREIGN KEY ([ReversalGLID]) REFERENCES [dbo].[GeneralLedger] ([GLID])
);

-- Cost Center table
CREATE TABLE [dbo].[CostCenter] (
    [CostCenterID] INT IDENTITY(1,1) NOT NULL,
    [CostCenterCode] NVARCHAR(20) UNIQUE NOT NULL,
    [CostCenterName] NVARCHAR(100) NOT NULL,
    [ParentCostCenterID] INT NULL,
    [Department] NVARCHAR(100) NULL,
    [Manager] NVARCHAR(100) NULL,
    [IsActive] BIT DEFAULT 1 NOT NULL,
    [CreatedDate] DATETIME2 DEFAULT GETDATE() NOT NULL,
    [UpdatedDate] DATETIME2 DEFAULT GETDATE() NOT NULL,
    CONSTRAINT [PK_CostCenter] PRIMARY KEY CLUSTERED ([CostCenterID] ASC),
    CONSTRAINT [FK_CostCenter_Parent] FOREIGN KEY ([ParentCostCenterID]) REFERENCES [dbo].[CostCenter] ([CostCenterID])
);

-- Budget table
CREATE TABLE [dbo].[Budget] (
    [BudgetID] INT IDENTITY(1,1) NOT NULL,
    [BudgetName] NVARCHAR(100) NOT NULL,
    [BudgetYear] INT NOT NULL,
    [BudgetType] NVARCHAR(20) NOT NULL, -- Annual, Quarterly, Monthly
    [AccountID] INT NOT NULL,
    [CostCenterID] INT NULL,
    [Period1] DECIMAL(18,2) DEFAULT 0 NOT NULL, -- Jan or Q1
    [Period2] DECIMAL(18,2) DEFAULT 0 NOT NULL,
    [Period3] DECIMAL(18,2) DEFAULT 0 NOT NULL,
    [Period4] DECIMAL(18,2) DEFAULT 0 NOT NULL,
    [Period5] DECIMAL(18,2) DEFAULT 0 NOT NULL,
    [Period6] DECIMAL(18,2) DEFAULT 0 NOT NULL,
    [Period7] DECIMAL(18,2) DEFAULT 0 NOT NULL,
    [Period8] DECIMAL(18,2) DEFAULT 0 NOT NULL,
    [Period9] DECIMAL(18,2) DEFAULT 0 NOT NULL,
    [Period10] DECIMAL(18,2) DEFAULT 0 NOT NULL,
    [Period11] DECIMAL(18,2) DEFAULT 0 NOT NULL,
    [Period12] DECIMAL(18,2) DEFAULT 0 NOT NULL,
    [TotalBudget] AS ([Period1] + [Period2] + [Period3] + [Period4] + [Period5] + [Period6] + 
                      [Period7] + [Period8] + [Period9] + [Period10] + [Period11] + [Period12]),
    [Status] NVARCHAR(20) DEFAULT 'Draft' NOT NULL, -- Draft, Approved, Revised
    [Notes] NVARCHAR(MAX) NULL,
    [CreatedDate] DATETIME2 DEFAULT GETDATE() NOT NULL,
    [CreatedByUserID] INT NULL,
    [UpdatedDate] DATETIME2 DEFAULT GETDATE() NOT NULL,
    [UpdatedByUserID] INT NULL,
    CONSTRAINT [PK_Budget] PRIMARY KEY CLUSTERED ([BudgetID] ASC),
    CONSTRAINT [FK_Budget_Account] FOREIGN KEY ([AccountID]) REFERENCES [dbo].[ChartOfAccounts] ([AccountID]),
    CONSTRAINT [FK_Budget_CostCenter] FOREIGN KEY ([CostCenterID]) REFERENCES [dbo].[CostCenter] ([CostCenterID]),
    CONSTRAINT [UQ_Budget] UNIQUE ([BudgetYear], [AccountID], [CostCenterID])
);

-- Expense table (from original Excel)
CREATE TABLE [dbo].[Expense] (
    [ExpenseID] INT IDENTITY(1,1) NOT NULL,
    [ExpenseNumber] NVARCHAR(50) UNIQUE NOT NULL,
    [ExpenseDate] DATETIME2 DEFAULT GETDATE() NOT NULL,
    [ExpenseType] NVARCHAR(50) NOT NULL, -- Travel, Office, Utilities, Marketing, etc.
    [EmployeeID] INT NULL,
    [SupplierID] INT NULL,
    [Description] NVARCHAR(500) NOT NULL,
    [Amount] DECIMAL(18,2) NOT NULL,
    [CurrencyID] INT NOT NULL,
    [TaxAmount] DECIMAL(18,2) DEFAULT 0 NULL,
    [TotalAmount] DECIMAL(18,2) NOT NULL,
    [PaymentMethodID] INT NULL,
    [PaymentStatus] NVARCHAR(20) DEFAULT 'Unpaid' NOT NULL, -- Unpaid, Paid, Reimbursed
    [PaymentID] INT NULL,
    [AccountID] INT NULL,
    [CostCenterID] INT NULL,
    [ProjectID] INT NULL,
    [ApprovalStatus] NVARCHAR(20) DEFAULT 'Pending' NOT NULL, -- Pending, Approved, Rejected
    [ApprovedByUserID] INT NULL,
    [ApprovedDate] DATETIME2 NULL,
    [ReceiptAttached] BIT DEFAULT 0 NOT NULL,
    [Notes] NVARCHAR(MAX) NULL,
    [CreatedDate] DATETIME2 DEFAULT GETDATE() NOT NULL,
    [CreatedByUserID] INT NULL,
    [UpdatedDate] DATETIME2 DEFAULT GETDATE() NOT NULL,
    [UpdatedByUserID] INT NULL,
    CONSTRAINT [PK_Expense] PRIMARY KEY CLUSTERED ([ExpenseID] ASC),
    CONSTRAINT [FK_Expense_Employee] FOREIGN KEY ([EmployeeID]) REFERENCES [dbo].[User] ([UserID]),
    CONSTRAINT [FK_Expense_Supplier] FOREIGN KEY ([SupplierID]) REFERENCES [dbo].[Supplier] ([SupplierID]),
    CONSTRAINT [FK_Expense_Currency] FOREIGN KEY ([CurrencyID]) REFERENCES [dbo].[Currency] ([CurrencyID]),
    CONSTRAINT [FK_Expense_PaymentMethod] FOREIGN KEY ([PaymentMethodID]) REFERENCES [dbo].[PaymentMethod] ([PaymentMethodID]),
    CONSTRAINT [FK_Expense_Payment] FOREIGN KEY ([PaymentID]) REFERENCES [dbo].[Payment] ([PaymentID]),
    CONSTRAINT [FK_Expense_Account] FOREIGN KEY ([AccountID]) REFERENCES [dbo].[ChartOfAccounts] ([AccountID]),
    CONSTRAINT [FK_Expense_CostCenter] FOREIGN KEY ([CostCenterID]) REFERENCES [dbo].[CostCenter] ([CostCenterID]),
    CONSTRAINT [FK_Expense_ApprovedBy] FOREIGN KEY ([ApprovedByUserID]) REFERENCES [dbo].[User] ([UserID])
);

-- Cash Register table
CREATE TABLE [dbo].[CashRegister] (
    [RegisterID] INT IDENTITY(1,1) NOT NULL,
    [RegisterCode] NVARCHAR(20) UNIQUE NOT NULL,
    [RegisterName] NVARCHAR(100) NOT NULL,
    [Location] NVARCHAR(100) NULL,
    [CurrencyID] INT NOT NULL,
    [OpeningBalance] DECIMAL(18,2) DEFAULT 0 NOT NULL,
    [CurrentBalance] DECIMAL(18,2) DEFAULT 0 NOT NULL,
    [IsActive] BIT DEFAULT 1 NOT NULL,
    [LastClosedDate] DATETIME2 NULL,
    [LastClosedByUserID] INT NULL,
    [CreatedDate] DATETIME2 DEFAULT GETDATE() NOT NULL,
    [UpdatedDate] DATETIME2 DEFAULT GETDATE() NOT NULL,
    CONSTRAINT [PK_CashRegister] PRIMARY KEY CLUSTERED ([RegisterID] ASC),
    CONSTRAINT [FK_CashRegister_Currency] FOREIGN KEY ([CurrencyID]) REFERENCES [dbo].[Currency] ([CurrencyID])
);

-- Receipt table
CREATE TABLE [dbo].[Receipt] (
    [ReceiptID] INT IDENTITY(1,1) NOT NULL,
    [ReceiptNumber] NVARCHAR(50) UNIQUE NOT NULL,
    [ReceiptDate] DATETIME2 DEFAULT GETDATE() NOT NULL,
    [RegisterID] INT NULL,
    [CustomerID] INT NULL,
    [PaymentID] INT NOT NULL,
    [Amount] DECIMAL(18,2) NOT NULL,
    [PrintCount] INT DEFAULT 0 NOT NULL,
    [LastPrintDate] DATETIME2 NULL,
    [Notes] NVARCHAR(500) NULL,
    [CreatedDate] DATETIME2 DEFAULT GETDATE() NOT NULL,
    [CreatedByUserID] INT NULL,
    CONSTRAINT [PK_Receipt] PRIMARY KEY CLUSTERED ([ReceiptID] ASC),
    CONSTRAINT [FK_Receipt_Register] FOREIGN KEY ([RegisterID]) REFERENCES [dbo].[CashRegister] ([RegisterID]),
    CONSTRAINT [FK_Receipt_Customer] FOREIGN KEY ([CustomerID]) REFERENCES [dbo].[Customer] ([CustomerID]),
    CONSTRAINT [FK_Receipt_Payment] FOREIGN KEY ([PaymentID]) REFERENCES [dbo].[Payment] ([PaymentID])
);

-- Create indexes
CREATE INDEX [IX_Currency_CurrencyCode] ON [dbo].[Currency] ([CurrencyCode]);
CREATE INDEX [IX_ExchangeRate_Currencies_Date] ON [dbo].[ExchangeRate] ([FromCurrencyID], [ToCurrencyID], [RateDate]);
CREATE INDEX [IX_Payment_PaymentNumber] ON [dbo].[Payment] ([PaymentNumber]);
CREATE INDEX [IX_Payment_PaymentDate] ON [dbo].[Payment] ([PaymentDate]);
CREATE INDEX [IX_Payment_CustomerID] ON [dbo].[Payment] ([CustomerID]);
CREATE INDEX [IX_Payment_SupplierID] ON [dbo].[Payment] ([SupplierID]);
CREATE INDEX [IX_Payment_PaymentStatus] ON [dbo].[Payment] ([PaymentStatus]);
CREATE INDEX [IX_PaymentAllocation_PaymentID] ON [dbo].[PaymentAllocation] ([PaymentID]);
CREATE INDEX [IX_PaymentAllocation_InvoiceType_InvoiceID] ON [dbo].[PaymentAllocation] ([InvoiceType], [InvoiceID]);
CREATE INDEX [IX_BankTransaction_BankID_TransactionDate] ON [dbo].[BankTransaction] ([BankID], [TransactionDate]);
CREATE INDEX [IX_BankTransaction_ReconcileStatus] ON [dbo].[BankTransaction] ([ReconcileStatus]);
CREATE INDEX [IX_TaxTransaction_TaxCodeID] ON [dbo].[TaxTransaction] ([TaxCodeID]);
CREATE INDEX [IX_TaxTransaction_ReportingPeriod] ON [dbo].[TaxTransaction] ([ReportingPeriod]);
CREATE INDEX [IX_GeneralLedger_PostingDate] ON [dbo].[GeneralLedger] ([PostingDate]);
CREATE INDEX [IX_GeneralLedger_AccountID] ON [dbo].[GeneralLedger] ([AccountID]);
CREATE INDEX [IX_GeneralLedger_TransactionType_TransactionID] ON [dbo].[GeneralLedger] ([TransactionType], [TransactionID]);
CREATE INDEX [IX_ChartOfAccounts_AccountCode] ON [dbo].[ChartOfAccounts] ([AccountCode]);
CREATE INDEX [IX_ChartOfAccounts_AccountType] ON [dbo].[ChartOfAccounts] ([AccountType]);
CREATE INDEX [IX_Expense_ExpenseNumber] ON [dbo].[Expense] ([ExpenseNumber]);
CREATE INDEX [IX_Expense_ExpenseDate] ON [dbo].[Expense] ([ExpenseDate]);
CREATE INDEX [IX_Expense_EmployeeID] ON [dbo].[Expense] ([EmployeeID]);
CREATE INDEX [IX_Expense_PaymentStatus] ON [dbo].[Expense] ([PaymentStatus]);

-- Insert default currencies
INSERT INTO [dbo].[Currency] ([CurrencyCode], [CurrencyName], [Symbol], [DecimalPlaces], [IsBaseCurrency])
VALUES 
    ('GBP', 'British Pound', '£', 2, 1),
    ('USD', 'US Dollar', '$', 2, 0),
    ('EUR', 'Euro', '€', 2, 0),
    ('CAD', 'Canadian Dollar', 'C$', 2, 0),
    ('AUD', 'Australian Dollar', 'A$', 2, 0);

-- Insert default payment methods
INSERT INTO [dbo].[PaymentMethod] ([MethodCode], [MethodName], [MethodType], [RequiresBankDetails], [ProcessingDays])
VALUES 
    ('CASH', 'Cash', 'Cash', 0, 0),
    ('CHECK', 'Check', 'Check', 1, 3),
    ('BACS', 'BACS Transfer', 'BankTransfer', 1, 3),
    ('WIRE', 'Wire Transfer', 'BankTransfer', 1, 1),
    ('CC', 'Credit Card', 'CreditCard', 0, 1),
    ('DC', 'Debit Card', 'CreditCard', 0, 1),
    ('DD', 'Direct Debit', 'DirectDebit', 1, 3),
    ('PAYPAL', 'PayPal', 'Other', 0, 1);

-- Insert default tax codes
INSERT INTO [dbo].[TaxCode] ([TaxCode], [TaxName], [TaxType], [TaxRate], [EffectiveFrom])
VALUES 
    ('STD', 'Standard Rate VAT', 'VAT', 20.00, '2011-01-04'),
    ('RED', 'Reduced Rate VAT', 'VAT', 5.00, '2011-01-04'),
    ('ZERO', 'Zero Rate VAT', 'VAT', 0.00, '2011-01-04'),
    ('EXEMPT', 'VAT Exempt', 'VAT', 0.00, '2011-01-04'),
    ('NOVAT', 'No VAT', 'VAT', 0.00, '2011-01-04');