-- =============================================
-- nCarry Complete Sample Data Script
-- =============================================
-- This script inserts comprehensive sample data into all tables
-- Run this after all table creation scripts (01 through 12)
-- =============================================

USE nCarryDB;
GO

PRINT 'Starting sample data insertion...';
GO

-- =============================================
-- 1. COMPANY DATA
-- =============================================
PRINT 'Inserting Companies...';

-- Check if companies already exist
IF NOT EXISTS (SELECT 1 FROM Company WHERE CompanyCode = 'NC001')
BEGIN
    INSERT INTO Company (CompanyCode, CompanyName, LegalName, TaxNumber, RegistrationNumber, Website, Email, Phone, IsActive, CreatedDate, UpdatedDate)
    VALUES 
    ('NC001', 'nCarry Logistics Ltd', 'nCarry Logistics Limited', 'GB123456789', 'REG123456', 'www.ncarry.com', '<EMAIL>', '+44 20 1234 5678', 1, GETDATE(), GETDATE()),
    ('NC002', 'Global Supply Co', 'Global Supply Company Ltd', 'GB987654321', 'REG654321', 'www.globalsupply.com', '<EMAIL>', '+44 20 9876 5432', 1, GETDATE(), GETDATE()),
    ('NC003', 'Tech Distribution Hub', 'Technology Distribution Hub Ltd', 'GB456789123', 'REG789123', 'www.techdist.com', '<EMAIL>', '+44 20 4567 8912', 1, GETDATE(), GETDATE()),
    ('NC004', 'Express Cargo Services', 'Express Cargo Services PLC', 'GB789123456', 'REG321654', 'www.expresscargo.com', '<EMAIL>', '+44 20 7891 2345', 1, GETDATE(), GETDATE()),
    ('NC005', 'Smart Inventory Solutions', 'Smart Inventory Solutions Ltd', 'GB321654987', 'REG987321', 'www.smartinv.com', '<EMAIL>', '+44 20 3216 5498', 1, GETDATE(), GETDATE());
    
    PRINT '  - 5 Companies inserted';
END
ELSE
BEGIN
    PRINT '  - Companies already exist, skipping...';
END
GO

-- =============================================
-- 2. BRANCH DATA
-- =============================================
PRINT 'Inserting Branches...';

DECLARE @CompanyID INT;
SELECT @CompanyID = CompanyID FROM Company WHERE CompanyCode = 'NC001';

IF @CompanyID IS NOT NULL AND NOT EXISTS (SELECT 1 FROM Branch WHERE BranchCode = 'BR001')
BEGIN
    INSERT INTO Branch (BranchCode, BranchName, CompanyID, BranchType, Address1, City, PostCode, Country, Phone, Email, IsDefault, IsActive, CreatedDate, UpdatedDate)
    VALUES 
    ('BR001', 'London Main Branch', @CompanyID, 'Warehouse', '123 Logistics Way', 'London', 'E1 6AN', 'United Kingdom', '+44 20 1111 1111', '<EMAIL>', 1, 1, GETDATE(), GETDATE()),
    ('BR002', 'Manchester Branch', @CompanyID, 'Distribution', '456 Supply Street', 'Manchester', 'M1 2AB', 'United Kingdom', '+44 ************', '<EMAIL>', 0, 1, GETDATE(), GETDATE()),
    ('BR003', 'Birmingham Branch', @CompanyID, 'Warehouse', '789 Storage Road', 'Birmingham', 'B1 3CD', 'United Kingdom', '+44 ************', '<EMAIL>', 0, 1, GETDATE(), GETDATE()),
    ('BR004', 'Glasgow Branch', @CompanyID, 'Distribution', '321 Delivery Avenue', 'Glasgow', 'G1 4EF', 'United Kingdom', '+44 ************', '<EMAIL>', 0, 1, GETDATE(), GETDATE()),
    ('BR005', 'Bristol Branch', @CompanyID, 'Warehouse', '654 Cargo Lane', 'Bristol', 'BS1 5GH', 'United Kingdom', '+44 ************', '<EMAIL>', 0, 1, GETDATE(), GETDATE());
    
    PRINT '  - 5 Branches inserted';
END
GO

-- =============================================
-- 3. CURRENCY DATA
-- =============================================
PRINT 'Inserting Currencies...';

IF NOT EXISTS (SELECT 1 FROM Currency WHERE CurrencyCode = 'GBP')
BEGIN
    INSERT INTO Currency (CurrencyCode, CurrencyName, Symbol, IsBaseCurrency, IsActive, CreatedDate, UpdatedDate)
    VALUES 
    ('GBP', 'British Pound Sterling', '£', 1, 1, GETDATE(), GETDATE()),
    ('USD', 'US Dollar', '$', 0, 1, GETDATE(), GETDATE()),
    ('EUR', 'Euro', '€', 0, 1, GETDATE(), GETDATE()),
    ('JPY', 'Japanese Yen', '¥', 0, 1, GETDATE(), GETDATE()),
    ('AUD', 'Australian Dollar', 'A$', 0, 1, GETDATE(), GETDATE());
    
    PRINT '  - 5 Currencies inserted';
END
GO

-- =============================================
-- 4. TAX CODE DATA
-- =============================================
PRINT 'Inserting Tax Codes...';

IF NOT EXISTS (SELECT 1 FROM TaxCode WHERE TaxCode = 'VAT20')
BEGIN
    INSERT INTO TaxCode (TaxCode, TaxCodeName, Description, TaxRate, TaxType, IsActive, CreatedDate, UpdatedDate)
    VALUES 
    ('VAT20', 'Standard VAT', 'Standard VAT Rate 20%', 20.00, 'Output', 1, GETDATE(), GETDATE()),
    ('VAT5', 'Reduced VAT', 'Reduced VAT Rate 5%', 5.00, 'Output', 1, GETDATE(), GETDATE()),
    ('VAT0', 'Zero Rated', 'Zero Rated VAT', 0.00, 'Output', 1, GETDATE(), GETDATE()),
    ('EXEMPT', 'VAT Exempt', 'VAT Exempt Items', 0.00, 'Exempt', 1, GETDATE(), GETDATE()),
    ('IMPORT', 'Import Duty', 'Import Duty Tax', 12.00, 'Import', 1, GETDATE(), GETDATE());
    
    PRINT '  - 5 Tax Codes inserted';
END
GO

-- =============================================
-- 5. PAYMENT METHOD DATA
-- =============================================
PRINT 'Inserting Payment Methods...';

IF NOT EXISTS (SELECT 1 FROM PaymentMethod WHERE MethodCode = 'CASH')
BEGIN
    INSERT INTO PaymentMethod (MethodCode, MethodName, MethodType, IsActive, RequiresBankDetails, ProcessingDays, CreatedDate, UpdatedDate)
    VALUES 
    ('CASH', 'Cash Payment', 'Cash', 1, 0, 0, GETDATE(), GETDATE()),
    ('BACS', 'Bank Transfer (BACS)', 'BankTransfer', 1, 1, 3, GETDATE(), GETDATE()),
    ('CARD', 'Credit/Debit Card', 'Card', 1, 0, 1, GETDATE(), GETDATE()),
    ('CHQ', 'Cheque', 'Cheque', 1, 1, 5, GETDATE(), GETDATE()),
    ('PP', 'PayPal', 'Online', 1, 0, 1, GETDATE(), GETDATE());
    
    PRINT '  - 5 Payment Methods inserted';
END
GO

-- =============================================
-- 6. BANK DATA
-- =============================================
PRINT 'Inserting Banks...';

IF NOT EXISTS (SELECT 1 FROM Bank WHERE BankCode = 'HSBC')
BEGIN
    INSERT INTO Bank (BankCode, BankName, SwiftCode, SortCode, Country, IsActive, CreatedDate, UpdatedDate)
    VALUES 
    ('HSBC', 'HSBC UK Bank', 'HBUKGB4B', '40-00-00', 'United Kingdom', 1, GETDATE(), GETDATE()),
    ('BARC', 'Barclays Bank', 'BARCGB22', '20-00-00', 'United Kingdom', 1, GETDATE(), GETDATE()),
    ('LLOY', 'Lloyds Bank', 'LOYDGB2L', '30-00-00', 'United Kingdom', 1, GETDATE(), GETDATE()),
    ('NATW', 'NatWest Bank', 'NWBKGB2L', '60-00-00', 'United Kingdom', 1, GETDATE(), GETDATE()),
    ('SANT', 'Santander UK', 'ABBYGB2L', '09-01-00', 'United Kingdom', 1, GETDATE(), GETDATE());
    
    PRINT '  - 5 Banks inserted';
END
GO

-- =============================================
-- 7. UNIT OF MEASURE DATA
-- =============================================
PRINT 'Inserting Units of Measure...';

IF NOT EXISTS (SELECT 1 FROM UnitOfMeasure WHERE UOMCode = 'PC')
BEGIN
    INSERT INTO UnitOfMeasure (UOMCode, UOMName, UOMType, ConversionFactor, IsActive, CreatedDate, UpdatedDate)
    VALUES 
    ('PC', 'Piece', 'Quantity', 1.00, 1, GETDATE(), GETDATE()),
    ('BOX', 'Box', 'Quantity', 12.00, 1, GETDATE(), GETDATE()),
    ('PALLET', 'Pallet', 'Quantity', 480.00, 1, GETDATE(), GETDATE()),
    ('KG', 'Kilogram', 'Weight', 1.00, 1, GETDATE(), GETDATE()),
    ('TON', 'Ton', 'Weight', 1000.00, 1, GETDATE(), GETDATE());
    
    PRINT '  - 5 Units of Measure inserted';
END
GO

-- =============================================
-- 8. PRODUCT CATEGORY DATA
-- =============================================
PRINT 'Inserting Product Categories...';

IF NOT EXISTS (SELECT 1 FROM ProductCategory WHERE CategoryCode = 'ELEC')
BEGIN
    -- Parent Categories
    INSERT INTO ProductCategory (CategoryCode, CategoryName, CategoryPath, IsActive, CreatedDate, UpdatedDate)
    VALUES 
    ('ELEC', 'Electronics', '/ELEC/', 1, GETDATE(), GETDATE()),
    ('FURN', 'Furniture', '/FURN/', 1, GETDATE(), GETDATE()),
    ('STAT', 'Stationery', '/STAT/', 1, GETDATE(), GETDATE()),
    ('TOOL', 'Tools & Equipment', '/TOOL/', 1, GETDATE(), GETDATE()),
    ('SOFT', 'Software', '/SOFT/', 1, GETDATE(), GETDATE());
    
    PRINT '  - 5 Parent Categories inserted';
END
GO

-- Insert Sub Categories
DECLARE @ElecCatID INT, @FurnCatID INT, @StatCatID INT;
SELECT @ElecCatID = CategoryID FROM ProductCategory WHERE CategoryCode = 'ELEC';
SELECT @FurnCatID = CategoryID FROM ProductCategory WHERE CategoryCode = 'FURN';
SELECT @StatCatID = CategoryID FROM ProductCategory WHERE CategoryCode = 'STAT';

IF @ElecCatID IS NOT NULL AND NOT EXISTS (SELECT 1 FROM ProductCategory WHERE CategoryCode = 'COMP')
BEGIN
    INSERT INTO ProductCategory (CategoryCode, CategoryName, ParentCategoryID, CategoryPath, IsActive, CreatedDate, UpdatedDate)
    VALUES 
    ('COMP', 'Computers', @ElecCatID, '/ELEC/COMP/', 1, GETDATE(), GETDATE()),
    ('MOBILE', 'Mobile Devices', @ElecCatID, '/ELEC/MOBILE/', 1, GETDATE(), GETDATE()),
    ('OFFICE', 'Office Furniture', @FurnCatID, '/FURN/OFFICE/', 1, GETDATE(), GETDATE()),
    ('STORAGE', 'Storage Furniture', @FurnCatID, '/FURN/STORAGE/', 1, GETDATE(), GETDATE()),
    ('PAPER', 'Paper Products', @StatCatID, '/STAT/PAPER/', 1, GETDATE(), GETDATE());
    
    PRINT '  - 5 Sub Categories inserted';
END
GO

-- =============================================
-- 9. CUSTOMER DATA
-- =============================================
PRINT 'Inserting Customers...';

IF NOT EXISTS (SELECT 1 FROM Customer WHERE CustomerCode = 'CUST001')
BEGIN
    INSERT INTO Customer (CustomerCode, CustomerName, CustomerType, TaxNumber, CreditLimit, PaymentTerm, Currency, IsActive, CreatedDate, UpdatedDate)
    VALUES 
    ('CUST001', 'Tech Solutions Ltd', 'Business', 'GB111222333', 50000.00, 30, 'GBP', 1, GETDATE(), GETDATE()),
    ('CUST002', 'Office Supplies Direct', 'Business', 'GB444555666', 75000.00, 45, 'GBP', 1, GETDATE(), GETDATE()),
    ('CUST003', 'Retail World PLC', 'Business', 'GB777888999', 100000.00, 60, 'GBP', 1, GETDATE(), GETDATE()),
    ('CUST004', 'Smart Systems Inc', 'Business', 'GB123123123', 60000.00, 30, 'GBP', 1, GETDATE(), GETDATE()),
    ('CUST005', 'Digital Store UK', 'Business', 'GB456456456', 80000.00, 45, 'GBP', 1, GETDATE(), GETDATE());
    
    PRINT '  - 5 Customers inserted';
END
GO

-- Insert Customer Addresses
DECLARE @Cust1 INT, @Cust2 INT, @Cust3 INT, @Cust4 INT, @Cust5 INT;
SELECT @Cust1 = CustomerID FROM Customer WHERE CustomerCode = 'CUST001';
SELECT @Cust2 = CustomerID FROM Customer WHERE CustomerCode = 'CUST002';
SELECT @Cust3 = CustomerID FROM Customer WHERE CustomerCode = 'CUST003';
SELECT @Cust4 = CustomerID FROM Customer WHERE CustomerCode = 'CUST004';
SELECT @Cust5 = CustomerID FROM Customer WHERE CustomerCode = 'CUST005';

IF @Cust1 IS NOT NULL AND NOT EXISTS (SELECT 1 FROM CustomerAddress WHERE CustomerID = @Cust1)
BEGIN
    INSERT INTO CustomerAddress (CustomerID, AddressType, Address1, City, PostCode, Country, IsDefault, IsActive, CreatedDate, UpdatedDate)
    VALUES 
    (@Cust1, 'Billing', '10 Tech Park', 'London', 'EC1V 2NX', 'United Kingdom', 1, 1, GETDATE(), GETDATE()),
    (@Cust1, 'Shipping', '10 Tech Park, Unit 5', 'London', 'EC1V 2NX', 'United Kingdom', 0, 1, GETDATE(), GETDATE()),
    (@Cust2, 'Billing', '25 Commerce Street', 'Manchester', 'M2 5AB', 'United Kingdom', 1, 1, GETDATE(), GETDATE()),
    (@Cust3, 'Billing', '100 Retail Plaza', 'Birmingham', 'B2 4XY', 'United Kingdom', 1, 1, GETDATE(), GETDATE()),
    (@Cust4, 'Billing', '50 Innovation Drive', 'Leeds', 'LS1 3DE', 'United Kingdom', 1, 1, GETDATE(), GETDATE());
    
    PRINT '  - 5 Customer Addresses inserted';
END
GO

-- =============================================
-- 10. SUPPLIER DATA
-- =============================================
PRINT 'Inserting Suppliers...';

IF NOT EXISTS (SELECT 1 FROM Supplier WHERE SupplierCode = 'SUPP001')
BEGIN
    INSERT INTO Supplier (SupplierCode, SupplierName, TaxNumber, PaymentTermDays, PreferredCurrency, IsActive, CreatedDate, UpdatedDate)
    VALUES 
    ('SUPP001', 'Global Electronics Manufacturing', 'GB999888777', 60, 'GBP', 1, GETDATE(), GETDATE()),
    ('SUPP002', 'Computer Components Ltd', 'GB666555444', 45, 'GBP', 1, GETDATE(), GETDATE()),
    ('SUPP003', 'Mobile Tech Suppliers', 'GB333222111', 30, 'USD', 1, GETDATE(), GETDATE()),
    ('SUPP004', 'Furniture Factory Direct', 'GB123456789', 90, 'GBP', 1, GETDATE(), GETDATE()),
    ('SUPP005', 'Office Equipment Wholesale', 'GB987654321', 60, 'EUR', 1, GETDATE(), GETDATE());
    
    PRINT '  - 5 Suppliers inserted';
END
GO

-- Insert Supplier Addresses
DECLARE @Supp1 INT, @Supp2 INT, @Supp3 INT, @Supp4 INT, @Supp5 INT;
SELECT @Supp1 = SupplierID FROM Supplier WHERE SupplierCode = 'SUPP001';
SELECT @Supp2 = SupplierID FROM Supplier WHERE SupplierCode = 'SUPP002';
SELECT @Supp3 = SupplierID FROM Supplier WHERE SupplierCode = 'SUPP003';
SELECT @Supp4 = SupplierID FROM Supplier WHERE SupplierCode = 'SUPP004';
SELECT @Supp5 = SupplierID FROM Supplier WHERE SupplierCode = 'SUPP005';

IF @Supp1 IS NOT NULL AND NOT EXISTS (SELECT 1 FROM SupplierAddress WHERE SupplierID = @Supp1)
BEGIN
    INSERT INTO SupplierAddress (SupplierID, AddressType, Address1, City, PostCode, Country, IsDefault, IsActive, CreatedDate, UpdatedDate)
    VALUES 
    (@Supp1, 'Main', '200 Industrial Estate', 'Birmingham', 'B10 1AB', 'United Kingdom', 1, 1, GETDATE(), GETDATE()),
    (@Supp2, 'Main', '50 Tech Industrial Park', 'Manchester', 'M15 2CD', 'United Kingdom', 1, 1, GETDATE(), GETDATE()),
    (@Supp3, 'Main', '300 Import Business Park', 'London', 'E14 3EF', 'United Kingdom', 1, 1, GETDATE(), GETDATE()),
    (@Supp4, 'Main', '150 Manufacturing Way', 'Leeds', 'LS10 4GH', 'United Kingdom', 1, 1, GETDATE(), GETDATE()),
    (@Supp5, 'Main', '75 Wholesale Avenue', 'Glasgow', 'G40 5IJ', 'United Kingdom', 1, 1, GETDATE(), GETDATE());
    
    PRINT '  - 5 Supplier Addresses inserted';
END
GO

-- =============================================
-- 11. WAREHOUSE DATA
-- =============================================
PRINT 'Inserting Warehouses...';

DECLARE @Branch1 INT;
SELECT @Branch1 = BranchID FROM Branch WHERE BranchCode = 'BR001';

IF @Branch1 IS NOT NULL AND NOT EXISTS (SELECT 1 FROM Warehouse WHERE WarehouseCode = 'WH001')
BEGIN
    INSERT INTO Warehouse (WarehouseCode, WarehouseName, WarehouseType, StorageCapacity, CurrentUtilization, Address1, City, PostCode, Country, IsActive, CreatedDate, UpdatedDate)
    VALUES 
    ('WH001', 'Main Distribution Center', 'Distribution', 10000, 5000, '123 Logistics Way', 'London', 'E1 6AN', 'United Kingdom', 1, GETDATE(), GETDATE()),
    ('WH002', 'North Storage Facility', 'Storage', 8000, 3000, '456 Supply Street', 'Manchester', 'M1 2AB', 'United Kingdom', 1, GETDATE(), GETDATE()),
    ('WH003', 'Central Warehouse', 'Mixed', 12000, 7000, '789 Storage Road', 'Birmingham', 'B1 3CD', 'United Kingdom', 1, GETDATE(), GETDATE()),
    ('WH004', 'Scotland Distribution Hub', 'Distribution', 6000, 2500, '321 Delivery Avenue', 'Glasgow', 'G1 4EF', 'United Kingdom', 1, GETDATE(), GETDATE()),
    ('WH005', 'West Storage Center', 'Storage', 7000, 4000, '654 Cargo Lane', 'Bristol', 'BS1 5GH', 'United Kingdom', 1, GETDATE(), GETDATE());
    
    PRINT '  - 5 Warehouses inserted';
END
GO

-- Insert Warehouse Locations
DECLARE @WH1 INT, @WH2 INT, @WH3 INT;
SELECT @WH1 = WarehouseID FROM Warehouse WHERE WarehouseCode = 'WH001';
SELECT @WH2 = WarehouseID FROM Warehouse WHERE WarehouseCode = 'WH002';
SELECT @WH3 = WarehouseID FROM Warehouse WHERE WarehouseCode = 'WH003';

IF @WH1 IS NOT NULL AND NOT EXISTS (SELECT 1 FROM WarehouseLocation WHERE WarehouseID = @WH1)
BEGIN
    INSERT INTO WarehouseLocation (WarehouseID, LocationCode, LocationName, LocationType, Aisle, Bay, Level, Capacity, CurrentOccupancy, IsActive, CreatedDate, UpdatedDate)
    VALUES 
    (@WH1, 'A-01-01', 'Aisle A Bay 01 Level 01', 'Storage', 'A', '01', '01', 100, 50, 1, GETDATE(), GETDATE()),
    (@WH1, 'A-01-02', 'Aisle A Bay 01 Level 02', 'Storage', 'A', '01', '02', 100, 30, 1, GETDATE(), GETDATE()),
    (@WH2, 'B-01-01', 'Aisle B Bay 01 Level 01', 'Picking', 'B', '01', '01', 150, 75, 1, GETDATE(), GETDATE()),
    (@WH3, 'C-01-01', 'Aisle C Bay 01 Level 01', 'Bulk', 'C', '01', '01', 200, 100, 1, GETDATE(), GETDATE()),
    (@WH1, 'D-01-01', 'Aisle D Bay 01 Level 01', 'Storage', 'D', '01', '01', 120, 60, 1, GETDATE(), GETDATE());
    
    PRINT '  - 5 Warehouse Locations inserted';
END
GO

-- =============================================
-- 12. PRODUCT DATA
-- =============================================
PRINT 'Inserting Products...';

DECLARE @CompCatID INT, @OfficeCatID INT, @MobileCatID INT;
DECLARE @UOMID INT;
SELECT @CompCatID = CategoryID FROM ProductCategory WHERE CategoryCode = 'COMP';
SELECT @OfficeCatID = CategoryID FROM ProductCategory WHERE CategoryCode = 'OFFICE';
SELECT @MobileCatID = CategoryID FROM ProductCategory WHERE CategoryCode = 'MOBILE';
SELECT @UOMID = UOMID FROM UnitOfMeasure WHERE UOMCode = 'PC';

IF @CompCatID IS NOT NULL AND @UOMID IS NOT NULL AND NOT EXISTS (SELECT 1 FROM Product WHERE ProductCode = 'LAPTOP001')
BEGIN
    INSERT INTO Product (ProductCode, ProductName, CategoryID, InventoryUOMID, StandardCost, ListPrice, MinStockLevel, MaxStockLevel, ReorderPoint, LeadTimeDays, IsActive, CreatedDate, UpdatedDate)
    VALUES 
    ('LAPTOP001', 'Business Laptop Pro 15"', @CompCatID, @UOMID, 650.00, 899.99, 10, 50, 15, 7, 1, GETDATE(), GETDATE()),
    ('DESKTOP001', 'Office Desktop PC', @CompCatID, @UOMID, 420.00, 599.99, 5, 25, 8, 5, 1, GETDATE(), GETDATE()),
    ('PHONE001', 'Smartphone X Pro', @MobileCatID, @UOMID, 480.00, 699.99, 20, 100, 30, 3, 1, GETDATE(), GETDATE()),
    ('DESK001', 'Executive Office Desk', @OfficeCatID, @UOMID, 250.00, 399.99, 3, 15, 5, 14, 1, GETDATE(), GETDATE()),
    ('CHAIR001', 'Ergonomic Office Chair', @OfficeCatID, @UOMID, 150.00, 249.99, 5, 30, 10, 10, 1, GETDATE(), GETDATE()),
    ('MONITOR001', '27" LED Monitor', @CompCatID, @UOMID, 200.00, 299.99, 8, 40, 12, 5, 1, GETDATE(), GETDATE()),
    ('KEYBOARD001', 'Mechanical Keyboard', @CompCatID, @UOMID, 50.00, 89.99, 15, 60, 20, 3, 1, GETDATE(), GETDATE()),
    ('MOUSE001', 'Wireless Mouse', @CompCatID, @UOMID, 25.00, 49.99, 20, 80, 25, 3, 1, GETDATE(), GETDATE()),
    ('TABLET001', 'Tablet Pro 10"', @MobileCatID, @UOMID, 350.00, 499.99, 10, 40, 15, 5, 1, GETDATE(), GETDATE()),
    ('CABINET001', 'Filing Cabinet 4-Drawer', @OfficeCatID, @UOMID, 120.00, 199.99, 5, 20, 8, 10, 1, GETDATE(), GETDATE());
    
    PRINT '  - 10 Products inserted';
END
GO

-- =============================================
-- 13. INITIAL INVENTORY
-- =============================================
PRINT 'Inserting Initial Inventory...';

DECLARE @Loc1 INT, @Loc2 INT, @Loc3 INT, @Loc4 INT, @Loc5 INT;
SELECT @Loc1 = LocationID FROM WarehouseLocation WHERE LocationCode = 'A-01-01';
SELECT @Loc2 = LocationID FROM WarehouseLocation WHERE LocationCode = 'A-01-02';
SELECT @Loc3 = LocationID FROM WarehouseLocation WHERE LocationCode = 'B-01-01';
SELECT @Loc4 = LocationID FROM WarehouseLocation WHERE LocationCode = 'C-01-01';
SELECT @Loc5 = LocationID FROM WarehouseLocation WHERE LocationCode = 'D-01-01';

DECLARE @Prod1 INT, @Prod2 INT, @Prod3 INT, @Prod4 INT, @Prod5 INT, @Prod6 INT, @Prod7 INT, @Prod8 INT, @Prod9 INT, @Prod10 INT;
SELECT @Prod1 = ProductID FROM Product WHERE ProductCode = 'LAPTOP001';
SELECT @Prod2 = ProductID FROM Product WHERE ProductCode = 'DESKTOP001';
SELECT @Prod3 = ProductID FROM Product WHERE ProductCode = 'PHONE001';
SELECT @Prod4 = ProductID FROM Product WHERE ProductCode = 'DESK001';
SELECT @Prod5 = ProductID FROM Product WHERE ProductCode = 'CHAIR001';
SELECT @Prod6 = ProductID FROM Product WHERE ProductCode = 'MONITOR001';
SELECT @Prod7 = ProductID FROM Product WHERE ProductCode = 'KEYBOARD001';
SELECT @Prod8 = ProductID FROM Product WHERE ProductCode = 'MOUSE001';
SELECT @Prod9 = ProductID FROM Product WHERE ProductCode = 'TABLET001';
SELECT @Prod10 = ProductID FROM Product WHERE ProductCode = 'CABINET001';

IF @WH1 IS NOT NULL AND @Loc1 IS NOT NULL AND @Prod1 IS NOT NULL AND NOT EXISTS (SELECT 1 FROM Inventory WHERE ProductID = @Prod1)
BEGIN
    INSERT INTO Inventory (WarehouseID, LocationID, ProductID, QuantityOnHand, QuantityReserved, QuantityAvailable, LastCountDate, CreatedDate, UpdatedDate)
    VALUES 
    (@WH1, @Loc1, @Prod1, 25, 5, 20, GETDATE(), GETDATE(), GETDATE()),    -- Laptops
    (@WH1, @Loc2, @Prod2, 15, 2, 13, GETDATE(), GETDATE(), GETDATE()),    -- Desktops
    (@WH2, @Loc3, @Prod3, 50, 10, 40, GETDATE(), GETDATE(), GETDATE()),   -- Phones
    (@WH3, @Loc4, @Prod4, 8, 1, 7, GETDATE(), GETDATE(), GETDATE()),      -- Desks
    (@WH1, @Loc5, @Prod5, 20, 3, 17, GETDATE(), GETDATE(), GETDATE()),    -- Chairs
    (@WH1, @Loc1, @Prod6, 5, 1, 4, GETDATE(), GETDATE(), GETDATE()),      -- Monitors (Low Stock!)
    (@WH1, @Loc2, @Prod7, 30, 5, 25, GETDATE(), GETDATE(), GETDATE()),    -- Keyboards
    (@WH2, @Loc3, @Prod8, 45, 8, 37, GETDATE(), GETDATE(), GETDATE()),    -- Mice
    (@WH1, @Loc1, @Prod9, 12, 2, 10, GETDATE(), GETDATE(), GETDATE()),    -- Tablets
    (@WH3, @Loc4, @Prod10, 6, 0, 6, GETDATE(), GETDATE(), GETDATE());     -- Cabinets
    
    PRINT '  - 10 Inventory records inserted';
END
GO

-- =============================================
-- 14. SALES ORDER DATA
-- =============================================
PRINT 'Inserting Sales Orders...';

DECLARE @UserID INT;
SELECT TOP 1 @UserID = UserID FROM [User] WHERE IsActive = 1;

IF @UserID IS NOT NULL AND @Branch1 IS NOT NULL AND @Cust1 IS NOT NULL AND NOT EXISTS (SELECT 1 FROM SalesOrder WHERE OrderNumber = 'SO-2025-0001')
BEGIN
    INSERT INTO SalesOrder (OrderNumber, CustomerID, BranchID, OrderDate, RequestedDate, OrderStatus, SubTotal, TaxAmount, TotalAmount, Currency, PaymentTerm, CreatedDate, UpdatedDate, CreatedByUserID)
    VALUES 
    ('SO-2025-0001', @Cust1, @Branch1, GETDATE(), DATEADD(day, 7, GETDATE()), 'Confirmed', 2500.00, 500.00, 3000.00, 'GBP', 30, GETDATE(), GETDATE(), @UserID),
    ('SO-2025-0002', @Cust2, @Branch1, GETDATE(), DATEADD(day, 10, GETDATE()), 'Processing', 1800.00, 360.00, 2160.00, 'GBP', 45, GETDATE(), GETDATE(), @UserID),
    ('SO-2025-0003', @Cust3, @Branch1, DATEADD(day, -1, GETDATE()), DATEADD(day, 5, GETDATE()), 'Shipped', 3500.00, 700.00, 4200.00, 'GBP', 60, GETDATE(), GETDATE(), @UserID),
    ('SO-2025-0004', @Cust4, @Branch1, DATEADD(day, -2, GETDATE()), DATEADD(day, 4, GETDATE()), 'Pending', 1200.00, 240.00, 1440.00, 'GBP', 30, GETDATE(), GETDATE(), @UserID),
    ('SO-2025-0005', @Cust5, @Branch1, DATEADD(day, -3, GETDATE()), DATEADD(day, 3, GETDATE()), 'Confirmed', 2100.00, 420.00, 2520.00, 'GBP', 45, GETDATE(), GETDATE(), @UserID);
    
    PRINT '  - 5 Sales Orders inserted';
END
GO

-- Insert Sales Order Items
DECLARE @SO1 INT, @SO2 INT, @SO3 INT, @SO4 INT, @SO5 INT;
SELECT @SO1 = SalesOrderID FROM SalesOrder WHERE OrderNumber = 'SO-2025-0001';
SELECT @SO2 = SalesOrderID FROM SalesOrder WHERE OrderNumber = 'SO-2025-0002';
SELECT @SO3 = SalesOrderID FROM SalesOrder WHERE OrderNumber = 'SO-2025-0003';
SELECT @SO4 = SalesOrderID FROM SalesOrder WHERE OrderNumber = 'SO-2025-0004';
SELECT @SO5 = SalesOrderID FROM SalesOrder WHERE OrderNumber = 'SO-2025-0005';

IF @SO1 IS NOT NULL AND NOT EXISTS (SELECT 1 FROM SalesOrderItem WHERE OrderID = @SO1)
BEGIN
    INSERT INTO SalesOrderItem (OrderID, LineNumber, ProductID, OrderedQuantity, UnitPrice, TaxAmount, LineTotal, CreatedDate, UpdatedDate)
    VALUES 
    (@SO1, 1, @Prod1, 2, 899.99, 359.99, 2159.96, GETDATE(), GETDATE()),     -- 2 Laptops
    (@SO1, 2, @Prod5, 2, 249.99, 99.99, 599.96, GETDATE(), GETDATE()),       -- 2 Chairs
    (@SO2, 1, @Prod2, 3, 599.99, 359.99, 2159.96, GETDATE(), GETDATE()),     -- 3 Desktops
    (@SO3, 1, @Prod3, 5, 699.99, 699.99, 4199.94, GETDATE(), GETDATE()),     -- 5 Phones
    (@SO4, 1, @Prod4, 3, 399.99, 239.99, 1439.96, GETDATE(), GETDATE()),     -- 3 Desks
    (@SO5, 1, @Prod1, 1, 899.99, 179.99, 1079.98, GETDATE(), GETDATE()),     -- 1 Laptop
    (@SO5, 2, @Prod2, 1, 599.99, 119.99, 719.98, GETDATE(), GETDATE()),      -- 1 Desktop
    (@SO5, 3, @Prod3, 1, 699.99, 139.99, 839.98, GETDATE(), GETDATE());      -- 1 Phone
    
    PRINT '  - 8 Sales Order Items inserted';
END
GO

-- =============================================
-- 15. PURCHASE ORDER DATA
-- =============================================
PRINT 'Inserting Purchase Orders...';

IF @UserID IS NOT NULL AND @Branch1 IS NOT NULL AND @Supp1 IS NOT NULL AND NOT EXISTS (SELECT 1 FROM PurchaseOrder WHERE PurchaseOrderNumber = 'PO-2025-0001')
BEGIN
    INSERT INTO PurchaseOrder (PurchaseOrderNumber, SupplierID, BranchID, OrderDate, ExpectedDate, Status, SubTotal, TaxAmount, TotalAmount, Currency, PaymentTermDays, CreatedDate, UpdatedDate, CreatedByUserID)
    VALUES 
    ('PO-2025-0001', @Supp1, @Branch1, DATEADD(day, -10, GETDATE()), GETDATE(), 'Received', 6500.00, 1300.00, 7800.00, 'GBP', 60, GETDATE(), GETDATE(), @UserID),
    ('PO-2025-0002', @Supp2, @Branch1, DATEADD(day, -7, GETDATE()), DATEADD(day, 3, GETDATE()), 'Confirmed', 4200.00, 840.00, 5040.00, 'GBP', 45, GETDATE(), GETDATE(), @UserID),
    ('PO-2025-0003', @Supp3, @Branch1, DATEADD(day, -5, GETDATE()), DATEADD(day, 5, GETDATE()), 'Pending', 9600.00, 1920.00, 11520.00, 'USD', 30, GETDATE(), GETDATE(), @UserID),
    ('PO-2025-0004', @Supp4, @Branch1, DATEADD(day, -15, GETDATE()), DATEADD(day, -5, GETDATE()), 'Received', 2500.00, 500.00, 3000.00, 'GBP', 90, GETDATE(), GETDATE(), @UserID),
    ('PO-2025-0005', @Supp5, @Branch1, DATEADD(day, -3, GETDATE()), DATEADD(day, 7, GETDATE()), 'Confirmed', 1500.00, 300.00, 1800.00, 'EUR', 60, GETDATE(), GETDATE(), @UserID);
    
    PRINT '  - 5 Purchase Orders inserted';
END
GO

-- =============================================
-- 16. PAYMENT DATA (Customer Payments)
-- =============================================
PRINT 'Inserting Payments...';

DECLARE @PayMethodID INT;
SELECT @PayMethodID = PaymentMethodID FROM PaymentMethod WHERE MethodCode = 'BACS';

IF @PayMethodID IS NOT NULL AND @Cust1 IS NOT NULL AND NOT EXISTS (SELECT 1 FROM Payment WHERE PaymentNumber = 'PAY-2025-0001')
BEGIN
    INSERT INTO Payment (PaymentNumber, PaymentType, PayerType, PayerID, BranchID, PaymentDate, Amount, Currency, PaymentMethodID, PaymentStatus, Reference, Description, CreatedDate, UpdatedDate, CreatedByUserID)
    VALUES 
    ('PAY-2025-0001', 'CustomerPayment', 'Customer', @Cust1, @Branch1, GETDATE(), 3000.00, 'GBP', @PayMethodID, 'Completed', 'INV-001', 'Payment for SO-2025-0001', GETDATE(), GETDATE(), @UserID),
    ('PAY-2025-0002', 'CustomerPayment', 'Customer', @Cust3, @Branch1, DATEADD(day, -1, GETDATE()), 4200.00, 'GBP', @PayMethodID, 'Completed', 'INV-003', 'Payment for SO-2025-0003', GETDATE(), GETDATE(), @UserID),
    ('PAY-2025-0003', 'SupplierPayment', 'Supplier', @Supp1, @Branch1, DATEADD(day, -2, GETDATE()), 7800.00, 'GBP', @PayMethodID, 'Completed', 'PO-2025-0001', 'Payment to supplier', GETDATE(), GETDATE(), @UserID),
    ('PAY-2025-0004', 'CustomerPayment', 'Customer', @Cust2, @Branch1, GETDATE(), 1000.00, 'GBP', @PayMethodID, 'Completed', 'PART-PAY', 'Partial payment for SO-2025-0002', GETDATE(), GETDATE(), @UserID),
    ('PAY-2025-0005', 'SupplierPayment', 'Supplier', @Supp4, @Branch1, DATEADD(day, -3, GETDATE()), 3000.00, 'GBP', @PayMethodID, 'Completed', 'PO-2025-0004', 'Payment to furniture supplier', GETDATE(), GETDATE(), @UserID);
    
    PRINT '  - 5 Payments inserted';
END
GO

-- =============================================
-- 17. INVENTORY TRANSACTIONS
-- =============================================
PRINT 'Inserting Inventory Transactions...';

IF @WH1 IS NOT NULL AND @Loc1 IS NOT NULL AND @Prod1 IS NOT NULL AND NOT EXISTS (SELECT 1 FROM InventoryTransaction WHERE TransactionNumber = 'IT-2025-0001')
BEGIN
    INSERT INTO InventoryTransaction (TransactionNumber, TransactionType, TransactionDate, WarehouseID, LocationID, ProductID, Quantity, UnitCost, TotalCost, ReferenceType, ReferenceID, Notes, CreatedDate, UpdatedDate, CreatedByUserID)
    VALUES 
    ('IT-2025-0001', 'Receipt', DATEADD(day, -10, GETDATE()), @WH1, @Loc1, @Prod1, 10, 650.00, 6500.00, 'PurchaseOrder', 1, 'Received laptops from PO-2025-0001', GETDATE(), GETDATE(), @UserID),
    ('IT-2025-0002', 'Issue', DATEADD(day, -1, GETDATE()), @WH1, @Loc1, @Prod1, 2, 650.00, 1300.00, 'SalesOrder', 1, 'Issued for SO-2025-0001', GETDATE(), GETDATE(), @UserID),
    ('IT-2025-0003', 'Receipt', DATEADD(day, -15, GETDATE()), @WH3, @Loc4, @Prod4, 10, 250.00, 2500.00, 'PurchaseOrder', 4, 'Received desks from PO-2025-0004', GETDATE(), GETDATE(), @UserID),
    ('IT-2025-0004', 'Issue', DATEADD(day, -2, GETDATE()), @WH3, @Loc4, @Prod4, 3, 250.00, 750.00, 'SalesOrder', 4, 'Issued for SO-2025-0004', GETDATE(), GETDATE(), @UserID),
    ('IT-2025-0005', 'Adjustment', GETDATE(), @WH2, @Loc3, @Prod3, 5, 480.00, 2400.00, 'Manual', 0, 'Stock adjustment - found extra units', GETDATE(), GETDATE(), @UserID);
    
    PRINT '  - 5 Inventory Transactions inserted';
END
GO

-- =============================================
-- 18. CARRIER DATA
-- =============================================
PRINT 'Inserting Carriers...';

IF NOT EXISTS (SELECT 1 FROM Carrier WHERE CarrierCode = 'DHL')
BEGIN
    INSERT INTO Carrier (CarrierCode, CarrierName, CarrierType, ContactName, Phone, Email, Website, IsActive, CreatedDate, UpdatedDate)
    VALUES 
    ('DHL', 'DHL Express UK', 'Express', 'DHL Support', '+44 20 7111 1111', '<EMAIL>', 'www.dhl.co.uk', 1, GETDATE(), GETDATE()),
    ('UPS', 'UPS United Kingdom', 'Standard', 'UPS Customer Service', '+44 20 7222 2222', '<EMAIL>', 'www.ups.com/gb', 1, GETDATE(), GETDATE()),
    ('FEDEX', 'FedEx UK', 'Express', 'FedEx Support', '+44 20 7333 3333', '<EMAIL>', 'www.fedex.com/gb', 1, GETDATE(), GETDATE()),
    ('ROYAL', 'Royal Mail', 'Standard', 'Royal Mail Business', '+44 20 7444 4444', '<EMAIL>', 'www.royalmail.com', 1, GETDATE(), GETDATE()),
    ('LOCAL', 'Local Courier Services', 'SameDay', 'Local Support', '+44 20 7555 5555', '<EMAIL>', 'www.localcourier.co.uk', 1, GETDATE(), GETDATE());
    
    PRINT '  - 5 Carriers inserted';
END
GO

-- =============================================
-- 19. DELIVERY ZONE DATA
-- =============================================
PRINT 'Inserting Delivery Zones...';

DECLARE @Branch2 INT, @Branch3 INT, @Branch4 INT, @Branch5 INT;
SELECT @Branch2 = BranchID FROM Branch WHERE BranchCode = 'BR002';
SELECT @Branch3 = BranchID FROM Branch WHERE BranchCode = 'BR003';
SELECT @Branch4 = BranchID FROM Branch WHERE BranchCode = 'BR004';
SELECT @Branch5 = BranchID FROM Branch WHERE BranchCode = 'BR005';

IF @Branch1 IS NOT NULL AND NOT EXISTS (SELECT 1 FROM DeliveryZone WHERE ZoneCode = 'ZONE-LON-1')
BEGIN
    INSERT INTO DeliveryZone (ZoneCode, ZoneName, BranchID, Description, BaseDeliveryCharge, IsActive, CreatedDate, UpdatedDate)
    VALUES 
    ('ZONE-LON-1', 'London Central', @Branch1, 'Central London delivery zone', 15.00, 1, GETDATE(), GETDATE()),
    ('ZONE-LON-2', 'Greater London', @Branch1, 'Greater London area', 25.00, 1, GETDATE(), GETDATE()),
    ('ZONE-MAN-1', 'Manchester City', @Branch2, 'Manchester city center', 12.00, 1, GETDATE(), GETDATE()),
    ('ZONE-BIR-1', 'Birmingham Central', @Branch3, 'Birmingham city center', 12.00, 1, GETDATE(), GETDATE()),
    ('ZONE-GLA-1', 'Glasgow City', @Branch4, 'Glasgow city center', 15.00, 1, GETDATE(), GETDATE());
    
    PRINT '  - 5 Delivery Zones inserted';
END
GO

-- =============================================
-- 20. VEHICLE DATA
-- =============================================
PRINT 'Inserting Vehicles...';

IF NOT EXISTS (SELECT 1 FROM Vehicle WHERE VehicleNumber = 'LON-VAN-001')
BEGIN
    INSERT INTO Vehicle (VehicleNumber, VehicleType, Make, Model, Year, Capacity, CurrentStatus, IsActive, CreatedDate, UpdatedDate)
    VALUES 
    ('LON-VAN-001', 'Van', 'Ford', 'Transit', 2023, 1000, 'Available', 1, GETDATE(), GETDATE()),
    ('LON-TRK-001', 'Truck', 'Mercedes', 'Sprinter', 2022, 3000, 'Available', 1, GETDATE(), GETDATE()),
    ('MAN-VAN-001', 'Van', 'Peugeot', 'Boxer', 2023, 1200, 'InUse', 1, GETDATE(), GETDATE()),
    ('BIR-VAN-001', 'Van', 'Citroen', 'Relay', 2022, 1100, 'Available', 1, GETDATE(), GETDATE()),
    ('GLA-TRK-001', 'Truck', 'Iveco', 'Daily', 2023, 2500, 'Maintenance', 1, GETDATE(), GETDATE());
    
    PRINT '  - 5 Vehicles inserted';
END
GO

-- =============================================
-- 21. SHIPMENT DATA
-- =============================================
PRINT 'Inserting Shipments...';

DECLARE @CarrierID1 INT, @CarrierID2 INT;
SELECT @CarrierID1 = CarrierID FROM Carrier WHERE CarrierCode = 'DHL';
SELECT @CarrierID2 = CarrierID FROM Carrier WHERE CarrierCode = 'UPS';

IF @CarrierID1 IS NOT NULL AND @Cust1 IS NOT NULL AND NOT EXISTS (SELECT 1 FROM Shipment WHERE ShipmentNumber = 'SH-2025-0001')
BEGIN
    INSERT INTO Shipment (ShipmentNumber, BranchID, CustomerID, CarrierID, ShipmentDate, EstimatedDeliveryDate, ShipmentStatus, RecipientName, RecipientPhone, ShipToAddress, ShipToPostCode, ShipToCity, ShipToCountry, Weight, DeclaredValue, Priority, CreatedDate, UpdatedDate, CreatedByUserID)
    VALUES 
    ('SH-2025-0001', @Branch1, @Cust1, @CarrierID1, GETDATE(), DATEADD(day, 1, GETDATE()), 'InTransit', 'Tech Solutions Receiving', '+44 20 1111 2222', '10 Tech Park', 'EC1V 2NX', 'London', 'United Kingdom', 15.5, 2159.96, 'High', GETDATE(), GETDATE(), @UserID),
    ('SH-2025-0002', @Branch1, @Cust3, @CarrierID2, DATEADD(day, -1, GETDATE()), DATEADD(day, 1, GETDATE()), 'Delivered', 'Retail World Warehouse', '+44 121 3333 4444', '100 Retail Plaza', 'B2 4XY', 'Birmingham', 'United Kingdom', 10.0, 4199.94, 'Medium', GETDATE(), GETDATE(), @UserID),
    ('SH-2025-0003', @Branch1, @Cust2, @CarrierID1, GETDATE(), DATEADD(day, 2, GETDATE()), 'Pending', 'Office Supplies Receiving', '+44 161 5555 6666', '25 Commerce Street', 'M2 5AB', 'Manchester', 'United Kingdom', 25.0, 2159.96, 'Low', GETDATE(), GETDATE(), @UserID),
    ('SH-2025-0004', @Branch1, @Cust4, @CarrierID2, DATEADD(day, -2, GETDATE()), GETDATE(), 'OutForDelivery', 'Smart Systems Dock', '+44 113 7777 8888', '50 Innovation Drive', 'LS1 3DE', 'Leeds', 'United Kingdom', 120.0, 1439.96, 'High', GETDATE(), GETDATE(), @UserID),
    ('SH-2025-0005', @Branch1, @Cust5, @CarrierID1, GETDATE(), DATEADD(day, 3, GETDATE()), 'Pending', 'Digital Store Receiving', '+44 141 9999 0000', '75 Digital Avenue', 'G2 6FG', 'Glasgow', 'United Kingdom', 8.0, 2520.00, 'Medium', GETDATE(), GETDATE(), @UserID);
    
    PRINT '  - 5 Shipments inserted';
END
GO

-- =============================================
-- FINAL STATUS REPORT
-- =============================================
PRINT '';
PRINT '========================================';
PRINT 'Sample Data Insertion Complete!';
PRINT '========================================';
PRINT '';

-- Show record counts
SELECT 'Companies' as [Table], COUNT(*) as [Records] FROM Company
UNION ALL SELECT 'Branches', COUNT(*) FROM Branch
UNION ALL SELECT 'Currencies', COUNT(*) FROM Currency
UNION ALL SELECT 'Tax Codes', COUNT(*) FROM TaxCode
UNION ALL SELECT 'Payment Methods', COUNT(*) FROM PaymentMethod
UNION ALL SELECT 'Banks', COUNT(*) FROM Bank
UNION ALL SELECT 'Units of Measure', COUNT(*) FROM UnitOfMeasure
UNION ALL SELECT 'Product Categories', COUNT(*) FROM ProductCategory
UNION ALL SELECT 'Customers', COUNT(*) FROM Customer
UNION ALL SELECT 'Customer Addresses', COUNT(*) FROM CustomerAddress
UNION ALL SELECT 'Suppliers', COUNT(*) FROM Supplier
UNION ALL SELECT 'Supplier Addresses', COUNT(*) FROM SupplierAddress
UNION ALL SELECT 'Warehouses', COUNT(*) FROM Warehouse
UNION ALL SELECT 'Warehouse Locations', COUNT(*) FROM WarehouseLocation
UNION ALL SELECT 'Products', COUNT(*) FROM Product
UNION ALL SELECT 'Inventory', COUNT(*) FROM Inventory
UNION ALL SELECT 'Sales Orders', COUNT(*) FROM SalesOrder
UNION ALL SELECT 'Sales Order Items', COUNT(*) FROM SalesOrderItem
UNION ALL SELECT 'Purchase Orders', COUNT(*) FROM PurchaseOrder
UNION ALL SELECT 'Payments', COUNT(*) FROM Payment
UNION ALL SELECT 'Inventory Transactions', COUNT(*) FROM InventoryTransaction
UNION ALL SELECT 'Carriers', COUNT(*) FROM Carrier
UNION ALL SELECT 'Delivery Zones', COUNT(*) FROM DeliveryZone
UNION ALL SELECT 'Vehicles', COUNT(*) FROM Vehicle
UNION ALL SELECT 'Shipments', COUNT(*) FROM Shipment
ORDER BY [Table];

PRINT '';
PRINT 'You can now use the application with sample data!';
PRINT '========================================';
GO