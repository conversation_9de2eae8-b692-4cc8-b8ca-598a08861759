-- Supplier Extended Tables Creation Script
-- Purpose: Additional tables for comprehensive supplier management
-- Complements the base Supplier table with advanced features

-- Supplier Contact table for multiple contacts per supplier
CREATE TABLE [dbo].[SupplierContact] (
    [ContactID] INT IDENTITY(1,1) NOT NULL,
    [SupplierID] INT NOT NULL,
    [ContactName] NVARCHAR(255) NOT NULL,
    [JobTitle] NVARCHAR(100) NULL,
    [Department] NVARCHAR(100) NULL,
    [Email] NVARCHAR(255) NULL,
    [Phone] NVARCHAR(50) NULL,
    [Mobile] NVARCHAR(50) NULL,
    [Extension] NVARCHAR(20) NULL,
    [IsPrimary] BIT DEFAULT 0 NOT NULL,
    [ContactType] NVARCHAR(50) NULL, -- Sales, Support, Accounts, Technical, Management
    [PreferredContactMethod] NVARCHAR(20) NULL, -- Email, Phone, Mobile
    [BestTimeToContact] NVARCHAR(100) NULL,
    [Language] NVARCHAR(10) DEFAULT 'en-GB' NULL,
    [TimeZone] NVARCHAR(50) NULL,
    [Notes] NVARCHAR(MAX) NULL,
    [IsActive] BIT DEFAULT 1 NOT NULL,
    [CreatedDate] DATETIME2 DEFAULT GETDATE() NOT NULL,
    [CreatedByUserID] INT NULL,
    [UpdatedDate] DATETIME2 DEFAULT GETDATE() NOT NULL,
    [UpdatedByUserID] INT NULL,
    CONSTRAINT [PK_SupplierContact] PRIMARY KEY CLUSTERED ([ContactID] ASC),
    CONSTRAINT [FK_SupplierContact_Supplier] FOREIGN KEY ([SupplierID]) REFERENCES [dbo].[Supplier] ([SupplierID])
);

-- Supplier Price List table for supplier product pricing
CREATE TABLE [dbo].[SupplierPriceList] (
    [PriceListID] INT IDENTITY(1,1) NOT NULL,
    [SupplierID] INT NOT NULL,
    [PriceListCode] NVARCHAR(20) UNIQUE NOT NULL,
    [PriceListName] NVARCHAR(100) NOT NULL,
    [Description] NVARCHAR(500) NULL,
    [Currency] NVARCHAR(3) DEFAULT 'GBP' NOT NULL,
    [EffectiveFrom] DATETIME2 NOT NULL,
    [EffectiveTo] DATETIME2 NULL,
    [MinOrderValue] DECIMAL(18,2) DEFAULT 0 NULL,
    [VolumeDiscountEnabled] BIT DEFAULT 0 NULL,
    [EarlyPaymentDiscountPercent] DECIMAL(5,2) DEFAULT 0 NULL,
    [IsContractPricing] BIT DEFAULT 0 NULL,
    [ContractNumber] NVARCHAR(50) NULL,
    [Priority] INT DEFAULT 100 NOT NULL, -- Lower number = higher priority
    [IsActive] BIT DEFAULT 1 NOT NULL,
    [CreatedDate] DATETIME2 DEFAULT GETDATE() NOT NULL,
    [CreatedByUserID] INT NULL,
    [UpdatedDate] DATETIME2 DEFAULT GETDATE() NOT NULL,
    [UpdatedByUserID] INT NULL,
    CONSTRAINT [PK_SupplierPriceList] PRIMARY KEY CLUSTERED ([PriceListID] ASC),
    CONSTRAINT [FK_SupplierPriceList_Supplier] FOREIGN KEY ([SupplierID]) REFERENCES [dbo].[Supplier] ([SupplierID])
);

-- Supplier Price List Items for specific product pricing
CREATE TABLE [dbo].[SupplierPriceListItem] (
    [PriceListItemID] INT IDENTITY(1,1) NOT NULL,
    [PriceListID] INT NOT NULL,
    [SupplierProductCode] NVARCHAR(100) NOT NULL,
    [ProductID] INT NULL, -- Our internal product ID mapping
    [ProductDescription] NVARCHAR(500) NOT NULL,
    [UnitCost] DECIMAL(18,4) NOT NULL,
    [UOM] NVARCHAR(20) NOT NULL, -- Unit of Measure
    [PackSize] INT DEFAULT 1 NOT NULL,
    [MinOrderQuantity] DECIMAL(18,3) DEFAULT 1 NOT NULL,
    [OrderMultiple] DECIMAL(18,3) DEFAULT 1 NOT NULL,
    [LeadTimeDays] INT DEFAULT 7 NOT NULL,
    [VolumeBreaks] NVARCHAR(MAX) NULL, -- JSON format for volume pricing
    [Notes] NVARCHAR(500) NULL,
    [IsActive] BIT DEFAULT 1 NOT NULL,
    [CreatedDate] DATETIME2 DEFAULT GETDATE() NOT NULL,
    [UpdatedDate] DATETIME2 DEFAULT GETDATE() NOT NULL,
    CONSTRAINT [PK_SupplierPriceListItem] PRIMARY KEY CLUSTERED ([PriceListItemID] ASC),
    CONSTRAINT [FK_SupplierPriceListItem_PriceList] FOREIGN KEY ([PriceListID]) REFERENCES [dbo].[SupplierPriceList] ([PriceListID])
);

-- Supplier Performance table for tracking supplier metrics
CREATE TABLE [dbo].[SupplierPerformance] (
    [PerformanceID] INT IDENTITY(1,1) NOT NULL,
    [SupplierID] INT NOT NULL,
    [PeriodYear] INT NOT NULL,
    [PeriodMonth] INT NOT NULL,
    [OnTimeDeliveryCount] INT DEFAULT 0 NOT NULL,
    [LateDeliveryCount] INT DEFAULT 0 NOT NULL,
    [OnTimeDeliveryRate] DECIMAL(5,2) NULL, -- Percentage
    [QualityAcceptedCount] INT DEFAULT 0 NOT NULL,
    [QualityRejectedCount] INT DEFAULT 0 NOT NULL,
    [QualityRate] DECIMAL(5,2) NULL, -- Percentage
    [TotalOrderCount] INT DEFAULT 0 NOT NULL,
    [TotalOrderValue] DECIMAL(18,2) DEFAULT 0 NOT NULL,
    [AverageLeadTime] DECIMAL(10,2) NULL, -- Days
    [PriceVariancePercent] DECIMAL(5,2) NULL, -- Actual vs quoted
    [ResponseTime] DECIMAL(10,2) NULL, -- Hours
    [OverallScore] DECIMAL(5,2) NULL, -- 0-100
    [Notes] NVARCHAR(MAX) NULL,
    [CreatedDate] DATETIME2 DEFAULT GETDATE() NOT NULL,
    [UpdatedDate] DATETIME2 DEFAULT GETDATE() NOT NULL,
    CONSTRAINT [PK_SupplierPerformance] PRIMARY KEY CLUSTERED ([PerformanceID] ASC),
    CONSTRAINT [FK_SupplierPerformance_Supplier] FOREIGN KEY ([SupplierID]) REFERENCES [dbo].[Supplier] ([SupplierID]),
    CONSTRAINT [UQ_SupplierPerformance_Period] UNIQUE ([SupplierID], [PeriodYear], [PeriodMonth])
);

-- Supplier Product mapping table for linking suppliers to products
CREATE TABLE [dbo].[SupplierProduct] (
    [SupplierProductID] INT IDENTITY(1,1) NOT NULL,
    [SupplierID] INT NOT NULL,
    [ProductID] INT NOT NULL,
    [SupplierProductCode] NVARCHAR(100) NOT NULL,
    [SupplierProductName] NVARCHAR(255) NULL,
    [IsPreferredSupplier] BIT DEFAULT 0 NOT NULL,
    [Priority] INT DEFAULT 100 NOT NULL, -- Lower = higher priority
    [MinOrderQuantity] DECIMAL(18,3) DEFAULT 1 NOT NULL,
    [StandardLeadTime] INT DEFAULT 7 NOT NULL, -- Days
    [LastPurchasePrice] DECIMAL(18,4) NULL,
    [LastPurchaseDate] DATETIME2 NULL,
    [Notes] NVARCHAR(MAX) NULL,
    [IsActive] BIT DEFAULT 1 NOT NULL,
    [CreatedDate] DATETIME2 DEFAULT GETDATE() NOT NULL,
    [UpdatedDate] DATETIME2 DEFAULT GETDATE() NOT NULL,
    CONSTRAINT [PK_SupplierProduct] PRIMARY KEY CLUSTERED ([SupplierProductID] ASC),
    CONSTRAINT [FK_SupplierProduct_Supplier] FOREIGN KEY ([SupplierID]) REFERENCES [dbo].[Supplier] ([SupplierID]),
    CONSTRAINT [UQ_SupplierProduct] UNIQUE ([SupplierID], [ProductID])
);

-- Supplier Document table for storing supplier-related documents
CREATE TABLE [dbo].[SupplierDocument] (
    [DocumentID] INT IDENTITY(1,1) NOT NULL,
    [SupplierID] INT NOT NULL,
    [DocumentType] NVARCHAR(50) NOT NULL, -- Certificate, Insurance, Contract, Catalog, Other
    [DocumentName] NVARCHAR(255) NOT NULL,
    [DocumentPath] NVARCHAR(500) NULL,
    [DocumentData] VARBINARY(MAX) NULL,
    [FileSize] BIGINT NULL,
    [MimeType] NVARCHAR(100) NULL,
    [ExpiryDate] DATETIME2 NULL,
    [ReminderDays] INT DEFAULT 30 NULL, -- Days before expiry to remind
    [Notes] NVARCHAR(MAX) NULL,
    [IsActive] BIT DEFAULT 1 NOT NULL,
    [UploadedDate] DATETIME2 DEFAULT GETDATE() NOT NULL,
    [UploadedByUserID] INT NULL,
    CONSTRAINT [PK_SupplierDocument] PRIMARY KEY CLUSTERED ([DocumentID] ASC),
    CONSTRAINT [FK_SupplierDocument_Supplier] FOREIGN KEY ([SupplierID]) REFERENCES [dbo].[Supplier] ([SupplierID])
);

-- Supplier Audit table for tracking supplier audits and assessments
CREATE TABLE [dbo].[SupplierAudit] (
    [AuditID] INT IDENTITY(1,1) NOT NULL,
    [SupplierID] INT NOT NULL,
    [AuditDate] DATETIME2 NOT NULL,
    [AuditType] NVARCHAR(50) NOT NULL, -- Quality, Financial, Compliance, Site Visit
    [AuditorName] NVARCHAR(255) NULL,
    [AuditScore] DECIMAL(5,2) NULL, -- 0-100
    [AuditStatus] NVARCHAR(20) NOT NULL, -- Passed, Failed, Conditional
    [Findings] NVARCHAR(MAX) NULL,
    [CorrectiveActions] NVARCHAR(MAX) NULL,
    [NextAuditDate] DATETIME2 NULL,
    [DocumentID] INT NULL, -- Link to audit report document
    [CreatedDate] DATETIME2 DEFAULT GETDATE() NOT NULL,
    [CreatedByUserID] INT NULL,
    CONSTRAINT [PK_SupplierAudit] PRIMARY KEY CLUSTERED ([AuditID] ASC),
    CONSTRAINT [FK_SupplierAudit_Supplier] FOREIGN KEY ([SupplierID]) REFERENCES [dbo].[Supplier] ([SupplierID]),
    CONSTRAINT [FK_SupplierAudit_Document] FOREIGN KEY ([DocumentID]) REFERENCES [dbo].[SupplierDocument] ([DocumentID])
);

-- Create indexes
CREATE INDEX [IX_SupplierContact_SupplierID] ON [dbo].[SupplierContact] ([SupplierID]);
CREATE INDEX [IX_SupplierContact_Email] ON [dbo].[SupplierContact] ([Email]);
CREATE INDEX [IX_SupplierPriceList_SupplierID] ON [dbo].[SupplierPriceList] ([SupplierID]);
CREATE INDEX [IX_SupplierPriceList_EffectiveDates] ON [dbo].[SupplierPriceList] ([EffectiveFrom], [EffectiveTo]);
CREATE INDEX [IX_SupplierPriceListItem_PriceListID] ON [dbo].[SupplierPriceListItem] ([PriceListID]);
CREATE INDEX [IX_SupplierPriceListItem_SupplierProductCode] ON [dbo].[SupplierPriceListItem] ([SupplierProductCode]);
CREATE INDEX [IX_SupplierPerformance_SupplierID_Period] ON [dbo].[SupplierPerformance] ([SupplierID], [PeriodYear], [PeriodMonth]);
CREATE INDEX [IX_SupplierProduct_SupplierID] ON [dbo].[SupplierProduct] ([SupplierID]);
CREATE INDEX [IX_SupplierProduct_ProductID] ON [dbo].[SupplierProduct] ([ProductID]);
CREATE INDEX [IX_SupplierDocument_SupplierID] ON [dbo].[SupplierDocument] ([SupplierID]);
CREATE INDEX [IX_SupplierDocument_ExpiryDate] ON [dbo].[SupplierDocument] ([ExpiryDate]);
CREATE INDEX [IX_SupplierAudit_SupplierID] ON [dbo].[SupplierAudit] ([SupplierID]);
CREATE INDEX [IX_SupplierAudit_AuditDate] ON [dbo].[SupplierAudit] ([AuditDate]);