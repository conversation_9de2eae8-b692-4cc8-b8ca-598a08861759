-- Customer Table Creation Script
-- Purpose: Stores customer information for the wholesale management system
-- Enhanced version with industry best practices

CREATE TABLE [dbo].[Customer] (
    [CustomerID] INT IDENTITY(1,1) NOT NULL,
    [CustomerCode] NVARCHAR(20) UNIQUE NOT NULL, -- Auto-generated unique code
    [CustomerName] NVARCHAR(255) NOT NULL,
    [TradingName] NVARCHAR(255) NULL,
    [ReferenceCode] NVARCHAR(50) UNIQUE NULL,
    [TaxNumber] NVARCHAR(50) NULL,
    [CustomerType] NVARCHAR(50) DEFAULT 'Wholesale' NULL, -- Wholesale, Retail, Distributor
    [CustomerStatus] NVARCHAR(20) DEFAULT 'Active' NOT NULL, -- Active, Inactive, Blocked, Suspended
    [CustomerGroup] NVARCHAR(50) NULL, -- VIP, Regular, New
    
    -- Contact Information
    [ResponsibleContact] NVARCHAR(255) NULL,
    [ContactPhone] NVARCHAR(50) NULL,
    [ContactLandline] NVARCHAR(50) NULL,
    [ContactEmail] NVARCHAR(255) NULL,
    [ContactMobile] NVARCHAR(50) NULL,
    [Website] NVARCHAR(255) NULL,
    
    -- Address Information (default billing address)
    [CustomerAddress] NVARCHAR(500) NULL,
    [CustomerAddress2] NVARCHAR(500) NULL,
    [CustomerCity] NVARCHAR(100) NULL,
    [CustomerState] NVARCHAR(100) NULL,
    [CustomerPostCode] NVARCHAR(20) NULL,
    [CustomerCountry] NVARCHAR(100) DEFAULT 'United Kingdom' NULL,
    
    -- Financial Information
    [PaymentTerm] INT DEFAULT 30 NULL, -- Payment terms in days
    [PaymentMethodID] INT NULL,
    [CreditLimit] DECIMAL(18, 2) DEFAULT 0 NULL,
    [CreditStatus] NVARCHAR(20) DEFAULT 'Good' NULL, -- Good, Warning, Blocked
    [CreditReviewDate] DATETIME2 NULL,
    [DiscountPercent] DECIMAL(5,2) DEFAULT 0 NULL,
    [PriceListID] INT NULL,
    [CurrencyCode] NVARCHAR(3) DEFAULT 'GBP' NULL,
    [TaxExempt] BIT DEFAULT 0 NULL,
    
    -- Sales Information
    [SalesRepID] INT NULL,
    [TerritoryID] INT NULL,
    [LeadSource] NVARCHAR(50) NULL,
    [CustomerSince] DATETIME2 DEFAULT GETDATE() NULL,
    [LastOrderDate] DATETIME2 NULL,
    [TotalOrderValue] DECIMAL(18,2) DEFAULT 0 NULL,
    [OutstandingBalance] DECIMAL(18,2) DEFAULT 0 NULL,
    [YTDSales] DECIMAL(18,2) DEFAULT 0 NULL,
    
    -- Shipping Information
    [PreferredCarrierID] INT NULL,
    [DeliveryInstructions] NVARCHAR(MAX) NULL,
    [DeliveryTimePreference] NVARCHAR(100) NULL,
    
    -- Additional Information
    [Notes] NVARCHAR(MAX) NULL,
    [InternalNotes] NVARCHAR(MAX) NULL, -- Not visible to customer
    [IsActive] BIT DEFAULT 1 NOT NULL,
    [BlockedReason] NVARCHAR(500) NULL,
    [BlockedDate] DATETIME2 NULL,
    [BlockedByUserID] INT NULL,
    [LoyaltyPoints] INT DEFAULT 0 NULL,
    [Rating] INT NULL, -- 1-5 star rating
    
    -- System Fields
    [CreatedDate] DATETIME2 DEFAULT GETDATE() NOT NULL,
    [CreatedByUserID] INT NULL,
    [UpdatedDate] DATETIME2 DEFAULT GETDATE() NOT NULL,
    [UpdatedByUserID] INT NULL,
    [IsDeleted] BIT DEFAULT 0 NOT NULL,
    [DeletedDate] DATETIME2 NULL,
    [DeletedByUserID] INT NULL,
    
    CONSTRAINT [PK_Customer] PRIMARY KEY CLUSTERED ([CustomerID] ASC),
    CONSTRAINT [CHK_Customer_Rating] CHECK ([Rating] IS NULL OR ([Rating] >= 1 AND [Rating] <= 5)),
    CONSTRAINT [CHK_Customer_DiscountPercent] CHECK ([DiscountPercent] >= 0 AND [DiscountPercent] <= 100)
);

-- Create indexes for better performance
CREATE INDEX [IX_Customer_CustomerCode] ON [dbo].[Customer] ([CustomerCode]);
CREATE INDEX [IX_Customer_CustomerName] ON [dbo].[Customer] ([CustomerName]);
CREATE INDEX [IX_Customer_ReferenceCode] ON [dbo].[Customer] ([ReferenceCode]);
CREATE INDEX [IX_Customer_ContactEmail] ON [dbo].[Customer] ([ContactEmail]);
CREATE INDEX [IX_Customer_CustomerStatus] ON [dbo].[Customer] ([CustomerStatus]);
CREATE INDEX [IX_Customer_CustomerType] ON [dbo].[Customer] ([CustomerType]);
CREATE INDEX [IX_Customer_SalesRepID] ON [dbo].[Customer] ([SalesRepID]);
CREATE INDEX [IX_Customer_TerritoryID] ON [dbo].[Customer] ([TerritoryID]);
CREATE INDEX [IX_Customer_IsActive_IsDeleted] ON [dbo].[Customer] ([IsActive], [IsDeleted]);
CREATE INDEX [IX_Customer_TaxNumber] ON [dbo].[Customer] ([TaxNumber]);

-- Additional address table for multiple addresses per customer
CREATE TABLE [dbo].[CustomerAddress] (
    [AddressID] INT IDENTITY(1,1) NOT NULL,
    [CustomerID] INT NOT NULL,
    [AddressType] NVARCHAR(20) NOT NULL, -- Billing, Shipping, Other
    [AddressName] NVARCHAR(100) NULL, -- e.g., "Main Warehouse", "London Office"
    [Address1] NVARCHAR(500) NOT NULL,
    [Address2] NVARCHAR(500) NULL,
    [City] NVARCHAR(100) NULL,
    [State] NVARCHAR(100) NULL,
    [PostCode] NVARCHAR(20) NULL,
    [Country] NVARCHAR(100) DEFAULT 'United Kingdom' NULL,
    [ContactName] NVARCHAR(255) NULL,
    [ContactPhone] NVARCHAR(50) NULL,
    [IsDefault] BIT DEFAULT 0 NULL,
    [IsActive] BIT DEFAULT 1 NOT NULL,
    [CreatedDate] DATETIME2 DEFAULT GETDATE() NOT NULL,
    [UpdatedDate] DATETIME2 DEFAULT GETDATE() NOT NULL,
    CONSTRAINT [PK_CustomerAddress] PRIMARY KEY CLUSTERED ([AddressID] ASC),
    CONSTRAINT [FK_CustomerAddress_Customer] FOREIGN KEY ([CustomerID]) REFERENCES [dbo].[Customer] ([CustomerID])
);

CREATE INDEX [IX_CustomerAddress_CustomerID] ON [dbo].[CustomerAddress] ([CustomerID]);
CREATE INDEX [IX_CustomerAddress_AddressType] ON [dbo].[CustomerAddress] ([AddressType]);