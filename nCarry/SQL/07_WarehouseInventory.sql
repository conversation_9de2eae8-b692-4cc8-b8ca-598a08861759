-- Warehouse and Inventory Management Tables Creation Script
-- Purpose: Comprehensive warehouse and inventory tracking system
-- Supports multiple warehouses, locations, and real-time inventory

-- Warehouse table for multiple warehouse locations
CREATE TABLE [dbo].[Warehouse] (
    [WarehouseID] INT IDENTITY(1,1) NOT NULL,
    [WarehouseCode] NVARCHAR(20) UNIQUE NOT NULL,
    [WarehouseName] NVARCHAR(100) NOT NULL,
    [WarehouseType] NVARCHAR(20) DEFAULT 'Standard' NOT NULL, -- Standard, Bonded, Temperature-Controlled, Hazmat
    [Description] NVARCHAR(500) NULL,
    
    -- Address information
    [Address1] NVARCHAR(500) NOT NULL,
    [Address2] NVARCHAR(500) NULL,
    [City] NVARCHAR(100) NULL,
    [State] NVARCHAR(100) NULL,
    [PostCode] NVARCHAR(20) NULL,
    [Country] NVARCHAR(100) DEFAULT 'United Kingdom' NULL,
    
    -- Contact information
    [ManagerName] NVARCHAR(255) NULL,
    [Phone] NVARCHAR(50) NULL,
    [Email] NVARCHAR(255) NULL,
    
    -- Operational settings
    [IsActive] BIT DEFAULT 1 NOT NULL,
    [IsDefault] BIT DEFAULT 0 NOT NULL,
    [AllowNegativeStock] BIT DEFAULT 0 NOT NULL,
    [OperatingHours] NVARCHAR(100) NULL,
    [TimeZone] NVARCHAR(50) DEFAULT 'GMT Standard Time' NULL,
    
    -- Capacity information
    [TotalArea] DECIMAL(18,2) NULL, -- Square meters
    [StorageCapacity] DECIMAL(18,2) NULL, -- Cubic meters
    [CurrentUtilization] DECIMAL(5,2) DEFAULT 0 NULL, -- Percentage
    
    -- System fields
    [CreatedDate] DATETIME2 DEFAULT GETDATE() NOT NULL,
    [CreatedByUserID] INT NULL,
    [UpdatedDate] DATETIME2 DEFAULT GETDATE() NOT NULL,
    [UpdatedByUserID] INT NULL,
    
    CONSTRAINT [PK_Warehouse] PRIMARY KEY CLUSTERED ([WarehouseID] ASC)
);

-- Warehouse Location table for bin/shelf locations
CREATE TABLE [dbo].[WarehouseLocation] (
    [LocationID] INT IDENTITY(1,1) NOT NULL,
    [WarehouseID] INT NOT NULL,
    [LocationCode] NVARCHAR(50) NOT NULL,
    [LocationName] NVARCHAR(100) NULL,
    [LocationType] NVARCHAR(20) DEFAULT 'Storage' NOT NULL, -- Storage, Staging, Quarantine, Damage, Returns
    [Zone] NVARCHAR(20) NULL, -- A, B, C zones
    [Aisle] NVARCHAR(10) NULL,
    [Bay] NVARCHAR(10) NULL,
    [Level] NVARCHAR(10) NULL,
    [Bin] NVARCHAR(10) NULL,
    [Capacity] DECIMAL(18,3) NULL,
    [CurrentOccupancy] DECIMAL(18,3) DEFAULT 0 NULL,
    [MaxWeight] DECIMAL(18,3) NULL, -- kg
    [IsActive] BIT DEFAULT 1 NOT NULL,
    [IsDefault] BIT DEFAULT 0 NOT NULL,
    [AllowMixedProducts] BIT DEFAULT 1 NOT NULL,
    [CreatedDate] DATETIME2 DEFAULT GETDATE() NOT NULL,
    [UpdatedDate] DATETIME2 DEFAULT GETDATE() NOT NULL,
    CONSTRAINT [PK_WarehouseLocation] PRIMARY KEY CLUSTERED ([LocationID] ASC),
    CONSTRAINT [FK_WarehouseLocation_Warehouse] FOREIGN KEY ([WarehouseID]) REFERENCES [dbo].[Warehouse] ([WarehouseID]),
    CONSTRAINT [UQ_WarehouseLocation_Code] UNIQUE ([WarehouseID], [LocationCode])
);

-- Inventory table for current stock levels
CREATE TABLE [dbo].[Inventory] (
    [InventoryID] INT IDENTITY(1,1) NOT NULL,
    [ProductID] INT NOT NULL,
    [VariantID] INT NULL,
    [WarehouseID] INT NOT NULL,
    [LocationID] INT NULL,
    [BatchNumber] NVARCHAR(50) NULL,
    [SerialNumber] NVARCHAR(100) NULL,
    [QuantityOnHand] DECIMAL(18,3) DEFAULT 0 NOT NULL,
    [QuantityAvailable] DECIMAL(18,3) DEFAULT 0 NOT NULL, -- OnHand - Reserved
    [QuantityReserved] DECIMAL(18,3) DEFAULT 0 NOT NULL,
    [QuantityOnOrder] DECIMAL(18,3) DEFAULT 0 NOT NULL,
    [QuantityInTransit] DECIMAL(18,3) DEFAULT 0 NOT NULL,
    [LastCost] DECIMAL(18,4) NULL,
    [AverageCost] DECIMAL(18,4) NULL,
    [LastCountDate] DATETIME2 NULL,
    [ExpiryDate] DATE NULL,
    [CreatedDate] DATETIME2 DEFAULT GETDATE() NOT NULL,
    [UpdatedDate] DATETIME2 DEFAULT GETDATE() NOT NULL,
    CONSTRAINT [PK_Inventory] PRIMARY KEY CLUSTERED ([InventoryID] ASC),
    CONSTRAINT [FK_Inventory_Product] FOREIGN KEY ([ProductID]) REFERENCES [dbo].[Product] ([ProductID]),
    CONSTRAINT [FK_Inventory_Variant] FOREIGN KEY ([VariantID]) REFERENCES [dbo].[ProductVariant] ([VariantID]),
    CONSTRAINT [FK_Inventory_Warehouse] FOREIGN KEY ([WarehouseID]) REFERENCES [dbo].[Warehouse] ([WarehouseID]),
    CONSTRAINT [FK_Inventory_Location] FOREIGN KEY ([LocationID]) REFERENCES [dbo].[WarehouseLocation] ([LocationID])
);

-- Inventory Transaction table for all stock movements
CREATE TABLE [dbo].[InventoryTransaction] (
    [TransactionID] BIGINT IDENTITY(1,1) NOT NULL,
    [TransactionNumber] NVARCHAR(50) UNIQUE NOT NULL,
    [TransactionDate] DATETIME2 DEFAULT GETDATE() NOT NULL,
    [TransactionType] NVARCHAR(50) NOT NULL, -- Receipt, Issue, Transfer, Adjustment, Count, Return
    [ProductID] INT NOT NULL,
    [VariantID] INT NULL,
    [WarehouseID] INT NOT NULL,
    [LocationID] INT NULL,
    [BatchNumber] NVARCHAR(50) NULL,
    [SerialNumber] NVARCHAR(100) NULL,
    [Quantity] DECIMAL(18,3) NOT NULL, -- Positive for IN, Negative for OUT
    [UnitCost] DECIMAL(18,4) NULL,
    [TotalCost] DECIMAL(18,4) NULL,
    [ReferenceType] NVARCHAR(50) NULL, -- PurchaseOrder, SalesOrder, Transfer, Adjustment
    [ReferenceID] INT NULL,
    [ReferenceNumber] NVARCHAR(50) NULL,
    [Notes] NVARCHAR(500) NULL,
    [CreatedDate] DATETIME2 DEFAULT GETDATE() NOT NULL,
    [CreatedByUserID] INT NULL,
    CONSTRAINT [PK_InventoryTransaction] PRIMARY KEY CLUSTERED ([TransactionID] ASC),
    CONSTRAINT [FK_InventoryTransaction_Product] FOREIGN KEY ([ProductID]) REFERENCES [dbo].[Product] ([ProductID]),
    CONSTRAINT [FK_InventoryTransaction_Variant] FOREIGN KEY ([VariantID]) REFERENCES [dbo].[ProductVariant] ([VariantID]),
    CONSTRAINT [FK_InventoryTransaction_Warehouse] FOREIGN KEY ([WarehouseID]) REFERENCES [dbo].[Warehouse] ([WarehouseID]),
    CONSTRAINT [FK_InventoryTransaction_Location] FOREIGN KEY ([LocationID]) REFERENCES [dbo].[WarehouseLocation] ([LocationID])
);

-- Stock Transfer table for warehouse-to-warehouse transfers
CREATE TABLE [dbo].[StockTransfer] (
    [TransferID] INT IDENTITY(1,1) NOT NULL,
    [TransferNumber] NVARCHAR(50) UNIQUE NOT NULL,
    [TransferDate] DATETIME2 DEFAULT GETDATE() NOT NULL,
    [TransferStatus] NVARCHAR(20) DEFAULT 'Pending' NOT NULL, -- Pending, InTransit, Completed, Cancelled
    [FromWarehouseID] INT NOT NULL,
    [ToWarehouseID] INT NOT NULL,
    [ShipDate] DATETIME2 NULL,
    [ExpectedDate] DATETIME2 NULL,
    [ReceivedDate] DATETIME2 NULL,
    [TransferReason] NVARCHAR(500) NULL,
    [Notes] NVARCHAR(MAX) NULL,
    [CreatedDate] DATETIME2 DEFAULT GETDATE() NOT NULL,
    [CreatedByUserID] INT NULL,
    [UpdatedDate] DATETIME2 DEFAULT GETDATE() NOT NULL,
    [UpdatedByUserID] INT NULL,
    CONSTRAINT [PK_StockTransfer] PRIMARY KEY CLUSTERED ([TransferID] ASC),
    CONSTRAINT [FK_StockTransfer_FromWarehouse] FOREIGN KEY ([FromWarehouseID]) REFERENCES [dbo].[Warehouse] ([WarehouseID]),
    CONSTRAINT [FK_StockTransfer_ToWarehouse] FOREIGN KEY ([ToWarehouseID]) REFERENCES [dbo].[Warehouse] ([WarehouseID])
);

-- Stock Transfer Detail table
CREATE TABLE [dbo].[StockTransferDetail] (
    [TransferDetailID] INT IDENTITY(1,1) NOT NULL,
    [TransferID] INT NOT NULL,
    [ProductID] INT NOT NULL,
    [VariantID] INT NULL,
    [FromLocationID] INT NULL,
    [ToLocationID] INT NULL,
    [BatchNumber] NVARCHAR(50) NULL,
    [SerialNumber] NVARCHAR(100) NULL,
    [RequestedQuantity] DECIMAL(18,3) NOT NULL,
    [ShippedQuantity] DECIMAL(18,3) DEFAULT 0 NULL,
    [ReceivedQuantity] DECIMAL(18,3) DEFAULT 0 NULL,
    [UnitCost] DECIMAL(18,4) NULL,
    [Notes] NVARCHAR(500) NULL,
    CONSTRAINT [PK_StockTransferDetail] PRIMARY KEY CLUSTERED ([TransferDetailID] ASC),
    CONSTRAINT [FK_StockTransferDetail_Transfer] FOREIGN KEY ([TransferID]) REFERENCES [dbo].[StockTransfer] ([TransferID]),
    CONSTRAINT [FK_StockTransferDetail_Product] FOREIGN KEY ([ProductID]) REFERENCES [dbo].[Product] ([ProductID]),
    CONSTRAINT [FK_StockTransferDetail_Variant] FOREIGN KEY ([VariantID]) REFERENCES [dbo].[ProductVariant] ([VariantID]),
    CONSTRAINT [FK_StockTransferDetail_FromLocation] FOREIGN KEY ([FromLocationID]) REFERENCES [dbo].[WarehouseLocation] ([LocationID]),
    CONSTRAINT [FK_StockTransferDetail_ToLocation] FOREIGN KEY ([ToLocationID]) REFERENCES [dbo].[WarehouseLocation] ([LocationID])
);

-- Stock Adjustment table for inventory corrections
CREATE TABLE [dbo].[StockAdjustment] (
    [AdjustmentID] INT IDENTITY(1,1) NOT NULL,
    [AdjustmentNumber] NVARCHAR(50) UNIQUE NOT NULL,
    [AdjustmentDate] DATETIME2 DEFAULT GETDATE() NOT NULL,
    [AdjustmentType] NVARCHAR(50) NOT NULL, -- Damage, Loss, Found, Correction, Write-off
    [WarehouseID] INT NOT NULL,
    [AdjustmentReason] NVARCHAR(500) NOT NULL,
    [TotalValue] DECIMAL(18,4) DEFAULT 0 NULL,
    [Status] NVARCHAR(20) DEFAULT 'Draft' NOT NULL, -- Draft, Approved, Posted, Cancelled
    [ApprovedByUserID] INT NULL,
    [ApprovedDate] DATETIME2 NULL,
    [Notes] NVARCHAR(MAX) NULL,
    [CreatedDate] DATETIME2 DEFAULT GETDATE() NOT NULL,
    [CreatedByUserID] INT NULL,
    [UpdatedDate] DATETIME2 DEFAULT GETDATE() NOT NULL,
    [UpdatedByUserID] INT NULL,
    CONSTRAINT [PK_StockAdjustment] PRIMARY KEY CLUSTERED ([AdjustmentID] ASC),
    CONSTRAINT [FK_StockAdjustment_Warehouse] FOREIGN KEY ([WarehouseID]) REFERENCES [dbo].[Warehouse] ([WarehouseID])
);

-- Stock Adjustment Detail table
CREATE TABLE [dbo].[StockAdjustmentDetail] (
    [AdjustmentDetailID] INT IDENTITY(1,1) NOT NULL,
    [AdjustmentID] INT NOT NULL,
    [ProductID] INT NOT NULL,
    [VariantID] INT NULL,
    [LocationID] INT NULL,
    [BatchNumber] NVARCHAR(50) NULL,
    [SerialNumber] NVARCHAR(100) NULL,
    [CurrentQuantity] DECIMAL(18,3) NOT NULL,
    [AdjustedQuantity] DECIMAL(18,3) NOT NULL,
    [VarianceQuantity] DECIMAL(18,3) NOT NULL, -- AdjustedQuantity - CurrentQuantity
    [UnitCost] DECIMAL(18,4) NULL,
    [TotalValue] DECIMAL(18,4) NULL,
    [Reason] NVARCHAR(500) NULL,
    CONSTRAINT [PK_StockAdjustmentDetail] PRIMARY KEY CLUSTERED ([AdjustmentDetailID] ASC),
    CONSTRAINT [FK_StockAdjustmentDetail_Adjustment] FOREIGN KEY ([AdjustmentID]) REFERENCES [dbo].[StockAdjustment] ([AdjustmentID]),
    CONSTRAINT [FK_StockAdjustmentDetail_Product] FOREIGN KEY ([ProductID]) REFERENCES [dbo].[Product] ([ProductID]),
    CONSTRAINT [FK_StockAdjustmentDetail_Variant] FOREIGN KEY ([VariantID]) REFERENCES [dbo].[ProductVariant] ([VariantID]),
    CONSTRAINT [FK_StockAdjustmentDetail_Location] FOREIGN KEY ([LocationID]) REFERENCES [dbo].[WarehouseLocation] ([LocationID])
);

-- Stock Reservation table for reserving inventory
CREATE TABLE [dbo].[StockReservation] (
    [ReservationID] INT IDENTITY(1,1) NOT NULL,
    [ReservationNumber] NVARCHAR(50) UNIQUE NOT NULL,
    [ProductID] INT NOT NULL,
    [VariantID] INT NULL,
    [WarehouseID] INT NOT NULL,
    [LocationID] INT NULL,
    [BatchNumber] NVARCHAR(50) NULL,
    [Quantity] DECIMAL(18,3) NOT NULL,
    [ReservationType] NVARCHAR(50) NOT NULL, -- SalesOrder, Transfer, Production
    [ReferenceType] NVARCHAR(50) NOT NULL,
    [ReferenceID] INT NOT NULL,
    [ExpiryDate] DATETIME2 NOT NULL,
    [Status] NVARCHAR(20) DEFAULT 'Active' NOT NULL, -- Active, Released, Expired, Cancelled
    [Notes] NVARCHAR(500) NULL,
    [CreatedDate] DATETIME2 DEFAULT GETDATE() NOT NULL,
    [CreatedByUserID] INT NULL,
    [UpdatedDate] DATETIME2 DEFAULT GETDATE() NOT NULL,
    CONSTRAINT [PK_StockReservation] PRIMARY KEY CLUSTERED ([ReservationID] ASC),
    CONSTRAINT [FK_StockReservation_Product] FOREIGN KEY ([ProductID]) REFERENCES [dbo].[Product] ([ProductID]),
    CONSTRAINT [FK_StockReservation_Variant] FOREIGN KEY ([VariantID]) REFERENCES [dbo].[ProductVariant] ([VariantID]),
    CONSTRAINT [FK_StockReservation_Warehouse] FOREIGN KEY ([WarehouseID]) REFERENCES [dbo].[Warehouse] ([WarehouseID]),
    CONSTRAINT [FK_StockReservation_Location] FOREIGN KEY ([LocationID]) REFERENCES [dbo].[WarehouseLocation] ([LocationID])
);

-- Cycle Count table for inventory counting
CREATE TABLE [dbo].[CycleCount] (
    [CycleCountID] INT IDENTITY(1,1) NOT NULL,
    [CycleCountNumber] NVARCHAR(50) UNIQUE NOT NULL,
    [CountDate] DATETIME2 DEFAULT GETDATE() NOT NULL,
    [WarehouseID] INT NOT NULL,
    [CountType] NVARCHAR(50) NOT NULL, -- Full, Partial, Category, Location, ABC
    [Status] NVARCHAR(20) DEFAULT 'Planned' NOT NULL, -- Planned, InProgress, Completed, Cancelled
    [ScheduledDate] DATETIME2 NOT NULL,
    [StartedDate] DATETIME2 NULL,
    [CompletedDate] DATETIME2 NULL,
    [TotalItems] INT DEFAULT 0 NULL,
    [CountedItems] INT DEFAULT 0 NULL,
    [VarianceItems] INT DEFAULT 0 NULL,
    [TotalVarianceValue] DECIMAL(18,4) DEFAULT 0 NULL,
    [Notes] NVARCHAR(MAX) NULL,
    [CreatedDate] DATETIME2 DEFAULT GETDATE() NOT NULL,
    [CreatedByUserID] INT NULL,
    CONSTRAINT [PK_CycleCount] PRIMARY KEY CLUSTERED ([CycleCountID] ASC),
    CONSTRAINT [FK_CycleCount_Warehouse] FOREIGN KEY ([WarehouseID]) REFERENCES [dbo].[Warehouse] ([WarehouseID])
);

-- Cycle Count Detail table
CREATE TABLE [dbo].[CycleCountDetail] (
    [CountDetailID] INT IDENTITY(1,1) NOT NULL,
    [CycleCountID] INT NOT NULL,
    [ProductID] INT NOT NULL,
    [VariantID] INT NULL,
    [LocationID] INT NULL,
    [BatchNumber] NVARCHAR(50) NULL,
    [SystemQuantity] DECIMAL(18,3) NOT NULL,
    [CountedQuantity] DECIMAL(18,3) NULL,
    [VarianceQuantity] DECIMAL(18,3) NULL,
    [UnitCost] DECIMAL(18,4) NULL,
    [VarianceValue] DECIMAL(18,4) NULL,
    [CountedByUserID] INT NULL,
    [CountedDate] DATETIME2 NULL,
    [Notes] NVARCHAR(500) NULL,
    CONSTRAINT [PK_CycleCountDetail] PRIMARY KEY CLUSTERED ([CountDetailID] ASC),
    CONSTRAINT [FK_CycleCountDetail_CycleCount] FOREIGN KEY ([CycleCountID]) REFERENCES [dbo].[CycleCount] ([CycleCountID]),
    CONSTRAINT [FK_CycleCountDetail_Product] FOREIGN KEY ([ProductID]) REFERENCES [dbo].[Product] ([ProductID]),
    CONSTRAINT [FK_CycleCountDetail_Variant] FOREIGN KEY ([VariantID]) REFERENCES [dbo].[ProductVariant] ([VariantID]),
    CONSTRAINT [FK_CycleCountDetail_Location] FOREIGN KEY ([LocationID]) REFERENCES [dbo].[WarehouseLocation] ([LocationID])
);

-- Product Batch table for batch tracking
CREATE TABLE [dbo].[ProductBatch] (
    [BatchID] INT IDENTITY(1,1) NOT NULL,
    [BatchNumber] NVARCHAR(50) UNIQUE NOT NULL,
    [ProductID] INT NOT NULL,
    [SupplierID] INT NULL,
    [ManufactureDate] DATE NULL,
    [ExpiryDate] DATE NULL,
    [ReceivedDate] DATETIME2 DEFAULT GETDATE() NOT NULL,
    [InitialQuantity] DECIMAL(18,3) NOT NULL,
    [RemainingQuantity] DECIMAL(18,3) NOT NULL,
    [Status] NVARCHAR(20) DEFAULT 'Active' NOT NULL, -- Active, Quarantine, Released, Expired, Depleted
    [QualityStatus] NVARCHAR(20) NULL, -- Pending, Passed, Failed
    [Notes] NVARCHAR(MAX) NULL,
    [CreatedDate] DATETIME2 DEFAULT GETDATE() NOT NULL,
    [CreatedByUserID] INT NULL,
    CONSTRAINT [PK_ProductBatch] PRIMARY KEY CLUSTERED ([BatchID] ASC),
    CONSTRAINT [FK_ProductBatch_Product] FOREIGN KEY ([ProductID]) REFERENCES [dbo].[Product] ([ProductID]),
    CONSTRAINT [FK_ProductBatch_Supplier] FOREIGN KEY ([SupplierID]) REFERENCES [dbo].[Supplier] ([SupplierID])
);

-- Serial Number table for serial tracking
CREATE TABLE [dbo].[SerialNumber] (
    [SerialID] INT IDENTITY(1,1) NOT NULL,
    [SerialNumber] NVARCHAR(100) UNIQUE NOT NULL,
    [ProductID] INT NOT NULL,
    [VariantID] INT NULL,
    [BatchID] INT NULL,
    [Status] NVARCHAR(20) DEFAULT 'Available' NOT NULL, -- Available, Sold, Reserved, Damaged, Lost
    [WarehouseID] INT NULL,
    [LocationID] INT NULL,
    [ReceivedDate] DATETIME2 DEFAULT GETDATE() NOT NULL,
    [SoldDate] DATETIME2 NULL,
    [CustomerID] INT NULL,
    [WarrantyEndDate] DATE NULL,
    [Notes] NVARCHAR(MAX) NULL,
    [CreatedDate] DATETIME2 DEFAULT GETDATE() NOT NULL,
    [UpdatedDate] DATETIME2 DEFAULT GETDATE() NOT NULL,
    CONSTRAINT [PK_SerialNumber] PRIMARY KEY CLUSTERED ([SerialID] ASC),
    CONSTRAINT [FK_SerialNumber_Product] FOREIGN KEY ([ProductID]) REFERENCES [dbo].[Product] ([ProductID]),
    CONSTRAINT [FK_SerialNumber_Variant] FOREIGN KEY ([VariantID]) REFERENCES [dbo].[ProductVariant] ([VariantID]),
    CONSTRAINT [FK_SerialNumber_Batch] FOREIGN KEY ([BatchID]) REFERENCES [dbo].[ProductBatch] ([BatchID]),
    CONSTRAINT [FK_SerialNumber_Warehouse] FOREIGN KEY ([WarehouseID]) REFERENCES [dbo].[Warehouse] ([WarehouseID]),
    CONSTRAINT [FK_SerialNumber_Location] FOREIGN KEY ([LocationID]) REFERENCES [dbo].[WarehouseLocation] ([LocationID]),
    CONSTRAINT [FK_SerialNumber_Customer] FOREIGN KEY ([CustomerID]) REFERENCES [dbo].[Customer] ([CustomerID])
);

-- Create indexes
CREATE INDEX [IX_Warehouse_WarehouseCode] ON [dbo].[Warehouse] ([WarehouseCode]);
CREATE INDEX [IX_WarehouseLocation_WarehouseID] ON [dbo].[WarehouseLocation] ([WarehouseID]);
CREATE INDEX [IX_WarehouseLocation_LocationType] ON [dbo].[WarehouseLocation] ([LocationType]);
CREATE INDEX [IX_Inventory_ProductID_WarehouseID] ON [dbo].[Inventory] ([ProductID], [WarehouseID]);
CREATE INDEX [IX_Inventory_BatchNumber] ON [dbo].[Inventory] ([BatchNumber]);
CREATE INDEX [IX_Inventory_SerialNumber] ON [dbo].[Inventory] ([SerialNumber]);
CREATE INDEX [IX_InventoryTransaction_TransactionDate] ON [dbo].[InventoryTransaction] ([TransactionDate]);
CREATE INDEX [IX_InventoryTransaction_ProductID] ON [dbo].[InventoryTransaction] ([ProductID]);
CREATE INDEX [IX_InventoryTransaction_TransactionType] ON [dbo].[InventoryTransaction] ([TransactionType]);
CREATE INDEX [IX_InventoryTransaction_ReferenceType_ReferenceID] ON [dbo].[InventoryTransaction] ([ReferenceType], [ReferenceID]);
CREATE INDEX [IX_StockTransfer_TransferNumber] ON [dbo].[StockTransfer] ([TransferNumber]);
CREATE INDEX [IX_StockTransfer_TransferStatus] ON [dbo].[StockTransfer] ([TransferStatus]);
CREATE INDEX [IX_StockReservation_ProductID_WarehouseID] ON [dbo].[StockReservation] ([ProductID], [WarehouseID]);
CREATE INDEX [IX_StockReservation_Status] ON [dbo].[StockReservation] ([Status]);
CREATE INDEX [IX_StockReservation_ExpiryDate] ON [dbo].[StockReservation] ([ExpiryDate]);
CREATE INDEX [IX_ProductBatch_BatchNumber] ON [dbo].[ProductBatch] ([BatchNumber]);
CREATE INDEX [IX_ProductBatch_ProductID] ON [dbo].[ProductBatch] ([ProductID]);
CREATE INDEX [IX_ProductBatch_ExpiryDate] ON [dbo].[ProductBatch] ([ExpiryDate]);
CREATE INDEX [IX_SerialNumber_SerialNumber] ON [dbo].[SerialNumber] ([SerialNumber]);
CREATE INDEX [IX_SerialNumber_ProductID] ON [dbo].[SerialNumber] ([ProductID]);
CREATE INDEX [IX_SerialNumber_Status] ON [dbo].[SerialNumber] ([Status]);