-- nCarry Sample Data Script
-- This script inserts sample data into all tables with proper relationships
-- Run this after all table creation scripts

USE nCarryDB;
GO

-- Disable foreign key checks temporarily
ALTER TABLE Customer NOCHECK CONSTRAINT ALL;
ALTER TABLE Supplier NOCHECK CONSTRAINT ALL;
ALTER TABLE Product NOCHECK CONSTRAINT ALL;
ALTER TABLE SalesOrder NOCHECK CONSTRAINT ALL;
ALTER TABLE PurchaseOrder NOCHECK CONSTRAINT ALL;
GO

-- Clear existing data (optional - be careful in production!)
/*
DELETE FROM PaymentAllocation;
DELETE FROM Payment;
DELETE FROM DeliveryItem;
DELETE FROM Delivery;
DELETE FROM ShipmentTracking;
DELETE FROM Shipment;
DELETE FROM InventoryTransaction;
DELETE FROM Inventory;
DELETE FROM GoodsReceiptItem;
DELETE FROM GoodsReceipt;
DELETE FROM PurchaseOrderItem;
DELETE FROM PurchaseOrder;
DELETE FROM SalesInvoiceItem;
DELETE FROM SalesInvoice;
DELETE FROM SalesOrderItem;
DELETE FROM SalesOrder;
DELETE FROM QuoteItem;
DELETE FROM Quote;
DELETE FROM ProductBarcode;
DELETE FROM ProductVariant;
DELETE FROM Product;
DELETE FROM ProductCategory;
DELETE FROM UnitOfMeasure;
DELETE FROM SupplierContact;
DELETE FROM SupplierAddress;
DELETE FROM Supplier;
DELETE FROM CustomerContact;
DELETE FROM CustomerAddress;
DELETE FROM Customer;
DELETE FROM UserSession;
DELETE FROM UserRole;
DELETE FROM RolePermission;
DELETE FROM Permission;
DELETE FROM Role;
DELETE FROM [User];
DELETE FROM Branch;
DELETE FROM Company;
DELETE FROM Currency;
DELETE FROM PaymentMethod;
DELETE FROM TaxCode;
DELETE FROM Bank;
DELETE FROM Carrier;
DELETE FROM CarrierService;
DELETE FROM DeliveryZone;
DELETE FROM DeliveryRoute;
DELETE FROM Vehicle;
DELETE FROM Warehouse;
DELETE FROM WarehouseLocation;
*/

-- 1. Insert Companies
INSERT INTO Company (CompanyCode, CompanyName, LegalName, TaxNumber, RegistrationNumber, Website, Email, Phone, IsActive, CreatedDate, UpdatedDate)
VALUES 
('NC001', 'nCarry Logistics Ltd', 'nCarry Logistics Limited', 'GB123456789', 'REG123456', 'www.ncarry.com', '<EMAIL>', '+44 20 1234 5678', 1, GETDATE(), GETDATE()),
('NC002', 'Global Supply Co', 'Global Supply Company Ltd', 'GB987654321', 'REG654321', 'www.globalsupply.com', '<EMAIL>', '+44 20 9876 5432', 1, GETDATE(), GETDATE()),
('NC003', 'Tech Distribution Hub', 'Technology Distribution Hub Ltd', 'GB456789123', 'REG789123', 'www.techdist.com', '<EMAIL>', '+44 20 4567 8912', 1, GETDATE(), GETDATE()),
('NC004', 'Express Cargo Services', 'Express Cargo Services PLC', 'GB789123456', 'REG321654', 'www.expresscargo.com', '<EMAIL>', '+44 20 7891 2345', 1, GETDATE(), GETDATE()),
('NC005', 'Smart Inventory Solutions', 'Smart Inventory Solutions Ltd', 'GB321654987', 'REG987321', 'www.smartinv.com', '<EMAIL>', '+44 20 3216 5498', 1, GETDATE(), GETDATE());

-- 2. Insert Branches
INSERT INTO Branch (BranchCode, BranchName, CompanyID, BranchType, Address1, City, PostCode, Country, Phone, Email, IsActive, IsDefault, CreatedDate, UpdatedDate)
VALUES 
('BR001', 'London Main Branch', 1, 'Warehouse', '123 Logistics Way', 'London', 'E1 6AN', 'United Kingdom', '+44 20 1111 1111', '<EMAIL>', 1, 1, GETDATE(), GETDATE()),
('BR002', 'Manchester Branch', 1, 'Warehouse', '456 Supply Street', 'Manchester', 'M1 2AB', 'United Kingdom', '+44 ************', '<EMAIL>', 1, 0, GETDATE(), GETDATE()),
('BR003', 'Birmingham Branch', 1, 'Warehouse', '789 Storage Road', 'Birmingham', 'B1 3CD', 'United Kingdom', '+44 ************', '<EMAIL>', 1, 0, GETDATE(), GETDATE()),
('BR004', 'Glasgow Branch', 1, 'Warehouse', '321 Delivery Avenue', 'Glasgow', 'G1 4EF', 'United Kingdom', '+44 ************', '<EMAIL>', 1, 0, GETDATE(), GETDATE()),
('BR005', 'Bristol Branch', 1, 'Office', '654 Cargo Lane', 'Bristol', 'BS1 5GH', 'United Kingdom', '+44 ************', '<EMAIL>', 1, 0, GETDATE(), GETDATE());

-- 3. Insert Currencies
INSERT INTO Currency (CurrencyCode, CurrencyName, Symbol, ExchangeRate, IsBaseCurrency, IsActive, CreatedDate, UpdatedDate)
VALUES 
('GBP', 'British Pound Sterling', '£', 1.00, 1, 1, GETDATE(), GETDATE()),
('USD', 'US Dollar', '$', 1.27, 0, 1, GETDATE(), GETDATE()),
('EUR', 'Euro', '€', 1.17, 0, 1, GETDATE(), GETDATE()),
('JPY', 'Japanese Yen', '¥', 183.45, 0, 1, GETDATE(), GETDATE()),
('AUD', 'Australian Dollar', 'A$', 1.92, 0, 1, GETDATE(), GETDATE());

-- 4. Insert Tax Codes
INSERT INTO TaxCode (TaxCode, TaxDescription, TaxRate, TaxType, IsActive, CreatedDate, UpdatedDate)
VALUES 
('VAT20', 'Standard VAT 20%', 20.00, 'Output', 1, GETDATE(), GETDATE()),
('VAT5', 'Reduced VAT 5%', 5.00, 'Output', 1, GETDATE(), GETDATE()),
('VAT0', 'Zero Rated VAT', 0.00, 'Output', 1, GETDATE(), GETDATE()),
('EXEMPT', 'VAT Exempt', 0.00, 'Exempt', 1, GETDATE(), GETDATE()),
('IMPORT', 'Import Duty', 12.00, 'Import', 1, GETDATE(), GETDATE());

-- 5. Insert Payment Methods
INSERT INTO PaymentMethod (MethodCode, MethodName, MethodType, IsActive, RequiresBankDetails, ProcessingDays, CreatedDate, UpdatedDate)
VALUES 
('CASH', 'Cash Payment', 'Cash', 1, 0, 0, GETDATE(), GETDATE()),
('BACS', 'Bank Transfer (BACS)', 'BankTransfer', 1, 1, 3, GETDATE(), GETDATE()),
('CARD', 'Credit/Debit Card', 'Card', 1, 0, 1, GETDATE(), GETDATE()),
('CHQ', 'Cheque', 'Cheque', 1, 1, 5, GETDATE(), GETDATE()),
('PP', 'PayPal', 'Online', 1, 0, 1, GETDATE(), GETDATE());

-- 6. Insert Banks
INSERT INTO Bank (BankCode, BankName, SwiftCode, SortCode, Country, IsActive, CreatedDate, UpdatedDate)
VALUES 
('HSBC', 'HSBC UK Bank', 'HBUKGB4B', '40-00-00', 'United Kingdom', 1, GETDATE(), GETDATE()),
('BARC', 'Barclays Bank', 'BARCGB22', '20-00-00', 'United Kingdom', 1, GETDATE(), GETDATE()),
('LLOY', 'Lloyds Bank', 'LOYDGB2L', '30-00-00', 'United Kingdom', 1, GETDATE(), GETDATE()),
('NATW', 'NatWest Bank', 'NWBKGB2L', '60-00-00', 'United Kingdom', 1, GETDATE(), GETDATE()),
('SANT', 'Santander UK', 'ABBYGB2L', '09-01-00', 'United Kingdom', 1, GETDATE(), GETDATE());

-- 7. Insert Users
INSERT INTO [User] (Username, Email, PasswordHash, PasswordSalt, FullName, PhoneNumber, IsActive, IsSystemAdmin, CreatedDate, UpdatedDate, LastLoginDate)
VALUES 
('admin', '<EMAIL>', 'hashed_password_here', 'salt_here', 'System Administrator', '+44 20 1234 5678', 1, 1, GETDATE(), GETDATE(), GETDATE()),
('john.smith', '<EMAIL>', 'hashed_password_here', 'salt_here', 'John Smith', '+44 20 1234 5679', 1, 0, GETDATE(), GETDATE(), GETDATE()),
('sarah.jones', '<EMAIL>', 'hashed_password_here', 'salt_here', 'Sarah Jones', '+44 20 1234 5680', 1, 0, GETDATE(), GETDATE(), GETDATE()),
('mike.wilson', '<EMAIL>', 'hashed_password_here', 'salt_here', 'Mike Wilson', '+44 20 1234 5681', 1, 0, GETDATE(), GETDATE(), GETDATE()),
('emma.brown', '<EMAIL>', 'hashed_password_here', 'salt_here', 'Emma Brown', '+44 20 1234 5682', 1, 0, GETDATE(), GETDATE(), GETDATE());

-- 8. Insert Roles
INSERT INTO Role (RoleCode, RoleName, RoleDescription, IsSystemRole, IsActive, CreatedDate, UpdatedDate)
VALUES 
('ADMIN', 'Administrator', 'Full system access', 1, 1, GETDATE(), GETDATE()),
('MANAGER', 'Manager', 'Management level access', 0, 1, GETDATE(), GETDATE()),
('SALES', 'Sales Representative', 'Sales operations access', 0, 1, GETDATE(), GETDATE()),
('WAREHOUSE', 'Warehouse Staff', 'Warehouse operations access', 0, 1, GETDATE(), GETDATE()),
('FINANCE', 'Finance Officer', 'Financial operations access', 0, 1, GETDATE(), GETDATE());

-- 9. Insert UserRoles
INSERT INTO UserRole (UserID, RoleID, AssignedDate, AssignedByUserID)
VALUES 
(1, 1, GETDATE(), 1), -- admin -> Administrator
(2, 2, GETDATE(), 1), -- john.smith -> Manager
(3, 3, GETDATE(), 1), -- sarah.jones -> Sales
(4, 4, GETDATE(), 1), -- mike.wilson -> Warehouse
(5, 5, GETDATE(), 1); -- emma.brown -> Finance

-- 10. Insert Unit of Measures
INSERT INTO UnitOfMeasure (UOMCode, UOMName, UOMType, ConversionFactor, BaseUOM, IsActive, CreatedDate, UpdatedDate)
VALUES 
('PC', 'Piece', 'Quantity', 1.00, NULL, 1, GETDATE(), GETDATE()),
('BOX', 'Box', 'Quantity', 12.00, 'PC', 1, GETDATE(), GETDATE()),
('PALLET', 'Pallet', 'Quantity', 480.00, 'PC', 1, GETDATE(), GETDATE()),
('KG', 'Kilogram', 'Weight', 1.00, NULL, 1, GETDATE(), GETDATE()),
('TON', 'Ton', 'Weight', 1000.00, 'KG', 1, GETDATE(), GETDATE());

-- 11. Insert Product Categories
INSERT INTO ProductCategory (CategoryCode, CategoryName, ParentCategoryID, CategoryLevel, CategoryPath, IsActive, CreatedDate, UpdatedDate)
VALUES 
('ELEC', 'Electronics', NULL, 1, '/ELEC/', 1, GETDATE(), GETDATE()),
('COMP', 'Computers', 1, 2, '/ELEC/COMP/', 1, GETDATE(), GETDATE()),
('MOBILE', 'Mobile Devices', 1, 2, '/ELEC/MOBILE/', 1, GETDATE(), GETDATE()),
('FURN', 'Furniture', NULL, 1, '/FURN/', 1, GETDATE(), GETDATE()),
('OFFICE', 'Office Furniture', 4, 2, '/FURN/OFFICE/', 1, GETDATE(), GETDATE());

-- 12. Insert Customers
INSERT INTO Customer (CustomerCode, CustomerName, CustomerType, TaxNumber, CreditLimit, PaymentTerms, CurrencyCode, IsActive, CreatedDate, UpdatedDate)
VALUES 
('CUST001', 'Tech Solutions Ltd', 'Business', 'GB111222333', 50000.00, 30, 'GBP', 1, GETDATE(), GETDATE()),
('CUST002', 'Office Supplies Direct', 'Business', 'GB444555666', 75000.00, 45, 'GBP', 1, GETDATE(), GETDATE()),
('CUST003', 'Retail World PLC', 'Business', 'GB777888999', 100000.00, 60, 'GBP', 1, GETDATE(), GETDATE()),
('CUST004', 'Smart Systems Inc', 'Business', 'GB123123123', 60000.00, 30, 'GBP', 1, GETDATE(), GETDATE()),
('CUST005', 'Digital Store UK', 'Business', 'GB456456456', 80000.00, 45, 'GBP', 1, GETDATE(), GETDATE());

-- 13. Insert Customer Addresses
INSERT INTO CustomerAddress (CustomerID, AddressType, Address1, City, PostCode, Country, IsDefault, IsActive, CreatedDate, UpdatedDate)
VALUES 
(1, 'Billing', '10 Tech Park', 'London', 'EC1V 2NX', 'United Kingdom', 1, 1, GETDATE(), GETDATE()),
(1, 'Shipping', '10 Tech Park, Unit 5', 'London', 'EC1V 2NX', 'United Kingdom', 0, 1, GETDATE(), GETDATE()),
(2, 'Billing', '25 Commerce Street', 'Manchester', 'M2 5AB', 'United Kingdom', 1, 1, GETDATE(), GETDATE()),
(3, 'Billing', '100 Retail Plaza', 'Birmingham', 'B2 4XY', 'United Kingdom', 1, 1, GETDATE(), GETDATE()),
(4, 'Billing', '50 Innovation Drive', 'Leeds', 'LS1 3DE', 'United Kingdom', 1, 1, GETDATE(), GETDATE());

-- 14. Insert Suppliers
INSERT INTO Supplier (SupplierCode, SupplierName, TaxNumber, PaymentTerms, CurrencyCode, IsActive, CreatedDate, UpdatedDate)
VALUES 
('SUPP001', 'Global Electronics Manufacturing', 'GB999888777', 60, 'GBP', 1, GETDATE(), GETDATE()),
('SUPP002', 'Computer Components Ltd', 'GB666555444', 45, 'GBP', 1, GETDATE(), GETDATE()),
('SUPP003', 'Mobile Tech Suppliers', 'GB333222111', 30, 'USD', 1, GETDATE(), GETDATE()),
('SUPP004', 'Furniture Factory Direct', 'GB123456789', 90, 'GBP', 1, GETDATE(), GETDATE()),
('SUPP005', 'Office Equipment Wholesale', 'GB987654321', 60, 'EUR', 1, GETDATE(), GETDATE());

-- 15. Insert Supplier Addresses
INSERT INTO SupplierAddress (SupplierID, AddressType, Address1, City, PostCode, Country, IsDefault, IsActive, CreatedDate, UpdatedDate)
VALUES 
(1, 'Main', '200 Industrial Estate', 'Birmingham', 'B10 1AB', 'United Kingdom', 1, 1, GETDATE(), GETDATE()),
(2, 'Main', '50 Tech Industrial Park', 'Manchester', 'M15 2CD', 'United Kingdom', 1, 1, GETDATE(), GETDATE()),
(3, 'Main', '300 Import Business Park', 'London', 'E14 3EF', 'United Kingdom', 1, 1, GETDATE(), GETDATE()),
(4, 'Main', '150 Manufacturing Way', 'Leeds', 'LS10 4GH', 'United Kingdom', 1, 1, GETDATE(), GETDATE()),
(5, 'Main', '75 Wholesale Avenue', 'Glasgow', 'G40 5IJ', 'United Kingdom', 1, 1, GETDATE(), GETDATE());

-- 16. Insert Products
INSERT INTO Product (ProductCode, ProductName, CategoryID, SupplierID, UOMID, UnitPrice, StandardCost, MinStockLevel, MaxStockLevel, ReorderPoint, LeadTimeDays, IsActive, CreatedDate, UpdatedDate)
VALUES 
('LAPTOP001', 'Business Laptop Pro 15"', 2, 1, 1, 899.99, 650.00, 10, 50, 15, 7, 1, GETDATE(), GETDATE()),
('DESKTOP001', 'Office Desktop PC', 2, 2, 1, 599.99, 420.00, 5, 25, 8, 5, 1, GETDATE(), GETDATE()),
('PHONE001', 'Smartphone X Pro', 3, 3, 1, 699.99, 480.00, 20, 100, 30, 3, 1, GETDATE(), GETDATE()),
('DESK001', 'Executive Office Desk', 5, 4, 1, 399.99, 250.00, 3, 15, 5, 14, 1, GETDATE(), GETDATE()),
('CHAIR001', 'Ergonomic Office Chair', 5, 5, 1, 249.99, 150.00, 5, 30, 10, 10, 1, GETDATE(), GETDATE());

-- 17. Insert Warehouses
INSERT INTO Warehouse (WarehouseCode, WarehouseName, WarehouseType, Address1, City, PostCode, Country, IsActive, CreatedDate, UpdatedDate)
VALUES 
('WH001', 'Main Distribution Center', 'Standard', '123 Logistics Way', 'London', 'E1 6AN', 'United Kingdom', 1, GETDATE(), GETDATE()),
('WH002', 'North Storage Facility', 'Standard', '456 Supply Street', 'Manchester', 'M1 2AB', 'United Kingdom', 1, GETDATE(), GETDATE()),
('WH003', 'Central Warehouse', 'Temperature-Controlled', '789 Storage Road', 'Birmingham', 'B1 3CD', 'United Kingdom', 1, GETDATE(), GETDATE()),
('WH004', 'Scotland Distribution Hub', 'Standard', '321 Delivery Avenue', 'Glasgow', 'G1 4EF', 'United Kingdom', 1, GETDATE(), GETDATE()),
('WH005', 'West Storage Center', 'Bonded', '654 Cargo Lane', 'Bristol', 'BS1 5GH', 'United Kingdom', 1, GETDATE(), GETDATE());

-- 18. Insert Warehouse Locations
INSERT INTO WarehouseLocation (WarehouseID, LocationCode, LocationName, LocationType, Aisle, Bay, Level, Capacity, CurrentOccupancy, IsActive, CreatedDate, UpdatedDate)
VALUES 
(1, 'A-01-01', 'Aisle A Bay 01 Level 01', 'Storage', 'A', '01', '01', 100, 50, 1, GETDATE(), GETDATE()),
(1, 'A-01-02', 'Aisle A Bay 01 Level 02', 'Storage', 'A', '01', '02', 100, 30, 1, GETDATE(), GETDATE()),
(2, 'B-01-01', 'Aisle B Bay 01 Level 01', 'Staging', 'B', '01', '01', 150, 75, 1, GETDATE(), GETDATE()),
(3, 'C-01-01', 'Aisle C Bay 01 Level 01', 'Storage', 'C', '01', '01', 200, 100, 1, GETDATE(), GETDATE()),
(4, 'D-01-01', 'Aisle D Bay 01 Level 01', 'Storage', 'D', '01', '01', 120, 60, 1, GETDATE(), GETDATE());

-- 19. Insert Initial Inventory
INSERT INTO Inventory (WarehouseID, LocationID, ProductID, QuantityOnHand, QuantityReserved, QuantityAvailable, LastCountDate, CreatedDate, UpdatedDate)
VALUES 
(1, 1, 1, 25, 5, 20, GETDATE(), GETDATE(), GETDATE()),  -- Laptops in London
(1, 2, 2, 15, 2, 13, GETDATE(), GETDATE(), GETDATE()),  -- Desktops in London
(2, 3, 3, 50, 10, 40, GETDATE(), GETDATE(), GETDATE()), -- Phones in Manchester
(3, 4, 4, 8, 1, 7, GETDATE(), GETDATE(), GETDATE()),    -- Desks in Birmingham
(4, 5, 5, 20, 3, 17, GETDATE(), GETDATE(), GETDATE());  -- Chairs in Glasgow

-- 20. Insert Carriers
INSERT INTO Carrier (CarrierCode, CarrierName, CarrierType, ContactName, Phone, Email, Website, IsActive, CreatedDate, UpdatedDate)
VALUES 
('DHL', 'DHL Express UK', 'Express', 'DHL Support', '+44 20 7111 1111', '<EMAIL>', 'www.dhl.co.uk', 1, GETDATE(), GETDATE()),
('UPS', 'UPS United Kingdom', 'Standard', 'UPS Customer Service', '+44 20 7222 2222', '<EMAIL>', 'www.ups.com/gb', 1, GETDATE(), GETDATE()),
('FEDEX', 'FedEx UK', 'Express', 'FedEx Support', '+44 20 7333 3333', '<EMAIL>', 'www.fedex.com/gb', 1, GETDATE(), GETDATE()),
('ROYAL', 'Royal Mail', 'Standard', 'Royal Mail Business', '+44 20 7444 4444', '<EMAIL>', 'www.royalmail.com', 1, GETDATE(), GETDATE()),
('LOCAL', 'Local Courier Services', 'SameDay', 'Local Support', '+44 20 7555 5555', '<EMAIL>', 'www.localcourier.co.uk', 1, GETDATE(), GETDATE());

-- 21. Insert Sales Orders
INSERT INTO SalesOrder (OrderNumber, CustomerID, OrderDate, RequestedDate, OrderStatus, SubTotal, TaxAmount, TotalAmount, CreatedDate, UpdatedDate, CreatedByUserID)
VALUES 
('SO-2025-0001', 1, GETDATE(), DATEADD(day, 7, GETDATE()), 'Confirmed', 2500.00, 500.00, 3000.00, GETDATE(), GETDATE(), 3),
('SO-2025-0002', 2, GETDATE(), DATEADD(day, 10, GETDATE()), 'Processing', 1800.00, 360.00, 2160.00, GETDATE(), GETDATE(), 3),
('SO-2025-0003', 3, DATEADD(day, -1, GETDATE()), DATEADD(day, 5, GETDATE()), 'Shipped', 3500.00, 700.00, 4200.00, GETDATE(), GETDATE(), 3),
('SO-2025-0004', 4, DATEADD(day, -2, GETDATE()), DATEADD(day, 4, GETDATE()), 'Pending', 1200.00, 240.00, 1440.00, GETDATE(), GETDATE(), 3),
('SO-2025-0005', 5, DATEADD(day, -3, GETDATE()), DATEADD(day, 3, GETDATE()), 'Confirmed', 2100.00, 420.00, 2520.00, GETDATE(), GETDATE(), 3);

-- 22. Insert Sales Order Items
INSERT INTO SalesOrderItem (SalesOrderID, ProductID, OrderedQuantity, UnitPrice, DiscountPercent, DiscountAmount, TaxAmount, LineTotal, CreatedDate, UpdatedDate)
VALUES 
(1, 1, 2, 899.99, 0, 0, 359.99, 2159.96, GETDATE(), GETDATE()),   -- 2 Laptops
(1, 5, 2, 249.99, 0, 0, 99.99, 599.96, GETDATE(), GETDATE()),     -- 2 Chairs
(2, 2, 3, 599.99, 0, 0, 359.99, 2159.96, GETDATE(), GETDATE()),   -- 3 Desktops
(3, 3, 5, 699.99, 0, 0, 699.99, 4199.94, GETDATE(), GETDATE()),   -- 5 Phones
(4, 4, 3, 399.99, 0, 0, 239.99, 1439.96, GETDATE(), GETDATE()),   -- 3 Desks
(5, 1, 1, 899.99, 0, 0, 179.99, 1079.98, GETDATE(), GETDATE()),   -- 1 Laptop
(5, 2, 1, 599.99, 0, 0, 119.99, 719.98, GETDATE(), GETDATE()),    -- 1 Desktop
(5, 3, 1, 699.99, 0, 0, 139.99, 839.98, GETDATE(), GETDATE());    -- 1 Phone

-- 23. Insert Purchase Orders
INSERT INTO PurchaseOrder (PurchaseOrderNumber, SupplierID, OrderDate, RequiredDate, OrderStatus, SubTotal, TaxAmount, TotalAmount, CreatedDate, UpdatedDate, CreatedByUserID)
VALUES 
('PO-2025-0001', 1, DATEADD(day, -10, GETDATE()), GETDATE(), 'Received', 6500.00, 1300.00, 7800.00, GETDATE(), GETDATE(), 2),
('PO-2025-0002', 2, DATEADD(day, -7, GETDATE()), DATEADD(day, 3, GETDATE()), 'Confirmed', 4200.00, 840.00, 5040.00, GETDATE(), GETDATE(), 2),
('PO-2025-0003', 3, DATEADD(day, -5, GETDATE()), DATEADD(day, 5, GETDATE()), 'Sent', 9600.00, 1920.00, 11520.00, GETDATE(), GETDATE(), 2),
('PO-2025-0004', 4, DATEADD(day, -15, GETDATE()), DATEADD(day, -5, GETDATE()), 'Received', 2500.00, 500.00, 3000.00, GETDATE(), GETDATE(), 2),
('PO-2025-0005', 5, DATEADD(day, -3, GETDATE()), DATEADD(day, 7, GETDATE()), 'Confirmed', 1500.00, 300.00, 1800.00, GETDATE(), GETDATE(), 2);

-- 24. Insert Purchase Order Items
INSERT INTO PurchaseOrderItem (PurchaseOrderID, ProductID, OrderedQuantity, ReceivedQuantity, UnitPrice, DiscountPercent, DiscountAmount, TaxAmount, LineTotal, CreatedDate, UpdatedDate)
VALUES 
(1, 1, 10, 10, 650.00, 0, 0, 1300.00, 7800.00, GETDATE(), GETDATE()),    -- 10 Laptops received
(2, 2, 10, 0, 420.00, 0, 0, 840.00, 5040.00, GETDATE(), GETDATE()),      -- 10 Desktops ordered
(3, 3, 20, 0, 480.00, 0, 0, 1920.00, 11520.00, GETDATE(), GETDATE()),    -- 20 Phones ordered
(4, 4, 10, 10, 250.00, 0, 0, 500.00, 3000.00, GETDATE(), GETDATE()),     -- 10 Desks received
(5, 5, 10, 0, 150.00, 0, 0, 300.00, 1800.00, GETDATE(), GETDATE());      -- 10 Chairs ordered

-- 25. Insert Payments from Customers
INSERT INTO Payment (PaymentNumber, PaymentDate, PaymentType, PaymentMethodID, PaymentStatus, CustomerID, SupplierID, Amount, CurrencyID, BaseAmount, ReferenceNumber, Description, CreatedDate, UpdatedDate, CreatedByUserID)
VALUES 
('PAY-2025-0001', GETDATE(), 'CustomerPayment', 2, 'Completed', 1, NULL, 3000.00, 1, 3000.00, 'INV-001', 'Payment for SO-2025-0001', GETDATE(), GETDATE(), 5),
('PAY-2025-0002', DATEADD(day, -1, GETDATE()), 'CustomerPayment', 3, 'Completed', 3, NULL, 4200.00, 1, 4200.00, 'INV-003', 'Payment for SO-2025-0003', GETDATE(), GETDATE(), 5),
('PAY-2025-0003', DATEADD(day, -2, GETDATE()), 'SupplierPayment', 2, 'Completed', NULL, 1, 7800.00, 1, 7800.00, 'PO-2025-0001', 'Payment to supplier', GETDATE(), GETDATE(), 5),
('PAY-2025-0004', GETDATE(), 'CustomerPayment', 1, 'Completed', 2, NULL, 1000.00, 1, 1000.00, 'PART-PAY', 'Partial payment for SO-2025-0002', GETDATE(), GETDATE(), 5),
('PAY-2025-0005', DATEADD(day, -3, GETDATE()), 'SupplierPayment', 2, 'Completed', NULL, 4, 3000.00, 1, 3000.00, 'PO-2025-0004', 'Payment to furniture supplier', GETDATE(), GETDATE(), 5);

-- 26. Insert Inventory Transactions
INSERT INTO InventoryTransaction (TransactionNumber, TransactionDate, TransactionType, ProductID, WarehouseID, LocationID, Quantity, UnitCost, TotalCost, ReferenceType, ReferenceID, CreatedDate, CreatedByUserID)
VALUES 
('IT-2025-0001', DATEADD(day, -10, GETDATE()), 'Receipt', 1, 1, 1, 10, 650.00, 6500.00, 'PurchaseOrder', 1, GETDATE(), 4),
('IT-2025-0002', DATEADD(day, -1, GETDATE()), 'Issue', 1, 1, 1, -2, 650.00, -1300.00, 'SalesOrder', 1, GETDATE(), 4),
('IT-2025-0003', DATEADD(day, -15, GETDATE()), 'Receipt', 4, 3, 4, 10, 250.00, 2500.00, 'PurchaseOrder', 4, GETDATE(), 4),
('IT-2025-0004', DATEADD(day, -2, GETDATE()), 'Issue', 4, 3, 4, -3, 250.00, -750.00, 'SalesOrder', 4, GETDATE(), 4),
('IT-2025-0005', GETDATE(), 'Adjustment', 3, 2, 3, 5, 480.00, 2400.00, 'Adjustment', NULL, GETDATE(), 4);

-- 27. Insert Delivery Zones
INSERT INTO DeliveryZone (ZoneCode, ZoneName, Description, PostCodePrefix, City, DeliveryDays, IsActive, CreatedDate, UpdatedDate)
VALUES 
('ZONE-LON-1', 'London Central', 'Central London delivery zone', 'EC,WC,E1,W1', 'London', 1, 1, GETDATE(), GETDATE()),
('ZONE-LON-2', 'Greater London', 'Greater London area', 'SE,SW,N,NW', 'London', 1, 1, GETDATE(), GETDATE()),
('ZONE-MAN-1', 'Manchester City', 'Manchester city center', 'M1,M2,M3,M4', 'Manchester', 1, 1, GETDATE(), GETDATE()),
('ZONE-BIR-1', 'Birmingham Central', 'Birmingham city center', 'B1,B2,B3,B4', 'Birmingham', 1, 1, GETDATE(), GETDATE()),
('ZONE-GLA-1', 'Glasgow City', 'Glasgow city center', 'G1,G2,G3,G4', 'Glasgow', 2, 1, GETDATE(), GETDATE());

-- 28. Insert Vehicles
INSERT INTO Vehicle (VehicleNumber, VehicleType, Make, Model, Year, Capacity, CurrentStatus, IsActive, CreatedDate, UpdatedDate)
VALUES 
('LON-VAN-001', 'Van', 'Ford', 'Transit', 2023, 1000, 'Available', 1, GETDATE(), GETDATE()),
('LON-TRK-001', 'Truck', 'Mercedes', 'Sprinter', 2022, 3000, 'Available', 1, GETDATE(), GETDATE()),
('MAN-VAN-001', 'Van', 'Peugeot', 'Boxer', 2023, 1200, 'InUse', 1, GETDATE(), GETDATE()),
('BIR-VAN-001', 'Van', 'Citroen', 'Relay', 2022, 1100, 'Available', 1, GETDATE(), GETDATE()),
('GLA-TRK-001', 'Truck', 'Iveco', 'Daily', 2023, 2500, 'Maintenance', 1, GETDATE(), GETDATE());

-- 29. Insert Delivery Routes
INSERT INTO DeliveryRoute (RouteCode, RouteName, RouteType, DriverName, VehicleNumber, MaxCapacityKg, StartTime, EndTime, IsActive, CreatedDate, UpdatedDate)
VALUES 
('RT-LON-001', 'London Central Route 1', 'Daily', 'John Smith', 'LON-VAN-001', 1000, '08:00', '17:00', 1, GETDATE(), GETDATE()),
('RT-LON-002', 'Greater London Route 1', 'Daily', 'Jane Doe', 'LON-TRK-001', 3000, '07:00', '16:00', 1, GETDATE(), GETDATE()),
('RT-MAN-001', 'Manchester City Route 1', 'Daily', 'Mike Johnson', 'MAN-VAN-001', 1200, '08:30', '17:30', 1, GETDATE(), GETDATE()),
('RT-BIR-001', 'Birmingham Central Route 1', 'Daily', 'Sarah Wilson', 'BIR-VAN-001', 1100, '08:00', '17:00', 1, GETDATE(), GETDATE()),
('RT-GLA-001', 'Glasgow City Route 1', 'Weekly', 'David Brown', 'GLA-TRK-001', 2500, '07:30', '16:30', 1, GETDATE(), GETDATE());

-- 30. Insert Shipments
INSERT INTO Shipment (ShipmentNumber, ShipmentDate, ShipmentType, CarrierID, ServiceID, ShipFromWarehouseID, ShipToName, ShipToAddress1, ShipToCity, ShipToPostCode, ShipToCountry, ShipToPhone, TotalWeight, EstimatedDeliveryDate, ShipmentStatus, InsuranceValue, CreatedDate, UpdatedDate, CreatedByUserID)
VALUES 
('SH-2025-0001', GETDATE(), 'Customer', 1, NULL, 1, 'Tech Solutions Receiving', '10 Tech Park', 'London', 'EC1V 2NX', 'United Kingdom', '+44 20 1111 2222', 15.5, DATEADD(day, 1, GETDATE()), 'InTransit', 2159.96, GETDATE(), GETDATE(), 4),
('SH-2025-0002', DATEADD(day, -1, GETDATE()), 'Customer', 2, NULL, 2, 'Retail World Warehouse', '100 Retail Plaza', 'Birmingham', 'B2 4XY', 'United Kingdom', '+44 121 3333 4444', 10.0, DATEADD(day, 1, GETDATE()), 'Delivered', 4199.94, GETDATE(), GETDATE(), 4),
('SH-2025-0003', GETDATE(), 'Customer', 3, NULL, 1, 'Office Supplies Receiving', '25 Commerce Street', 'Manchester', 'M2 5AB', 'United Kingdom', '+44 161 5555 6666', 25.0, DATEADD(day, 2, GETDATE()), 'Pending', 2159.96, GETDATE(), GETDATE(), 4),
('SH-2025-0004', DATEADD(day, -2, GETDATE()), 'Customer', 4, NULL, 3, 'Smart Systems Dock', '50 Innovation Drive', 'Leeds', 'LS1 3DE', 'United Kingdom', '+44 113 7777 8888', 120.0, GETDATE(), 'InTransit', 1439.96, GETDATE(), GETDATE(), 4),
('SH-2025-0005', GETDATE(), 'Customer', 5, NULL, 4, 'Digital Store Receiving', '75 Digital Avenue', 'Glasgow', 'G2 6FG', 'United Kingdom', '+44 141 9999 0000', 8.0, DATEADD(day, 3, GETDATE()), 'Pending', 2520.00, GETDATE(), GETDATE(), 4);

-- Enable foreign key checks
ALTER TABLE Customer CHECK CONSTRAINT ALL;
ALTER TABLE Supplier CHECK CONSTRAINT ALL;
ALTER TABLE Product CHECK CONSTRAINT ALL;
ALTER TABLE SalesOrder CHECK CONSTRAINT ALL;
ALTER TABLE PurchaseOrder CHECK CONSTRAINT ALL;
GO

PRINT 'Sample data inserted successfully!'
GO