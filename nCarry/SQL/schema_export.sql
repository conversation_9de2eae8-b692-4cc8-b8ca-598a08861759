CREATE TABLE [__EFMigrationsHistory] (

  [ProductVersion] NVARCHAR(32)  NOT NULL,
  CONSTRAINT [PK___EFMigrationsHistory] PRIMARY KEY ([MigrationId])
);
GO
CREATE TABLE [APIKey] (

  [UpdatedDate] DATETIME2 DEFAULT (getdate()) NOT NULL,
  CONSTRAINT [PK_APIKey] PRIMARY KEY ([APIKeyID])
);
GO
CREATE TABLE [AuditLog] (

  [ErrorMessage] NVARCHAR(MAX)  NULL,
  CONSTRAINT [PK_AuditLog] PRIMARY KEY ([AuditLogID])
);
GO
CREATE TABLE [Bank] (

  [UpdatedByUserID] INT  NULL,
  CONSTRAINT [PK_Bank] PRIMARY KEY ([BankID])
);
GO
CREATE TABLE [BankTransaction] (

  [CreatedByUserID] INT  NULL,
  CONSTRAINT [PK_BankTransaction] PRIMARY KEY ([TransactionID])
);
GO
CREATE TABLE [Barcode] (

  [UpdatedDate] DATETIME2 DEFAULT (getdate()) NOT NULL,
  CONSTRAINT [PK_Barcode] PRIMARY KEY ([BarcodeID])
);
GO
CREATE TABLE [Branch] (

  [UpdatedDate] DATETIME2 DEFAULT (getdate()) NOT NULL,
  CONSTRAINT [PK_Branch] PRIMARY KEY ([BranchID])
);
GO
CREATE TABLE [Budget] (

  [UpdatedByUserID] INT  NULL,
  CONSTRAINT [PK_Budget] PRIMARY KEY ([BudgetID])
);
GO
CREATE TABLE [Carrier] (

  [UpdatedByUserID] INT  NULL,
  CONSTRAINT [PK_Carrier] PRIMARY KEY ([CarrierID])
);
GO
CREATE TABLE [CarrierService] (

  [UpdatedByUserID] INT  NULL,
  CONSTRAINT [PK_CarrierService] PRIMARY KEY ([ServiceID])
);
GO
CREATE TABLE [CashRegister] (

  [UpdatedDate] DATETIME2 DEFAULT (getdate()) NOT NULL,
  CONSTRAINT [PK_CashRegister] PRIMARY KEY ([RegisterID])
);
GO
CREATE TABLE [ChartOfAccounts] (

  [UpdatedByUserID] INT  NULL,
  CONSTRAINT [PK_ChartOfAccounts] PRIMARY KEY ([AccountID])
);
GO
CREATE TABLE [Commission] (

  [CreatedByUserID] INT  NULL,
  CONSTRAINT [PK_Commission] PRIMARY KEY ([CommissionID])
);
GO
CREATE TABLE [Company] (

  [UpdatedDate] DATETIME2 DEFAULT (getdate()) NOT NULL,
  CONSTRAINT [PK_Company] PRIMARY KEY ([CompanyID])
);
GO
CREATE TABLE [CostCenter] (

  [UpdatedDate] DATETIME2 DEFAULT (getdate()) NOT NULL,
  CONSTRAINT [PK_CostCenter] PRIMARY KEY ([CostCenterID])
);
GO
CREATE TABLE [CreditNote] (

  [UpdatedByUserID] INT  NULL,
  CONSTRAINT [PK_CreditNote] PRIMARY KEY ([CreditNoteID])
);
GO
CREATE TABLE [CreditNoteItem] (

  [SortOrder] INT DEFAULT ((100)) NOT NULL,
  CONSTRAINT [PK_CreditNoteItem] PRIMARY KEY ([CreditNoteItemID])
);
GO
CREATE TABLE [Currency] (

  [UpdatedDate] DATETIME2 DEFAULT (getdate()) NOT NULL,
  CONSTRAINT [PK_Currency] PRIMARY KEY ([CurrencyID])
);
GO
CREATE TABLE [Customer] (

  [DeletedByUserID] INT  NULL,
  CONSTRAINT [PK_Customer] PRIMARY KEY ([CustomerID])
);
GO
CREATE TABLE [CustomerAddress] (

  [UpdatedDate] DATETIME2 DEFAULT (getdate()) NOT NULL,
  CONSTRAINT [PK_CustomerAddress] PRIMARY KEY ([AddressID])
);
GO
CREATE TABLE [CustomerGroup] (

  [UpdatedDate] DATETIME2 DEFAULT (getdate()) NOT NULL,
  CONSTRAINT [PK_CustomerGroup] PRIMARY KEY ([GroupID])
);
GO
CREATE TABLE [CustomerGroupMember] (

  [IsActive] BIT DEFAULT ((1)) NOT NULL,
  CONSTRAINT [PK_CustomerGroupMember] PRIMARY KEY ([MemberID])
);
GO
CREATE TABLE [CustomerPriceList] (

  [UpdatedByUserID] INT  NULL,
  CONSTRAINT [PK_CustomerPriceList] PRIMARY KEY ([PriceListID])
);
GO
CREATE TABLE [CustomerPriceListItem] (

  [UpdatedDate] DATETIME2 DEFAULT (getdate()) NOT NULL,
  CONSTRAINT [PK_CustomerPriceListItem] PRIMARY KEY ([PriceListItemID])
);
GO
CREATE TABLE [CycleCount] (

  [CreatedByUserID] INT  NULL,
  CONSTRAINT [PK_CycleCount] PRIMARY KEY ([CycleCountID])
);
GO
CREATE TABLE [CycleCountDetail] (

  [Notes] NVARCHAR(500)  NULL,
  CONSTRAINT [PK_CycleCountDetail] PRIMARY KEY ([CountDetailID])
);
GO
CREATE TABLE [Dashboard] (

  [UpdatedDate] DATETIME2 DEFAULT (getdate()) NOT NULL,
  CONSTRAINT [PK_Dashboard] PRIMARY KEY ([DashboardID])
);
GO
CREATE TABLE [DataExport] (

  [ErrorMessage] NVARCHAR(MAX)  NULL,
  CONSTRAINT [PK_DataExport] PRIMARY KEY ([ExportID])
);
GO
CREATE TABLE [DeliveryRoute] (

  [UpdatedByUserID] INT  NULL,
  CONSTRAINT [PK_DeliveryRoute] PRIMARY KEY ([RouteID])
);
GO
CREATE TABLE [DeliveryZone] (

  [UpdatedByUserID] INT  NULL,
  CONSTRAINT [PK_DeliveryZone] PRIMARY KEY ([ZoneID])
);
GO
CREATE TABLE [Document] (

  [CreatedByUserID] INT  NULL,
  CONSTRAINT [PK_Document] PRIMARY KEY ([DocumentID])
);
GO
CREATE TABLE [EDIConfiguration] (

  [UpdatedDate] DATETIME2 DEFAULT (getdate()) NOT NULL,
  CONSTRAINT [PK_EDIConfiguration] PRIMARY KEY ([EDIConfigID])
);
GO
CREATE TABLE [EmailTemplate] (

  [UpdatedByUserID] INT  NULL,
  CONSTRAINT [PK_EmailTemplate] PRIMARY KEY ([TemplateID])
);
GO
CREATE TABLE [ExchangeRate] (

  [CreatedByUserID] INT  NULL,
  CONSTRAINT [PK_ExchangeRate] PRIMARY KEY ([ExchangeRateID])
);
GO
CREATE TABLE [Expense] (

  [UpdatedByUserID] INT  NULL,
  CONSTRAINT [PK_Expense] PRIMARY KEY ([ExpenseID])
);
GO
CREATE TABLE [FreightRate] (

  [UpdatedDate] DATETIME2 DEFAULT (getdate()) NOT NULL,
  CONSTRAINT [PK_FreightRate] PRIMARY KEY ([RateID])
);
GO
CREATE TABLE [GeneralLedger] (

  [CreatedByUserID] INT  NULL,
  CONSTRAINT [PK_GeneralLedger] PRIMARY KEY ([GLID])
);
GO
CREATE TABLE [GoodsReceipt] (

  [UpdatedByUserID] INT  NULL,
  CONSTRAINT [PK_GoodsReceipt] PRIMARY KEY ([ReceiptID])
);
GO
CREATE TABLE [GoodsReceiptItem] (

  [VariantID] INT  NULL,
  CONSTRAINT [PK_GoodsReceiptItem] PRIMARY KEY ([ReceiptItemID])
);
GO
CREATE TABLE [IntegrationLog] (

  [UserAgent] NVARCHAR(500)  NULL,
  CONSTRAINT [PK_IntegrationLog] PRIMARY KEY ([LogID])
);
GO
CREATE TABLE [Inventory] (

  [UpdatedDate] DATETIME2 DEFAULT (getdate()) NOT NULL,
  CONSTRAINT [PK_Inventory] PRIMARY KEY ([InventoryID])
);
GO
CREATE TABLE [InventoryTransaction] (

  [CreatedByUserID] INT  NULL,
  CONSTRAINT [PK_InventoryTransaction] PRIMARY KEY ([TransactionID])
);
GO
CREATE TABLE [KPIDefinition] (

  [UpdatedDate] DATETIME2 DEFAULT (getdate()) NOT NULL,
  CONSTRAINT [PK_KPIDefinition] PRIMARY KEY ([KPIID])
);
GO
CREATE TABLE [NotificationTemplate] (

  [UpdatedDate] DATETIME2 DEFAULT (getdate()) NOT NULL,
  CONSTRAINT [PK_NotificationTemplate] PRIMARY KEY ([NotificationID])
);
GO
CREATE TABLE [NumberSequence] (

  [UpdatedDate] DATETIME2 DEFAULT (getdate()) NOT NULL,
  CONSTRAINT [PK_NumberSequence] PRIMARY KEY ([SequenceID])
);
GO
CREATE TABLE [PackingSlip] (

  [UpdatedByUserID] INT  NULL,
  CONSTRAINT [PK_PackingSlip] PRIMARY KEY ([PackingSlipID])
);
GO
CREATE TABLE [PackingSlipItem] (

  [Volume] DECIMAL(18, 2)  NULL,
  CONSTRAINT [PK_PackingSlipItem] PRIMARY KEY ([PackingItemID])
);
GO
CREATE TABLE [PasswordResetToken] (

  [IPAddress] NVARCHAR(45)  NULL,
  CONSTRAINT [PK_PasswordResetToken] PRIMARY KEY ([TokenID])
);
GO
CREATE TABLE [Payment] (

  [DeletedByUserID] INT  NULL,
  CONSTRAINT [PK_Payment] PRIMARY KEY ([PaymentID])
);
GO
CREATE TABLE [PaymentAllocation] (

  [CreatedByUserID] INT  NULL,
  CONSTRAINT [PK_PaymentAllocation] PRIMARY KEY ([AllocationID])
);
GO
CREATE TABLE [PaymentMethod] (

  [UpdatedDate] DATETIME2 DEFAULT (getdate()) NOT NULL,
  CONSTRAINT [PK_PaymentMethod] PRIMARY KEY ([PaymentMethodID])
);
GO
CREATE TABLE [Permission] (

  [CreatedDate] DATETIME2 DEFAULT (getdate()) NOT NULL,
  CONSTRAINT [PK_Permission] PRIMARY KEY ([PermissionID])
);
GO
CREATE TABLE [PickingList] (

  [UpdatedByUserID] INT  NULL,
  CONSTRAINT [PK_PickingList] PRIMARY KEY ([PickingListID])
);
GO
CREATE TABLE [PickingListItem] (

  [UpdatedDate] DATETIME2  NULL,
  CONSTRAINT [PK_PickingListItem] PRIMARY KEY ([PickingItemID])
);
GO
CREATE TABLE [Product] (

  [Currency] NVARCHAR(3) DEFAULT ('GBP') NOT NULL,
  CONSTRAINT [PK_Product] PRIMARY KEY ([ProductID])
);
GO
CREATE TABLE [ProductBatch] (

  [CreatedByUserID] INT  NULL,
  CONSTRAINT [PK_ProductBatch] PRIMARY KEY ([BatchID])
);
GO
CREATE TABLE [ProductCategory] (

  [UpdatedByUserID] INT  NULL,
  CONSTRAINT [PK_ProductCategory] PRIMARY KEY ([CategoryID])
);
GO
CREATE TABLE [ProductImage] (

  [CreatedByUserID] INT  NULL,
  CONSTRAINT [PK_ProductImage] PRIMARY KEY ([ImageID])
);
GO
CREATE TABLE [ProductKitComponent] (

  [UpdatedDate] DATETIME2 DEFAULT (getdate()) NOT NULL,
  CONSTRAINT [PK_ProductKitComponent] PRIMARY KEY ([ComponentID])
);
GO
CREATE TABLE [ProductPriceHistory] (

  [CreatedByUserID] INT  NULL,
  CONSTRAINT [PK_ProductPriceHistory] PRIMARY KEY ([HistoryID])
);
GO
CREATE TABLE [ProductPricing] (

  [UpdatedByUserID] INT  NULL,
  CONSTRAINT [PK_ProductPricing] PRIMARY KEY ([PricingID])
);
GO
CREATE TABLE [ProductUOMConversion] (

  [UpdatedDate] DATETIME2 DEFAULT (getdate()) NOT NULL,
  CONSTRAINT [PK_ProductUOMConversion] PRIMARY KEY ([ConversionID])
);
GO
CREATE TABLE [ProductVariant] (

  [UpdatedDate] DATETIME2 DEFAULT (getdate()) NOT NULL,
  CONSTRAINT [PK_ProductVariant] PRIMARY KEY ([VariantID])
);
GO
CREATE TABLE [PurchaseInvoice] (

  [DeletedByUserID] INT  NULL,
  CONSTRAINT [PK_PurchaseInvoice] PRIMARY KEY ([InvoiceID])
);
GO
CREATE TABLE [PurchaseInvoiceItem] (

  [SortOrder] INT DEFAULT ((100)) NOT NULL,
  CONSTRAINT [PK_PurchaseInvoiceItem] PRIMARY KEY ([InvoiceItemID])
);
GO
CREATE TABLE [PurchaseOrder] (

  [DeletedByUserID] INT  NULL,
  CONSTRAINT [PK_PurchaseOrder] PRIMARY KEY ([PurchaseOrderID])
);
GO
CREATE TABLE [PurchaseOrderItem] (

  [Currency] NVARCHAR(3)  NULL,
  CONSTRAINT [PK_PurchaseOrderItem] PRIMARY KEY ([PurchaseOrderItemID])
);
GO
CREATE TABLE [PurchaseRequisition] (

  [UpdatedByUserID] INT  NULL,
  CONSTRAINT [PK_PurchaseRequisition] PRIMARY KEY ([RequisitionID])
);
GO
CREATE TABLE [PurchaseRequisitionItem] (

  [Notes] NVARCHAR(500)  NULL,
  CONSTRAINT [PK_PurchaseRequisitionItem] PRIMARY KEY ([RequisitionItemID])
);
GO
CREATE TABLE [PurchaseReturn] (

  [UpdatedByUserID] INT  NULL,
  CONSTRAINT [PK_PurchaseReturn] PRIMARY KEY ([ReturnID])
);
GO
CREATE TABLE [PurchaseReturnItem] (

  [SortOrder] INT DEFAULT ((100)) NOT NULL,
  CONSTRAINT [PK_PurchaseReturnItem] PRIMARY KEY ([ReturnItemID])
);
GO
CREATE TABLE [Quote] (

  [ContactID] INT  NULL,
  CONSTRAINT [PK_Quote] PRIMARY KEY ([QuoteID])
);
GO
CREATE TABLE [QuoteItem] (

  [SortOrder] INT DEFAULT ((100)) NOT NULL,
  CONSTRAINT [PK_QuoteItem] PRIMARY KEY ([QuoteItemID])
);
GO
CREATE TABLE [Receipt] (

  [CreatedByUserID] INT  NULL,
  CONSTRAINT [PK_Receipt] PRIMARY KEY ([ReceiptID])
);
GO
CREATE TABLE [ReportTemplate] (

  [UpdatedByUserID] INT  NULL,
  CONSTRAINT [PK_ReportTemplate] PRIMARY KEY ([ReportID])
);
GO
CREATE TABLE [Role] (

  [UpdatedByUserID] INT  NULL,
  CONSTRAINT [PK_Role] PRIMARY KEY ([RoleID])
);
GO
CREATE TABLE [RolePermission] (

  [GrantedByUserID] INT  NULL,
  CONSTRAINT [PK_RolePermission] PRIMARY KEY ([RolePermissionID])
);
GO
CREATE TABLE [RouteZone] (

  [IsActive] BIT DEFAULT ((1)) NOT NULL,
  CONSTRAINT [PK_RouteZone] PRIMARY KEY ([RouteZoneID])
);
GO
CREATE TABLE [SalesInvoice] (

  [DeletedByUserID] INT  NULL,
  CONSTRAINT [PK_SalesInvoice] PRIMARY KEY ([InvoiceID])
);
GO
CREATE TABLE [SalesInvoiceItem] (

  [UpdatedByUserID] INT  NULL,
  CONSTRAINT [PK_SalesInvoiceItem] PRIMARY KEY ([InvoiceItemID])
);
GO
CREATE TABLE [SalesOrder] (

  [DeletedByUserID] INT  NULL,
  CONSTRAINT [PK_SalesOrder] PRIMARY KEY ([OrderID])
);
GO
CREATE TABLE [SalesOrderItem] (

  [SortOrder] INT DEFAULT ((100)) NOT NULL,
  CONSTRAINT [PK_SalesOrderItem] PRIMARY KEY ([OrderItemID])
);
GO
CREATE TABLE [SalesTarget] (

  [UpdatedDate] DATETIME2 DEFAULT (getdate()) NOT NULL,
  CONSTRAINT [PK_SalesTarget] PRIMARY KEY ([TargetID])
);
GO
CREATE TABLE [SavedSearch] (

  [UpdatedDate] DATETIME2 DEFAULT (getdate()) NOT NULL,
  CONSTRAINT [PK_SavedSearch] PRIMARY KEY ([SearchID])
);
GO
CREATE TABLE [SerialNumber] (

  [UpdatedDate] DATETIME2 DEFAULT (getdate()) NOT NULL,
  CONSTRAINT [PK_SerialNumber] PRIMARY KEY ([SerialID])
);
GO
CREATE TABLE [Shipment] (

  [UpdatedByUserID] INT  NULL,
  CONSTRAINT [PK_Shipment] PRIMARY KEY ([ShipmentID])
);
GO
CREATE TABLE [ShipmentItem] (

  [UpdatedByUserID] INT  NULL,
  CONSTRAINT [PK_ShipmentItem] PRIMARY KEY ([ShipmentItemID])
);
GO
CREATE TABLE [ShipmentTracking] (

  [UpdatedDate] DATETIME2 DEFAULT (getdate()) NOT NULL,
  CONSTRAINT [PK_ShipmentTracking] PRIMARY KEY ([TrackingID])
);
GO
CREATE TABLE [StockAdjustment] (

  [UpdatedByUserID] INT  NULL,
  CONSTRAINT [PK_StockAdjustment] PRIMARY KEY ([AdjustmentID])
);
GO
CREATE TABLE [StockAdjustmentDetail] (

  [Reason] NVARCHAR(500)  NULL,
  CONSTRAINT [PK_StockAdjustmentDetail] PRIMARY KEY ([AdjustmentDetailID])
);
GO
CREATE TABLE [StockReservation] (

  [UpdatedDate] DATETIME2 DEFAULT (getdate()) NOT NULL,
  CONSTRAINT [PK_StockReservation] PRIMARY KEY ([ReservationID])
);
GO
CREATE TABLE [StockTransfer] (

  [UpdatedByUserID] INT  NULL,
  CONSTRAINT [PK_StockTransfer] PRIMARY KEY ([TransferID])
);
GO
CREATE TABLE [StockTransferDetail] (

  [Notes] NVARCHAR(500)  NULL,
  CONSTRAINT [PK_StockTransferDetail] PRIMARY KEY ([TransferDetailID])
);
GO
CREATE TABLE [Supplier] (

  [DeletedByUserID] INT  NULL,
  CONSTRAINT [PK_Supplier] PRIMARY KEY ([SupplierID])
);
GO
CREATE TABLE [SupplierAddress] (

  [UpdatedDate] DATETIME2 DEFAULT (getdate()) NOT NULL,
  CONSTRAINT [PK_SupplierAddress] PRIMARY KEY ([AddressID])
);
GO
CREATE TABLE [SupplierAudit] (

  [CreatedByUserID] INT  NULL,
  CONSTRAINT [PK_SupplierAudit] PRIMARY KEY ([AuditID])
);
GO
CREATE TABLE [SupplierContact] (

  [UpdatedByUserID] INT  NULL,
  CONSTRAINT [PK_SupplierContact] PRIMARY KEY ([ContactID])
);
GO
CREATE TABLE [SupplierDocument] (

  [UploadedByUserID] INT  NULL,
  CONSTRAINT [PK_SupplierDocument] PRIMARY KEY ([DocumentID])
);
GO
CREATE TABLE [SupplierPerformance] (

  [UpdatedDate] DATETIME2 DEFAULT (getdate()) NOT NULL,
  CONSTRAINT [PK_SupplierPerformance] PRIMARY KEY ([PerformanceID])
);
GO
CREATE TABLE [SupplierPriceList] (

  [UpdatedByUserID] INT  NULL,
  CONSTRAINT [PK_SupplierPriceList] PRIMARY KEY ([PriceListID])
);
GO
CREATE TABLE [SupplierPriceListItem] (

  [UpdatedDate] DATETIME2 DEFAULT (getdate()) NOT NULL,
  CONSTRAINT [PK_SupplierPriceListItem] PRIMARY KEY ([PriceListItemID])
);
GO
CREATE TABLE [SupplierProduct] (

  [UpdatedDate] DATETIME2 DEFAULT (getdate()) NOT NULL,
  CONSTRAINT [PK_SupplierProduct] PRIMARY KEY ([SupplierProductID])
);
GO
CREATE TABLE [SystemConfiguration] (

  [UpdatedByUserID] INT  NULL,
  CONSTRAINT [PK_SystemConfiguration] PRIMARY KEY ([ConfigID])
);
GO
CREATE TABLE [TaxCode] (

  [UpdatedDate] DATETIME2 DEFAULT (getdate()) NOT NULL,
  CONSTRAINT [PK_TaxCode] PRIMARY KEY ([TaxCodeID])
);
GO
CREATE TABLE [TaxTransaction] (

  [CreatedDate] DATETIME2 DEFAULT (getdate()) NOT NULL,
  CONSTRAINT [PK_TaxTransaction] PRIMARY KEY ([TaxTransactionID])
);
GO
CREATE TABLE [UnitOfMeasure] (

  [UpdatedDate] DATETIME2 DEFAULT (getdate()) NOT NULL,
  CONSTRAINT [PK_UnitOfMeasure] PRIMARY KEY ([UOMID])
);
GO
CREATE TABLE [User] (

  [DeletedByUserID] INT  NULL,
  CONSTRAINT [PK_User] PRIMARY KEY ([UserID])
);
GO
CREATE TABLE [UserRole] (

  [IsActive] BIT DEFAULT ((1)) NOT NULL,
  CONSTRAINT [PK_UserRole] PRIMARY KEY ([UserRoleID])
);
GO
CREATE TABLE [UserSession] (

  [ExpiryDate] DATETIME2  NOT NULL,
  CONSTRAINT [PK_UserSession] PRIMARY KEY ([SessionID])
);
GO
CREATE TABLE [Warehouse] (

  [UpdatedByUserID] INT  NULL,
  CONSTRAINT [PK_Warehouse] PRIMARY KEY ([WarehouseID])
);
GO
CREATE TABLE [WarehouseLocation] (

  [UpdatedDate] DATETIME2 DEFAULT (getdate()) NOT NULL,
  CONSTRAINT [PK_WarehouseLocation] PRIMARY KEY ([LocationID])
);
GO
CREATE TABLE [WebhookConfiguration] (

  [UpdatedDate] DATETIME2 DEFAULT (getdate()) NOT NULL,
  CONSTRAINT [PK_WebhookConfiguration] PRIMARY KEY ([WebhookID])
);
GO
