-- Sales Management Tables Creation Script
-- Purpose: Comprehensive sales process from quotation to invoice
-- Supports quotes, orders, invoices, and credit notes

-- Quote table for sales quotations
CREATE TABLE [dbo].[Quote] (
    [QuoteID] INT IDENTITY(1,1) NOT NULL,
    [QuoteNumber] NVARCHAR(50) UNIQUE NOT NULL,
    [QuoteDate] DATETIME2 DEFAULT GETDATE() NOT NULL,
    [CustomerID] INT NOT NULL,
    [CustomerReference] NVARCHAR(100) NULL,
    [SalesRepID] INT NULL,
    [QuoteStatus] NVARCHAR(20) DEFAULT 'Draft' NOT NULL, -- Draft, Sent, Accepted, Rejected, Expired, Converted
    
    -- Validity
    [ValidFrom] DATETIME2 DEFAULT GETDATE() NOT NULL,
    [ValidTo] DATETIME2 NOT NULL,
    
    -- Financial Summary
    [SubTotal] DECIMAL(18,2) DEFAULT 0 NOT NULL,
    [DiscountAmount] DECIMAL(18,2) DEFAULT 0 NULL,
    [TaxAmount] DECIMAL(18,2) DEFAULT 0 NOT NULL,
    [ShippingAmount] DECIMAL(18,2) DEFAULT 0 NULL,
    [TotalAmount] DECIMAL(18,2) DEFAULT 0 NOT NULL,
    [Currency] NVARCHAR(3) DEFAULT 'GBP' NOT NULL,
    [ExchangeRate] DECIMAL(18,6) DEFAULT 1 NOT NULL,
    
    -- Addresses
    [BillingAddressID] INT NULL,
    [ShippingAddressID] INT NULL,
    [DeliveryInstructions] NVARCHAR(MAX) NULL,
    
    -- Terms and conditions
    [PaymentTerms] NVARCHAR(100) NULL,
    [DeliveryTerms] NVARCHAR(100) NULL,
    [Notes] NVARCHAR(MAX) NULL,
    [InternalNotes] NVARCHAR(MAX) NULL,
    [TermsAndConditions] NVARCHAR(MAX) NULL,
    
    -- Conversion tracking
    [ConvertedToOrderID] INT NULL,
    [ConversionDate] DATETIME2 NULL,
    
    -- System fields
    [CreatedDate] DATETIME2 DEFAULT GETDATE() NOT NULL,
    [CreatedByUserID] INT NULL,
    [UpdatedDate] DATETIME2 DEFAULT GETDATE() NOT NULL,
    [UpdatedByUserID] INT NULL,
    
    CONSTRAINT [PK_Quote] PRIMARY KEY CLUSTERED ([QuoteID] ASC),
    CONSTRAINT [FK_Quote_Customer] FOREIGN KEY ([CustomerID]) REFERENCES [dbo].[Customer] ([CustomerID]),
    CONSTRAINT [FK_Quote_SalesRep] FOREIGN KEY ([SalesRepID]) REFERENCES [dbo].[User] ([UserID]),
    CONSTRAINT [FK_Quote_BillingAddress] FOREIGN KEY ([BillingAddressID]) REFERENCES [dbo].[CustomerAddress] ([AddressID]),
    CONSTRAINT [FK_Quote_ShippingAddress] FOREIGN KEY ([ShippingAddressID]) REFERENCES [dbo].[CustomerAddress] ([AddressID])
);

-- Quote Item table
CREATE TABLE [dbo].[QuoteItem] (
    [QuoteItemID] INT IDENTITY(1,1) NOT NULL,
    [QuoteID] INT NOT NULL,
    [LineNumber] INT NOT NULL,
    [ProductID] INT NOT NULL,
    [VariantID] INT NULL,
    [ProductCode] NVARCHAR(50) NOT NULL,
    [ProductName] NVARCHAR(255) NOT NULL,
    [Description] NVARCHAR(MAX) NULL,
    [Quantity] DECIMAL(18,3) NOT NULL,
    [UOMID] INT NOT NULL,
    [UnitPrice] DECIMAL(18,4) NOT NULL,
    [DiscountPercent] DECIMAL(5,2) DEFAULT 0 NULL,
    [DiscountAmount] DECIMAL(18,2) DEFAULT 0 NULL,
    [TaxRate] DECIMAL(5,2) DEFAULT 20 NOT NULL,
    [TaxAmount] DECIMAL(18,2) DEFAULT 0 NOT NULL,
    [LineTotal] DECIMAL(18,2) NOT NULL,
    [Notes] NVARCHAR(500) NULL,
    [SortOrder] INT DEFAULT 100 NOT NULL,
    CONSTRAINT [PK_QuoteItem] PRIMARY KEY CLUSTERED ([QuoteItemID] ASC),
    CONSTRAINT [FK_QuoteItem_Quote] FOREIGN KEY ([QuoteID]) REFERENCES [dbo].[Quote] ([QuoteID]),
    CONSTRAINT [FK_QuoteItem_Product] FOREIGN KEY ([ProductID]) REFERENCES [dbo].[Product] ([ProductID]),
    CONSTRAINT [FK_QuoteItem_Variant] FOREIGN KEY ([VariantID]) REFERENCES [dbo].[ProductVariant] ([VariantID]),
    CONSTRAINT [FK_QuoteItem_UOM] FOREIGN KEY ([UOMID]) REFERENCES [dbo].[UnitOfMeasure] ([UOMID])
);

-- Sales Order table
CREATE TABLE [dbo].[SalesOrder] (
    [OrderID] INT IDENTITY(1,1) NOT NULL,
    [OrderNumber] NVARCHAR(50) UNIQUE NOT NULL,
    [OrderDate] DATETIME2 DEFAULT GETDATE() NOT NULL,
    [CustomerID] INT NOT NULL,
    [CustomerPO] NVARCHAR(100) NULL,
    [QuoteID] INT NULL,
    [SalesRepID] INT NULL,
    [OrderStatus] NVARCHAR(20) DEFAULT 'Pending' NOT NULL, -- Pending, Confirmed, Processing, Shipped, Delivered, Cancelled, On-Hold
    [OrderType] NVARCHAR(20) DEFAULT 'Standard' NOT NULL, -- Standard, Rush, Backorder, Pre-order
    
    -- Dates
    [RequestedDate] DATETIME2 NULL,
    [PromisedDate] DATETIME2 NULL,
    [ShippedDate] DATETIME2 NULL,
    [DeliveredDate] DATETIME2 NULL,
    
    -- Financial Summary
    [SubTotal] DECIMAL(18,2) DEFAULT 0 NOT NULL,
    [DiscountAmount] DECIMAL(18,2) DEFAULT 0 NULL,
    [TaxAmount] DECIMAL(18,2) DEFAULT 0 NOT NULL,
    [ShippingAmount] DECIMAL(18,2) DEFAULT 0 NULL,
    [TotalAmount] DECIMAL(18,2) DEFAULT 0 NOT NULL,
    [PaidAmount] DECIMAL(18,2) DEFAULT 0 NOT NULL,
    [BalanceAmount] DECIMAL(18,2) DEFAULT 0 NOT NULL,
    [Currency] NVARCHAR(3) DEFAULT 'GBP' NOT NULL,
    [ExchangeRate] DECIMAL(18,6) DEFAULT 1 NOT NULL,
    
    -- Addresses
    [BillingAddressID] INT NULL,
    [ShippingAddressID] INT NULL,
    [DeliveryInstructions] NVARCHAR(MAX) NULL,
    
    -- Shipping Information
    [ShippingMethod] NVARCHAR(100) NULL,
    [CarrierID] INT NULL,
    [TrackingNumber] NVARCHAR(100) NULL,
    [FreightTerms] NVARCHAR(50) NULL,
    
    -- Terms and conditions
    [PaymentTerms] NVARCHAR(100) NULL,
    [PaymentDueDate] DATETIME2 NULL,
    [Notes] NVARCHAR(MAX) NULL,
    [InternalNotes] NVARCHAR(MAX) NULL,
    
    -- Warehouse
    [WarehouseID] INT NULL,
    [PickingPriority] INT DEFAULT 100 NULL,
    
    -- System fields
    [CreatedDate] DATETIME2 DEFAULT GETDATE() NOT NULL,
    [CreatedByUserID] INT NULL,
    [UpdatedDate] DATETIME2 DEFAULT GETDATE() NOT NULL,
    [UpdatedByUserID] INT NULL,
    [IsDeleted] BIT DEFAULT 0 NOT NULL,
    [DeletedDate] DATETIME2 NULL,
    [DeletedByUserID] INT NULL,
    
    CONSTRAINT [PK_SalesOrder] PRIMARY KEY CLUSTERED ([OrderID] ASC),
    CONSTRAINT [FK_SalesOrder_Customer] FOREIGN KEY ([CustomerID]) REFERENCES [dbo].[Customer] ([CustomerID]),
    CONSTRAINT [FK_SalesOrder_Quote] FOREIGN KEY ([QuoteID]) REFERENCES [dbo].[Quote] ([QuoteID]),
    CONSTRAINT [FK_SalesOrder_SalesRep] FOREIGN KEY ([SalesRepID]) REFERENCES [dbo].[User] ([UserID]),
    CONSTRAINT [FK_SalesOrder_BillingAddress] FOREIGN KEY ([BillingAddressID]) REFERENCES [dbo].[CustomerAddress] ([AddressID]),
    CONSTRAINT [FK_SalesOrder_ShippingAddress] FOREIGN KEY ([ShippingAddressID]) REFERENCES [dbo].[CustomerAddress] ([AddressID]),
    CONSTRAINT [FK_SalesOrder_Warehouse] FOREIGN KEY ([WarehouseID]) REFERENCES [dbo].[Warehouse] ([WarehouseID])
);

-- Sales Order Item table
CREATE TABLE [dbo].[SalesOrderItem] (
    [OrderItemID] INT IDENTITY(1,1) NOT NULL,
    [OrderID] INT NOT NULL,
    [LineNumber] INT NOT NULL,
    [ProductID] INT NOT NULL,
    [VariantID] INT NULL,
    [ProductCode] NVARCHAR(50) NOT NULL,
    [ProductName] NVARCHAR(255) NOT NULL,
    [Description] NVARCHAR(MAX) NULL,
    [OrderedQuantity] DECIMAL(18,3) NOT NULL,
    [ShippedQuantity] DECIMAL(18,3) DEFAULT 0 NOT NULL,
    [BackorderedQuantity] DECIMAL(18,3) DEFAULT 0 NOT NULL,
    [CancelledQuantity] DECIMAL(18,3) DEFAULT 0 NOT NULL,
    [UOMID] INT NOT NULL,
    [UnitPrice] DECIMAL(18,4) NOT NULL,
    [UnitCost] DECIMAL(18,4) NULL,
    [DiscountPercent] DECIMAL(5,2) DEFAULT 0 NULL,
    [DiscountAmount] DECIMAL(18,2) DEFAULT 0 NULL,
    [TaxRate] DECIMAL(5,2) DEFAULT 20 NOT NULL,
    [TaxAmount] DECIMAL(18,2) DEFAULT 0 NOT NULL,
    [LineTotal] DECIMAL(18,2) NOT NULL,
    [ItemStatus] NVARCHAR(20) DEFAULT 'Pending' NOT NULL, -- Pending, Allocated, Picked, Packed, Shipped, Delivered, Cancelled
    [WarehouseID] INT NULL,
    [LocationID] INT NULL,
    [BatchNumber] NVARCHAR(50) NULL,
    [SerialNumber] NVARCHAR(100) NULL,
    [Notes] NVARCHAR(500) NULL,
    [SortOrder] INT DEFAULT 100 NOT NULL,
    CONSTRAINT [PK_SalesOrderItem] PRIMARY KEY CLUSTERED ([OrderItemID] ASC),
    CONSTRAINT [FK_SalesOrderItem_Order] FOREIGN KEY ([OrderID]) REFERENCES [dbo].[SalesOrder] ([OrderID]),
    CONSTRAINT [FK_SalesOrderItem_Product] FOREIGN KEY ([ProductID]) REFERENCES [dbo].[Product] ([ProductID]),
    CONSTRAINT [FK_SalesOrderItem_Variant] FOREIGN KEY ([VariantID]) REFERENCES [dbo].[ProductVariant] ([VariantID]),
    CONSTRAINT [FK_SalesOrderItem_UOM] FOREIGN KEY ([UOMID]) REFERENCES [dbo].[UnitOfMeasure] ([UOMID]),
    CONSTRAINT [FK_SalesOrderItem_Warehouse] FOREIGN KEY ([WarehouseID]) REFERENCES [dbo].[Warehouse] ([WarehouseID]),
    CONSTRAINT [FK_SalesOrderItem_Location] FOREIGN KEY ([LocationID]) REFERENCES [dbo].[WarehouseLocation] ([LocationID])
);

-- Sales Invoice table
CREATE TABLE [dbo].[SalesInvoice] (
    [InvoiceID] INT IDENTITY(1,1) NOT NULL,
    [InvoiceNumber] NVARCHAR(50) UNIQUE NOT NULL,
    [InvoiceDate] DATETIME2 DEFAULT GETDATE() NOT NULL,
    [CustomerID] INT NOT NULL,
    [OrderID] INT NULL,
    [InvoiceType] NVARCHAR(20) DEFAULT 'Standard' NOT NULL, -- Standard, Credit, Debit, Proforma
    [InvoiceStatus] NVARCHAR(20) DEFAULT 'Draft' NOT NULL, -- Draft, Sent, Paid, PartiallyPaid, Overdue, Cancelled, Written-off
    
    -- Financial Summary
    [SubTotal] DECIMAL(18,2) DEFAULT 0 NOT NULL,
    [DiscountAmount] DECIMAL(18,2) DEFAULT 0 NULL,
    [TaxAmount] DECIMAL(18,2) DEFAULT 0 NOT NULL,
    [ShippingAmount] DECIMAL(18,2) DEFAULT 0 NULL,
    [TotalAmount] DECIMAL(18,2) DEFAULT 0 NOT NULL,
    [PaidAmount] DECIMAL(18,2) DEFAULT 0 NOT NULL,
    [BalanceAmount] DECIMAL(18,2) DEFAULT 0 NOT NULL,
    [Currency] NVARCHAR(3) DEFAULT 'GBP' NOT NULL,
    [ExchangeRate] DECIMAL(18,6) DEFAULT 1 NOT NULL,
    
    -- Payment Information
    [PaymentTerms] NVARCHAR(100) NULL,
    [DueDate] DATETIME2 NOT NULL,
    [PaymentStatus] NVARCHAR(20) DEFAULT 'Unpaid' NOT NULL, -- Unpaid, Partial, Paid, Overdue
    [LastPaymentDate] DATETIME2 NULL,
    
    -- Addresses
    [BillingAddressID] INT NULL,
    [ShippingAddressID] INT NULL,
    
    -- Reference Information
    [CustomerPO] NVARCHAR(100) NULL,
    [DeliveryNoteNumber] NVARCHAR(50) NULL,
    [Notes] NVARCHAR(MAX) NULL,
    [InternalNotes] NVARCHAR(MAX) NULL,
    [FooterText] NVARCHAR(MAX) NULL,
    
    -- System fields
    [PostedDate] DATETIME2 NULL,
    [PostedByUserID] INT NULL,
    [CreatedDate] DATETIME2 DEFAULT GETDATE() NOT NULL,
    [CreatedByUserID] INT NULL,
    [UpdatedDate] DATETIME2 DEFAULT GETDATE() NOT NULL,
    [UpdatedByUserID] INT NULL,
    [IsDeleted] BIT DEFAULT 0 NOT NULL,
    [DeletedDate] DATETIME2 NULL,
    [DeletedByUserID] INT NULL,
    
    CONSTRAINT [PK_SalesInvoice] PRIMARY KEY CLUSTERED ([InvoiceID] ASC),
    CONSTRAINT [FK_SalesInvoice_Customer] FOREIGN KEY ([CustomerID]) REFERENCES [dbo].[Customer] ([CustomerID]),
    CONSTRAINT [FK_SalesInvoice_Order] FOREIGN KEY ([OrderID]) REFERENCES [dbo].[SalesOrder] ([OrderID]),
    CONSTRAINT [FK_SalesInvoice_BillingAddress] FOREIGN KEY ([BillingAddressID]) REFERENCES [dbo].[CustomerAddress] ([AddressID]),
    CONSTRAINT [FK_SalesInvoice_ShippingAddress] FOREIGN KEY ([ShippingAddressID]) REFERENCES [dbo].[CustomerAddress] ([AddressID])
);

-- Sales Invoice Item table
CREATE TABLE [dbo].[SalesInvoiceItem] (
    [InvoiceItemID] INT IDENTITY(1,1) NOT NULL,
    [InvoiceID] INT NOT NULL,
    [OrderItemID] INT NULL,
    [LineNumber] INT NOT NULL,
    [ProductID] INT NOT NULL,
    [VariantID] INT NULL,
    [ProductCode] NVARCHAR(50) NOT NULL,
    [ProductName] NVARCHAR(255) NOT NULL,
    [Description] NVARCHAR(MAX) NULL,
    [Quantity] DECIMAL(18,3) NOT NULL,
    [UOMID] INT NOT NULL,
    [UnitPrice] DECIMAL(18,4) NOT NULL,
    [DiscountPercent] DECIMAL(5,2) DEFAULT 0 NULL,
    [DiscountAmount] DECIMAL(18,2) DEFAULT 0 NULL,
    [TaxRate] DECIMAL(5,2) DEFAULT 20 NOT NULL,
    [TaxAmount] DECIMAL(18,2) DEFAULT 0 NOT NULL,
    [LineTotal] DECIMAL(18,2) NOT NULL,
    [AccountCode] NVARCHAR(20) NULL,
    [Notes] NVARCHAR(500) NULL,
    [SortOrder] INT DEFAULT 100 NOT NULL,
    CONSTRAINT [PK_SalesInvoiceItem] PRIMARY KEY CLUSTERED ([InvoiceItemID] ASC),
    CONSTRAINT [FK_SalesInvoiceItem_Invoice] FOREIGN KEY ([InvoiceID]) REFERENCES [dbo].[SalesInvoice] ([InvoiceID]),
    CONSTRAINT [FK_SalesInvoiceItem_OrderItem] FOREIGN KEY ([OrderItemID]) REFERENCES [dbo].[SalesOrderItem] ([OrderItemID]),
    CONSTRAINT [FK_SalesInvoiceItem_Product] FOREIGN KEY ([ProductID]) REFERENCES [dbo].[Product] ([ProductID]),
    CONSTRAINT [FK_SalesInvoiceItem_Variant] FOREIGN KEY ([VariantID]) REFERENCES [dbo].[ProductVariant] ([VariantID]),
    CONSTRAINT [FK_SalesInvoiceItem_UOM] FOREIGN KEY ([UOMID]) REFERENCES [dbo].[UnitOfMeasure] ([UOMID])
);

-- Credit Note table
CREATE TABLE [dbo].[CreditNote] (
    [CreditNoteID] INT IDENTITY(1,1) NOT NULL,
    [CreditNoteNumber] NVARCHAR(50) UNIQUE NOT NULL,
    [CreditNoteDate] DATETIME2 DEFAULT GETDATE() NOT NULL,
    [CustomerID] INT NOT NULL,
    [InvoiceID] INT NULL,
    [CreditNoteStatus] NVARCHAR(20) DEFAULT 'Draft' NOT NULL, -- Draft, Approved, Applied, Cancelled
    [CreditReason] NVARCHAR(100) NOT NULL, -- Return, Damage, Pricing Error, Other
    
    -- Financial Summary
    [SubTotal] DECIMAL(18,2) DEFAULT 0 NOT NULL,
    [TaxAmount] DECIMAL(18,2) DEFAULT 0 NOT NULL,
    [TotalAmount] DECIMAL(18,2) DEFAULT 0 NOT NULL,
    [AppliedAmount] DECIMAL(18,2) DEFAULT 0 NOT NULL,
    [BalanceAmount] DECIMAL(18,2) DEFAULT 0 NOT NULL,
    [Currency] NVARCHAR(3) DEFAULT 'GBP' NOT NULL,
    
    -- Reference Information
    [RMANumber] NVARCHAR(50) NULL, -- Return Merchandise Authorization
    [Notes] NVARCHAR(MAX) NULL,
    [InternalNotes] NVARCHAR(MAX) NULL,
    
    -- System fields
    [ApprovedByUserID] INT NULL,
    [ApprovedDate] DATETIME2 NULL,
    [CreatedDate] DATETIME2 DEFAULT GETDATE() NOT NULL,
    [CreatedByUserID] INT NULL,
    [UpdatedDate] DATETIME2 DEFAULT GETDATE() NOT NULL,
    [UpdatedByUserID] INT NULL,
    
    CONSTRAINT [PK_CreditNote] PRIMARY KEY CLUSTERED ([CreditNoteID] ASC),
    CONSTRAINT [FK_CreditNote_Customer] FOREIGN KEY ([CustomerID]) REFERENCES [dbo].[Customer] ([CustomerID]),
    CONSTRAINT [FK_CreditNote_Invoice] FOREIGN KEY ([InvoiceID]) REFERENCES [dbo].[SalesInvoice] ([InvoiceID])
);

-- Credit Note Item table
CREATE TABLE [dbo].[CreditNoteItem] (
    [CreditNoteItemID] INT IDENTITY(1,1) NOT NULL,
    [CreditNoteID] INT NOT NULL,
    [InvoiceItemID] INT NULL,
    [LineNumber] INT NOT NULL,
    [ProductID] INT NOT NULL,
    [VariantID] INT NULL,
    [ProductCode] NVARCHAR(50) NOT NULL,
    [ProductName] NVARCHAR(255) NOT NULL,
    [Description] NVARCHAR(MAX) NULL,
    [Quantity] DECIMAL(18,3) NOT NULL,
    [UOMID] INT NOT NULL,
    [UnitPrice] DECIMAL(18,4) NOT NULL,
    [TaxRate] DECIMAL(5,2) DEFAULT 20 NOT NULL,
    [TaxAmount] DECIMAL(18,2) DEFAULT 0 NOT NULL,
    [LineTotal] DECIMAL(18,2) NOT NULL,
    [ReturnReason] NVARCHAR(100) NULL,
    [Notes] NVARCHAR(500) NULL,
    [SortOrder] INT DEFAULT 100 NOT NULL,
    CONSTRAINT [PK_CreditNoteItem] PRIMARY KEY CLUSTERED ([CreditNoteItemID] ASC),
    CONSTRAINT [FK_CreditNoteItem_CreditNote] FOREIGN KEY ([CreditNoteID]) REFERENCES [dbo].[CreditNote] ([CreditNoteID]),
    CONSTRAINT [FK_CreditNoteItem_InvoiceItem] FOREIGN KEY ([InvoiceItemID]) REFERENCES [dbo].[SalesInvoiceItem] ([InvoiceItemID]),
    CONSTRAINT [FK_CreditNoteItem_Product] FOREIGN KEY ([ProductID]) REFERENCES [dbo].[Product] ([ProductID]),
    CONSTRAINT [FK_CreditNoteItem_Variant] FOREIGN KEY ([VariantID]) REFERENCES [dbo].[ProductVariant] ([VariantID]),
    CONSTRAINT [FK_CreditNoteItem_UOM] FOREIGN KEY ([UOMID]) REFERENCES [dbo].[UnitOfMeasure] ([UOMID])
);

-- Commission table for sales rep commissions
CREATE TABLE [dbo].[Commission] (
    [CommissionID] INT IDENTITY(1,1) NOT NULL,
    [SalesRepID] INT NOT NULL,
    [PeriodYear] INT NOT NULL,
    [PeriodMonth] INT NOT NULL,
    [OrderID] INT NULL,
    [InvoiceID] INT NULL,
    [CommissionType] NVARCHAR(20) NOT NULL, -- Order, Invoice, Payment
    [CommissionRate] DECIMAL(5,2) NOT NULL,
    [CommissionBase] DECIMAL(18,2) NOT NULL, -- Amount on which commission is calculated
    [CommissionAmount] DECIMAL(18,2) NOT NULL,
    [Status] NVARCHAR(20) DEFAULT 'Pending' NOT NULL, -- Pending, Approved, Paid
    [PaymentDate] DATETIME2 NULL,
    [Notes] NVARCHAR(500) NULL,
    [CreatedDate] DATETIME2 DEFAULT GETDATE() NOT NULL,
    [CreatedByUserID] INT NULL,
    CONSTRAINT [PK_Commission] PRIMARY KEY CLUSTERED ([CommissionID] ASC),
    CONSTRAINT [FK_Commission_SalesRep] FOREIGN KEY ([SalesRepID]) REFERENCES [dbo].[User] ([UserID]),
    CONSTRAINT [FK_Commission_Order] FOREIGN KEY ([OrderID]) REFERENCES [dbo].[SalesOrder] ([OrderID]),
    CONSTRAINT [FK_Commission_Invoice] FOREIGN KEY ([InvoiceID]) REFERENCES [dbo].[SalesInvoice] ([InvoiceID])
);

-- Sales Target table
CREATE TABLE [dbo].[SalesTarget] (
    [TargetID] INT IDENTITY(1,1) NOT NULL,
    [SalesRepID] INT NULL,
    [TerritoryID] INT NULL,
    [TargetYear] INT NOT NULL,
    [TargetMonth] INT NULL,
    [TargetType] NVARCHAR(20) NOT NULL, -- Revenue, Quantity, NewCustomers
    [TargetAmount] DECIMAL(18,2) NOT NULL,
    [AchievedAmount] DECIMAL(18,2) DEFAULT 0 NOT NULL,
    [AchievementPercent] AS (CASE WHEN [TargetAmount] > 0 THEN ([AchievedAmount] / [TargetAmount]) * 100 ELSE 0 END),
    [Notes] NVARCHAR(500) NULL,
    [CreatedDate] DATETIME2 DEFAULT GETDATE() NOT NULL,
    [CreatedByUserID] INT NULL,
    [UpdatedDate] DATETIME2 DEFAULT GETDATE() NOT NULL,
    CONSTRAINT [PK_SalesTarget] PRIMARY KEY CLUSTERED ([TargetID] ASC),
    CONSTRAINT [FK_SalesTarget_SalesRep] FOREIGN KEY ([SalesRepID]) REFERENCES [dbo].[User] ([UserID])
);

-- Create indexes
CREATE INDEX [IX_Quote_QuoteNumber] ON [dbo].[Quote] ([QuoteNumber]);
CREATE INDEX [IX_Quote_CustomerID] ON [dbo].[Quote] ([CustomerID]);
CREATE INDEX [IX_Quote_QuoteStatus] ON [dbo].[Quote] ([QuoteStatus]);
CREATE INDEX [IX_Quote_QuoteDate] ON [dbo].[Quote] ([QuoteDate]);
CREATE INDEX [IX_QuoteItem_QuoteID] ON [dbo].[QuoteItem] ([QuoteID]);
CREATE INDEX [IX_QuoteItem_ProductID] ON [dbo].[QuoteItem] ([ProductID]);

CREATE INDEX [IX_SalesOrder_OrderNumber] ON [dbo].[SalesOrder] ([OrderNumber]);
CREATE INDEX [IX_SalesOrder_CustomerID] ON [dbo].[SalesOrder] ([CustomerID]);
CREATE INDEX [IX_SalesOrder_OrderStatus] ON [dbo].[SalesOrder] ([OrderStatus]);
CREATE INDEX [IX_SalesOrder_OrderDate] ON [dbo].[SalesOrder] ([OrderDate]);
CREATE INDEX [IX_SalesOrderItem_OrderID] ON [dbo].[SalesOrderItem] ([OrderID]);
CREATE INDEX [IX_SalesOrderItem_ProductID] ON [dbo].[SalesOrderItem] ([ProductID]);
CREATE INDEX [IX_SalesOrderItem_ItemStatus] ON [dbo].[SalesOrderItem] ([ItemStatus]);

CREATE INDEX [IX_SalesInvoice_InvoiceNumber] ON [dbo].[SalesInvoice] ([InvoiceNumber]);
CREATE INDEX [IX_SalesInvoice_CustomerID] ON [dbo].[SalesInvoice] ([CustomerID]);
CREATE INDEX [IX_SalesInvoice_InvoiceStatus] ON [dbo].[SalesInvoice] ([InvoiceStatus]);
CREATE INDEX [IX_SalesInvoice_InvoiceDate] ON [dbo].[SalesInvoice] ([InvoiceDate]);
CREATE INDEX [IX_SalesInvoice_DueDate] ON [dbo].[SalesInvoice] ([DueDate]);
CREATE INDEX [IX_SalesInvoiceItem_InvoiceID] ON [dbo].[SalesInvoiceItem] ([InvoiceID]);

CREATE INDEX [IX_CreditNote_CreditNoteNumber] ON [dbo].[CreditNote] ([CreditNoteNumber]);
CREATE INDEX [IX_CreditNote_CustomerID] ON [dbo].[CreditNote] ([CustomerID]);
CREATE INDEX [IX_CreditNote_InvoiceID] ON [dbo].[CreditNote] ([InvoiceID]);
CREATE INDEX [IX_CreditNoteItem_CreditNoteID] ON [dbo].[CreditNoteItem] ([CreditNoteID]);

CREATE INDEX [IX_Commission_SalesRepID_Period] ON [dbo].[Commission] ([SalesRepID], [PeriodYear], [PeriodMonth]);
CREATE INDEX [IX_Commission_Status] ON [dbo].[Commission] ([Status]);
CREATE INDEX [IX_SalesTarget_SalesRepID_Period] ON [dbo].[SalesTarget] ([SalesRepID], [TargetYear], [TargetMonth]);