-- Purchase Management Tables Creation Script
-- Purpose: Comprehensive purchasing process from requisition to payment
-- Supports requisitions, purchase orders, goods receipts, and invoices

-- Purchase Requisition table
CREATE TABLE [dbo].[PurchaseRequisition] (
    [RequisitionID] INT IDENTITY(1,1) NOT NULL,
    [RequisitionNumber] NVARCHAR(50) UNIQUE NOT NULL,
    [RequisitionDate] DATETIME2 DEFAULT GETDATE() NOT NULL,
    [RequestedByUserID] INT NOT NULL,
    [Department] NVARCHAR(100) NULL,
    [Priority] NVARCHAR(20) DEFAULT 'Normal' NOT NULL, -- Low, Normal, High, Urgent
    [RequiredDate] DATETIME2 NOT NULL,
    [RequisitionStatus] NVARCHAR(20) DEFAULT 'Draft' NOT NULL, -- Draft, Submitted, Approved, Rejected, Converted, Cancelled
    [Purpose] NVARCHAR(500) NOT NULL,
    [TotalAmount] DECIMAL(18,2) DEFAULT 0 NOT NULL,
    [ApprovedByUserID] INT NULL,
    [ApprovedDate] DATETIME2 NULL,
    [RejectionReason] NVARCHAR(500) NULL,
    [Notes] NVARCHAR(MAX) NULL,
    [CreatedDate] DATETIME2 DEFAULT GETDATE() NOT NULL,
    [CreatedByUserID] INT NULL,
    [UpdatedDate] DATETIME2 DEFAULT GETDATE() NOT NULL,
    [UpdatedByUserID] INT NULL,
    CONSTRAINT [PK_PurchaseRequisition] PRIMARY KEY CLUSTERED ([RequisitionID] ASC),
    CONSTRAINT [FK_PurchaseRequisition_RequestedBy] FOREIGN KEY ([RequestedByUserID]) REFERENCES [dbo].[User] ([UserID]),
    CONSTRAINT [FK_PurchaseRequisition_ApprovedBy] FOREIGN KEY ([ApprovedByUserID]) REFERENCES [dbo].[User] ([UserID])
);

-- Purchase Requisition Item table
CREATE TABLE [dbo].[PurchaseRequisitionItem] (
    [RequisitionItemID] INT IDENTITY(1,1) NOT NULL,
    [RequisitionID] INT NOT NULL,
    [LineNumber] INT NOT NULL,
    [ProductID] INT NULL,
    [ItemDescription] NVARCHAR(500) NOT NULL,
    [Quantity] DECIMAL(18,3) NOT NULL,
    [UOMID] INT NULL,
    [EstimatedUnitPrice] DECIMAL(18,4) NULL,
    [EstimatedTotal] DECIMAL(18,2) NULL,
    [SuggestedSupplierID] INT NULL,
    [Justification] NVARCHAR(500) NULL,
    [Notes] NVARCHAR(500) NULL,
    CONSTRAINT [PK_PurchaseRequisitionItem] PRIMARY KEY CLUSTERED ([RequisitionItemID] ASC),
    CONSTRAINT [FK_PurchaseRequisitionItem_Requisition] FOREIGN KEY ([RequisitionID]) REFERENCES [dbo].[PurchaseRequisition] ([RequisitionID]),
    CONSTRAINT [FK_PurchaseRequisitionItem_Product] FOREIGN KEY ([ProductID]) REFERENCES [dbo].[Product] ([ProductID]),
    CONSTRAINT [FK_PurchaseRequisitionItem_UOM] FOREIGN KEY ([UOMID]) REFERENCES [dbo].[UnitOfMeasure] ([UOMID]),
    CONSTRAINT [FK_PurchaseRequisitionItem_Supplier] FOREIGN KEY ([SuggestedSupplierID]) REFERENCES [dbo].[Supplier] ([SupplierID])
);

-- Purchase Order table
CREATE TABLE [dbo].[PurchaseOrder] (
    [PurchaseOrderID] INT IDENTITY(1,1) NOT NULL,
    [PurchaseOrderNumber] NVARCHAR(50) UNIQUE NOT NULL,
    [OrderDate] DATETIME2 DEFAULT GETDATE() NOT NULL,
    [SupplierID] INT NOT NULL,
    [SupplierReference] NVARCHAR(100) NULL,
    [RequisitionID] INT NULL,
    [BuyerID] INT NULL,
    [OrderStatus] NVARCHAR(20) DEFAULT 'Draft' NOT NULL, -- Draft, Sent, Confirmed, PartiallyReceived, Received, Closed, Cancelled
    [OrderType] NVARCHAR(20) DEFAULT 'Standard' NOT NULL, -- Standard, Blanket, Contract, Planned
    
    -- Dates
    [RequiredDate] DATETIME2 NULL,
    [PromisedDate] DATETIME2 NULL,
    [ExpectedDate] DATETIME2 NULL,
    
    -- Financial Summary
    [SubTotal] DECIMAL(18,2) DEFAULT 0 NOT NULL,
    [DiscountAmount] DECIMAL(18,2) DEFAULT 0 NULL,
    [TaxAmount] DECIMAL(18,2) DEFAULT 0 NOT NULL,
    [FreightAmount] DECIMAL(18,2) DEFAULT 0 NULL,
    [TotalAmount] DECIMAL(18,2) DEFAULT 0 NOT NULL,
    [Currency] NVARCHAR(3) DEFAULT 'GBP' NOT NULL,
    [ExchangeRate] DECIMAL(18,6) DEFAULT 1 NOT NULL,
    
    -- Delivery Information
    [DeliveryWarehouseID] INT NOT NULL,
    [DeliveryAddressID] INT NULL,
    [DeliveryInstructions] NVARCHAR(MAX) NULL,
    [ShippingMethod] NVARCHAR(100) NULL,
    [FreightTerms] NVARCHAR(50) NULL, -- FOB, CIF, EXW, etc.
    
    -- Payment Information
    [PaymentTerms] NVARCHAR(100) NULL,
    [PaymentMethod] NVARCHAR(50) NULL,
    
    -- Approval
    [ApprovalStatus] NVARCHAR(20) DEFAULT 'Pending' NOT NULL, -- Pending, Approved, Rejected
    [ApprovedByUserID] INT NULL,
    [ApprovedDate] DATETIME2 NULL,
    [ApprovalNotes] NVARCHAR(500) NULL,
    
    -- Additional Information
    [Notes] NVARCHAR(MAX) NULL,
    [InternalNotes] NVARCHAR(MAX) NULL,
    [TermsAndConditions] NVARCHAR(MAX) NULL,
    
    -- System fields
    [CreatedDate] DATETIME2 DEFAULT GETDATE() NOT NULL,
    [CreatedByUserID] INT NULL,
    [UpdatedDate] DATETIME2 DEFAULT GETDATE() NOT NULL,
    [UpdatedByUserID] INT NULL,
    [IsDeleted] BIT DEFAULT 0 NOT NULL,
    [DeletedDate] DATETIME2 NULL,
    [DeletedByUserID] INT NULL,
    
    CONSTRAINT [PK_PurchaseOrder] PRIMARY KEY CLUSTERED ([PurchaseOrderID] ASC),
    CONSTRAINT [FK_PurchaseOrder_Supplier] FOREIGN KEY ([SupplierID]) REFERENCES [dbo].[Supplier] ([SupplierID]),
    CONSTRAINT [FK_PurchaseOrder_Requisition] FOREIGN KEY ([RequisitionID]) REFERENCES [dbo].[PurchaseRequisition] ([RequisitionID]),
    CONSTRAINT [FK_PurchaseOrder_Buyer] FOREIGN KEY ([BuyerID]) REFERENCES [dbo].[User] ([UserID]),
    CONSTRAINT [FK_PurchaseOrder_Warehouse] FOREIGN KEY ([DeliveryWarehouseID]) REFERENCES [dbo].[Warehouse] ([WarehouseID]),
    CONSTRAINT [FK_PurchaseOrder_ApprovedBy] FOREIGN KEY ([ApprovedByUserID]) REFERENCES [dbo].[User] ([UserID])
);

-- Purchase Order Item table
CREATE TABLE [dbo].[PurchaseOrderItem] (
    [PurchaseOrderItemID] INT IDENTITY(1,1) NOT NULL,
    [PurchaseOrderID] INT NOT NULL,
    [LineNumber] INT NOT NULL,
    [ProductID] INT NOT NULL,
    [SupplierProductCode] NVARCHAR(100) NULL,
    [ProductName] NVARCHAR(255) NOT NULL,
    [Description] NVARCHAR(MAX) NULL,
    [OrderedQuantity] DECIMAL(18,3) NOT NULL,
    [ReceivedQuantity] DECIMAL(18,3) DEFAULT 0 NOT NULL,
    [InvoicedQuantity] DECIMAL(18,3) DEFAULT 0 NOT NULL,
    [CancelledQuantity] DECIMAL(18,3) DEFAULT 0 NOT NULL,
    [UOMID] INT NOT NULL,
    [UnitPrice] DECIMAL(18,4) NOT NULL,
    [DiscountPercent] DECIMAL(5,2) DEFAULT 0 NULL,
    [DiscountAmount] DECIMAL(18,2) DEFAULT 0 NULL,
    [TaxRate] DECIMAL(5,2) DEFAULT 20 NOT NULL,
    [TaxAmount] DECIMAL(18,2) DEFAULT 0 NOT NULL,
    [LineTotal] DECIMAL(18,2) NOT NULL,
    [RequiredDate] DATETIME2 NULL,
    [PromisedDate] DATETIME2 NULL,
    [LastReceiptDate] DATETIME2 NULL,
    [ItemStatus] NVARCHAR(20) DEFAULT 'Open' NOT NULL, -- Open, PartiallyReceived, Received, Closed, Cancelled
    [Notes] NVARCHAR(500) NULL,
    [SortOrder] INT DEFAULT 100 NOT NULL,
    CONSTRAINT [PK_PurchaseOrderItem] PRIMARY KEY CLUSTERED ([PurchaseOrderItemID] ASC),
    CONSTRAINT [FK_PurchaseOrderItem_Order] FOREIGN KEY ([PurchaseOrderID]) REFERENCES [dbo].[PurchaseOrder] ([PurchaseOrderID]),
    CONSTRAINT [FK_PurchaseOrderItem_Product] FOREIGN KEY ([ProductID]) REFERENCES [dbo].[Product] ([ProductID]),
    CONSTRAINT [FK_PurchaseOrderItem_UOM] FOREIGN KEY ([UOMID]) REFERENCES [dbo].[UnitOfMeasure] ([UOMID])
);

-- Goods Receipt table
CREATE TABLE [dbo].[GoodsReceipt] (
    [ReceiptID] INT IDENTITY(1,1) NOT NULL,
    [ReceiptNumber] NVARCHAR(50) UNIQUE NOT NULL,
    [ReceiptDate] DATETIME2 DEFAULT GETDATE() NOT NULL,
    [SupplierID] INT NOT NULL,
    [PurchaseOrderID] INT NULL,
    [ReceiptType] NVARCHAR(20) DEFAULT 'Standard' NOT NULL, -- Standard, Return, Direct
    [ReceiptStatus] NVARCHAR(20) DEFAULT 'Draft' NOT NULL, -- Draft, Posted, Cancelled
    [SupplierDeliveryNote] NVARCHAR(100) NULL,
    [WarehouseID] INT NOT NULL,
    [ReceivedByUserID] INT NOT NULL,
    [QualityCheckStatus] NVARCHAR(20) NULL, -- Pending, Passed, Failed, Partial
    [QualityCheckDate] DATETIME2 NULL,
    [QualityCheckByUserID] INT NULL,
    [Notes] NVARCHAR(MAX) NULL,
    [PostedDate] DATETIME2 NULL,
    [PostedByUserID] INT NULL,
    [CreatedDate] DATETIME2 DEFAULT GETDATE() NOT NULL,
    [CreatedByUserID] INT NULL,
    [UpdatedDate] DATETIME2 DEFAULT GETDATE() NOT NULL,
    [UpdatedByUserID] INT NULL,
    CONSTRAINT [PK_GoodsReceipt] PRIMARY KEY CLUSTERED ([ReceiptID] ASC),
    CONSTRAINT [FK_GoodsReceipt_Supplier] FOREIGN KEY ([SupplierID]) REFERENCES [dbo].[Supplier] ([SupplierID]),
    CONSTRAINT [FK_GoodsReceipt_PurchaseOrder] FOREIGN KEY ([PurchaseOrderID]) REFERENCES [dbo].[PurchaseOrder] ([PurchaseOrderID]),
    CONSTRAINT [FK_GoodsReceipt_Warehouse] FOREIGN KEY ([WarehouseID]) REFERENCES [dbo].[Warehouse] ([WarehouseID]),
    CONSTRAINT [FK_GoodsReceipt_ReceivedBy] FOREIGN KEY ([ReceivedByUserID]) REFERENCES [dbo].[User] ([UserID]),
    CONSTRAINT [FK_GoodsReceipt_QualityCheckBy] FOREIGN KEY ([QualityCheckByUserID]) REFERENCES [dbo].[User] ([UserID])
);

-- Goods Receipt Item table
CREATE TABLE [dbo].[GoodsReceiptItem] (
    [ReceiptItemID] INT IDENTITY(1,1) NOT NULL,
    [ReceiptID] INT NOT NULL,
    [PurchaseOrderItemID] INT NULL,
    [LineNumber] INT NOT NULL,
    [ProductID] INT NOT NULL,
    [ProductName] NVARCHAR(255) NOT NULL,
    [ReceivedQuantity] DECIMAL(18,3) NOT NULL,
    [AcceptedQuantity] DECIMAL(18,3) NOT NULL,
    [RejectedQuantity] DECIMAL(18,3) DEFAULT 0 NOT NULL,
    [UOMID] INT NOT NULL,
    [UnitCost] DECIMAL(18,4) NOT NULL,
    [LocationID] INT NULL,
    [BatchNumber] NVARCHAR(50) NULL,
    [SerialNumber] NVARCHAR(100) NULL,
    [ExpiryDate] DATE NULL,
    [QualityStatus] NVARCHAR(20) NULL, -- Accepted, Rejected, Quarantine
    [RejectionReason] NVARCHAR(500) NULL,
    [Notes] NVARCHAR(500) NULL,
    [SortOrder] INT DEFAULT 100 NOT NULL,
    CONSTRAINT [PK_GoodsReceiptItem] PRIMARY KEY CLUSTERED ([ReceiptItemID] ASC),
    CONSTRAINT [FK_GoodsReceiptItem_Receipt] FOREIGN KEY ([ReceiptID]) REFERENCES [dbo].[GoodsReceipt] ([ReceiptID]),
    CONSTRAINT [FK_GoodsReceiptItem_POItem] FOREIGN KEY ([PurchaseOrderItemID]) REFERENCES [dbo].[PurchaseOrderItem] ([PurchaseOrderItemID]),
    CONSTRAINT [FK_GoodsReceiptItem_Product] FOREIGN KEY ([ProductID]) REFERENCES [dbo].[Product] ([ProductID]),
    CONSTRAINT [FK_GoodsReceiptItem_UOM] FOREIGN KEY ([UOMID]) REFERENCES [dbo].[UnitOfMeasure] ([UOMID]),
    CONSTRAINT [FK_GoodsReceiptItem_Location] FOREIGN KEY ([LocationID]) REFERENCES [dbo].[WarehouseLocation] ([LocationID])
);

-- Purchase Invoice table
CREATE TABLE [dbo].[PurchaseInvoice] (
    [InvoiceID] INT IDENTITY(1,1) NOT NULL,
    [InvoiceNumber] NVARCHAR(50) UNIQUE NOT NULL,
    [SupplierInvoiceNumber] NVARCHAR(100) NOT NULL,
    [InvoiceDate] DATETIME2 NOT NULL,
    [SupplierID] INT NOT NULL,
    [PurchaseOrderID] INT NULL,
    [InvoiceType] NVARCHAR(20) DEFAULT 'Standard' NOT NULL, -- Standard, Credit, Debit
    [InvoiceStatus] NVARCHAR(20) DEFAULT 'Draft' NOT NULL, -- Draft, Posted, Paid, PartiallyPaid, Cancelled
    
    -- Financial Summary
    [SubTotal] DECIMAL(18,2) DEFAULT 0 NOT NULL,
    [DiscountAmount] DECIMAL(18,2) DEFAULT 0 NULL,
    [TaxAmount] DECIMAL(18,2) DEFAULT 0 NOT NULL,
    [FreightAmount] DECIMAL(18,2) DEFAULT 0 NULL,
    [TotalAmount] DECIMAL(18,2) DEFAULT 0 NOT NULL,
    [PaidAmount] DECIMAL(18,2) DEFAULT 0 NOT NULL,
    [BalanceAmount] DECIMAL(18,2) DEFAULT 0 NOT NULL,
    [Currency] NVARCHAR(3) DEFAULT 'GBP' NOT NULL,
    [ExchangeRate] DECIMAL(18,6) DEFAULT 1 NOT NULL,
    
    -- Payment Information
    [PaymentTerms] NVARCHAR(100) NULL,
    [DueDate] DATETIME2 NOT NULL,
    [PaymentStatus] NVARCHAR(20) DEFAULT 'Unpaid' NOT NULL, -- Unpaid, Partial, Paid
    [LastPaymentDate] DATETIME2 NULL,
    
    -- Three-way matching
    [POMatchStatus] NVARCHAR(20) NULL, -- Matched, Unmatched, Partial
    [GRMatchStatus] NVARCHAR(20) NULL, -- Matched, Unmatched, Partial
    [MatchingNotes] NVARCHAR(500) NULL,
    
    -- Approval
    [ApprovalStatus] NVARCHAR(20) DEFAULT 'Pending' NOT NULL, -- Pending, Approved, Rejected, OnHold
    [ApprovedByUserID] INT NULL,
    [ApprovedDate] DATETIME2 NULL,
    
    -- Additional Information
    [Notes] NVARCHAR(MAX) NULL,
    [InternalNotes] NVARCHAR(MAX) NULL,
    
    -- System fields
    [PostedDate] DATETIME2 NULL,
    [PostedByUserID] INT NULL,
    [CreatedDate] DATETIME2 DEFAULT GETDATE() NOT NULL,
    [CreatedByUserID] INT NULL,
    [UpdatedDate] DATETIME2 DEFAULT GETDATE() NOT NULL,
    [UpdatedByUserID] INT NULL,
    [IsDeleted] BIT DEFAULT 0 NOT NULL,
    [DeletedDate] DATETIME2 NULL,
    [DeletedByUserID] INT NULL,
    
    CONSTRAINT [PK_PurchaseInvoice] PRIMARY KEY CLUSTERED ([InvoiceID] ASC),
    CONSTRAINT [FK_PurchaseInvoice_Supplier] FOREIGN KEY ([SupplierID]) REFERENCES [dbo].[Supplier] ([SupplierID]),
    CONSTRAINT [FK_PurchaseInvoice_PurchaseOrder] FOREIGN KEY ([PurchaseOrderID]) REFERENCES [dbo].[PurchaseOrder] ([PurchaseOrderID]),
    CONSTRAINT [FK_PurchaseInvoice_ApprovedBy] FOREIGN KEY ([ApprovedByUserID]) REFERENCES [dbo].[User] ([UserID])
);

-- Purchase Invoice Item table
CREATE TABLE [dbo].[PurchaseInvoiceItem] (
    [InvoiceItemID] INT IDENTITY(1,1) NOT NULL,
    [InvoiceID] INT NOT NULL,
    [PurchaseOrderItemID] INT NULL,
    [GoodsReceiptItemID] INT NULL,
    [LineNumber] INT NOT NULL,
    [ProductID] INT NOT NULL,
    [ProductName] NVARCHAR(255) NOT NULL,
    [Description] NVARCHAR(MAX) NULL,
    [Quantity] DECIMAL(18,3) NOT NULL,
    [UOMID] INT NOT NULL,
    [UnitPrice] DECIMAL(18,4) NOT NULL,
    [DiscountPercent] DECIMAL(5,2) DEFAULT 0 NULL,
    [DiscountAmount] DECIMAL(18,2) DEFAULT 0 NULL,
    [TaxRate] DECIMAL(5,2) DEFAULT 20 NOT NULL,
    [TaxAmount] DECIMAL(18,2) DEFAULT 0 NOT NULL,
    [LineTotal] DECIMAL(18,2) NOT NULL,
    [AccountCode] NVARCHAR(20) NULL,
    [Notes] NVARCHAR(500) NULL,
    [SortOrder] INT DEFAULT 100 NOT NULL,
    CONSTRAINT [PK_PurchaseInvoiceItem] PRIMARY KEY CLUSTERED ([InvoiceItemID] ASC),
    CONSTRAINT [FK_PurchaseInvoiceItem_Invoice] FOREIGN KEY ([InvoiceID]) REFERENCES [dbo].[PurchaseInvoice] ([InvoiceID]),
    CONSTRAINT [FK_PurchaseInvoiceItem_POItem] FOREIGN KEY ([PurchaseOrderItemID]) REFERENCES [dbo].[PurchaseOrderItem] ([PurchaseOrderItemID]),
    CONSTRAINT [FK_PurchaseInvoiceItem_GRItem] FOREIGN KEY ([GoodsReceiptItemID]) REFERENCES [dbo].[GoodsReceiptItem] ([ReceiptItemID]),
    CONSTRAINT [FK_PurchaseInvoiceItem_Product] FOREIGN KEY ([ProductID]) REFERENCES [dbo].[Product] ([ProductID]),
    CONSTRAINT [FK_PurchaseInvoiceItem_UOM] FOREIGN KEY ([UOMID]) REFERENCES [dbo].[UnitOfMeasure] ([UOMID])
);

-- Purchase Return table
CREATE TABLE [dbo].[PurchaseReturn] (
    [ReturnID] INT IDENTITY(1,1) NOT NULL,
    [ReturnNumber] NVARCHAR(50) UNIQUE NOT NULL,
    [ReturnDate] DATETIME2 DEFAULT GETDATE() NOT NULL,
    [SupplierID] INT NOT NULL,
    [GoodsReceiptID] INT NULL,
    [ReturnStatus] NVARCHAR(20) DEFAULT 'Draft' NOT NULL, -- Draft, Approved, Shipped, Completed, Cancelled
    [ReturnReason] NVARCHAR(100) NOT NULL, -- Defective, Wrong Item, Excess, Damaged, Other
    [RMANumber] NVARCHAR(50) NULL, -- Return Merchandise Authorization
    [WarehouseID] INT NOT NULL,
    [ShippingMethod] NVARCHAR(100) NULL,
    [TrackingNumber] NVARCHAR(100) NULL,
    [TotalAmount] DECIMAL(18,2) DEFAULT 0 NOT NULL,
    [Notes] NVARCHAR(MAX) NULL,
    [CreatedDate] DATETIME2 DEFAULT GETDATE() NOT NULL,
    [CreatedByUserID] INT NULL,
    [UpdatedDate] DATETIME2 DEFAULT GETDATE() NOT NULL,
    [UpdatedByUserID] INT NULL,
    CONSTRAINT [PK_PurchaseReturn] PRIMARY KEY CLUSTERED ([ReturnID] ASC),
    CONSTRAINT [FK_PurchaseReturn_Supplier] FOREIGN KEY ([SupplierID]) REFERENCES [dbo].[Supplier] ([SupplierID]),
    CONSTRAINT [FK_PurchaseReturn_GoodsReceipt] FOREIGN KEY ([GoodsReceiptID]) REFERENCES [dbo].[GoodsReceipt] ([ReceiptID]),
    CONSTRAINT [FK_PurchaseReturn_Warehouse] FOREIGN KEY ([WarehouseID]) REFERENCES [dbo].[Warehouse] ([WarehouseID])
);

-- Purchase Return Item table
CREATE TABLE [dbo].[PurchaseReturnItem] (
    [ReturnItemID] INT IDENTITY(1,1) NOT NULL,
    [ReturnID] INT NOT NULL,
    [GoodsReceiptItemID] INT NULL,
    [LineNumber] INT NOT NULL,
    [ProductID] INT NOT NULL,
    [ProductName] NVARCHAR(255) NOT NULL,
    [ReturnQuantity] DECIMAL(18,3) NOT NULL,
    [UOMID] INT NOT NULL,
    [UnitCost] DECIMAL(18,4) NOT NULL,
    [BatchNumber] NVARCHAR(50) NULL,
    [SerialNumber] NVARCHAR(100) NULL,
    [ReturnReason] NVARCHAR(100) NULL,
    [LineTotal] DECIMAL(18,2) NOT NULL,
    [Notes] NVARCHAR(500) NULL,
    [SortOrder] INT DEFAULT 100 NOT NULL,
    CONSTRAINT [PK_PurchaseReturnItem] PRIMARY KEY CLUSTERED ([ReturnItemID] ASC),
    CONSTRAINT [FK_PurchaseReturnItem_Return] FOREIGN KEY ([ReturnID]) REFERENCES [dbo].[PurchaseReturn] ([ReturnID]),
    CONSTRAINT [FK_PurchaseReturnItem_GRItem] FOREIGN KEY ([GoodsReceiptItemID]) REFERENCES [dbo].[GoodsReceiptItem] ([ReceiptItemID]),
    CONSTRAINT [FK_PurchaseReturnItem_Product] FOREIGN KEY ([ProductID]) REFERENCES [dbo].[Product] ([ProductID]),
    CONSTRAINT [FK_PurchaseReturnItem_UOM] FOREIGN KEY ([UOMID]) REFERENCES [dbo].[UnitOfMeasure] ([UOMID])
);

-- Create indexes
CREATE INDEX [IX_PurchaseRequisition_RequisitionNumber] ON [dbo].[PurchaseRequisition] ([RequisitionNumber]);
CREATE INDEX [IX_PurchaseRequisition_RequestedByUserID] ON [dbo].[PurchaseRequisition] ([RequestedByUserID]);
CREATE INDEX [IX_PurchaseRequisition_RequisitionStatus] ON [dbo].[PurchaseRequisition] ([RequisitionStatus]);

CREATE INDEX [IX_PurchaseOrder_PurchaseOrderNumber] ON [dbo].[PurchaseOrder] ([PurchaseOrderNumber]);
CREATE INDEX [IX_PurchaseOrder_SupplierID] ON [dbo].[PurchaseOrder] ([SupplierID]);
CREATE INDEX [IX_PurchaseOrder_OrderStatus] ON [dbo].[PurchaseOrder] ([OrderStatus]);
CREATE INDEX [IX_PurchaseOrder_OrderDate] ON [dbo].[PurchaseOrder] ([OrderDate]);
CREATE INDEX [IX_PurchaseOrderItem_PurchaseOrderID] ON [dbo].[PurchaseOrderItem] ([PurchaseOrderID]);
CREATE INDEX [IX_PurchaseOrderItem_ProductID] ON [dbo].[PurchaseOrderItem] ([ProductID]);

CREATE INDEX [IX_GoodsReceipt_ReceiptNumber] ON [dbo].[GoodsReceipt] ([ReceiptNumber]);
CREATE INDEX [IX_GoodsReceipt_SupplierID] ON [dbo].[GoodsReceipt] ([SupplierID]);
CREATE INDEX [IX_GoodsReceipt_PurchaseOrderID] ON [dbo].[GoodsReceipt] ([PurchaseOrderID]);
CREATE INDEX [IX_GoodsReceiptItem_ReceiptID] ON [dbo].[GoodsReceiptItem] ([ReceiptID]);
CREATE INDEX [IX_GoodsReceiptItem_ProductID] ON [dbo].[GoodsReceiptItem] ([ProductID]);

CREATE INDEX [IX_PurchaseInvoice_InvoiceNumber] ON [dbo].[PurchaseInvoice] ([InvoiceNumber]);
CREATE INDEX [IX_PurchaseInvoice_SupplierInvoiceNumber] ON [dbo].[PurchaseInvoice] ([SupplierInvoiceNumber]);
CREATE INDEX [IX_PurchaseInvoice_SupplierID] ON [dbo].[PurchaseInvoice] ([SupplierID]);
CREATE INDEX [IX_PurchaseInvoice_InvoiceStatus] ON [dbo].[PurchaseInvoice] ([InvoiceStatus]);
CREATE INDEX [IX_PurchaseInvoice_DueDate] ON [dbo].[PurchaseInvoice] ([DueDate]);
CREATE INDEX [IX_PurchaseInvoiceItem_InvoiceID] ON [dbo].[PurchaseInvoiceItem] ([InvoiceID]);

CREATE INDEX [IX_PurchaseReturn_ReturnNumber] ON [dbo].[PurchaseReturn] ([ReturnNumber]);
CREATE INDEX [IX_PurchaseReturn_SupplierID] ON [dbo].[PurchaseReturn] ([SupplierID]);
CREATE INDEX [IX_PurchaseReturnItem_ReturnID] ON [dbo].[PurchaseReturnItem] ([ReturnID]);