-- =============================================
-- nCarry Sample Data Script - FIXED VERSION
-- =============================================
-- This script inserts sample data with correct column names
-- =============================================

USE nCarryDB;
GO

PRINT 'Starting sample data insertion...';
GO

-- =============================================
-- 1. COMPANY DATA - FIXED
-- =============================================
PRINT 'Inserting Companies...';

IF NOT EXISTS (SELECT 1 FROM Company WHERE CompanyCode = 'NC001')
BEGIN
    INSERT INTO Company (CompanyCode, CompanyName, LegalName, TaxNumber, RegistrationNumber, 
        Address1, City, PostCode, Country, Website, Email, Phone, IsActive, CreatedDate, UpdatedDate)
    VALUES 
    ('NC001', 'nCarry Logistics Ltd', 'nCarry Logistics Limited', 'GB123456789', 'REG123456', 
        '123 Business Centre', 'London', 'E1 6AN', 'United Kingdom', 'www.ncarry.com', '<EMAIL>', '+44 20 1234 5678', 1, GETDATE(), GETDATE()),
    ('NC002', 'Global Supply Co', 'Global Supply Company Ltd', 'GB987654321', 'REG654321', 
        '456 Trade Park', 'Manchester', 'M1 2AB', 'United Kingdom', 'www.globalsupply.com', '<EMAIL>', '+44 20 9876 5432', 1, GETDATE(), GETDATE()),
    ('NC003', 'Tech Distribution Hub', 'Technology Distribution Hub Ltd', 'GB456789123', 'REG789123', 
        '789 Tech Zone', 'Birmingham', 'B1 3CD', 'United Kingdom', 'www.techdist.com', '<EMAIL>', '+44 20 4567 8912', 1, GETDATE(), GETDATE()),
    ('NC004', 'Express Cargo Services', 'Express Cargo Services PLC', 'GB789123456', 'REG321654', 
        '321 Logistics Way', 'Glasgow', 'G1 4EF', 'United Kingdom', 'www.expresscargo.com', '<EMAIL>', '+44 20 7891 2345', 1, GETDATE(), GETDATE()),
    ('NC005', 'Smart Inventory Solutions', 'Smart Inventory Solutions Ltd', 'GB321654987', 'REG987321', 
        '654 Storage Road', 'Bristol', 'BS1 5GH', 'United Kingdom', 'www.smartinv.com', '<EMAIL>', '+44 20 3216 5498', 1, GETDATE(), GETDATE());
    
    PRINT '  - Companies inserted successfully';
END
GO

-- =============================================
-- 2. BRANCH DATA
-- =============================================
PRINT 'Inserting Branches...';

DECLARE @CompanyID INT;
SELECT @CompanyID = CompanyID FROM Company WHERE CompanyCode = 'NC001';

IF @CompanyID IS NOT NULL AND NOT EXISTS (SELECT 1 FROM Branch WHERE BranchCode = 'BR001')
BEGIN
    INSERT INTO Branch (BranchCode, BranchName, CompanyID, BranchType, Address1, City, PostCode, Country, Phone, Email, IsDefault, IsActive, CreatedDate, UpdatedDate)
    VALUES 
    ('BR001', 'London Main Branch', @CompanyID, 'Warehouse', '123 Logistics Way', 'London', 'E1 6AN', 'United Kingdom', '+44 20 1111 1111', '<EMAIL>', 1, 1, GETDATE(), GETDATE()),
    ('BR002', 'Manchester Branch', @CompanyID, 'Distribution', '456 Supply Street', 'Manchester', 'M1 2AB', 'United Kingdom', '+44 ************', '<EMAIL>', 0, 1, GETDATE(), GETDATE()),
    ('BR003', 'Birmingham Branch', @CompanyID, 'Warehouse', '789 Storage Road', 'Birmingham', 'B1 3CD', 'United Kingdom', '+44 ************', '<EMAIL>', 0, 1, GETDATE(), GETDATE()),
    ('BR004', 'Glasgow Branch', @CompanyID, 'Distribution', '321 Delivery Avenue', 'Glasgow', 'G1 4EF', 'United Kingdom', '+44 ************', '<EMAIL>', 0, 1, GETDATE(), GETDATE()),
    ('BR005', 'Bristol Branch', @CompanyID, 'Warehouse', '654 Cargo Lane', 'Bristol', 'BS1 5GH', 'United Kingdom', '+44 ************', '<EMAIL>', 0, 1, GETDATE(), GETDATE());
    
    PRINT '  - Branches inserted successfully';
END
GO

-- =============================================
-- 3. CURRENCY DATA
-- =============================================
PRINT 'Inserting Currencies...';

IF NOT EXISTS (SELECT 1 FROM Currency WHERE CurrencyCode = 'GBP')
BEGIN
    INSERT INTO Currency (CurrencyCode, CurrencyName, Symbol, IsBaseCurrency, IsActive, CreatedDate, UpdatedDate)
    VALUES 
    ('GBP', 'British Pound Sterling', '£', 1, 1, GETDATE(), GETDATE()),
    ('USD', 'US Dollar', '$', 0, 1, GETDATE(), GETDATE()),
    ('EUR', 'Euro', '€', 0, 1, GETDATE(), GETDATE()),
    ('JPY', 'Japanese Yen', '¥', 0, 1, GETDATE(), GETDATE()),
    ('AUD', 'Australian Dollar', 'A$', 0, 1, GETDATE(), GETDATE());
    
    PRINT '  - Currencies inserted successfully';
END
GO

-- =============================================
-- 4. TAX CODE DATA - FIXED
-- =============================================
PRINT 'Inserting Tax Codes...';

IF NOT EXISTS (SELECT 1 FROM TaxCode WHERE TaxCode = 'VAT20')
BEGIN
    INSERT INTO TaxCode (TaxCode, TaxDescription, TaxRate, TaxType, IsActive, CreatedDate, UpdatedDate)
    VALUES 
    ('VAT20', 'Standard VAT 20%', 20.00, 'Output', 1, GETDATE(), GETDATE()),
    ('VAT5', 'Reduced VAT 5%', 5.00, 'Output', 1, GETDATE(), GETDATE()),
    ('VAT0', 'Zero Rated VAT', 0.00, 'Output', 1, GETDATE(), GETDATE()),
    ('EXEMPT', 'VAT Exempt', 0.00, 'Exempt', 1, GETDATE(), GETDATE()),
    ('IMPORT', 'Import Duty', 12.00, 'Import', 1, GETDATE(), GETDATE());
    
    PRINT '  - Tax Codes inserted successfully';
END
GO

-- =============================================
-- 5. PAYMENT METHOD DATA
-- =============================================
PRINT 'Inserting Payment Methods...';

IF NOT EXISTS (SELECT 1 FROM PaymentMethod WHERE MethodCode = 'CASH')
BEGIN
    INSERT INTO PaymentMethod (MethodCode, MethodName, MethodType, IsActive, RequiresBankDetails, ProcessingDays, CreatedDate, UpdatedDate)
    VALUES 
    ('CASH', 'Cash Payment', 'Cash', 1, 0, 0, GETDATE(), GETDATE()),
    ('BACS', 'Bank Transfer (BACS)', 'BankTransfer', 1, 1, 3, GETDATE(), GETDATE()),
    ('CARD', 'Credit/Debit Card', 'Card', 1, 0, 1, GETDATE(), GETDATE()),
    ('CHQ', 'Cheque', 'Cheque', 1, 1, 5, GETDATE(), GETDATE()),
    ('PP', 'PayPal', 'Online', 1, 0, 1, GETDATE(), GETDATE());
    
    PRINT '  - Payment Methods inserted successfully';
END
GO

-- =============================================
-- 6. BANK DATA - FIXED
-- =============================================
PRINT 'Inserting Banks...';

IF NOT EXISTS (SELECT 1 FROM Bank WHERE BankCode = 'HSBC')
BEGIN
    INSERT INTO Bank (BankCode, BankName, SortCode, IsActive, CreatedDate, UpdatedDate)
    VALUES 
    ('HSBC', 'HSBC UK Bank', '40-00-00', 1, GETDATE(), GETDATE()),
    ('BARC', 'Barclays Bank', '20-00-00', 1, GETDATE(), GETDATE()),
    ('LLOY', 'Lloyds Bank', '30-00-00', 1, GETDATE(), GETDATE()),
    ('NATW', 'NatWest Bank', '60-00-00', 1, GETDATE(), GETDATE()),
    ('SANT', 'Santander UK', '09-01-00', 1, GETDATE(), GETDATE());
    
    PRINT '  - Banks inserted successfully';
END
GO

-- =============================================
-- 7. UNIT OF MEASURE DATA
-- =============================================
PRINT 'Inserting Units of Measure...';

-- Delete existing UOM data to avoid duplicates
DELETE FROM UnitOfMeasure WHERE UOMCode IN ('PC', 'BOX', 'PALLET');

INSERT INTO UnitOfMeasure (UOMCode, UOMName, UOMType, ConversionFactor, IsActive, CreatedDate, UpdatedDate)
VALUES 
('PC', 'Piece', 'Quantity', 1.00, 1, GETDATE(), GETDATE()),
('BOX', 'Box', 'Quantity', 12.00, 1, GETDATE(), GETDATE()),
('PALLET', 'Pallet', 'Quantity', 480.00, 1, GETDATE(), GETDATE());

PRINT '  - Units of Measure inserted successfully';
GO

-- =============================================
-- 8. PRODUCT CATEGORY DATA
-- =============================================
PRINT 'Inserting Product Categories...';

IF NOT EXISTS (SELECT 1 FROM ProductCategory WHERE CategoryCode = 'ELEC')
BEGIN
    INSERT INTO ProductCategory (CategoryCode, CategoryName, CategoryPath, IsActive, CreatedDate, UpdatedDate)
    VALUES 
    ('ELEC', 'Electronics', '/ELEC/', 1, GETDATE(), GETDATE()),
    ('FURN', 'Furniture', '/FURN/', 1, GETDATE(), GETDATE()),
    ('STAT', 'Stationery', '/STAT/', 1, GETDATE(), GETDATE());
    
    PRINT '  - Parent Categories inserted successfully';
END

-- Insert Sub Categories
DECLARE @ElecCatID INT, @FurnCatID INT;
SELECT @ElecCatID = CategoryID FROM ProductCategory WHERE CategoryCode = 'ELEC';
SELECT @FurnCatID = CategoryID FROM ProductCategory WHERE CategoryCode = 'FURN';

IF @ElecCatID IS NOT NULL AND NOT EXISTS (SELECT 1 FROM ProductCategory WHERE CategoryCode = 'COMP')
BEGIN
    INSERT INTO ProductCategory (CategoryCode, CategoryName, ParentCategoryID, CategoryPath, IsActive, CreatedDate, UpdatedDate)
    VALUES 
    ('COMP', 'Computers', @ElecCatID, '/ELEC/COMP/', 1, GETDATE(), GETDATE()),
    ('MOBILE', 'Mobile Devices', @ElecCatID, '/ELEC/MOBILE/', 1, GETDATE(), GETDATE()),
    ('OFFICE', 'Office Furniture', @FurnCatID, '/FURN/OFFICE/', 1, GETDATE(), GETDATE());
    
    PRINT '  - Sub Categories inserted successfully';
END
GO

-- =============================================
-- 9. CUSTOMER DATA - FIXED
-- =============================================
PRINT 'Inserting Customers...';

IF NOT EXISTS (SELECT 1 FROM Customer WHERE CustomerCode = 'CUST001')
BEGIN
    INSERT INTO Customer (CustomerCode, CustomerName, CustomerType, TaxNumber, CreditLimit, PaymentTerm, CurrencyCode, IsActive, CreatedDate, UpdatedDate)
    VALUES 
    ('CUST001', 'Tech Solutions Ltd', 'Business', 'GB111222333', 50000.00, 30, 'GBP', 1, GETDATE(), GETDATE()),
    ('CUST002', 'Office Supplies Direct', 'Business', 'GB444555666', 75000.00, 45, 'GBP', 1, GETDATE(), GETDATE()),
    ('CUST003', 'Retail World PLC', 'Business', 'GB777888999', 100000.00, 60, 'GBP', 1, GETDATE(), GETDATE()),
    ('CUST004', 'Smart Systems Inc', 'Business', 'GB123123123', 60000.00, 30, 'GBP', 1, GETDATE(), GETDATE()),
    ('CUST005', 'Digital Store UK', 'Business', 'GB456456456', 80000.00, 45, 'GBP', 1, GETDATE(), GETDATE());
    
    PRINT '  - Customers inserted successfully';
END

-- Insert Customer Addresses
DECLARE @Cust1 INT, @Cust2 INT, @Cust3 INT, @Cust4 INT, @Cust5 INT;
SELECT @Cust1 = CustomerID FROM Customer WHERE CustomerCode = 'CUST001';
SELECT @Cust2 = CustomerID FROM Customer WHERE CustomerCode = 'CUST002';
SELECT @Cust3 = CustomerID FROM Customer WHERE CustomerCode = 'CUST003';
SELECT @Cust4 = CustomerID FROM Customer WHERE CustomerCode = 'CUST004';
SELECT @Cust5 = CustomerID FROM Customer WHERE CustomerCode = 'CUST005';

IF @Cust1 IS NOT NULL AND NOT EXISTS (SELECT 1 FROM CustomerAddress WHERE CustomerID = @Cust1)
BEGIN
    INSERT INTO CustomerAddress (CustomerID, AddressType, Address1, City, PostCode, Country, IsDefault, IsActive, CreatedDate, UpdatedDate)
    VALUES 
    (@Cust1, 'Billing', '10 Tech Park', 'London', 'EC1V 2NX', 'United Kingdom', 1, 1, GETDATE(), GETDATE()),
    (@Cust2, 'Billing', '25 Commerce Street', 'Manchester', 'M2 5AB', 'United Kingdom', 1, 1, GETDATE(), GETDATE()),
    (@Cust3, 'Billing', '100 Retail Plaza', 'Birmingham', 'B2 4XY', 'United Kingdom', 1, 1, GETDATE(), GETDATE()),
    (@Cust4, 'Billing', '50 Innovation Drive', 'Leeds', 'LS1 3DE', 'United Kingdom', 1, 1, GETDATE(), GETDATE()),
    (@Cust5, 'Billing', '75 Digital Avenue', 'Glasgow', 'G2 6FG', 'United Kingdom', 1, 1, GETDATE(), GETDATE());
    
    PRINT '  - Customer Addresses inserted successfully';
END
GO

-- =============================================
-- 10. SUPPLIER DATA - FIXED
-- =============================================
PRINT 'Inserting Suppliers...';

-- First check if suppliers exist and get unique codes
IF NOT EXISTS (SELECT 1 FROM Supplier WHERE SupplierCode = 'SUPP001')
BEGIN
    INSERT INTO Supplier (SupplierCode, SupplierName, TaxNumber, PaymentTermDays, PreferredCurrency, AccountNumber, IsActive, CreatedDate, UpdatedDate)
    VALUES 
    ('SUPP001', 'Global Electronics Manufacturing', 'GB999888777', 60, 'GBP', 'ACC001', 1, GETDATE(), GETDATE()),
    ('SUPP002', 'Computer Components Ltd', 'GB666555444', 45, 'GBP', 'ACC002', 1, GETDATE(), GETDATE()),
    ('SUPP003', 'Mobile Tech Suppliers', 'GB333222111', 30, 'USD', 'ACC003', 1, GETDATE(), GETDATE()),
    ('SUPP004', 'Furniture Factory Direct', 'GB123456789', 90, 'GBP', 'ACC004', 1, GETDATE(), GETDATE()),
    ('SUPP005', 'Office Equipment Wholesale', 'GB987654321', 60, 'EUR', 'ACC005', 1, GETDATE(), GETDATE());
    
    PRINT '  - Suppliers inserted successfully';
END

-- Insert Supplier Addresses
DECLARE @Supp1 INT, @Supp2 INT, @Supp3 INT, @Supp4 INT, @Supp5 INT;
SELECT @Supp1 = SupplierID FROM Supplier WHERE SupplierCode = 'SUPP001';
SELECT @Supp2 = SupplierID FROM Supplier WHERE SupplierCode = 'SUPP002';
SELECT @Supp3 = SupplierID FROM Supplier WHERE SupplierCode = 'SUPP003';
SELECT @Supp4 = SupplierID FROM Supplier WHERE SupplierCode = 'SUPP004';
SELECT @Supp5 = SupplierID FROM Supplier WHERE SupplierCode = 'SUPP005';

IF @Supp1 IS NOT NULL AND NOT EXISTS (SELECT 1 FROM SupplierAddress WHERE SupplierID = @Supp1)
BEGIN
    INSERT INTO SupplierAddress (SupplierID, AddressType, Address1, City, PostCode, Country, IsDefault, IsActive, CreatedDate, UpdatedDate)
    VALUES 
    (@Supp1, 'Main', '200 Industrial Estate', 'Birmingham', 'B10 1AB', 'United Kingdom', 1, 1, GETDATE(), GETDATE()),
    (@Supp2, 'Main', '50 Tech Industrial Park', 'Manchester', 'M15 2CD', 'United Kingdom', 1, 1, GETDATE(), GETDATE()),
    (@Supp3, 'Main', '300 Import Business Park', 'London', 'E14 3EF', 'United Kingdom', 1, 1, GETDATE(), GETDATE()),
    (@Supp4, 'Main', '150 Manufacturing Way', 'Leeds', 'LS10 4GH', 'United Kingdom', 1, 1, GETDATE(), GETDATE()),
    (@Supp5, 'Main', '75 Wholesale Avenue', 'Glasgow', 'G40 5IJ', 'United Kingdom', 1, 1, GETDATE(), GETDATE());
    
    PRINT '  - Supplier Addresses inserted successfully';
END
GO

-- =============================================
-- 11. WAREHOUSE DATA
-- =============================================
PRINT 'Inserting Warehouses...';

IF NOT EXISTS (SELECT 1 FROM Warehouse WHERE WarehouseCode = 'WH001')
BEGIN
    INSERT INTO Warehouse (WarehouseCode, WarehouseName, WarehouseType, StorageCapacity, CurrentUtilization, Address1, City, PostCode, Country, IsActive, CreatedDate, UpdatedDate)
    VALUES 
    ('WH001', 'Main Distribution Center', 'Distribution', 10000, 5000, '123 Logistics Way', 'London', 'E1 6AN', 'United Kingdom', 1, GETDATE(), GETDATE()),
    ('WH002', 'North Storage Facility', 'Storage', 8000, 3000, '456 Supply Street', 'Manchester', 'M1 2AB', 'United Kingdom', 1, GETDATE(), GETDATE()),
    ('WH003', 'Central Warehouse', 'Mixed', 12000, 7000, '789 Storage Road', 'Birmingham', 'B1 3CD', 'United Kingdom', 1, GETDATE(), GETDATE());
    
    PRINT '  - Warehouses inserted successfully';
END
GO

-- =============================================
-- 12. WAREHOUSE LOCATIONS
-- =============================================
PRINT 'Inserting Warehouse Locations...';

DECLARE @WH1 INT, @WH2 INT, @WH3 INT;
SELECT @WH1 = WarehouseID FROM Warehouse WHERE WarehouseCode = 'WH001';
SELECT @WH2 = WarehouseID FROM Warehouse WHERE WarehouseCode = 'WH002';
SELECT @WH3 = WarehouseID FROM Warehouse WHERE WarehouseCode = 'WH003';

IF @WH1 IS NOT NULL AND NOT EXISTS (SELECT 1 FROM WarehouseLocation WHERE LocationCode = 'A-01-01')
BEGIN
    INSERT INTO WarehouseLocation (WarehouseID, LocationCode, LocationName, LocationType, Aisle, Bay, Level, Capacity, CurrentOccupancy, IsActive, CreatedDate, UpdatedDate)
    VALUES 
    (@WH1, 'A-01-01', 'Aisle A Bay 01 Level 01', 'Storage', 'A', '01', '01', 100, 50, 1, GETDATE(), GETDATE()),
    (@WH1, 'A-01-02', 'Aisle A Bay 01 Level 02', 'Storage', 'A', '01', '02', 100, 30, 1, GETDATE(), GETDATE()),
    (@WH2, 'B-01-01', 'Aisle B Bay 01 Level 01', 'Picking', 'B', '01', '01', 150, 75, 1, GETDATE(), GETDATE()),
    (@WH3, 'C-01-01', 'Aisle C Bay 01 Level 01', 'Bulk', 'C', '01', '01', 200, 100, 1, GETDATE(), GETDATE());
    
    PRINT '  - Warehouse Locations inserted successfully';
END
GO

-- =============================================
-- 13. PRODUCT DATA
-- =============================================
PRINT 'Inserting Products...';

DECLARE @CompCatID INT, @OfficeCatID INT, @MobileCatID INT;
DECLARE @UOMID INT;
SELECT @CompCatID = CategoryID FROM ProductCategory WHERE CategoryCode = 'COMP';
SELECT @OfficeCatID = CategoryID FROM ProductCategory WHERE CategoryCode = 'OFFICE';
SELECT @MobileCatID = CategoryID FROM ProductCategory WHERE CategoryCode = 'MOBILE';
SELECT @UOMID = UOMID FROM UnitOfMeasure WHERE UOMCode = 'PC';

IF @CompCatID IS NOT NULL AND @UOMID IS NOT NULL AND NOT EXISTS (SELECT 1 FROM Product WHERE ProductCode = 'LAPTOP001')
BEGIN
    INSERT INTO Product (ProductCode, ProductName, CategoryID, InventoryUOMID, StandardCost, ListPrice, MinStockLevel, MaxStockLevel, ReorderPoint, LeadTimeDays, IsActive, CreatedDate, UpdatedDate)
    VALUES 
    ('LAPTOP001', 'Business Laptop Pro 15"', @CompCatID, @UOMID, 650.00, 899.99, 10, 50, 15, 7, 1, GETDATE(), GETDATE()),
    ('DESKTOP001', 'Office Desktop PC', @CompCatID, @UOMID, 420.00, 599.99, 5, 25, 8, 5, 1, GETDATE(), GETDATE()),
    ('PHONE001', 'Smartphone X Pro', @MobileCatID, @UOMID, 480.00, 699.99, 20, 100, 30, 3, 1, GETDATE(), GETDATE()),
    ('DESK001', 'Executive Office Desk', @OfficeCatID, @UOMID, 250.00, 399.99, 3, 15, 5, 14, 1, GETDATE(), GETDATE()),
    ('CHAIR001', 'Ergonomic Office Chair', @OfficeCatID, @UOMID, 150.00, 249.99, 5, 30, 10, 10, 1, GETDATE(), GETDATE()),
    ('MONITOR001', '27" LED Monitor', @CompCatID, @UOMID, 200.00, 299.99, 8, 40, 12, 5, 1, GETDATE(), GETDATE());
    
    PRINT '  - Products inserted successfully';
END
GO

-- =============================================
-- 14. INITIAL INVENTORY
-- =============================================
PRINT 'Inserting Initial Inventory...';

-- Re-declare variables
DECLARE @WH1 INT, @WH2 INT, @WH3 INT;
DECLARE @Loc1 INT, @Loc2 INT, @Loc3 INT, @Loc4 INT;
DECLARE @Prod1 INT, @Prod2 INT, @Prod3 INT, @Prod4 INT, @Prod5 INT, @Prod6 INT;

SELECT @WH1 = WarehouseID FROM Warehouse WHERE WarehouseCode = 'WH001';
SELECT @WH2 = WarehouseID FROM Warehouse WHERE WarehouseCode = 'WH002';
SELECT @WH3 = WarehouseID FROM Warehouse WHERE WarehouseCode = 'WH003';

SELECT @Loc1 = LocationID FROM WarehouseLocation WHERE LocationCode = 'A-01-01';
SELECT @Loc2 = LocationID FROM WarehouseLocation WHERE LocationCode = 'A-01-02';
SELECT @Loc3 = LocationID FROM WarehouseLocation WHERE LocationCode = 'B-01-01';
SELECT @Loc4 = LocationID FROM WarehouseLocation WHERE LocationCode = 'C-01-01';

SELECT @Prod1 = ProductID FROM Product WHERE ProductCode = 'LAPTOP001';
SELECT @Prod2 = ProductID FROM Product WHERE ProductCode = 'DESKTOP001';
SELECT @Prod3 = ProductID FROM Product WHERE ProductCode = 'PHONE001';
SELECT @Prod4 = ProductID FROM Product WHERE ProductCode = 'DESK001';
SELECT @Prod5 = ProductID FROM Product WHERE ProductCode = 'CHAIR001';
SELECT @Prod6 = ProductID FROM Product WHERE ProductCode = 'MONITOR001';

IF @WH1 IS NOT NULL AND @Loc1 IS NOT NULL AND @Prod1 IS NOT NULL 
   AND NOT EXISTS (SELECT 1 FROM Inventory WHERE ProductID = @Prod1 AND WarehouseID = @WH1)
BEGIN
    INSERT INTO Inventory (WarehouseID, LocationID, ProductID, QuantityOnHand, QuantityReserved, QuantityAvailable, LastCountDate, CreatedDate, UpdatedDate)
    VALUES 
    (@WH1, @Loc1, @Prod1, 25, 5, 20, GETDATE(), GETDATE(), GETDATE()),    -- Laptops
    (@WH1, @Loc2, @Prod2, 15, 2, 13, GETDATE(), GETDATE(), GETDATE()),    -- Desktops
    (@WH2, @Loc3, @Prod3, 50, 10, 40, GETDATE(), GETDATE(), GETDATE()),   -- Phones
    (@WH3, @Loc4, @Prod4, 8, 1, 7, GETDATE(), GETDATE(), GETDATE()),      -- Desks
    (@WH1, @Loc1, @Prod5, 20, 3, 17, GETDATE(), GETDATE(), GETDATE()),    -- Chairs
    (@WH1, @Loc2, @Prod6, 5, 1, 4, GETDATE(), GETDATE(), GETDATE());      -- Monitors (Low Stock!)
    
    PRINT '  - Inventory records inserted successfully';
END
GO

-- =============================================
-- 15. SALES ORDER DATA
-- =============================================
PRINT 'Inserting Sales Orders...';

DECLARE @UserID INT, @Branch1 INT, @Cust1 INT, @Cust2 INT, @Cust3 INT;
SELECT TOP 1 @UserID = UserID FROM [User] WHERE IsActive = 1;
SELECT @Branch1 = BranchID FROM Branch WHERE BranchCode = 'BR001';
SELECT @Cust1 = CustomerID FROM Customer WHERE CustomerCode = 'CUST001';
SELECT @Cust2 = CustomerID FROM Customer WHERE CustomerCode = 'CUST002';
SELECT @Cust3 = CustomerID FROM Customer WHERE CustomerCode = 'CUST003';

IF @UserID IS NOT NULL AND @Branch1 IS NOT NULL AND @Cust1 IS NOT NULL 
   AND NOT EXISTS (SELECT 1 FROM SalesOrder WHERE OrderNumber = 'SO-2025-0001')
BEGIN
    INSERT INTO SalesOrder (OrderNumber, CustomerID, BranchID, OrderDate, RequestedDate, OrderStatus, SubTotal, TaxAmount, TotalAmount, Currency, PaymentTerms, CreatedDate, UpdatedDate, CreatedByUserID)
    VALUES 
    ('SO-2025-0001', @Cust1, @Branch1, GETDATE(), DATEADD(day, 7, GETDATE()), 'Confirmed', 2500.00, 500.00, 3000.00, 'GBP', 30, GETDATE(), GETDATE(), @UserID),
    ('SO-2025-0002', @Cust2, @Branch1, GETDATE(), DATEADD(day, 10, GETDATE()), 'Processing', 1800.00, 360.00, 2160.00, 'GBP', 45, GETDATE(), GETDATE(), @UserID),
    ('SO-2025-0003', @Cust3, @Branch1, DATEADD(day, -1, GETDATE()), DATEADD(day, 5, GETDATE()), 'Shipped', 3500.00, 700.00, 4200.00, 'GBP', 60, GETDATE(), GETDATE(), @UserID);
    
    PRINT '  - Sales Orders inserted successfully';
END
GO

-- =============================================
-- 16. FINAL STATUS REPORT
-- =============================================
PRINT '';
PRINT '========================================';
PRINT 'Sample Data Insertion Complete!';
PRINT '========================================';
PRINT '';

-- Show record counts
SELECT 'Companies' as [Table], COUNT(*) as [Records] FROM Company
UNION ALL SELECT 'Branches', COUNT(*) FROM Branch
UNION ALL SELECT 'Currencies', COUNT(*) FROM Currency
UNION ALL SELECT 'Tax Codes', COUNT(*) FROM TaxCode
UNION ALL SELECT 'Payment Methods', COUNT(*) FROM PaymentMethod
UNION ALL SELECT 'Banks', COUNT(*) FROM Bank
UNION ALL SELECT 'Units of Measure', COUNT(*) FROM UnitOfMeasure
UNION ALL SELECT 'Product Categories', COUNT(*) FROM ProductCategory
UNION ALL SELECT 'Customers', COUNT(*) FROM Customer
UNION ALL SELECT 'Customer Addresses', COUNT(*) FROM CustomerAddress
UNION ALL SELECT 'Suppliers', COUNT(*) FROM Supplier
UNION ALL SELECT 'Supplier Addresses', COUNT(*) FROM SupplierAddress
UNION ALL SELECT 'Warehouses', COUNT(*) FROM Warehouse
UNION ALL SELECT 'Warehouse Locations', COUNT(*) FROM WarehouseLocation
UNION ALL SELECT 'Products', COUNT(*) FROM Product
UNION ALL SELECT 'Inventory', COUNT(*) FROM Inventory
UNION ALL SELECT 'Sales Orders', COUNT(*) FROM SalesOrder
ORDER BY [Table];

PRINT '';
PRINT 'Dashboard should now show:';
PRINT '- Customer count and statistics';
PRINT '- Low stock alerts (Monitor product)';
PRINT '- Recent orders';
PRINT '- Monthly revenue';
PRINT '========================================';
GO