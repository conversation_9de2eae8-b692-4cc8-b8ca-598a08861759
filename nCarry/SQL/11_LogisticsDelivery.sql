-- Logistics and Delivery Management Tables Creation Script
-- Purpose: Comprehensive shipping, carrier management, and delivery tracking
-- Supports picking, packing, shipping, and route optimization

-- Carrier table for shipping companies
CREATE TABLE [dbo].[Carrier] (
    [CarrierID] INT IDENTITY(1,1) NOT NULL,
    [CarrierCode] NVARCHAR(20) UNIQUE NOT NULL,
    [CarrierName] NVARCHAR(100) NOT NULL,
    [CarrierType] NVARCHAR(20) NOT NULL, -- Courier, Freight, Postal, Own Fleet
    [ContactName] NVARCHAR(100) NULL,
    [Phone] NVARCHAR(50) NULL,
    [Email] NVARCHAR(255) NULL,
    [Website] NVARCHAR(255) NULL,
    [AccountNumber] NVARCHAR(50) NULL,
    [TrackingURL] NVARCHAR(500) NULL,
    [APIEndpoint] NVARCHAR(500) NULL,
    [APIKey] NVARCHAR(255) NULL,
    [IsActive] BIT DEFAULT 1 NOT NULL,
    [IsDefault] BIT DEFAULT 0 NOT NULL,
    [Notes] NVARCHAR(MAX) NULL,
    [CreatedDate] DATETIME2 DEFAULT GETDATE() NOT NULL,
    [UpdatedDate] DATETIME2 DEFAULT GETDATE() NOT NULL,
    CONSTRAINT [PK_Carrier] PRIMARY KEY CLUSTERED ([CarrierID] ASC)
);

-- Carrier Service table for different shipping services
CREATE TABLE [dbo].[CarrierService] (
    [ServiceID] INT IDENTITY(1,1) NOT NULL,
    [CarrierID] INT NOT NULL,
    [ServiceCode] NVARCHAR(50) NOT NULL,
    [ServiceName] NVARCHAR(100) NOT NULL,
    [ServiceType] NVARCHAR(20) NOT NULL, -- Standard, Express, Overnight, Economy
    [TransitDays] INT NOT NULL,
    [CutoffTime] TIME NULL,
    [IsInternational] BIT DEFAULT 0 NOT NULL,
    [MaxWeight] DECIMAL(18,3) NULL, -- kg
    [MaxLength] DECIMAL(18,3) NULL, -- cm
    [MaxWidth] DECIMAL(18,3) NULL, -- cm
    [MaxHeight] DECIMAL(18,3) NULL, -- cm
    [IsActive] BIT DEFAULT 1 NOT NULL,
    [CreatedDate] DATETIME2 DEFAULT GETDATE() NOT NULL,
    [UpdatedDate] DATETIME2 DEFAULT GETDATE() NOT NULL,
    CONSTRAINT [PK_CarrierService] PRIMARY KEY CLUSTERED ([ServiceID] ASC),
    CONSTRAINT [FK_CarrierService_Carrier] FOREIGN KEY ([CarrierID]) REFERENCES [dbo].[Carrier] ([CarrierID]),
    CONSTRAINT [UQ_CarrierService] UNIQUE ([CarrierID], [ServiceCode])
);

-- Freight Rate table
CREATE TABLE [dbo].[FreightRate] (
    [RateID] INT IDENTITY(1,1) NOT NULL,
    [CarrierID] INT NOT NULL,
    [ServiceID] INT NOT NULL,
    [RateType] NVARCHAR(20) NOT NULL, -- Weight, Zone, Flat, Distance
    [FromZone] NVARCHAR(20) NULL,
    [ToZone] NVARCHAR(20) NULL,
    [MinWeight] DECIMAL(18,3) DEFAULT 0 NOT NULL, -- kg
    [MaxWeight] DECIMAL(18,3) NULL, -- kg
    [RatePerKg] DECIMAL(18,4) NULL,
    [FlatRate] DECIMAL(18,2) NULL,
    [MinCharge] DECIMAL(18,2) DEFAULT 0 NULL,
    [FuelSurchargePercent] DECIMAL(5,2) DEFAULT 0 NULL,
    [EffectiveFrom] DATETIME2 NOT NULL,
    [EffectiveTo] DATETIME2 NULL,
    [IsActive] BIT DEFAULT 1 NOT NULL,
    [CreatedDate] DATETIME2 DEFAULT GETDATE() NOT NULL,
    [UpdatedDate] DATETIME2 DEFAULT GETDATE() NOT NULL,
    CONSTRAINT [PK_FreightRate] PRIMARY KEY CLUSTERED ([RateID] ASC),
    CONSTRAINT [FK_FreightRate_Carrier] FOREIGN KEY ([CarrierID]) REFERENCES [dbo].[Carrier] ([CarrierID]),
    CONSTRAINT [FK_FreightRate_Service] FOREIGN KEY ([ServiceID]) REFERENCES [dbo].[CarrierService] ([ServiceID])
);

-- Delivery Zone table
CREATE TABLE [dbo].[DeliveryZone] (
    [ZoneID] INT IDENTITY(1,1) NOT NULL,
    [ZoneCode] NVARCHAR(20) UNIQUE NOT NULL,
    [ZoneName] NVARCHAR(100) NOT NULL,
    [Description] NVARCHAR(500) NULL,
    [PostCodePrefix] NVARCHAR(100) NULL, -- Comma-separated list
    [City] NVARCHAR(100) NULL,
    [Region] NVARCHAR(100) NULL,
    [Country] NVARCHAR(100) DEFAULT 'United Kingdom' NULL,
    [DeliveryDays] INT DEFAULT 1 NOT NULL,
    [SortOrder] INT DEFAULT 100 NOT NULL,
    [IsActive] BIT DEFAULT 1 NOT NULL,
    [CreatedDate] DATETIME2 DEFAULT GETDATE() NOT NULL,
    [UpdatedDate] DATETIME2 DEFAULT GETDATE() NOT NULL,
    CONSTRAINT [PK_DeliveryZone] PRIMARY KEY CLUSTERED ([ZoneID] ASC)
);

-- Delivery Route table
CREATE TABLE [dbo].[DeliveryRoute] (
    [RouteID] INT IDENTITY(1,1) NOT NULL,
    [RouteCode] NVARCHAR(20) UNIQUE NOT NULL,
    [RouteName] NVARCHAR(100) NOT NULL,
    [RouteType] NVARCHAR(20) NOT NULL, -- Daily, Weekly, Custom
    [DriverName] NVARCHAR(100) NULL,
    [VehicleNumber] NVARCHAR(50) NULL,
    [MaxCapacityKg] DECIMAL(18,3) NULL,
    [MaxCapacityM3] DECIMAL(18,3) NULL,
    [StartTime] TIME NULL,
    [EndTime] TIME NULL,
    [Monday] BIT DEFAULT 1 NOT NULL,
    [Tuesday] BIT DEFAULT 1 NOT NULL,
    [Wednesday] BIT DEFAULT 1 NOT NULL,
    [Thursday] BIT DEFAULT 1 NOT NULL,
    [Friday] BIT DEFAULT 1 NOT NULL,
    [Saturday] BIT DEFAULT 0 NOT NULL,
    [Sunday] BIT DEFAULT 0 NOT NULL,
    [IsActive] BIT DEFAULT 1 NOT NULL,
    [Notes] NVARCHAR(MAX) NULL,
    [CreatedDate] DATETIME2 DEFAULT GETDATE() NOT NULL,
    [UpdatedDate] DATETIME2 DEFAULT GETDATE() NOT NULL,
    CONSTRAINT [PK_DeliveryRoute] PRIMARY KEY CLUSTERED ([RouteID] ASC)
);

-- Route Zone mapping
CREATE TABLE [dbo].[RouteZone] (
    [RouteZoneID] INT IDENTITY(1,1) NOT NULL,
    [RouteID] INT NOT NULL,
    [ZoneID] INT NOT NULL,
    [SequenceOrder] INT DEFAULT 100 NOT NULL,
    [EstimatedMinutes] INT NULL,
    [IsActive] BIT DEFAULT 1 NOT NULL,
    CONSTRAINT [PK_RouteZone] PRIMARY KEY CLUSTERED ([RouteZoneID] ASC),
    CONSTRAINT [FK_RouteZone_Route] FOREIGN KEY ([RouteID]) REFERENCES [dbo].[DeliveryRoute] ([RouteID]),
    CONSTRAINT [FK_RouteZone_Zone] FOREIGN KEY ([ZoneID]) REFERENCES [dbo].[DeliveryZone] ([ZoneID]),
    CONSTRAINT [UQ_RouteZone] UNIQUE ([RouteID], [ZoneID])
);

-- Picking List table
CREATE TABLE [dbo].[PickingList] (
    [PickingListID] INT IDENTITY(1,1) NOT NULL,
    [PickingListNumber] NVARCHAR(50) UNIQUE NOT NULL,
    [PickingDate] DATETIME2 DEFAULT GETDATE() NOT NULL,
    [WarehouseID] INT NOT NULL,
    [PickingType] NVARCHAR(20) NOT NULL, -- Order, Batch, Wave, Zone
    [Priority] NVARCHAR(20) DEFAULT 'Normal' NOT NULL, -- Low, Normal, High, Urgent
    [Status] NVARCHAR(20) DEFAULT 'Pending' NOT NULL, -- Pending, InProgress, Completed, Cancelled
    [AssignedToUserID] INT NULL,
    [StartedDate] DATETIME2 NULL,
    [CompletedDate] DATETIME2 NULL,
    [TotalItems] INT DEFAULT 0 NOT NULL,
    [PickedItems] INT DEFAULT 0 NOT NULL,
    [Notes] NVARCHAR(MAX) NULL,
    [CreatedDate] DATETIME2 DEFAULT GETDATE() NOT NULL,
    [CreatedByUserID] INT NULL,
    [UpdatedDate] DATETIME2 DEFAULT GETDATE() NOT NULL,
    [UpdatedByUserID] INT NULL,
    CONSTRAINT [PK_PickingList] PRIMARY KEY CLUSTERED ([PickingListID] ASC),
    CONSTRAINT [FK_PickingList_Warehouse] FOREIGN KEY ([WarehouseID]) REFERENCES [dbo].[Warehouse] ([WarehouseID]),
    CONSTRAINT [FK_PickingList_AssignedTo] FOREIGN KEY ([AssignedToUserID]) REFERENCES [dbo].[User] ([UserID])
);

-- Picking List Item table
CREATE TABLE [dbo].[PickingListItem] (
    [PickingItemID] INT IDENTITY(1,1) NOT NULL,
    [PickingListID] INT NOT NULL,
    [OrderID] INT NOT NULL,
    [OrderItemID] INT NOT NULL,
    [ProductID] INT NOT NULL,
    [VariantID] INT NULL,
    [LocationID] INT NULL,
    [BatchNumber] NVARCHAR(50) NULL,
    [SerialNumber] NVARCHAR(100) NULL,
    [QuantityToPick] DECIMAL(18,3) NOT NULL,
    [QuantityPicked] DECIMAL(18,3) DEFAULT 0 NOT NULL,
    [PickedByUserID] INT NULL,
    [PickedDate] DATETIME2 NULL,
    [Status] NVARCHAR(20) DEFAULT 'Pending' NOT NULL, -- Pending, Picked, Short, Skipped
    [ShortageReason] NVARCHAR(500) NULL,
    [SortOrder] INT DEFAULT 100 NOT NULL,
    CONSTRAINT [PK_PickingListItem] PRIMARY KEY CLUSTERED ([PickingItemID] ASC),
    CONSTRAINT [FK_PickingListItem_PickingList] FOREIGN KEY ([PickingListID]) REFERENCES [dbo].[PickingList] ([PickingListID]),
    CONSTRAINT [FK_PickingListItem_Order] FOREIGN KEY ([OrderID]) REFERENCES [dbo].[SalesOrder] ([OrderID]),
    CONSTRAINT [FK_PickingListItem_OrderItem] FOREIGN KEY ([OrderItemID]) REFERENCES [dbo].[SalesOrderItem] ([OrderItemID]),
    CONSTRAINT [FK_PickingListItem_Product] FOREIGN KEY ([ProductID]) REFERENCES [dbo].[Product] ([ProductID]),
    CONSTRAINT [FK_PickingListItem_Variant] FOREIGN KEY ([VariantID]) REFERENCES [dbo].[ProductVariant] ([VariantID]),
    CONSTRAINT [FK_PickingListItem_Location] FOREIGN KEY ([LocationID]) REFERENCES [dbo].[WarehouseLocation] ([LocationID]),
    CONSTRAINT [FK_PickingListItem_PickedBy] FOREIGN KEY ([PickedByUserID]) REFERENCES [dbo].[User] ([UserID])
);

-- Packing Slip table
CREATE TABLE [dbo].[PackingSlip] (
    [PackingSlipID] INT IDENTITY(1,1) NOT NULL,
    [PackingSlipNumber] NVARCHAR(50) UNIQUE NOT NULL,
    [PackingDate] DATETIME2 DEFAULT GETDATE() NOT NULL,
    [OrderID] INT NOT NULL,
    [PickingListID] INT NULL,
    [WarehouseID] INT NOT NULL,
    [PackedByUserID] INT NOT NULL,
    [TotalBoxes] INT DEFAULT 0 NOT NULL,
    [TotalWeight] DECIMAL(18,3) DEFAULT 0 NOT NULL, -- kg
    [TotalVolume] DECIMAL(18,3) DEFAULT 0 NULL, -- m3
    [Status] NVARCHAR(20) DEFAULT 'Draft' NOT NULL, -- Draft, Completed, Shipped
    [Notes] NVARCHAR(MAX) NULL,
    [CreatedDate] DATETIME2 DEFAULT GETDATE() NOT NULL,
    [CreatedByUserID] INT NULL,
    [UpdatedDate] DATETIME2 DEFAULT GETDATE() NOT NULL,
    CONSTRAINT [PK_PackingSlip] PRIMARY KEY CLUSTERED ([PackingSlipID] ASC),
    CONSTRAINT [FK_PackingSlip_Order] FOREIGN KEY ([OrderID]) REFERENCES [dbo].[SalesOrder] ([OrderID]),
    CONSTRAINT [FK_PackingSlip_PickingList] FOREIGN KEY ([PickingListID]) REFERENCES [dbo].[PickingList] ([PickingListID]),
    CONSTRAINT [FK_PackingSlip_Warehouse] FOREIGN KEY ([WarehouseID]) REFERENCES [dbo].[Warehouse] ([WarehouseID]),
    CONSTRAINT [FK_PackingSlip_PackedBy] FOREIGN KEY ([PackedByUserID]) REFERENCES [dbo].[User] ([UserID])
);

-- Packing Slip Item table
CREATE TABLE [dbo].[PackingSlipItem] (
    [PackingItemID] INT IDENTITY(1,1) NOT NULL,
    [PackingSlipID] INT NOT NULL,
    [BoxNumber] INT NOT NULL,
    [ProductID] INT NOT NULL,
    [VariantID] INT NULL,
    [OrderItemID] INT NOT NULL,
    [Quantity] DECIMAL(18,3) NOT NULL,
    [BatchNumber] NVARCHAR(50) NULL,
    [SerialNumber] NVARCHAR(100) NULL,
    [Weight] DECIMAL(18,3) NULL, -- kg
    [Notes] NVARCHAR(500) NULL,
    CONSTRAINT [PK_PackingSlipItem] PRIMARY KEY CLUSTERED ([PackingItemID] ASC),
    CONSTRAINT [FK_PackingSlipItem_PackingSlip] FOREIGN KEY ([PackingSlipID]) REFERENCES [dbo].[PackingSlip] ([PackingSlipID]),
    CONSTRAINT [FK_PackingSlipItem_Product] FOREIGN KEY ([ProductID]) REFERENCES [dbo].[Product] ([ProductID]),
    CONSTRAINT [FK_PackingSlipItem_Variant] FOREIGN KEY ([VariantID]) REFERENCES [dbo].[ProductVariant] ([VariantID]),
    CONSTRAINT [FK_PackingSlipItem_OrderItem] FOREIGN KEY ([OrderItemID]) REFERENCES [dbo].[SalesOrderItem] ([OrderItemID])
);

-- Shipment table
CREATE TABLE [dbo].[Shipment] (
    [ShipmentID] INT IDENTITY(1,1) NOT NULL,
    [ShipmentNumber] NVARCHAR(50) UNIQUE NOT NULL,
    [ShipmentDate] DATETIME2 DEFAULT GETDATE() NOT NULL,
    [ShipmentType] NVARCHAR(20) NOT NULL, -- Customer, Transfer, Return
    [CarrierID] INT NULL,
    [ServiceID] INT NULL,
    [RouteID] INT NULL,
    [TrackingNumber] NVARCHAR(100) NULL,
    [ShipmentStatus] NVARCHAR(20) DEFAULT 'Pending' NOT NULL, -- Pending, Picked, InTransit, Delivered, Failed, Returned
    
    -- Address information
    [ShipFromWarehouseID] INT NOT NULL,
    [ShipToName] NVARCHAR(255) NOT NULL,
    [ShipToAddress1] NVARCHAR(500) NOT NULL,
    [ShipToAddress2] NVARCHAR(500) NULL,
    [ShipToCity] NVARCHAR(100) NULL,
    [ShipToState] NVARCHAR(100) NULL,
    [ShipToPostCode] NVARCHAR(20) NULL,
    [ShipToCountry] NVARCHAR(100) DEFAULT 'United Kingdom' NULL,
    [ShipToPhone] NVARCHAR(50) NULL,
    
    -- Shipment details
    [TotalPackages] INT DEFAULT 0 NOT NULL,
    [TotalWeight] DECIMAL(18,3) DEFAULT 0 NOT NULL, -- kg
    [TotalVolume] DECIMAL(18,3) DEFAULT 0 NULL, -- m3
    [FreightCost] DECIMAL(18,2) DEFAULT 0 NULL,
    [InsuranceValue] DECIMAL(18,2) DEFAULT 0 NULL,
    
    -- Delivery information
    [EstimatedDeliveryDate] DATETIME2 NULL,
    [ActualDeliveryDate] DATETIME2 NULL,
    [DeliveredToName] NVARCHAR(255) NULL,
    [DeliverySignature] NVARCHAR(255) NULL,
    [DeliveryNotes] NVARCHAR(MAX) NULL,
    
    -- System fields
    [CreatedDate] DATETIME2 DEFAULT GETDATE() NOT NULL,
    [CreatedByUserID] INT NULL,
    [UpdatedDate] DATETIME2 DEFAULT GETDATE() NOT NULL,
    [UpdatedByUserID] INT NULL,
    
    CONSTRAINT [PK_Shipment] PRIMARY KEY CLUSTERED ([ShipmentID] ASC),
    CONSTRAINT [FK_Shipment_Carrier] FOREIGN KEY ([CarrierID]) REFERENCES [dbo].[Carrier] ([CarrierID]),
    CONSTRAINT [FK_Shipment_Service] FOREIGN KEY ([ServiceID]) REFERENCES [dbo].[CarrierService] ([ServiceID]),
    CONSTRAINT [FK_Shipment_Route] FOREIGN KEY ([RouteID]) REFERENCES [dbo].[DeliveryRoute] ([RouteID]),
    CONSTRAINT [FK_Shipment_Warehouse] FOREIGN KEY ([ShipFromWarehouseID]) REFERENCES [dbo].[Warehouse] ([WarehouseID])
);

-- Shipment Item table (linking shipments to orders/packing slips)
CREATE TABLE [dbo].[ShipmentItem] (
    [ShipmentItemID] INT IDENTITY(1,1) NOT NULL,
    [ShipmentID] INT NOT NULL,
    [OrderID] INT NULL,
    [PackingSlipID] INT NULL,
    [TransferID] INT NULL,
    [PackageNumber] NVARCHAR(50) NULL,
    [PackageWeight] DECIMAL(18,3) NULL, -- kg
    [PackageLength] DECIMAL(18,3) NULL, -- cm
    [PackageWidth] DECIMAL(18,3) NULL, -- cm
    [PackageHeight] DECIMAL(18,3) NULL, -- cm
    [Notes] NVARCHAR(500) NULL,
    CONSTRAINT [PK_ShipmentItem] PRIMARY KEY CLUSTERED ([ShipmentItemID] ASC),
    CONSTRAINT [FK_ShipmentItem_Shipment] FOREIGN KEY ([ShipmentID]) REFERENCES [dbo].[Shipment] ([ShipmentID]),
    CONSTRAINT [FK_ShipmentItem_Order] FOREIGN KEY ([OrderID]) REFERENCES [dbo].[SalesOrder] ([OrderID]),
    CONSTRAINT [FK_ShipmentItem_PackingSlip] FOREIGN KEY ([PackingSlipID]) REFERENCES [dbo].[PackingSlip] ([PackingSlipID]),
    CONSTRAINT [FK_ShipmentItem_Transfer] FOREIGN KEY ([TransferID]) REFERENCES [dbo].[StockTransfer] ([TransferID])
);

-- Shipment Tracking table
CREATE TABLE [dbo].[ShipmentTracking] (
    [TrackingID] INT IDENTITY(1,1) NOT NULL,
    [ShipmentID] INT NOT NULL,
    [TrackingDate] DATETIME2 NOT NULL,
    [Status] NVARCHAR(100) NOT NULL,
    [Location] NVARCHAR(255) NULL,
    [Description] NVARCHAR(500) NULL,
    [CarrierStatus] NVARCHAR(100) NULL,
    [IsDelivered] BIT DEFAULT 0 NOT NULL,
    [CreatedDate] DATETIME2 DEFAULT GETDATE() NOT NULL,
    CONSTRAINT [PK_ShipmentTracking] PRIMARY KEY CLUSTERED ([TrackingID] ASC),
    CONSTRAINT [FK_ShipmentTracking_Shipment] FOREIGN KEY ([ShipmentID]) REFERENCES [dbo].[Shipment] ([ShipmentID])
);

-- Document table for all system documents
CREATE TABLE [dbo].[Document] (
    [DocumentID] INT IDENTITY(1,1) NOT NULL,
    [DocumentType] NVARCHAR(50) NOT NULL, -- PackingSlip, DeliveryNote, CMR, POD, Invoice, etc.
    [DocumentNumber] NVARCHAR(50) NOT NULL,
    [DocumentDate] DATETIME2 NOT NULL,
    [EntityType] NVARCHAR(50) NOT NULL, -- Order, Shipment, Invoice, etc.
    [EntityID] INT NOT NULL,
    [FileName] NVARCHAR(255) NOT NULL,
    [FilePath] NVARCHAR(500) NULL,
    [FileData] VARBINARY(MAX) NULL,
    [FileSize] BIGINT NULL,
    [MimeType] NVARCHAR(100) NULL,
    [IsPublic] BIT DEFAULT 0 NOT NULL,
    [Notes] NVARCHAR(500) NULL,
    [CreatedDate] DATETIME2 DEFAULT GETDATE() NOT NULL,
    [CreatedByUserID] INT NULL,
    CONSTRAINT [PK_Document] PRIMARY KEY CLUSTERED ([DocumentID] ASC)
);

-- Create indexes
CREATE INDEX [IX_Carrier_CarrierCode] ON [dbo].[Carrier] ([CarrierCode]);
CREATE INDEX [IX_CarrierService_CarrierID] ON [dbo].[CarrierService] ([CarrierID]);
CREATE INDEX [IX_FreightRate_CarrierID_ServiceID] ON [dbo].[FreightRate] ([CarrierID], [ServiceID]);
CREATE INDEX [IX_FreightRate_EffectiveDates] ON [dbo].[FreightRate] ([EffectiveFrom], [EffectiveTo]);
CREATE INDEX [IX_DeliveryZone_ZoneCode] ON [dbo].[DeliveryZone] ([ZoneCode]);
CREATE INDEX [IX_DeliveryRoute_RouteCode] ON [dbo].[DeliveryRoute] ([RouteCode]);
CREATE INDEX [IX_RouteZone_RouteID] ON [dbo].[RouteZone] ([RouteID]);
CREATE INDEX [IX_RouteZone_ZoneID] ON [dbo].[RouteZone] ([ZoneID]);

CREATE INDEX [IX_PickingList_PickingListNumber] ON [dbo].[PickingList] ([PickingListNumber]);
CREATE INDEX [IX_PickingList_WarehouseID] ON [dbo].[PickingList] ([WarehouseID]);
CREATE INDEX [IX_PickingList_Status] ON [dbo].[PickingList] ([Status]);
CREATE INDEX [IX_PickingListItem_PickingListID] ON [dbo].[PickingListItem] ([PickingListID]);
CREATE INDEX [IX_PickingListItem_OrderID] ON [dbo].[PickingListItem] ([OrderID]);
CREATE INDEX [IX_PickingListItem_ProductID] ON [dbo].[PickingListItem] ([ProductID]);

CREATE INDEX [IX_PackingSlip_PackingSlipNumber] ON [dbo].[PackingSlip] ([PackingSlipNumber]);
CREATE INDEX [IX_PackingSlip_OrderID] ON [dbo].[PackingSlip] ([OrderID]);
CREATE INDEX [IX_PackingSlipItem_PackingSlipID] ON [dbo].[PackingSlipItem] ([PackingSlipID]);

CREATE INDEX [IX_Shipment_ShipmentNumber] ON [dbo].[Shipment] ([ShipmentNumber]);
CREATE INDEX [IX_Shipment_TrackingNumber] ON [dbo].[Shipment] ([TrackingNumber]);
CREATE INDEX [IX_Shipment_ShipmentStatus] ON [dbo].[Shipment] ([ShipmentStatus]);
CREATE INDEX [IX_Shipment_ShipmentDate] ON [dbo].[Shipment] ([ShipmentDate]);
CREATE INDEX [IX_ShipmentItem_ShipmentID] ON [dbo].[ShipmentItem] ([ShipmentID]);
CREATE INDEX [IX_ShipmentTracking_ShipmentID] ON [dbo].[ShipmentTracking] ([ShipmentID]);
CREATE INDEX [IX_ShipmentTracking_TrackingDate] ON [dbo].[ShipmentTracking] ([TrackingDate]);

CREATE INDEX [IX_Document_EntityType_EntityID] ON [dbo].[Document] ([EntityType], [EntityID]);
CREATE INDEX [IX_Document_DocumentType] ON [dbo].[Document] ([DocumentType]);
CREATE INDEX [IX_Document_DocumentDate] ON [dbo].[Document] ([DocumentDate]);