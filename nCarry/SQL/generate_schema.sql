
SET NOCOUNT ON;

DECLARE @table_name SYSNAME;
DECLARE @sql NVARCHAR(MAX);

DECLARE table_cursor CURSOR FOR
SELECT TABLE_NAME
FROM INFORMATION_SCHEMA.TABLES
WHERE TABLE_TYPE = 'BASE TABLE'
ORDER BY TABLE_NAME;

OPEN table_cursor;

FETCH NEXT FROM table_cursor INTO @table_name;

WHILE @@FETCH_STATUS = 0
BEGIN
    SET @sql = 'CREATE TABLE [' + @table_name + '] (' + CHAR(13);

    -- Columns
    SELECT @sql = @sql + CHAR(13) + '  [' + c.COLUMN_NAME + '] ' +
           UPPER(c.DATA_TYPE) +
           CASE
               WHEN c.DATA_TYPE IN ('varchar', 'nvarchar', 'char', 'nchar') THEN '(' + IIF(c.CHARACTER_MAXIMUM_LENGTH = -1, 'MAX', CAST(c.CHARACTER_MAXIMUM_LENGTH AS VARCHAR(10))) + ')'
               WHEN c.DATA_TYPE IN ('decimal', 'numeric') THEN '(' + CAST(c.NUMERIC_PRECISION AS VARCHAR(10)) + ', ' + CAST(c.NUMERIC_SCALE AS VARCHAR(10)) + ')'
               ELSE ''
           END +
           ' ' +
           ISNULL('DEFAULT ' + d.definition, '') +
           ' ' +
           CASE
               WHEN c.IS_NULLABLE = 'NO' THEN 'NOT NULL'
               ELSE 'NULL'
           END + ','
    FROM INFORMATION_SCHEMA.COLUMNS c
    LEFT JOIN sys.default_constraints d ON c.TABLE_NAME = OBJECT_NAME(d.parent_object_id) AND c.COLUMN_NAME = COL_NAME(d.parent_object_id, d.parent_column_id)
    WHERE c.TABLE_NAME = @table_name
    ORDER BY c.ORDINAL_POSITION;

    -- Primary Key
    DECLARE @pk_constraint_name SYSNAME;
    DECLARE @pk_columns NVARCHAR(MAX);

    SELECT TOP 1 @pk_constraint_name = tc.CONSTRAINT_NAME
    FROM INFORMATION_SCHEMA.TABLE_CONSTRAINTS tc
    WHERE tc.TABLE_NAME = @table_name AND tc.CONSTRAINT_TYPE = 'PRIMARY KEY';

    IF @pk_constraint_name IS NOT NULL
    BEGIN
        SELECT @pk_columns = STRING_AGG('[' + kcu.COLUMN_NAME + ']', ', ')
        FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE kcu
        WHERE kcu.CONSTRAINT_NAME = @pk_constraint_name AND kcu.TABLE_NAME = @table_name;

        SET @sql = @sql + CHAR(13) + '  CONSTRAINT [' + @pk_constraint_name + '] PRIMARY KEY (' + @pk_columns + '),'
    END

    -- Remove last comma
    SET @sql = LEFT(@sql, LEN(@sql) - 1);
    SET @sql = @sql + CHAR(13) + ');' + CHAR(13) + 'GO' + CHAR(13);

    PRINT @sql;

    FETCH NEXT FROM table_cursor INTO @table_name;
END;

CLOSE table_cursor;
DEALLOCATE table_cursor;
