-- System Configuration Tables Creation Script
-- Purpose: System settings, multi-company support, and integration capabilities
-- Includes reporting, notifications, and API management

-- Company table for multi-company support
CREATE TABLE [dbo].[Company] (
    [CompanyID] INT IDENTITY(1,1) NOT NULL,
    [CompanyCode] NVARCHAR(20) UNIQUE NOT NULL,
    [CompanyName] NVARCHAR(255) NOT NULL,
    [LegalName] NVARCHAR(255) NULL,
    [TaxNumber] NVARCHAR(50) NULL,
    [RegistrationNumber] NVARCHAR(50) NULL,
    [Logo] VARBINARY(MAX) NULL,
    [Website] NVARCHAR(255) NULL,
    
    -- Address Information
    [Address1] NVARCHAR(500) NOT NULL,
    [Address2] NVARCHAR(500) NULL,
    [City] NVARCHAR(100) NULL,
    [State] NVARCHAR(100) NULL,
    [PostCode] NVARCHAR(20) NULL,
    [Country] NVARCHAR(100) DEFAULT 'United Kingdom' NULL,
    
    -- Contact Information
    [Phone] NVARCHAR(50) NULL,
    [Email] NVARCHAR(255) NULL,
    [ContactName] NVARCHAR(255) NULL,
    
    -- Financial Settings
    [BaseCurrencyID] INT NOT NULL,
    [FiscalYearStart] INT DEFAULT 1 NOT NULL, -- Month number
    [DefaultPaymentTerms] INT DEFAULT 30 NULL,
    
    -- System Settings
    [IsActive] BIT DEFAULT 1 NOT NULL,
    [IsDefault] BIT DEFAULT 0 NOT NULL,
    [CreatedDate] DATETIME2 DEFAULT GETDATE() NOT NULL,
    [UpdatedDate] DATETIME2 DEFAULT GETDATE() NOT NULL,
    
    CONSTRAINT [PK_Company] PRIMARY KEY CLUSTERED ([CompanyID] ASC),
    CONSTRAINT [FK_Company_Currency] FOREIGN KEY ([BaseCurrencyID]) REFERENCES [dbo].[Currency] ([CurrencyID])
);

-- Branch table for multiple locations
CREATE TABLE [dbo].[Branch] (
    [BranchID] INT IDENTITY(1,1) NOT NULL,
    [CompanyID] INT NOT NULL,
    [BranchCode] NVARCHAR(20) NOT NULL,
    [BranchName] NVARCHAR(100) NOT NULL,
    [BranchType] NVARCHAR(20) DEFAULT 'Office' NOT NULL, -- Office, Warehouse, Store, Factory
    [WarehouseID] INT NULL,
    
    -- Address Information
    [Address1] NVARCHAR(500) NOT NULL,
    [Address2] NVARCHAR(500) NULL,
    [City] NVARCHAR(100) NULL,
    [State] NVARCHAR(100) NULL,
    [PostCode] NVARCHAR(20) NULL,
    [Country] NVARCHAR(100) DEFAULT 'United Kingdom' NULL,
    
    -- Contact Information
    [Phone] NVARCHAR(50) NULL,
    [Email] NVARCHAR(255) NULL,
    [ManagerName] NVARCHAR(255) NULL,
    
    -- Settings
    [TimeZone] NVARCHAR(50) DEFAULT 'GMT Standard Time' NULL,
    [IsActive] BIT DEFAULT 1 NOT NULL,
    [IsDefault] BIT DEFAULT 0 NOT NULL,
    [CreatedDate] DATETIME2 DEFAULT GETDATE() NOT NULL,
    [UpdatedDate] DATETIME2 DEFAULT GETDATE() NOT NULL,
    
    CONSTRAINT [PK_Branch] PRIMARY KEY CLUSTERED ([BranchID] ASC),
    CONSTRAINT [FK_Branch_Company] FOREIGN KEY ([CompanyID]) REFERENCES [dbo].[Company] ([CompanyID]),
    CONSTRAINT [FK_Branch_Warehouse] FOREIGN KEY ([WarehouseID]) REFERENCES [dbo].[Warehouse] ([WarehouseID]),
    CONSTRAINT [UQ_Branch_Code] UNIQUE ([CompanyID], [BranchCode])
);

-- System Configuration table
CREATE TABLE [dbo].[SystemConfiguration] (
    [ConfigID] INT IDENTITY(1,1) NOT NULL,
    [CompanyID] INT NULL, -- NULL means system-wide
    [Module] NVARCHAR(50) NOT NULL, -- System, Sales, Purchase, Inventory, Finance, etc.
    [ConfigKey] NVARCHAR(100) NOT NULL,
    [ConfigValue] NVARCHAR(MAX) NOT NULL,
    [DataType] NVARCHAR(20) NOT NULL, -- String, Integer, Decimal, Boolean, DateTime, JSON
    [Description] NVARCHAR(500) NULL,
    [IsEncrypted] BIT DEFAULT 0 NOT NULL,
    [IsUserEditable] BIT DEFAULT 1 NOT NULL,
    [ValidationRule] NVARCHAR(500) NULL,
    [DefaultValue] NVARCHAR(MAX) NULL,
    [CreatedDate] DATETIME2 DEFAULT GETDATE() NOT NULL,
    [CreatedByUserID] INT NULL,
    [UpdatedDate] DATETIME2 DEFAULT GETDATE() NOT NULL,
    [UpdatedByUserID] INT NULL,
    CONSTRAINT [PK_SystemConfiguration] PRIMARY KEY CLUSTERED ([ConfigID] ASC),
    CONSTRAINT [FK_SystemConfiguration_Company] FOREIGN KEY ([CompanyID]) REFERENCES [dbo].[Company] ([CompanyID]),
    CONSTRAINT [UQ_SystemConfiguration] UNIQUE ([CompanyID], [Module], [ConfigKey])
);

-- Number Sequence table for document numbering
CREATE TABLE [dbo].[NumberSequence] (
    [SequenceID] INT IDENTITY(1,1) NOT NULL,
    [CompanyID] INT NULL,
    [SequenceType] NVARCHAR(50) NOT NULL, -- Quote, SalesOrder, Invoice, PO, etc.
    [SequenceFormat] NVARCHAR(100) NOT NULL, -- e.g., 'SO-{YYYY}-{0000}'
    [CurrentNumber] INT DEFAULT 0 NOT NULL,
    [MinNumber] INT DEFAULT 1 NOT NULL,
    [MaxNumber] INT DEFAULT 999999 NOT NULL,
    [Increment] INT DEFAULT 1 NOT NULL,
    [ResetFrequency] NVARCHAR(20) NULL, -- Never, Yearly, Monthly, Daily
    [LastResetDate] DATETIME2 NULL,
    [IsActive] BIT DEFAULT 1 NOT NULL,
    [CreatedDate] DATETIME2 DEFAULT GETDATE() NOT NULL,
    [UpdatedDate] DATETIME2 DEFAULT GETDATE() NOT NULL,
    CONSTRAINT [PK_NumberSequence] PRIMARY KEY CLUSTERED ([SequenceID] ASC),
    CONSTRAINT [FK_NumberSequence_Company] FOREIGN KEY ([CompanyID]) REFERENCES [dbo].[Company] ([CompanyID]),
    CONSTRAINT [UQ_NumberSequence] UNIQUE ([CompanyID], [SequenceType])
);

-- Email Template table
CREATE TABLE [dbo].[EmailTemplate] (
    [TemplateID] INT IDENTITY(1,1) NOT NULL,
    [CompanyID] INT NULL,
    [TemplateCode] NVARCHAR(50) NOT NULL,
    [TemplateName] NVARCHAR(100) NOT NULL,
    [TemplateType] NVARCHAR(50) NOT NULL, -- Quote, Order, Invoice, Statement, etc.
    [Subject] NVARCHAR(500) NOT NULL,
    [BodyHTML] NVARCHAR(MAX) NOT NULL,
    [BodyText] NVARCHAR(MAX) NULL,
    [FromEmail] NVARCHAR(255) NULL,
    [FromName] NVARCHAR(100) NULL,
    [CCEmails] NVARCHAR(500) NULL,
    [BCCEmails] NVARCHAR(500) NULL,
    [AttachPDF] BIT DEFAULT 1 NOT NULL,
    [IsActive] BIT DEFAULT 1 NOT NULL,
    [CreatedDate] DATETIME2 DEFAULT GETDATE() NOT NULL,
    [CreatedByUserID] INT NULL,
    [UpdatedDate] DATETIME2 DEFAULT GETDATE() NOT NULL,
    [UpdatedByUserID] INT NULL,
    CONSTRAINT [PK_EmailTemplate] PRIMARY KEY CLUSTERED ([TemplateID] ASC),
    CONSTRAINT [FK_EmailTemplate_Company] FOREIGN KEY ([CompanyID]) REFERENCES [dbo].[Company] ([CompanyID]),
    CONSTRAINT [UQ_EmailTemplate] UNIQUE ([CompanyID], [TemplateCode])
);

-- Notification Template table
CREATE TABLE [dbo].[NotificationTemplate] (
    [NotificationID] INT IDENTITY(1,1) NOT NULL,
    [NotificationCode] NVARCHAR(50) UNIQUE NOT NULL,
    [NotificationName] NVARCHAR(100) NOT NULL,
    [NotificationType] NVARCHAR(50) NOT NULL, -- Email, SMS, Push, InApp
    [EventType] NVARCHAR(100) NOT NULL, -- OrderCreated, PaymentReceived, StockLow, etc.
    [RecipientType] NVARCHAR(50) NOT NULL, -- User, Customer, Supplier, Role
    [Subject] NVARCHAR(500) NULL,
    [MessageTemplate] NVARCHAR(MAX) NOT NULL,
    [Priority] NVARCHAR(20) DEFAULT 'Normal' NOT NULL, -- Low, Normal, High, Critical
    [IsActive] BIT DEFAULT 1 NOT NULL,
    [CreatedDate] DATETIME2 DEFAULT GETDATE() NOT NULL,
    [UpdatedDate] DATETIME2 DEFAULT GETDATE() NOT NULL,
    CONSTRAINT [PK_NotificationTemplate] PRIMARY KEY CLUSTERED ([NotificationID] ASC)
);

-- Report Template table
CREATE TABLE [dbo].[ReportTemplate] (
    [ReportID] INT IDENTITY(1,1) NOT NULL,
    [ReportCode] NVARCHAR(50) UNIQUE NOT NULL,
    [ReportName] NVARCHAR(100) NOT NULL,
    [ReportCategory] NVARCHAR(50) NOT NULL, -- Sales, Purchase, Inventory, Financial, etc.
    [ReportType] NVARCHAR(20) NOT NULL, -- List, Summary, Detail, Chart
    [ReportFormat] NVARCHAR(20) DEFAULT 'PDF' NOT NULL, -- PDF, Excel, CSV, HTML
    [SQLQuery] NVARCHAR(MAX) NULL,
    [ReportDefinition] NVARCHAR(MAX) NULL, -- JSON or XML definition
    [Parameters] NVARCHAR(MAX) NULL, -- JSON format
    [IsScheduled] BIT DEFAULT 0 NOT NULL,
    [ScheduleFrequency] NVARCHAR(20) NULL, -- Daily, Weekly, Monthly
    [LastRunDate] DATETIME2 NULL,
    [IsActive] BIT DEFAULT 1 NOT NULL,
    [CreatedDate] DATETIME2 DEFAULT GETDATE() NOT NULL,
    [CreatedByUserID] INT NULL,
    [UpdatedDate] DATETIME2 DEFAULT GETDATE() NOT NULL,
    [UpdatedByUserID] INT NULL,
    CONSTRAINT [PK_ReportTemplate] PRIMARY KEY CLUSTERED ([ReportID] ASC)
);

-- Dashboard table
CREATE TABLE [dbo].[Dashboard] (
    [DashboardID] INT IDENTITY(1,1) NOT NULL,
    [DashboardName] NVARCHAR(100) NOT NULL,
    [UserID] INT NULL, -- NULL means system dashboard
    [IsDefault] BIT DEFAULT 0 NOT NULL,
    [IsPublic] BIT DEFAULT 0 NOT NULL,
    [Layout] NVARCHAR(MAX) NULL, -- JSON format
    [RefreshInterval] INT DEFAULT 300 NULL, -- Seconds
    [IsActive] BIT DEFAULT 1 NOT NULL,
    [CreatedDate] DATETIME2 DEFAULT GETDATE() NOT NULL,
    [CreatedByUserID] INT NULL,
    [UpdatedDate] DATETIME2 DEFAULT GETDATE() NOT NULL,
    CONSTRAINT [PK_Dashboard] PRIMARY KEY CLUSTERED ([DashboardID] ASC),
    CONSTRAINT [FK_Dashboard_User] FOREIGN KEY ([UserID]) REFERENCES [dbo].[User] ([UserID])
);

-- KPI Definition table
CREATE TABLE [dbo].[KPIDefinition] (
    [KPIID] INT IDENTITY(1,1) NOT NULL,
    [KPICode] NVARCHAR(50) UNIQUE NOT NULL,
    [KPIName] NVARCHAR(100) NOT NULL,
    [Category] NVARCHAR(50) NOT NULL, -- Sales, Finance, Operations, etc.
    [CalculationType] NVARCHAR(20) NOT NULL, -- Query, Formula, Aggregation
    [SQLQuery] NVARCHAR(MAX) NULL,
    [Formula] NVARCHAR(500) NULL,
    [UnitType] NVARCHAR(20) NULL, -- Currency, Percentage, Count, Days
    [TargetValue] DECIMAL(18,2) NULL,
    [ThresholdGood] DECIMAL(18,2) NULL,
    [ThresholdWarning] DECIMAL(18,2) NULL,
    [ThresholdCritical] DECIMAL(18,2) NULL,
    [TrendDirection] NVARCHAR(10) NULL, -- Up, Down, Neutral
    [RefreshFrequency] INT DEFAULT 3600 NULL, -- Seconds
    [IsActive] BIT DEFAULT 1 NOT NULL,
    [CreatedDate] DATETIME2 DEFAULT GETDATE() NOT NULL,
    [UpdatedDate] DATETIME2 DEFAULT GETDATE() NOT NULL,
    CONSTRAINT [PK_KPIDefinition] PRIMARY KEY CLUSTERED ([KPIID] ASC)
);

-- API Key table
CREATE TABLE [dbo].[APIKey] (
    [APIKeyID] INT IDENTITY(1,1) NOT NULL,
    [APIKey] NVARCHAR(255) UNIQUE NOT NULL,
    [APISecret] NVARCHAR(255) NOT NULL,
    [ClientName] NVARCHAR(100) NOT NULL,
    [Description] NVARCHAR(500) NULL,
    [Permissions] NVARCHAR(MAX) NULL, -- JSON format
    [IPWhitelist] NVARCHAR(MAX) NULL, -- Comma-separated
    [RateLimit] INT DEFAULT 1000 NULL, -- Requests per hour
    [ExpiryDate] DATETIME2 NULL,
    [LastUsedDate] DATETIME2 NULL,
    [IsActive] BIT DEFAULT 1 NOT NULL,
    [CreatedDate] DATETIME2 DEFAULT GETDATE() NOT NULL,
    [CreatedByUserID] INT NULL,
    [UpdatedDate] DATETIME2 DEFAULT GETDATE() NOT NULL,
    CONSTRAINT [PK_APIKey] PRIMARY KEY CLUSTERED ([APIKeyID] ASC)
);

-- Integration Log table
CREATE TABLE [dbo].[IntegrationLog] (
    [LogID] BIGINT IDENTITY(1,1) NOT NULL,
    [IntegrationType] NVARCHAR(50) NOT NULL, -- API, EDI, Import, Export
    [Direction] NVARCHAR(10) NOT NULL, -- Inbound, Outbound
    [EntityType] NVARCHAR(50) NOT NULL,
    [EntityID] INT NULL,
    [APIKeyID] INT NULL,
    [RequestDate] DATETIME2 DEFAULT GETDATE() NOT NULL,
    [RequestData] NVARCHAR(MAX) NULL,
    [ResponseDate] DATETIME2 NULL,
    [ResponseData] NVARCHAR(MAX) NULL,
    [Status] NVARCHAR(20) NOT NULL, -- Success, Failed, Error
    [ErrorMessage] NVARCHAR(MAX) NULL,
    [ProcessingTime] INT NULL, -- Milliseconds
    [IPAddress] NVARCHAR(45) NULL,
    [UserAgent] NVARCHAR(500) NULL,
    CONSTRAINT [PK_IntegrationLog] PRIMARY KEY CLUSTERED ([LogID] ASC),
    CONSTRAINT [FK_IntegrationLog_APIKey] FOREIGN KEY ([APIKeyID]) REFERENCES [dbo].[APIKey] ([APIKeyID])
);

-- Webhook Configuration table
CREATE TABLE [dbo].[WebhookConfiguration] (
    [WebhookID] INT IDENTITY(1,1) NOT NULL,
    [WebhookName] NVARCHAR(100) NOT NULL,
    [EventType] NVARCHAR(100) NOT NULL, -- OrderCreated, InvoicePaid, etc.
    [URL] NVARCHAR(500) NOT NULL,
    [Method] NVARCHAR(10) DEFAULT 'POST' NOT NULL,
    [Headers] NVARCHAR(MAX) NULL, -- JSON format
    [AuthType] NVARCHAR(20) NULL, -- None, Basic, Bearer, APIKey
    [AuthCredentials] NVARCHAR(500) NULL,
    [RetryCount] INT DEFAULT 3 NOT NULL,
    [TimeoutSeconds] INT DEFAULT 30 NOT NULL,
    [IsActive] BIT DEFAULT 1 NOT NULL,
    [LastTriggeredDate] DATETIME2 NULL,
    [CreatedDate] DATETIME2 DEFAULT GETDATE() NOT NULL,
    [CreatedByUserID] INT NULL,
    [UpdatedDate] DATETIME2 DEFAULT GETDATE() NOT NULL,
    CONSTRAINT [PK_WebhookConfiguration] PRIMARY KEY CLUSTERED ([WebhookID] ASC)
);

-- EDI Configuration table
CREATE TABLE [dbo].[EDIConfiguration] (
    [EDIConfigID] INT IDENTITY(1,1) NOT NULL,
    [PartnerType] NVARCHAR(20) NOT NULL, -- Customer, Supplier
    [PartnerID] INT NOT NULL,
    [DocumentType] NVARCHAR(50) NOT NULL, -- Order, Invoice, ASN, etc.
    [Direction] NVARCHAR(10) NOT NULL, -- Inbound, Outbound
    [Protocol] NVARCHAR(20) NOT NULL, -- FTP, SFTP, AS2, API
    [ConnectionSettings] NVARCHAR(MAX) NOT NULL, -- JSON format
    [FileFormat] NVARCHAR(20) NOT NULL, -- XML, JSON, CSV, EDI
    [MappingRules] NVARCHAR(MAX) NULL, -- JSON or XSLT
    [Schedule] NVARCHAR(100) NULL, -- Cron expression
    [IsActive] BIT DEFAULT 1 NOT NULL,
    [LastRunDate] DATETIME2 NULL,
    [CreatedDate] DATETIME2 DEFAULT GETDATE() NOT NULL,
    [UpdatedDate] DATETIME2 DEFAULT GETDATE() NOT NULL,
    CONSTRAINT [PK_EDIConfiguration] PRIMARY KEY CLUSTERED ([EDIConfigID] ASC)
);

-- Data Export table
CREATE TABLE [dbo].[DataExport] (
    [ExportID] INT IDENTITY(1,1) NOT NULL,
    [ExportName] NVARCHAR(100) NOT NULL,
    [ExportType] NVARCHAR(50) NOT NULL, -- Report, Data, Backup
    [EntityType] NVARCHAR(50) NOT NULL,
    [Filters] NVARCHAR(MAX) NULL, -- JSON format
    [Format] NVARCHAR(20) NOT NULL, -- CSV, Excel, JSON, XML
    [Status] NVARCHAR(20) DEFAULT 'Pending' NOT NULL, -- Pending, Processing, Completed, Failed
    [FilePath] NVARCHAR(500) NULL,
    [FileSize] BIGINT NULL,
    [RecordCount] INT NULL,
    [RequestedByUserID] INT NOT NULL,
    [RequestedDate] DATETIME2 DEFAULT GETDATE() NOT NULL,
    [CompletedDate] DATETIME2 NULL,
    [ExpiryDate] DATETIME2 NULL,
    [DownloadCount] INT DEFAULT 0 NOT NULL,
    [ErrorMessage] NVARCHAR(MAX) NULL,
    CONSTRAINT [PK_DataExport] PRIMARY KEY CLUSTERED ([ExportID] ASC),
    CONSTRAINT [FK_DataExport_User] FOREIGN KEY ([RequestedByUserID]) REFERENCES [dbo].[User] ([UserID])
);

-- Saved Search table
CREATE TABLE [dbo].[SavedSearch] (
    [SearchID] INT IDENTITY(1,1) NOT NULL,
    [UserID] INT NOT NULL,
    [SearchName] NVARCHAR(100) NOT NULL,
    [EntityType] NVARCHAR(50) NOT NULL,
    [SearchCriteria] NVARCHAR(MAX) NOT NULL, -- JSON format
    [SortOrder] NVARCHAR(100) NULL,
    [IsDefault] BIT DEFAULT 0 NOT NULL,
    [IsPublic] BIT DEFAULT 0 NOT NULL,
    [CreatedDate] DATETIME2 DEFAULT GETDATE() NOT NULL,
    [UpdatedDate] DATETIME2 DEFAULT GETDATE() NOT NULL,
    CONSTRAINT [PK_SavedSearch] PRIMARY KEY CLUSTERED ([SearchID] ASC),
    CONSTRAINT [FK_SavedSearch_User] FOREIGN KEY ([UserID]) REFERENCES [dbo].[User] ([UserID])
);

-- Create indexes
CREATE INDEX [IX_Company_CompanyCode] ON [dbo].[Company] ([CompanyCode]);
CREATE INDEX [IX_Branch_CompanyID] ON [dbo].[Branch] ([CompanyID]);
CREATE INDEX [IX_SystemConfiguration_Module_ConfigKey] ON [dbo].[SystemConfiguration] ([Module], [ConfigKey]);
CREATE INDEX [IX_NumberSequence_SequenceType] ON [dbo].[NumberSequence] ([SequenceType]);
CREATE INDEX [IX_EmailTemplate_TemplateType] ON [dbo].[EmailTemplate] ([TemplateType]);
CREATE INDEX [IX_NotificationTemplate_EventType] ON [dbo].[NotificationTemplate] ([EventType]);
CREATE INDEX [IX_ReportTemplate_ReportCategory] ON [dbo].[ReportTemplate] ([ReportCategory]);
CREATE INDEX [IX_Dashboard_UserID] ON [dbo].[Dashboard] ([UserID]);
CREATE INDEX [IX_KPIDefinition_Category] ON [dbo].[KPIDefinition] ([Category]);
CREATE INDEX [IX_APIKey_APIKey] ON [dbo].[APIKey] ([APIKey]);
CREATE INDEX [IX_IntegrationLog_RequestDate] ON [dbo].[IntegrationLog] ([RequestDate]);
CREATE INDEX [IX_IntegrationLog_EntityType_EntityID] ON [dbo].[IntegrationLog] ([EntityType], [EntityID]);
CREATE INDEX [IX_WebhookConfiguration_EventType] ON [dbo].[WebhookConfiguration] ([EventType]);
CREATE INDEX [IX_DataExport_RequestedByUserID] ON [dbo].[DataExport] ([RequestedByUserID]);
CREATE INDEX [IX_DataExport_Status] ON [dbo].[DataExport] ([Status]);
CREATE INDEX [IX_SavedSearch_UserID_EntityType] ON [dbo].[SavedSearch] ([UserID], [EntityType]);

-- Insert default system configurations
INSERT INTO [dbo].[SystemConfiguration] ([Module], [ConfigKey], [ConfigValue], [DataType], [Description], [IsUserEditable])
VALUES 
    ('System', 'DateFormat', 'dd/MM/yyyy', 'String', 'Default date format', 1),
    ('System', 'TimeFormat', 'HH:mm', 'String', 'Default time format', 1),
    ('System', 'PageSize', '20', 'Integer', 'Default page size for lists', 1),
    ('System', 'SessionTimeout', '30', 'Integer', 'Session timeout in minutes', 1),
    ('System', 'PasswordMinLength', '8', 'Integer', 'Minimum password length', 1),
    ('System', 'PasswordRequireUppercase', 'true', 'Boolean', 'Require uppercase in password', 1),
    ('System', 'PasswordRequireNumber', 'true', 'Boolean', 'Require number in password', 1),
    ('System', 'PasswordRequireSpecial', 'true', 'Boolean', 'Require special character in password', 1),
    ('Sales', 'QuoteValidityDays', '30', 'Integer', 'Default quote validity in days', 1),
    ('Sales', 'AllowBackorder', 'true', 'Boolean', 'Allow backorders by default', 1),
    ('Purchase', 'RequireApproval', 'true', 'Boolean', 'Require PO approval', 1),
    ('Purchase', 'ApprovalThreshold', '1000', 'Decimal', 'PO approval threshold amount', 1),
    ('Inventory', 'AllowNegativeStock', 'false', 'Boolean', 'Allow negative stock levels', 1),
    ('Inventory', 'StockValuationMethod', 'FIFO', 'String', 'Stock valuation method (FIFO/LIFO/Average)', 1),
    ('Finance', 'FiscalYearStart', '4', 'Integer', 'Fiscal year start month', 1),
    ('Finance', 'TaxInclusive', 'false', 'Boolean', 'Prices include tax by default', 1);