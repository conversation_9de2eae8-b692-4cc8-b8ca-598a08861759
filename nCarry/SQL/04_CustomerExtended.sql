-- Customer Extended Tables Creation Script
-- Purpose: Additional tables for comprehensive customer management
-- Complements the base Customer table with advanced features

-- Customer Contact table for multiple contacts per customer
CREATE TABLE [dbo].[CustomerContact] (
    [ContactID] INT IDENTITY(1,1) NOT NULL,
    [CustomerID] INT NOT NULL,
    [ContactName] NVARCHAR(255) NOT NULL,
    [JobTitle] NVARCHAR(100) NULL,
    [Department] NVARCHAR(100) NULL,
    [Email] NVARCHAR(255) NULL,
    [Phone] NVARCHAR(50) NULL,
    [Mobile] NVARCHAR(50) NULL,
    [Extension] NVARCHAR(20) NULL,
    [IsPrimary] BIT DEFAULT 0 NOT NULL,
    [ContactType] NVARCHAR(50) NULL, -- Sales, Accounts, Technical, Management
    [PreferredContactMethod] NVARCHAR(20) NULL, -- Email, Phone, Mobile
    [BestTimeToContact] NVARCHAR(100) NULL,
    [Notes] NVARCHAR(MAX) NULL,
    [Birthday] DATE NULL,
    [IsActive] BIT DEFAULT 1 NOT NULL,
    [CreatedDate] DATETIME2 DEFAULT GETDATE() NOT NULL,
    [CreatedByUserID] INT NULL,
    [UpdatedDate] DATETIME2 DEFAULT GETDATE() NOT NULL,
    [UpdatedByUserID] INT NULL,
    CONSTRAINT [PK_CustomerContact] PRIMARY KEY CLUSTERED ([ContactID] ASC),
    CONSTRAINT [FK_CustomerContact_Customer] FOREIGN KEY ([CustomerID]) REFERENCES [dbo].[Customer] ([CustomerID])
);

-- Customer Price List table for customer-specific pricing
CREATE TABLE [dbo].[CustomerPriceList] (
    [PriceListID] INT IDENTITY(1,1) NOT NULL,
    [PriceListCode] NVARCHAR(20) UNIQUE NOT NULL,
    [PriceListName] NVARCHAR(100) NOT NULL,
    [Description] NVARCHAR(500) NULL,
    [CustomerID] INT NULL, -- NULL means applicable to multiple customers
    [CustomerGroupID] INT NULL, -- For group pricing
    [Currency] NVARCHAR(3) DEFAULT 'GBP' NOT NULL,
    [EffectiveFrom] DATETIME2 NOT NULL,
    [EffectiveTo] DATETIME2 NULL,
    [DiscountPercent] DECIMAL(5,2) DEFAULT 0 NULL,
    [MarkupPercent] DECIMAL(5,2) DEFAULT 0 NULL,
    [Priority] INT DEFAULT 100 NOT NULL, -- Lower number = higher priority
    [IsActive] BIT DEFAULT 1 NOT NULL,
    [CreatedDate] DATETIME2 DEFAULT GETDATE() NOT NULL,
    [CreatedByUserID] INT NULL,
    [UpdatedDate] DATETIME2 DEFAULT GETDATE() NOT NULL,
    [UpdatedByUserID] INT NULL,
    CONSTRAINT [PK_CustomerPriceList] PRIMARY KEY CLUSTERED ([PriceListID] ASC),
    CONSTRAINT [FK_CustomerPriceList_Customer] FOREIGN KEY ([CustomerID]) REFERENCES [dbo].[Customer] ([CustomerID]),
    CONSTRAINT [CHK_CustomerPriceList_Discount] CHECK ([DiscountPercent] >= 0 AND [DiscountPercent] <= 100)
);

-- Customer Price List Items for specific product pricing
CREATE TABLE [dbo].[CustomerPriceListItem] (
    [PriceListItemID] INT IDENTITY(1,1) NOT NULL,
    [PriceListID] INT NOT NULL,
    [ProductID] INT NOT NULL,
    [UnitPrice] DECIMAL(18,4) NOT NULL,
    [MinQuantity] DECIMAL(18,3) DEFAULT 1 NOT NULL,
    [MaxQuantity] DECIMAL(18,3) NULL,
    [DiscountPercent] DECIMAL(5,2) DEFAULT 0 NULL,
    [FixedDiscount] DECIMAL(18,2) DEFAULT 0 NULL,
    [Notes] NVARCHAR(500) NULL,
    [IsActive] BIT DEFAULT 1 NOT NULL,
    [CreatedDate] DATETIME2 DEFAULT GETDATE() NOT NULL,
    [UpdatedDate] DATETIME2 DEFAULT GETDATE() NOT NULL,
    CONSTRAINT [PK_CustomerPriceListItem] PRIMARY KEY CLUSTERED ([PriceListItemID] ASC),
    CONSTRAINT [FK_CustomerPriceListItem_PriceList] FOREIGN KEY ([PriceListID]) REFERENCES [dbo].[CustomerPriceList] ([PriceListID])
);

-- Customer Credit History table for tracking credit changes
CREATE TABLE [dbo].[CustomerCreditHistory] (
    [CreditHistoryID] INT IDENTITY(1,1) NOT NULL,
    [CustomerID] INT NOT NULL,
    [ChangeDate] DATETIME2 DEFAULT GETDATE() NOT NULL,
    [ChangeType] NVARCHAR(50) NOT NULL, -- Increase, Decrease, Review, Block, Unblock
    [OldCreditLimit] DECIMAL(18,2) NULL,
    [NewCreditLimit] DECIMAL(18,2) NULL,
    [OldCreditStatus] NVARCHAR(20) NULL,
    [NewCreditStatus] NVARCHAR(20) NULL,
    [Reason] NVARCHAR(500) NOT NULL,
    [AuthorizedByUserID] INT NULL,
    [ReferenceType] NVARCHAR(50) NULL, -- Order, Payment, Review, etc.
    [ReferenceID] INT NULL,
    [Notes] NVARCHAR(MAX) NULL,
    [CreatedDate] DATETIME2 DEFAULT GETDATE() NOT NULL,
    [CreatedByUserID] INT NULL,
    CONSTRAINT [PK_CustomerCreditHistory] PRIMARY KEY CLUSTERED ([CreditHistoryID] ASC),
    CONSTRAINT [FK_CustomerCreditHistory_Customer] FOREIGN KEY ([CustomerID]) REFERENCES [dbo].[Customer] ([CustomerID])
);

-- Customer Sales History view for analytics
CREATE VIEW [dbo].[CustomerSalesHistory]
AS
SELECT 
    c.CustomerID,
    c.CustomerCode,
    c.CustomerName,
    c.CustomerType,
    c.CustomerStatus,
    c.SalesRepID,
    c.TerritoryID,
    YEAR(si.InvoiceDate) AS SalesYear,
    MONTH(si.InvoiceDate) AS SalesMonth,
    COUNT(DISTINCT si.InvoiceID) AS InvoiceCount,
    SUM(si.InvoiceNetTotal) AS TotalNetSales,
    SUM(si.InvoiceTaxTotal) AS TotalTax,
    SUM(si.InvoiceGrossTotal) AS TotalGrossSales,
    AVG(si.InvoiceGrossTotal) AS AverageInvoiceValue,
    MIN(si.InvoiceDate) AS FirstInvoiceDate,
    MAX(si.InvoiceDate) AS LastInvoiceDate,
    DATEDIFF(DAY, MIN(si.InvoiceDate), MAX(si.InvoiceDate)) AS CustomerLifetimeDays
FROM 
    [dbo].[Customer] c
    LEFT JOIN [dbo].[SalesInvoice] si ON c.CustomerID = si.CustomerID
WHERE 
    c.IsDeleted = 0
GROUP BY 
    c.CustomerID,
    c.CustomerCode,
    c.CustomerName,
    c.CustomerType,
    c.CustomerStatus,
    c.SalesRepID,
    c.TerritoryID,
    YEAR(si.InvoiceDate),
    MONTH(si.InvoiceDate);

-- Customer Group table for grouping customers
CREATE TABLE [dbo].[CustomerGroup] (
    [CustomerGroupID] INT IDENTITY(1,1) NOT NULL,
    [GroupCode] NVARCHAR(20) UNIQUE NOT NULL,
    [GroupName] NVARCHAR(100) NOT NULL,
    [Description] NVARCHAR(500) NULL,
    [ParentGroupID] INT NULL, -- For hierarchical groups
    [DiscountPercent] DECIMAL(5,2) DEFAULT 0 NULL,
    [PaymentTermDays] INT NULL,
    [CreditLimit] DECIMAL(18,2) NULL,
    [IsActive] BIT DEFAULT 1 NOT NULL,
    [CreatedDate] DATETIME2 DEFAULT GETDATE() NOT NULL,
    [UpdatedDate] DATETIME2 DEFAULT GETDATE() NOT NULL,
    CONSTRAINT [PK_CustomerGroup] PRIMARY KEY CLUSTERED ([CustomerGroupID] ASC),
    CONSTRAINT [FK_CustomerGroup_Parent] FOREIGN KEY ([ParentGroupID]) REFERENCES [dbo].[CustomerGroup] ([CustomerGroupID])
);

-- Customer Group Members mapping
CREATE TABLE [dbo].[CustomerGroupMember] (
    [MemberID] INT IDENTITY(1,1) NOT NULL,
    [CustomerGroupID] INT NOT NULL,
    [CustomerID] INT NOT NULL,
    [JoinDate] DATETIME2 DEFAULT GETDATE() NOT NULL,
    [LeaveDate] DATETIME2 NULL,
    [IsActive] BIT DEFAULT 1 NOT NULL,
    CONSTRAINT [PK_CustomerGroupMember] PRIMARY KEY CLUSTERED ([MemberID] ASC),
    CONSTRAINT [FK_CustomerGroupMember_Group] FOREIGN KEY ([CustomerGroupID]) REFERENCES [dbo].[CustomerGroup] ([CustomerGroupID]),
    CONSTRAINT [FK_CustomerGroupMember_Customer] FOREIGN KEY ([CustomerID]) REFERENCES [dbo].[Customer] ([CustomerID]),
    CONSTRAINT [UQ_CustomerGroupMember] UNIQUE ([CustomerGroupID], [CustomerID])
);

-- Create indexes
CREATE INDEX [IX_CustomerContact_CustomerID] ON [dbo].[CustomerContact] ([CustomerID]);
CREATE INDEX [IX_CustomerContact_Email] ON [dbo].[CustomerContact] ([Email]);
CREATE INDEX [IX_CustomerPriceList_CustomerID] ON [dbo].[CustomerPriceList] ([CustomerID]);
CREATE INDEX [IX_CustomerPriceList_EffectiveDates] ON [dbo].[CustomerPriceList] ([EffectiveFrom], [EffectiveTo]);
CREATE INDEX [IX_CustomerPriceListItem_PriceListID] ON [dbo].[CustomerPriceListItem] ([PriceListID]);
CREATE INDEX [IX_CustomerPriceListItem_ProductID] ON [dbo].[CustomerPriceListItem] ([ProductID]);
CREATE INDEX [IX_CustomerCreditHistory_CustomerID] ON [dbo].[CustomerCreditHistory] ([CustomerID]);
CREATE INDEX [IX_CustomerCreditHistory_ChangeDate] ON [dbo].[CustomerCreditHistory] ([ChangeDate]);
CREATE INDEX [IX_CustomerGroupMember_CustomerID] ON [dbo].[CustomerGroupMember] ([CustomerID]);
CREATE INDEX [IX_CustomerGroupMember_CustomerGroupID] ON [dbo].[CustomerGroupMember] ([CustomerGroupID]);