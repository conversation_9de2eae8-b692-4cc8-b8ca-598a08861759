-- Product Management Tables Creation Script
-- Purpose: Comprehensive product catalog with categories, variants, and pricing
-- Core tables for inventory management system

-- Unit of Measure table
CREATE TABLE [dbo].[UnitOfMeasure] (
    [UOMID] INT IDENTITY(1,1) NOT NULL,
    [UOMCode] NVARCHAR(20) UNIQUE NOT NULL,
    [UOMName] NVARCHAR(50) NOT NULL,
    [UOMType] NVARCHAR(20) NOT NULL, -- Weight, Volume, Length, Count, Time
    [BaseUnit] BIT DEFAULT 0 NOT NULL, -- Is this the base unit for its type
    [ConversionFactor] DECIMAL(18,6) DEFAULT 1 NOT NULL, -- To base unit
    [DecimalPlaces] INT DEFAULT 2 NOT NULL,
    [Symbol] NVARCHAR(10) NULL,
    [IsActive] BIT DEFAULT 1 NOT NULL,
    [CreatedDate] DATETIME2 DEFAULT GETDATE() NOT NULL,
    [UpdatedDate] DATETIME2 DEFAULT GETDATE() NOT NULL,
    CONSTRAINT [PK_UnitOfMeasure] PRIMARY KEY CLUSTERED ([UOMID] ASC)
);

-- Product Category table (hierarchical)
CREATE TABLE [dbo].[ProductCategory] (
    [CategoryID] INT IDENTITY(1,1) NOT NULL,
    [CategoryCode] NVARCHAR(20) UNIQUE NOT NULL,
    [CategoryName] NVARCHAR(100) NOT NULL,
    [ParentCategoryID] INT NULL,
    [CategoryPath] NVARCHAR(500) NULL, -- /Electronics/Computers/Laptops
    [Level] INT DEFAULT 0 NOT NULL,
    [Description] NVARCHAR(500) NULL,
    [ImageURL] NVARCHAR(500) NULL,
    [SortOrder] INT DEFAULT 100 NOT NULL,
    [IsActive] BIT DEFAULT 1 NOT NULL,
    [CreatedDate] DATETIME2 DEFAULT GETDATE() NOT NULL,
    [CreatedByUserID] INT NULL,
    [UpdatedDate] DATETIME2 DEFAULT GETDATE() NOT NULL,
    [UpdatedByUserID] INT NULL,
    CONSTRAINT [PK_ProductCategory] PRIMARY KEY CLUSTERED ([CategoryID] ASC),
    CONSTRAINT [FK_ProductCategory_Parent] FOREIGN KEY ([ParentCategoryID]) REFERENCES [dbo].[ProductCategory] ([CategoryID])
);

-- Product table (master product information)
CREATE TABLE [dbo].[Product] (
    [ProductID] INT IDENTITY(1,1) NOT NULL,
    [ProductCode] NVARCHAR(50) UNIQUE NOT NULL,
    [ProductName] NVARCHAR(255) NOT NULL,
    [Description] NVARCHAR(MAX) NULL,
    [ShortDescription] NVARCHAR(500) NULL,
    [CategoryID] INT NOT NULL,
    [ProductType] NVARCHAR(20) DEFAULT 'Standard' NOT NULL, -- Standard, Service, Kit, Bundle
    [Status] NVARCHAR(20) DEFAULT 'Active' NOT NULL, -- Active, Inactive, Discontinued, Pending
    
    -- Physical attributes
    [Weight] DECIMAL(18,3) NULL,
    [WeightUOMID] INT NULL,
    [Length] DECIMAL(18,3) NULL,
    [Width] DECIMAL(18,3) NULL,
    [Height] DECIMAL(18,3) NULL,
    [DimensionUOMID] INT NULL,
    [Volume] DECIMAL(18,3) NULL,
    [VolumeUOMID] INT NULL,
    
    -- Inventory settings
    [TrackInventory] BIT DEFAULT 1 NOT NULL,
    [InventoryUOMID] INT NOT NULL,
    [PurchaseUOMID] INT NULL,
    [SalesUOMID] INT NULL,
    [MinStockLevel] DECIMAL(18,3) DEFAULT 0 NULL,
    [MaxStockLevel] DECIMAL(18,3) NULL,
    [ReorderPoint] DECIMAL(18,3) NULL,
    [ReorderQuantity] DECIMAL(18,3) NULL,
    [LeadTimeDays] INT DEFAULT 7 NULL,
    
    -- Pricing defaults
    [StandardCost] DECIMAL(18,4) DEFAULT 0 NOT NULL,
    [ListPrice] DECIMAL(18,4) DEFAULT 0 NOT NULL,
    [SellPrice] DECIMAL(18,4) DEFAULT 0 NOT NULL,
    [TaxCodeID] INT NULL,
    [IsTaxExempt] BIT DEFAULT 0 NOT NULL,
    
    -- Tracking settings
    [RequiresBatch] BIT DEFAULT 0 NOT NULL,
    [RequiresSerial] BIT DEFAULT 0 NOT NULL,
    [HasVariants] BIT DEFAULT 0 NOT NULL,
    [IsKit] BIT DEFAULT 0 NOT NULL,
    [AllowBackorder] BIT DEFAULT 0 NOT NULL,
    [AllowPreorder] BIT DEFAULT 0 NOT NULL,
    
    -- Additional information
    [Brand] NVARCHAR(100) NULL,
    [Manufacturer] NVARCHAR(100) NULL,
    [ManufacturerPartNumber] NVARCHAR(100) NULL,
    [CountryOfOrigin] NVARCHAR(100) NULL,
    [HSCode] NVARCHAR(20) NULL, -- Harmonized System Code
    [CommodityCode] NVARCHAR(20) NULL,
    [WarrantyDays] INT DEFAULT 0 NULL,
    [ShelfLifeDays] INT NULL,
    [Notes] NVARCHAR(MAX) NULL,
    
    -- System fields
    [IsActive] BIT DEFAULT 1 NOT NULL,
    [CreatedDate] DATETIME2 DEFAULT GETDATE() NOT NULL,
    [CreatedByUserID] INT NULL,
    [UpdatedDate] DATETIME2 DEFAULT GETDATE() NOT NULL,
    [UpdatedByUserID] INT NULL,
    [IsDeleted] BIT DEFAULT 0 NOT NULL,
    [DeletedDate] DATETIME2 NULL,
    [DeletedByUserID] INT NULL,
    
    CONSTRAINT [PK_Product] PRIMARY KEY CLUSTERED ([ProductID] ASC),
    CONSTRAINT [FK_Product_Category] FOREIGN KEY ([CategoryID]) REFERENCES [dbo].[ProductCategory] ([CategoryID]),
    CONSTRAINT [FK_Product_WeightUOM] FOREIGN KEY ([WeightUOMID]) REFERENCES [dbo].[UnitOfMeasure] ([UOMID]),
    CONSTRAINT [FK_Product_DimensionUOM] FOREIGN KEY ([DimensionUOMID]) REFERENCES [dbo].[UnitOfMeasure] ([UOMID]),
    CONSTRAINT [FK_Product_VolumeUOM] FOREIGN KEY ([VolumeUOMID]) REFERENCES [dbo].[UnitOfMeasure] ([UOMID]),
    CONSTRAINT [FK_Product_InventoryUOM] FOREIGN KEY ([InventoryUOMID]) REFERENCES [dbo].[UnitOfMeasure] ([UOMID]),
    CONSTRAINT [FK_Product_PurchaseUOM] FOREIGN KEY ([PurchaseUOMID]) REFERENCES [dbo].[UnitOfMeasure] ([UOMID]),
    CONSTRAINT [FK_Product_SalesUOM] FOREIGN KEY ([SalesUOMID]) REFERENCES [dbo].[UnitOfMeasure] ([UOMID])
);

-- Product Variant table for variations (size, color, etc.)
CREATE TABLE [dbo].[ProductVariant] (
    [VariantID] INT IDENTITY(1,1) NOT NULL,
    [ProductID] INT NOT NULL,
    [VariantCode] NVARCHAR(50) UNIQUE NOT NULL,
    [VariantName] NVARCHAR(255) NOT NULL,
    [VariantType] NVARCHAR(50) NULL, -- Size, Color, Style, Material
    [VariantValue] NVARCHAR(100) NULL, -- Large, Red, Cotton, etc.
    [SortOrder] INT DEFAULT 100 NOT NULL,
    [AdditionalCost] DECIMAL(18,4) DEFAULT 0 NULL,
    [AdditionalPrice] DECIMAL(18,4) DEFAULT 0 NULL,
    [Weight] DECIMAL(18,3) NULL,
    [IsActive] BIT DEFAULT 1 NOT NULL,
    [CreatedDate] DATETIME2 DEFAULT GETDATE() NOT NULL,
    [UpdatedDate] DATETIME2 DEFAULT GETDATE() NOT NULL,
    CONSTRAINT [PK_ProductVariant] PRIMARY KEY CLUSTERED ([VariantID] ASC),
    CONSTRAINT [FK_ProductVariant_Product] FOREIGN KEY ([ProductID]) REFERENCES [dbo].[Product] ([ProductID])
);

-- Barcode table for multiple barcodes per product
CREATE TABLE [dbo].[Barcode] (
    [BarcodeID] INT IDENTITY(1,1) NOT NULL,
    [ProductID] INT NOT NULL,
    [VariantID] INT NULL,
    [BarcodeNumber] NVARCHAR(100) NOT NULL,
    [BarcodeType] NVARCHAR(20) NOT NULL, -- EAN13, UPC, Code128, QR
    [IsPrimary] BIT DEFAULT 0 NOT NULL,
    [PackLevel] NVARCHAR(20) DEFAULT 'Each' NOT NULL, -- Each, Case, Pallet
    [Quantity] INT DEFAULT 1 NOT NULL,
    [IsActive] BIT DEFAULT 1 NOT NULL,
    [CreatedDate] DATETIME2 DEFAULT GETDATE() NOT NULL,
    [UpdatedDate] DATETIME2 DEFAULT GETDATE() NOT NULL,
    CONSTRAINT [PK_Barcode] PRIMARY KEY CLUSTERED ([BarcodeID] ASC),
    CONSTRAINT [FK_Barcode_Product] FOREIGN KEY ([ProductID]) REFERENCES [dbo].[Product] ([ProductID]),
    CONSTRAINT [FK_Barcode_Variant] FOREIGN KEY ([VariantID]) REFERENCES [dbo].[ProductVariant] ([VariantID]),
    CONSTRAINT [UQ_Barcode_Number] UNIQUE ([BarcodeNumber])
);

-- Product Image table
CREATE TABLE [dbo].[ProductImage] (
    [ImageID] INT IDENTITY(1,1) NOT NULL,
    [ProductID] INT NOT NULL,
    [VariantID] INT NULL,
    [ImageName] NVARCHAR(255) NOT NULL,
    [ImagePath] NVARCHAR(500) NULL,
    [ImageData] VARBINARY(MAX) NULL,
    [ImageType] NVARCHAR(20) NOT NULL, -- Main, Thumbnail, Gallery, Technical
    [MimeType] NVARCHAR(100) NULL,
    [FileSize] BIGINT NULL,
    [SortOrder] INT DEFAULT 100 NOT NULL,
    [IsPrimary] BIT DEFAULT 0 NOT NULL,
    [CreatedDate] DATETIME2 DEFAULT GETDATE() NOT NULL,
    [CreatedByUserID] INT NULL,
    CONSTRAINT [PK_ProductImage] PRIMARY KEY CLUSTERED ([ImageID] ASC),
    CONSTRAINT [FK_ProductImage_Product] FOREIGN KEY ([ProductID]) REFERENCES [dbo].[Product] ([ProductID]),
    CONSTRAINT [FK_ProductImage_Variant] FOREIGN KEY ([VariantID]) REFERENCES [dbo].[ProductVariant] ([VariantID])
);

-- Product Pricing table for multiple price tiers
CREATE TABLE [dbo].[ProductPricing] (
    [PricingID] INT IDENTITY(1,1) NOT NULL,
    [ProductID] INT NOT NULL,
    [VariantID] INT NULL,
    [PriceType] NVARCHAR(50) NOT NULL, -- Standard, Promotional, Volume, Contract
    [MinQuantity] DECIMAL(18,3) DEFAULT 1 NOT NULL,
    [MaxQuantity] DECIMAL(18,3) NULL,
    [UnitPrice] DECIMAL(18,4) NOT NULL,
    [Currency] NVARCHAR(3) DEFAULT 'GBP' NOT NULL,
    [EffectiveFrom] DATETIME2 NOT NULL,
    [EffectiveTo] DATETIME2 NULL,
    [CustomerGroupID] INT NULL,
    [Notes] NVARCHAR(500) NULL,
    [IsActive] BIT DEFAULT 1 NOT NULL,
    [CreatedDate] DATETIME2 DEFAULT GETDATE() NOT NULL,
    [CreatedByUserID] INT NULL,
    [UpdatedDate] DATETIME2 DEFAULT GETDATE() NOT NULL,
    [UpdatedByUserID] INT NULL,
    CONSTRAINT [PK_ProductPricing] PRIMARY KEY CLUSTERED ([PricingID] ASC),
    CONSTRAINT [FK_ProductPricing_Product] FOREIGN KEY ([ProductID]) REFERENCES [dbo].[Product] ([ProductID]),
    CONSTRAINT [FK_ProductPricing_Variant] FOREIGN KEY ([VariantID]) REFERENCES [dbo].[ProductVariant] ([VariantID])
);

-- Product Price History for tracking price changes
CREATE TABLE [dbo].[ProductPriceHistory] (
    [HistoryID] INT IDENTITY(1,1) NOT NULL,
    [ProductID] INT NOT NULL,
    [ChangeDate] DATETIME2 DEFAULT GETDATE() NOT NULL,
    [PriceType] NVARCHAR(50) NOT NULL,
    [OldPrice] DECIMAL(18,4) NULL,
    [NewPrice] DECIMAL(18,4) NOT NULL,
    [ChangeReason] NVARCHAR(500) NULL,
    [ApprovedByUserID] INT NULL,
    [CreatedDate] DATETIME2 DEFAULT GETDATE() NOT NULL,
    [CreatedByUserID] INT NULL,
    CONSTRAINT [PK_ProductPriceHistory] PRIMARY KEY CLUSTERED ([HistoryID] ASC),
    CONSTRAINT [FK_ProductPriceHistory_Product] FOREIGN KEY ([ProductID]) REFERENCES [dbo].[Product] ([ProductID])
);

-- Product Kit Components for kit/bundle products
CREATE TABLE [dbo].[ProductKitComponent] (
    [ComponentID] INT IDENTITY(1,1) NOT NULL,
    [KitProductID] INT NOT NULL,
    [ComponentProductID] INT NOT NULL,
    [Quantity] DECIMAL(18,3) NOT NULL,
    [IsOptional] BIT DEFAULT 0 NOT NULL,
    [SortOrder] INT DEFAULT 100 NOT NULL,
    [CreatedDate] DATETIME2 DEFAULT GETDATE() NOT NULL,
    [UpdatedDate] DATETIME2 DEFAULT GETDATE() NOT NULL,
    CONSTRAINT [PK_ProductKitComponent] PRIMARY KEY CLUSTERED ([ComponentID] ASC),
    CONSTRAINT [FK_ProductKitComponent_Kit] FOREIGN KEY ([KitProductID]) REFERENCES [dbo].[Product] ([ProductID]),
    CONSTRAINT [FK_ProductKitComponent_Component] FOREIGN KEY ([ComponentProductID]) REFERENCES [dbo].[Product] ([ProductID]),
    CONSTRAINT [UQ_ProductKitComponent] UNIQUE ([KitProductID], [ComponentProductID])
);

-- Product UOM Conversion table
CREATE TABLE [dbo].[ProductUOMConversion] (
    [ConversionID] INT IDENTITY(1,1) NOT NULL,
    [ProductID] INT NOT NULL,
    [FromUOMID] INT NOT NULL,
    [ToUOMID] INT NOT NULL,
    [ConversionFactor] DECIMAL(18,6) NOT NULL,
    [IsActive] BIT DEFAULT 1 NOT NULL,
    [CreatedDate] DATETIME2 DEFAULT GETDATE() NOT NULL,
    [UpdatedDate] DATETIME2 DEFAULT GETDATE() NOT NULL,
    CONSTRAINT [PK_ProductUOMConversion] PRIMARY KEY CLUSTERED ([ConversionID] ASC),
    CONSTRAINT [FK_ProductUOMConversion_Product] FOREIGN KEY ([ProductID]) REFERENCES [dbo].[Product] ([ProductID]),
    CONSTRAINT [FK_ProductUOMConversion_FromUOM] FOREIGN KEY ([FromUOMID]) REFERENCES [dbo].[UnitOfMeasure] ([UOMID]),
    CONSTRAINT [FK_ProductUOMConversion_ToUOM] FOREIGN KEY ([ToUOMID]) REFERENCES [dbo].[UnitOfMeasure] ([UOMID]),
    CONSTRAINT [UQ_ProductUOMConversion] UNIQUE ([ProductID], [FromUOMID], [ToUOMID])
);

-- Create indexes
CREATE INDEX [IX_Product_ProductCode] ON [dbo].[Product] ([ProductCode]);
CREATE INDEX [IX_Product_ProductName] ON [dbo].[Product] ([ProductName]);
CREATE INDEX [IX_Product_CategoryID] ON [dbo].[Product] ([CategoryID]);
CREATE INDEX [IX_Product_Status] ON [dbo].[Product] ([Status]);
CREATE INDEX [IX_Product_IsActive_IsDeleted] ON [dbo].[Product] ([IsActive], [IsDeleted]);
CREATE INDEX [IX_ProductCategory_ParentCategoryID] ON [dbo].[ProductCategory] ([ParentCategoryID]);
CREATE INDEX [IX_ProductCategory_CategoryPath] ON [dbo].[ProductCategory] ([CategoryPath]);
CREATE INDEX [IX_ProductVariant_ProductID] ON [dbo].[ProductVariant] ([ProductID]);
CREATE INDEX [IX_Barcode_ProductID] ON [dbo].[Barcode] ([ProductID]);
CREATE INDEX [IX_Barcode_BarcodeNumber] ON [dbo].[Barcode] ([BarcodeNumber]);
CREATE INDEX [IX_ProductPricing_ProductID] ON [dbo].[ProductPricing] ([ProductID]);
CREATE INDEX [IX_ProductPricing_EffectiveDates] ON [dbo].[ProductPricing] ([EffectiveFrom], [EffectiveTo]);
CREATE INDEX [IX_ProductPriceHistory_ProductID_ChangeDate] ON [dbo].[ProductPriceHistory] ([ProductID], [ChangeDate]);

-- Insert default Units of Measure
INSERT INTO [dbo].[UnitOfMeasure] ([UOMCode], [UOMName], [UOMType], [BaseUnit], [ConversionFactor], [Symbol])
VALUES 
    -- Count units
    ('EA', 'Each', 'Count', 1, 1, 'EA'),
    ('DZ', 'Dozen', 'Count', 0, 12, 'DZ'),
    ('CS', 'Case', 'Count', 0, 1, 'CS'),
    ('BX', 'Box', 'Count', 0, 1, 'BX'),
    ('PK', 'Pack', 'Count', 0, 1, 'PK'),
    
    -- Weight units
    ('KG', 'Kilogram', 'Weight', 1, 1, 'kg'),
    ('G', 'Gram', 'Weight', 0, 0.001, 'g'),
    ('LB', 'Pound', 'Weight', 0, 0.453592, 'lb'),
    ('OZ', 'Ounce', 'Weight', 0, 0.0283495, 'oz'),
    
    -- Volume units
    ('L', 'Liter', 'Volume', 1, 1, 'L'),
    ('ML', 'Milliliter', 'Volume', 0, 0.001, 'ml'),
    ('GAL', 'Gallon', 'Volume', 0, 3.78541, 'gal'),
    
    -- Length units
    ('M', 'Meter', 'Length', 1, 1, 'm'),
    ('CM', 'Centimeter', 'Length', 0, 0.01, 'cm'),
    ('FT', 'Foot', 'Length', 0, 0.3048, 'ft'),
    ('IN', 'Inch', 'Length', 0, 0.0254, 'in');