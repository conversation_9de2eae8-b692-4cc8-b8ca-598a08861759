-- Schema Migration Script - 2025-07-29
-- This script fixes schema mismatches between the database and application models

-- 1. Remove CustomerGroupID from CustomerPriceList table
IF EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('CustomerPriceList') AND name = 'CustomerGroupID')
BEGIN
    -- First drop the foreign key constraint if it exists
    DECLARE @ConstraintName NVARCHAR(200)
    SELECT @ConstraintName = CONSTRAINT_NAME 
    FROM INFORMATION_SCHEMA.REFERENTIAL_CONSTRAINTS 
    WHERE CONSTRAINT_SCHEMA = 'dbo' 
    AND OBJECT_NAME(OBJECT_ID(CONSTRAINT_NAME)) LIKE '%CustomerPriceList%'
    AND OBJECT_NAME(OBJECT_ID(UNIQUE_CONSTRAINT_NAME)) LIKE '%CustomerGroup%'
    
    IF @ConstraintName IS NOT NULL
    BEGIN
        EXEC('ALTER TABLE CustomerPriceList DROP CONSTRAINT ' + @ConstraintName)
    END
    
    -- Now drop the column
    ALTER TABLE CustomerPriceList DROP COLUMN CustomerGroupID
    PRINT 'Removed CustomerGroupID from CustomerPriceList table'
END
ELSE
BEGIN
    PRINT 'CustomerGroupID column does not exist in CustomerPriceList table'
END
GO

-- 2. Remove columns from Product table that don't exist in the model
IF EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('Product') AND name = 'Attribute1Name')
BEGIN
    ALTER TABLE Product DROP COLUMN Attribute1Name
    PRINT 'Removed Attribute1Name from Product table'
END

IF EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('Product') AND name = 'Attribute1Value')
BEGIN
    ALTER TABLE Product DROP COLUMN Attribute1Value
    PRINT 'Removed Attribute1Value from Product table'
END

IF EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('Product') AND name = 'Attribute2Name')
BEGIN
    ALTER TABLE Product DROP COLUMN Attribute2Name
    PRINT 'Removed Attribute2Name from Product table'
END

IF EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('Product') AND name = 'Attribute2Value')
BEGIN
    ALTER TABLE Product DROP COLUMN Attribute2Value
    PRINT 'Removed Attribute2Value from Product table'
END

IF EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('Product') AND name = 'Attribute3Name')
BEGIN
    ALTER TABLE Product DROP COLUMN Attribute3Name
    PRINT 'Removed Attribute3Name from Product table'
END

IF EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('Product') AND name = 'Attribute3Value')
BEGIN
    ALTER TABLE Product DROP COLUMN Attribute3Value
    PRINT 'Removed Attribute3Value from Product table'
END

IF EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('Product') AND name = 'ImageURL')
BEGIN
    ALTER TABLE Product DROP COLUMN ImageURL
    PRINT 'Removed ImageURL from Product table'
END
GO

-- 3. Ensure SalesOrderItem doesn't have Currency column
IF EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('SalesOrderItem') AND name = 'Currency')
BEGIN
    ALTER TABLE SalesOrderItem DROP COLUMN Currency
    PRINT 'Removed Currency from SalesOrderItem table'
END
GO

-- 4. Ensure GoodsReceipt doesn't have Currency column
IF EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('GoodsReceipt') AND name = 'Currency')
BEGIN
    ALTER TABLE GoodsReceipt DROP COLUMN Currency
    PRINT 'Removed Currency from GoodsReceipt table'
END
GO

-- 5. Add missing columns to ProductVariant table if they don't exist
IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('ProductVariant') AND name = 'UpdatedDate')
BEGIN
    ALTER TABLE ProductVariant ADD UpdatedDate DATETIME2 NOT NULL DEFAULT GETDATE()
    PRINT 'Added UpdatedDate to ProductVariant table'
END
GO

-- 6. Remove extra columns from ProductVariant that exist in DB but not in model
IF EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('ProductVariant') AND name = 'Attribute1Name')
BEGIN
    ALTER TABLE ProductVariant DROP COLUMN Attribute1Name
    PRINT 'Removed Attribute1Name from ProductVariant table'
END

IF EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('ProductVariant') AND name = 'Attribute1Value')
BEGIN
    ALTER TABLE ProductVariant DROP COLUMN Attribute1Value
    PRINT 'Removed Attribute1Value from ProductVariant table'
END

IF EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('ProductVariant') AND name = 'Attribute2Name')
BEGIN
    ALTER TABLE ProductVariant DROP COLUMN Attribute2Name
    PRINT 'Removed Attribute2Name from ProductVariant table'
END

IF EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('ProductVariant') AND name = 'Attribute2Value')
BEGIN
    ALTER TABLE ProductVariant DROP COLUMN Attribute2Value
    PRINT 'Removed Attribute2Value from ProductVariant table'
END

IF EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('ProductVariant') AND name = 'Attribute3Name')
BEGIN
    ALTER TABLE ProductVariant DROP COLUMN Attribute3Name
    PRINT 'Removed Attribute3Name from ProductVariant table'
END

IF EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('ProductVariant') AND name = 'Attribute3Value')
BEGIN
    ALTER TABLE ProductVariant DROP COLUMN Attribute3Value
    PRINT 'Removed Attribute3Value from ProductVariant table'
END

IF EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('ProductVariant') AND name = 'ImageURL')
BEGIN
    ALTER TABLE ProductVariant DROP COLUMN ImageURL
    PRINT 'Removed ImageURL from ProductVariant table'
END

IF EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('ProductVariant') AND name = 'BarcodeValue')
BEGIN
    ALTER TABLE ProductVariant DROP COLUMN BarcodeValue
    PRINT 'Removed BarcodeValue from ProductVariant table'
END

IF EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('ProductVariant') AND name = 'UOMID')
BEGIN
    ALTER TABLE ProductVariant DROP COLUMN UOMID
    PRINT 'Removed UOMID from ProductVariant table'
END

IF EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('ProductVariant') AND name = 'CreatedByUserID')
BEGIN
    ALTER TABLE ProductVariant DROP COLUMN CreatedByUserID
    PRINT 'Removed CreatedByUserID from ProductVariant table'
END

IF EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('ProductVariant') AND name = 'UpdatedByUserID')
BEGIN
    ALTER TABLE ProductVariant DROP COLUMN UpdatedByUserID
    PRINT 'Removed UpdatedByUserID from ProductVariant table'
END
GO

-- 7. Ensure Warehouse table doesn't have Currency column
IF EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('Warehouse') AND name = 'Currency')
BEGIN
    ALTER TABLE Warehouse DROP COLUMN Currency
    PRINT 'Removed Currency from Warehouse table'
END
GO

-- Summary of changes
PRINT ''
PRINT '===== Schema Migration Summary ====='
PRINT '1. Removed CustomerGroupID from CustomerPriceList (CustomerGroup relationship removed)'
PRINT '2. Removed non-existent attribute columns from Product table'
PRINT '3. Removed Currency columns from SalesOrderItem and GoodsReceipt'
PRINT '4. Aligned ProductVariant table with model definition'
PRINT '5. Removed Currency from Warehouse table'
PRINT ''
PRINT 'Migration completed successfully!'