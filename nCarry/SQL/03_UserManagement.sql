-- User Management Tables Creation Script
-- Purpose: User authentication, authorization and role management
-- Enhanced with industry best practices from leading ERP systems

-- User table for authentication and basic information
CREATE TABLE [dbo].[User] (
    [UserID] INT IDENTITY(1,1) NOT NULL,
    [Username] NVARCHAR(50) UNIQUE NOT NULL,
    [Email] NVARCHAR(255) UNIQUE NOT NULL,
    [PasswordHash] NVARCHAR(255) NOT NULL,
    [PasswordSalt] NVARCHAR(255) NOT NULL,
    [FirstName] NVARCHAR(100) NOT NULL,
    [LastName] NVARCHAR(100) NOT NULL,
    [DisplayName] NVARCHAR(200) NULL,
    [JobTitle] NVARCHAR(100) NULL,
    [Department] NVARCHAR(100) NULL,
    [PhoneNumber] NVARCHAR(50) NULL,
    [MobileNumber] NVARCHAR(50) NULL,
    [ProfilePicture] NVARCHAR(500) NULL,
    
    -- Security Settings
    [IsActive] BIT DEFAULT 1 NOT NULL,
    [IsLocked] BIT DEFAULT 0 NOT NULL,
    [LockoutEnd] DATETIME2 NULL,
    [AccessFailedCount] INT DEFAULT 0 NOT NULL,
    [MustChangePassword] BIT DEFAULT 0 NOT NULL,
    [PasswordChangedDate] DATETIME2 DEFAULT GETDATE() NOT NULL,
    [LastLoginDate] DATETIME2 NULL,
    [LastActivityDate] DATETIME2 NULL,
    [TwoFactorEnabled] BIT DEFAULT 0 NOT NULL,
    [TwoFactorSecret] NVARCHAR(255) NULL,
    
    -- Preferences
    [Language] NVARCHAR(10) DEFAULT 'en-GB' NOT NULL,
    [TimeZone] NVARCHAR(50) DEFAULT 'GMT Standard Time' NOT NULL,
    [DateFormat] NVARCHAR(20) DEFAULT 'dd/MM/yyyy' NOT NULL,
    [ThemePreference] NVARCHAR(20) DEFAULT 'Light' NULL,
    
    -- System Fields
    [CreatedDate] DATETIME2 DEFAULT GETDATE() NOT NULL,
    [CreatedByUserID] INT NULL,
    [UpdatedDate] DATETIME2 DEFAULT GETDATE() NOT NULL,
    [UpdatedByUserID] INT NULL,
    [IsDeleted] BIT DEFAULT 0 NOT NULL,
    [DeletedDate] DATETIME2 NULL,
    [DeletedByUserID] INT NULL,
    
    CONSTRAINT [PK_User] PRIMARY KEY CLUSTERED ([UserID] ASC)
);

-- Role table for grouping permissions
CREATE TABLE [dbo].[Role] (
    [RoleID] INT IDENTITY(1,1) NOT NULL,
    [RoleName] NVARCHAR(50) UNIQUE NOT NULL,
    [RoleDescription] NVARCHAR(500) NULL,
    [IsSystemRole] BIT DEFAULT 0 NOT NULL, -- Cannot be deleted if true
    [IsActive] BIT DEFAULT 1 NOT NULL,
    [CreatedDate] DATETIME2 DEFAULT GETDATE() NOT NULL,
    [CreatedByUserID] INT NULL,
    [UpdatedDate] DATETIME2 DEFAULT GETDATE() NOT NULL,
    [UpdatedByUserID] INT NULL,
    CONSTRAINT [PK_Role] PRIMARY KEY CLUSTERED ([RoleID] ASC)
);

-- Permission table for defining system capabilities
CREATE TABLE [dbo].[Permission] (
    [PermissionID] INT IDENTITY(1,1) NOT NULL,
    [PermissionCode] NVARCHAR(100) UNIQUE NOT NULL,
    [PermissionName] NVARCHAR(200) NOT NULL,
    [PermissionDescription] NVARCHAR(500) NULL,
    [Module] NVARCHAR(50) NOT NULL, -- Customer, Supplier, Product, Sales, etc.
    [Category] NVARCHAR(50) NOT NULL, -- View, Create, Edit, Delete, Export, Print
    [IsActive] BIT DEFAULT 1 NOT NULL,
    [CreatedDate] DATETIME2 DEFAULT GETDATE() NOT NULL,
    CONSTRAINT [PK_Permission] PRIMARY KEY CLUSTERED ([PermissionID] ASC)
);

-- User-Role mapping table
CREATE TABLE [dbo].[UserRole] (
    [UserRoleID] INT IDENTITY(1,1) NOT NULL,
    [UserID] INT NOT NULL,
    [RoleID] INT NOT NULL,
    [AssignedDate] DATETIME2 DEFAULT GETDATE() NOT NULL,
    [AssignedByUserID] INT NULL,
    [ExpiryDate] DATETIME2 NULL,
    [IsActive] BIT DEFAULT 1 NOT NULL,
    CONSTRAINT [PK_UserRole] PRIMARY KEY CLUSTERED ([UserRoleID] ASC),
    CONSTRAINT [FK_UserRole_User] FOREIGN KEY ([UserID]) REFERENCES [dbo].[User] ([UserID]),
    CONSTRAINT [FK_UserRole_Role] FOREIGN KEY ([RoleID]) REFERENCES [dbo].[Role] ([RoleID]),
    CONSTRAINT [UQ_UserRole] UNIQUE ([UserID], [RoleID])
);

-- Role-Permission mapping table
CREATE TABLE [dbo].[RolePermission] (
    [RolePermissionID] INT IDENTITY(1,1) NOT NULL,
    [RoleID] INT NOT NULL,
    [PermissionID] INT NOT NULL,
    [GrantedDate] DATETIME2 DEFAULT GETDATE() NOT NULL,
    [GrantedByUserID] INT NULL,
    CONSTRAINT [PK_RolePermission] PRIMARY KEY CLUSTERED ([RolePermissionID] ASC),
    CONSTRAINT [FK_RolePermission_Role] FOREIGN KEY ([RoleID]) REFERENCES [dbo].[Role] ([RoleID]),
    CONSTRAINT [FK_RolePermission_Permission] FOREIGN KEY ([PermissionID]) REFERENCES [dbo].[Permission] ([PermissionID]),
    CONSTRAINT [UQ_RolePermission] UNIQUE ([RoleID], [PermissionID])
);

-- User session tracking
CREATE TABLE [dbo].[UserSession] (
    [SessionID] INT IDENTITY(1,1) NOT NULL,
    [UserID] INT NOT NULL,
    [SessionToken] NVARCHAR(255) UNIQUE NOT NULL,
    [IPAddress] NVARCHAR(45) NULL,
    [UserAgent] NVARCHAR(500) NULL,
    [LoginDate] DATETIME2 DEFAULT GETDATE() NOT NULL,
    [LastActivityDate] DATETIME2 DEFAULT GETDATE() NOT NULL,
    [LogoutDate] DATETIME2 NULL,
    [IsActive] BIT DEFAULT 1 NOT NULL,
    [ExpiryDate] DATETIME2 NOT NULL,
    CONSTRAINT [PK_UserSession] PRIMARY KEY CLUSTERED ([SessionID] ASC),
    CONSTRAINT [FK_UserSession_User] FOREIGN KEY ([UserID]) REFERENCES [dbo].[User] ([UserID])
);

-- Password reset tokens
CREATE TABLE [dbo].[PasswordResetToken] (
    [TokenID] INT IDENTITY(1,1) NOT NULL,
    [UserID] INT NOT NULL,
    [Token] NVARCHAR(255) UNIQUE NOT NULL,
    [ExpiryDate] DATETIME2 NOT NULL,
    [IsUsed] BIT DEFAULT 0 NOT NULL,
    [UsedDate] DATETIME2 NULL,
    [CreatedDate] DATETIME2 DEFAULT GETDATE() NOT NULL,
    [IPAddress] NVARCHAR(45) NULL,
    CONSTRAINT [PK_PasswordResetToken] PRIMARY KEY CLUSTERED ([TokenID] ASC),
    CONSTRAINT [FK_PasswordResetToken_User] FOREIGN KEY ([UserID]) REFERENCES [dbo].[User] ([UserID])
);

-- Audit log for security tracking
CREATE TABLE [dbo].[AuditLog] (
    [AuditLogID] BIGINT IDENTITY(1,1) NOT NULL,
    [UserID] INT NULL,
    [Username] NVARCHAR(50) NULL,
    [Action] NVARCHAR(100) NOT NULL, -- Login, Logout, Create, Update, Delete, View, Export
    [EntityType] NVARCHAR(100) NULL, -- Customer, Product, Order, etc.
    [EntityID] INT NULL,
    [OldValues] NVARCHAR(MAX) NULL, -- JSON format
    [NewValues] NVARCHAR(MAX) NULL, -- JSON format
    [IPAddress] NVARCHAR(45) NULL,
    [UserAgent] NVARCHAR(500) NULL,
    [Timestamp] DATETIME2 DEFAULT GETDATE() NOT NULL,
    [Result] NVARCHAR(20) NOT NULL, -- Success, Failed, Error
    [ErrorMessage] NVARCHAR(MAX) NULL,
    CONSTRAINT [PK_AuditLog] PRIMARY KEY CLUSTERED ([AuditLogID] ASC)
);

-- Create indexes
CREATE INDEX [IX_User_Username] ON [dbo].[User] ([Username]);
CREATE INDEX [IX_User_Email] ON [dbo].[User] ([Email]);
CREATE INDEX [IX_User_IsActive_IsDeleted] ON [dbo].[User] ([IsActive], [IsDeleted]);
CREATE INDEX [IX_UserRole_UserID] ON [dbo].[UserRole] ([UserID]);
CREATE INDEX [IX_UserRole_RoleID] ON [dbo].[UserRole] ([RoleID]);
CREATE INDEX [IX_RolePermission_RoleID] ON [dbo].[RolePermission] ([RoleID]);
CREATE INDEX [IX_RolePermission_PermissionID] ON [dbo].[RolePermission] ([PermissionID]);
CREATE INDEX [IX_Permission_Module_Category] ON [dbo].[Permission] ([Module], [Category]);
CREATE INDEX [IX_UserSession_UserID] ON [dbo].[UserSession] ([UserID]);
CREATE INDEX [IX_UserSession_SessionToken] ON [dbo].[UserSession] ([SessionToken]);
CREATE INDEX [IX_AuditLog_UserID_Timestamp] ON [dbo].[AuditLog] ([UserID], [Timestamp]);
CREATE INDEX [IX_AuditLog_EntityType_EntityID] ON [dbo].[AuditLog] ([EntityType], [EntityID]);
CREATE INDEX [IX_AuditLog_Timestamp] ON [dbo].[AuditLog] ([Timestamp]);

-- Insert default system roles
INSERT INTO [dbo].[Role] ([RoleName], [RoleDescription], [IsSystemRole], [CreatedByUserID])
VALUES 
    ('Administrator', 'Full system access', 1, NULL),
    ('Manager', 'Management level access', 1, NULL),
    ('Sales', 'Sales team access', 1, NULL),
    ('Warehouse', 'Warehouse operations access', 1, NULL),
    ('Finance', 'Financial operations access', 1, NULL),
    ('Customer Service', 'Customer service access', 1, NULL),
    ('Read Only', 'View only access', 1, NULL);

-- Insert default permissions
INSERT INTO [dbo].[Permission] ([PermissionCode], [PermissionName], [Module], [Category])
VALUES 
    -- Customer Module
    ('CUSTOMER_VIEW', 'View Customers', 'Customer', 'View'),
    ('CUSTOMER_CREATE', 'Create Customers', 'Customer', 'Create'),
    ('CUSTOMER_EDIT', 'Edit Customers', 'Customer', 'Edit'),
    ('CUSTOMER_DELETE', 'Delete Customers', 'Customer', 'Delete'),
    ('CUSTOMER_EXPORT', 'Export Customers', 'Customer', 'Export'),
    
    -- Supplier Module
    ('SUPPLIER_VIEW', 'View Suppliers', 'Supplier', 'View'),
    ('SUPPLIER_CREATE', 'Create Suppliers', 'Supplier', 'Create'),
    ('SUPPLIER_EDIT', 'Edit Suppliers', 'Supplier', 'Edit'),
    ('SUPPLIER_DELETE', 'Delete Suppliers', 'Supplier', 'Delete'),
    ('SUPPLIER_EXPORT', 'Export Suppliers', 'Supplier', 'Export'),
    
    -- Product Module
    ('PRODUCT_VIEW', 'View Products', 'Product', 'View'),
    ('PRODUCT_CREATE', 'Create Products', 'Product', 'Create'),
    ('PRODUCT_EDIT', 'Edit Products', 'Product', 'Edit'),
    ('PRODUCT_DELETE', 'Delete Products', 'Product', 'Delete'),
    ('PRODUCT_EXPORT', 'Export Products', 'Product', 'Export'),
    
    -- Sales Module
    ('SALES_VIEW', 'View Sales', 'Sales', 'View'),
    ('SALES_CREATE', 'Create Sales', 'Sales', 'Create'),
    ('SALES_EDIT', 'Edit Sales', 'Sales', 'Edit'),
    ('SALES_DELETE', 'Delete Sales', 'Sales', 'Delete'),
    ('SALES_EXPORT', 'Export Sales', 'Sales', 'Export'),
    
    -- Finance Module
    ('FINANCE_VIEW', 'View Finance', 'Finance', 'View'),
    ('FINANCE_CREATE', 'Create Finance', 'Finance', 'Create'),
    ('FINANCE_EDIT', 'Edit Finance', 'Finance', 'Edit'),
    ('FINANCE_DELETE', 'Delete Finance', 'Finance', 'Delete'),
    ('FINANCE_EXPORT', 'Export Finance', 'Finance', 'Export'),
    
    -- Reports Module
    ('REPORTS_VIEW', 'View Reports', 'Reports', 'View'),
    ('REPORTS_EXPORT', 'Export Reports', 'Reports', 'Export'),
    
    -- System Module
    ('SYSTEM_ADMIN', 'System Administration', 'System', 'Admin'),
    ('USER_MANAGE', 'Manage Users', 'System', 'Admin'),
    ('ROLE_MANAGE', 'Manage Roles', 'System', 'Admin');