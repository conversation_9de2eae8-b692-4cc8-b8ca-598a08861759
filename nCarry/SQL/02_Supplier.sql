-- Supplier Table Creation Script
-- Purpose: Stores supplier information for the wholesale management system
-- Enhanced version with industry best practices

CREATE TABLE [dbo].[Supplier] (
    [SupplierID] INT IDENTITY(1,1) NOT NULL,
    [SupplierCode] NVARCHAR(20) UNIQUE NOT NULL, -- Auto-generated unique code
    [SupplierName] NVARCHAR(255) NOT NULL,
    [TradingName] NVARCHAR(255) NULL,
    [ReferenceCode] NVARCHAR(50) UNIQUE NULL,
    [TaxNumber] NVARCHAR(50) NULL,
    [SupplierType] NVARCHAR(50) DEFAULT 'Standard' NULL, -- Manufacturer, Distributor, Importer
    [SupplierStatus] NVARCHAR(20) DEFAULT 'Active' NOT NULL, -- Active, Inactive, Blocked, Suspended
    [SupplierGroup] NVARCHAR(50) NULL, -- Preferred, Standard, New
    
    -- Contact Information
    [ResponsibleContact] NVARCHAR(255) NULL,
    [ContactPhone] NVARCHAR(50) NULL,
    [ContactLandline] NVARCHAR(50) NULL,
    [ContactEmail] NVARCHAR(255) NULL,
    [ContactMobile] NVARCHAR(50) NULL,
    [Website] NVARCHAR(255) NULL,
    
    -- Address Information
    [SupplierAddress] NVARCHAR(500) NULL,
    [SupplierAddress2] NVARCHAR(500) NULL,
    [SupplierCity] NVARCHAR(100) NULL,
    [SupplierState] NVARCHAR(100) NULL,
    [SupplierPostCode] NVARCHAR(20) NULL,
    [SupplierCountry] NVARCHAR(100) DEFAULT 'United Kingdom' NULL,
    
    -- Financial Information
    [PaymentTerms] NVARCHAR(50) DEFAULT 'Net 30' NULL,
    [PaymentTermDays] INT DEFAULT 30 NULL,
    [PaymentMethodID] INT NULL,
    [PreferredCurrency] NVARCHAR(3) DEFAULT 'GBP' NULL,
    [BankAccountDetails] NVARCHAR(500) NULL,
    [TaxRate] DECIMAL(5,2) DEFAULT 20.00 NULL,
    [WithholdingTaxRate] DECIMAL(5,2) DEFAULT 0 NULL,
    
    -- Purchasing Information
    [MinimumOrderValue] DECIMAL(18,2) DEFAULT 0 NULL,
    [LeadTimeDays] INT DEFAULT 7 NULL,
    [OrderCutoffTime] TIME NULL,
    [DeliveryDays] NVARCHAR(100) NULL, -- e.g., "Mon,Wed,Fri"
    [EarlyPaymentDiscountPercent] DECIMAL(5,2) DEFAULT 0 NULL,
    [EarlyPaymentDiscountDays] INT NULL,
    
    -- Performance Metrics
    [Rating] INT NULL, -- 1-5 star rating
    [QualityScore] DECIMAL(5,2) NULL, -- 0-100
    [DeliveryScore] DECIMAL(5,2) NULL, -- 0-100
    [PriceScore] DECIMAL(5,2) NULL, -- 0-100
    [LastOrderDate] DATETIME2 NULL,
    [TotalPurchaseValue] DECIMAL(18,2) DEFAULT 0 NULL,
    [OutstandingBalance] DECIMAL(18,2) DEFAULT 0 NULL,
    [YTDPurchases] DECIMAL(18,2) DEFAULT 0 NULL,
    
    -- Compliance Information
    [CertificateNumber] NVARCHAR(100) NULL,
    [CertificateExpiryDate] DATETIME2 NULL,
    [InsurancePolicyNumber] NVARCHAR(100) NULL,
    [InsuranceExpiryDate] DATETIME2 NULL,
    [ComplianceStatus] NVARCHAR(20) DEFAULT 'Compliant' NULL,
    
    -- Additional Information
    [ReturnPolicy] NVARCHAR(MAX) NULL,
    [WarrantyTerms] NVARCHAR(MAX) NULL,
    [Notes] NVARCHAR(MAX) NULL,
    [InternalNotes] NVARCHAR(MAX) NULL, -- Not visible to supplier
    [IsActive] BIT DEFAULT 1 NOT NULL,
    [IsPreferred] BIT DEFAULT 0 NULL,
    [BlockedReason] NVARCHAR(500) NULL,
    [BlockedDate] DATETIME2 NULL,
    [BlockedByUserID] INT NULL,
    
    -- Integration Information
    [EDIEnabled] BIT DEFAULT 0 NULL,
    [APIEnabled] BIT DEFAULT 0 NULL,
    [IntegrationKey] NVARCHAR(100) NULL,
    
    -- System Fields
    [CreatedDate] DATETIME2 DEFAULT GETDATE() NOT NULL,
    [CreatedByUserID] INT NULL,
    [UpdatedDate] DATETIME2 DEFAULT GETDATE() NOT NULL,
    [UpdatedByUserID] INT NULL,
    [IsDeleted] BIT DEFAULT 0 NOT NULL,
    [DeletedDate] DATETIME2 NULL,
    [DeletedByUserID] INT NULL,
    
    CONSTRAINT [PK_Supplier] PRIMARY KEY CLUSTERED ([SupplierID] ASC),
    CONSTRAINT [CHK_Supplier_Rating] CHECK ([Rating] IS NULL OR ([Rating] >= 1 AND [Rating] <= 5)),
    CONSTRAINT [CHK_Supplier_QualityScore] CHECK ([QualityScore] IS NULL OR ([QualityScore] >= 0 AND [QualityScore] <= 100)),
    CONSTRAINT [CHK_Supplier_DeliveryScore] CHECK ([DeliveryScore] IS NULL OR ([DeliveryScore] >= 0 AND [DeliveryScore] <= 100)),
    CONSTRAINT [CHK_Supplier_PriceScore] CHECK ([PriceScore] IS NULL OR ([PriceScore] >= 0 AND [PriceScore] <= 100))
);

-- Create indexes for better performance
CREATE INDEX [IX_Supplier_SupplierCode] ON [dbo].[Supplier] ([SupplierCode]);
CREATE INDEX [IX_Supplier_SupplierName] ON [dbo].[Supplier] ([SupplierName]);
CREATE INDEX [IX_Supplier_ReferenceCode] ON [dbo].[Supplier] ([ReferenceCode]);
CREATE INDEX [IX_Supplier_ContactEmail] ON [dbo].[Supplier] ([ContactEmail]);
CREATE INDEX [IX_Supplier_SupplierStatus] ON [dbo].[Supplier] ([SupplierStatus]);
CREATE INDEX [IX_Supplier_SupplierType] ON [dbo].[Supplier] ([SupplierType]);
CREATE INDEX [IX_Supplier_IsActive_IsDeleted] ON [dbo].[Supplier] ([IsActive], [IsDeleted]);
CREATE INDEX [IX_Supplier_TaxNumber] ON [dbo].[Supplier] ([TaxNumber]);

-- Additional address table for multiple addresses per supplier
CREATE TABLE [dbo].[SupplierAddress] (
    [AddressID] INT IDENTITY(1,1) NOT NULL,
    [SupplierID] INT NOT NULL,
    [AddressType] NVARCHAR(20) NOT NULL, -- Billing, Shipping, Warehouse, Return
    [AddressName] NVARCHAR(100) NULL, -- e.g., "Main Warehouse", "Returns Department"
    [Address1] NVARCHAR(500) NOT NULL,
    [Address2] NVARCHAR(500) NULL,
    [City] NVARCHAR(100) NULL,
    [State] NVARCHAR(100) NULL,
    [PostCode] NVARCHAR(20) NULL,
    [Country] NVARCHAR(100) DEFAULT 'United Kingdom' NULL,
    [ContactName] NVARCHAR(255) NULL,
    [ContactPhone] NVARCHAR(50) NULL,
    [IsDefault] BIT DEFAULT 0 NULL,
    [IsActive] BIT DEFAULT 1 NOT NULL,
    [CreatedDate] DATETIME2 DEFAULT GETDATE() NOT NULL,
    [UpdatedDate] DATETIME2 DEFAULT GETDATE() NOT NULL,
    CONSTRAINT [PK_SupplierAddress] PRIMARY KEY CLUSTERED ([AddressID] ASC),
    CONSTRAINT [FK_SupplierAddress_Supplier] FOREIGN KEY ([SupplierID]) REFERENCES [dbo].[Supplier] ([SupplierID])
);

CREATE INDEX [IX_SupplierAddress_SupplierID] ON [dbo].[SupplierAddress] ([SupplierID]);
CREATE INDEX [IX_SupplierAddress_AddressType] ON [dbo].[SupplierAddress] ([AddressType]);