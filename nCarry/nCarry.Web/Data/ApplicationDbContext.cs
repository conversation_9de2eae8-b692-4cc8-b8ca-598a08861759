using Microsoft.EntityFrameworkCore;
using System.ComponentModel.DataAnnotations.Schema;
using nCarry.Web.Models.Customers;
using nCarry.Web.Models.Suppliers;
using nCarry.Web.Models.Products;
using nCarry.Web.Models.Warehouse;
using nCarry.Web.Models.Sales;
using nCarry.Web.Models.Purchase;
using nCarry.Web.Models.Financial;
using nCarry.Web.Models.System;
using nCarry.Web.Models.Logistics;

namespace nCarry.Web.Data
{
    public class ApplicationDbContext : DbContext
    {
        public ApplicationDbContext(DbContextOptions<ApplicationDbContext> options)
            : base(options)
        {
        }

        // Customer Management
        public DbSet<Customer> Customers { get; set; }
        public DbSet<CustomerAddress> CustomerAddresses { get; set; }
        // public DbSet<CustomerContact> CustomerContacts { get; set; } // Table doesn't exist in database
        public DbSet<CustomerGroup> CustomerGroups { get; set; }
        public DbSet<CustomerGroupMember> CustomerGroupMembers { get; set; }
        public DbSet<CustomerPriceList> CustomerPriceLists { get; set; }
        public DbSet<CustomerPriceListItem> CustomerPriceListItems { get; set; }
        
        // Supplier Management
        public DbSet<Supplier> Suppliers { get; set; }
        public DbSet<SupplierAddress> SupplierAddresses { get; set; }
        // public DbSet<SupplierContact> SupplierContacts { get; set; } // Table doesn't exist in database
        
        // Product Management
        public DbSet<ProductCategory> ProductCategories { get; set; }
        public DbSet<Product> Products { get; set; }
        public DbSet<UnitOfMeasure> UnitOfMeasures { get; set; }
        public DbSet<ProductVariant> ProductVariants { get; set; }
        public DbSet<Barcode> Barcodes { get; set; }
        
        // Warehouse & Inventory
        public DbSet<Warehouse> Warehouses { get; set; }
        public DbSet<WarehouseLocation> WarehouseLocations { get; set; }
        public DbSet<Inventory> Inventories { get; set; }
        public DbSet<InventoryTransaction> InventoryTransactions { get; set; }
        
        // Sales Management - Quote disabled until manual schema fix
        // IMPORTANT: Run manual SQL fix then re-enable these lines:
        // public DbSet<Quote> Quotes { get; set; }
        // public DbSet<QuoteItem> QuoteItems { get; set; }
        public DbSet<SalesOrder> SalesOrders { get; set; }
        public DbSet<SalesOrderItem> SalesOrderItems { get; set; }
        public DbSet<SalesInvoice> SalesInvoices { get; set; }
        public DbSet<SalesInvoiceItem> SalesInvoiceItems { get; set; }
        
        // Purchase Management
        public DbSet<PurchaseOrder> PurchaseOrders { get; set; }
        public DbSet<PurchaseOrderItem> PurchaseOrderItems { get; set; }
        public DbSet<GoodsReceipt> GoodsReceipts { get; set; }
        public DbSet<GoodsReceiptItem> GoodsReceiptItems { get; set; }
        public DbSet<SupplierInvoice> SupplierInvoices { get; set; }
        public DbSet<SupplierInvoiceItem> SupplierInvoiceItems { get; set; }
        
        // Financial Management
        public DbSet<Currency> Currencies { get; set; }
        public DbSet<ExchangeRate> ExchangeRates { get; set; }
        public DbSet<PaymentMethod> PaymentMethods { get; set; }
        public DbSet<Bank> Banks { get; set; }
        public DbSet<Payment> Payments { get; set; }
        public DbSet<PaymentAllocation> PaymentAllocations { get; set; }
        public DbSet<TaxCode> TaxCodes { get; set; }
        
        // System Configuration
        public DbSet<Company> Companies { get; set; }
        public DbSet<Branch> Branches { get; set; }
        public DbSet<SystemConfiguration> SystemConfigurations { get; set; }
        public DbSet<NumberSequence> NumberSequences { get; set; }
        public DbSet<EmailTemplate> EmailTemplates { get; set; }
        public DbSet<NotificationTemplate> NotificationTemplates { get; set; }
        public DbSet<APIKey> APIKeys { get; set; }
        public DbSet<WebhookConfiguration> WebhookConfigurations { get; set; }
        
        // Logistics & Delivery
        public DbSet<Carrier> Carriers { get; set; }
        public DbSet<CarrierService> CarrierServices { get; set; }
        public DbSet<FreightRate> FreightRates { get; set; }
        public DbSet<DeliveryZone> DeliveryZones { get; set; }
        public DbSet<DeliveryRoute> DeliveryRoutes { get; set; }
        public DbSet<PickingList> PickingLists { get; set; }
        public DbSet<PickingListItem> PickingListItems { get; set; }
        public DbSet<PackingSlip> PackingSlips { get; set; }
        public DbSet<PackingSlipItem> PackingSlipItems { get; set; }
        public DbSet<PackingSlipBox> PackingSlipBoxes { get; set; }
        public DbSet<Shipment> Shipments { get; set; }
        public DbSet<ShipmentItem> ShipmentItems { get; set; }
        public DbSet<ShipmentTracking> ShipmentTrackings { get; set; }
        public DbSet<Document> Documents { get; set; }
        
        // User Management
        public DbSet<User> Users { get; set; }
        public DbSet<Role> Roles { get; set; }
        public DbSet<Permission> Permissions { get; set; }
        public DbSet<UserRole> UserRoles { get; set; }
        public DbSet<RolePermission> RolePermissions { get; set; }
        public DbSet<UserSession> UserSessions { get; set; }
        
        // Additional models that map to existing tables
        public DbSet<KPI> KPIs { get; set; }
        
        // Aliases for existing models to match controller expectations
        public DbSet<PackingSlip> PackingLists { get; set; }
        // Note: Routes is handled by DeliveryRoutes DbSet (Route model maps to DeliveryRoute table)
        public DbSet<Inventory> Inventory { get; set; }
        public DbSet<PickingListItem> PickingItems { get; set; }
        public DbSet<Vehicle> Vehicles { get; set; }
        public DbSet<Driver> Drivers { get; set; }

        protected override void OnModelCreating(ModelBuilder builder)
        {
            base.OnModelCreating(builder);


            // Explicitly ignore computed properties first
            builder.Entity<SalesOrder>().Ignore(so => so.RemainingAmount);
            builder.Entity<SalesOrder>().Ignore(so => so.IsFullyPaid);
            builder.Entity<SalesOrder>().Ignore(so => so.IsOverdue);
            builder.Entity<Payment>().Ignore(p => p.UnallocatedAmount);
            builder.Entity<Payment>().Ignore(p => p.AllocatedAmount);
            
            // Ignore computed properties for new models
            builder.Entity<KPI>().Ignore(k => k.DisplayOrder);
            builder.Entity<KPI>().Ignore(k => k.KPIType);
            builder.Entity<KPI>().Ignore(k => k.ChartType);
            builder.Entity<KPI>().Ignore(k => k.ChartData);
            builder.Entity<KPI>().Ignore(k => k.CurrentValue);
            builder.Entity<KPI>().Ignore(k => k.PercentageChange);
            builder.Entity<KPI>().Ignore(k => k.Status);
            
            // Ignore Product attributes that don't exist in database
            builder.Entity<Product>().Ignore(p => p.Attribute1Name);
            builder.Entity<Product>().Ignore(p => p.Attribute1Value);
            builder.Entity<Product>().Ignore(p => p.Attribute2Name);
            builder.Entity<Product>().Ignore(p => p.Attribute2Value);
            builder.Entity<Product>().Ignore(p => p.Attribute3Name);
            builder.Entity<Product>().Ignore(p => p.Attribute3Value);
            builder.Entity<Product>().Ignore(p => p.ImageURL);
            
            
            // Ignore computed properties for GoodsReceipt
            builder.Entity<GoodsReceipt>().Ignore(g => g.SubTotal);
            builder.Entity<GoodsReceipt>().Ignore(g => g.TaxAmount);
            builder.Entity<GoodsReceipt>().Ignore(g => g.TotalAmount);
            
            // Ignore computed properties for GoodsReceiptItem
            builder.Entity<GoodsReceiptItem>().Ignore(g => g.TaxRate);
            builder.Entity<GoodsReceiptItem>().Ignore(g => g.TaxAmount);
            builder.Entity<GoodsReceiptItem>().Ignore(g => g.LineTotal);
            
            // Ignore computed properties for SupplierInvoice
            builder.Entity<SupplierInvoice>().Ignore(s => s.OutstandingAmount);
            builder.Entity<SupplierInvoice>().Ignore(s => s.GoodsReceiptID);
            builder.Entity<SupplierInvoice>().Ignore(s => s.GoodsReceipt);
            
            // TrackingEvent removed - use ShipmentTracking instead
            
            // DeliveryRoute computed properties are handled as NotMapped in the model
            
            // Note: CreatedByUserID and UpdatedByUserID DO exist in the Shipment table
            // They should NOT be ignored - removing the Ignore statements

            // Configure decimal precision
            foreach (var entityType in builder.Model.GetEntityTypes())
            {
                var properties = entityType.ClrType.GetProperties()
                    .Where(p => p.PropertyType == typeof(decimal) || p.PropertyType == typeof(decimal?))
                    .Where(p => !p.GetCustomAttributes(typeof(NotMappedAttribute), false).Any()); // Skip NotMapped properties

                foreach (var property in properties)
                {
                    if (property.Name.Contains("Rate") || property.Name.Contains("Percent"))
                    {
                        builder.Entity(entityType.ClrType).Property(property.Name).HasPrecision(5, 2);
                    }
                    else if (property.Name.Contains("Quantity"))
                    {
                        builder.Entity(entityType.ClrType).Property(property.Name).HasPrecision(18, 4);
                    }
                    else
                    {
                        builder.Entity(entityType.ClrType).Property(property.Name).HasPrecision(18, 2);
                    }
                }
            }

            // Configure relationships
            builder.Entity<CustomerAddress>()
                .HasOne(ca => ca.Customer)
                .WithMany(c => c.Addresses)
                .HasForeignKey(ca => ca.CustomerID)
                .OnDelete(DeleteBehavior.Restrict);

            // CustomerContact configuration commented out - table doesn't exist
            // builder.Entity<CustomerContact>()
            //     .HasOne(cc => cc.Customer)
            //     .WithMany(c => c.Contacts)
            //     .HasForeignKey(cc => cc.CustomerID)
            //     .OnDelete(DeleteBehavior.Restrict);

            builder.Entity<ProductCategory>()
                .HasOne(pc => pc.ParentCategory)
                .WithMany(pc => pc.SubCategories)
                .HasForeignKey(pc => pc.ParentCategoryID)
                .OnDelete(DeleteBehavior.Restrict);

            builder.Entity<Product>()
                .HasOne(p => p.Category)
                .WithMany(c => c.Products)
                .HasForeignKey(p => p.CategoryID)
                .OnDelete(DeleteBehavior.Restrict);

            builder.Entity<Product>()
                .HasOne(p => p.InventoryUOM)
                .WithMany(u => u.InventoryProducts)
                .HasForeignKey(p => p.InventoryUOMID)
                .OnDelete(DeleteBehavior.Restrict);

            builder.Entity<Product>()
                .HasOne(p => p.PurchaseUOM)
                .WithMany(u => u.PurchaseProducts)
                .HasForeignKey(p => p.PurchaseUOMID)
                .OnDelete(DeleteBehavior.Restrict);

            builder.Entity<Product>()
                .HasOne(p => p.SalesUOM)
                .WithMany(u => u.SalesProducts)
                .HasForeignKey(p => p.SalesUOMID)
                .OnDelete(DeleteBehavior.Restrict);

            builder.Entity<Product>()
                .HasOne(p => p.WeightUOM)
                .WithMany(u => u.WeightProducts)
                .HasForeignKey(p => p.WeightUOMID)
                .OnDelete(DeleteBehavior.Restrict);

            builder.Entity<Product>()
                .HasOne(p => p.DimensionUOM)
                .WithMany(u => u.DimensionProducts)
                .HasForeignKey(p => p.DimensionUOMID)
                .OnDelete(DeleteBehavior.Restrict);

            builder.Entity<Product>()
                .HasOne(p => p.VolumeUOM)
                .WithMany(u => u.VolumeProducts)
                .HasForeignKey(p => p.VolumeUOMID)
                .OnDelete(DeleteBehavior.Restrict);

            builder.Entity<SalesOrder>()
                .HasOne(so => so.Customer)
                .WithMany(c => c.SalesOrders)
                .HasForeignKey(so => so.CustomerID)
                .OnDelete(DeleteBehavior.Restrict);

            builder.Entity<SalesOrderItem>()
                .HasOne(soi => soi.SalesOrder)
                .WithMany(so => so.SalesOrderItems)
                .HasForeignKey(soi => soi.OrderID)
                .OnDelete(DeleteBehavior.Cascade);

            builder.Entity<SalesOrderItem>()
                .HasOne(soi => soi.Product)
                .WithMany(p => p.SalesOrderItems)
                .HasForeignKey(soi => soi.ProductID)
                .OnDelete(DeleteBehavior.Restrict);

            // Configure decimal properties for SalesOrder
            builder.Entity<SalesOrder>()
                .Property(so => so.SubTotal).HasPrecision(18, 2);
            builder.Entity<SalesOrder>()
                .Property(so => so.DiscountAmount).HasPrecision(18, 2);
            builder.Entity<SalesOrder>()
                .Property(so => so.TaxAmount).HasPrecision(18, 2);
            builder.Entity<SalesOrder>()
                .Property(so => so.ShippingAmount).HasPrecision(18, 2);
            builder.Entity<SalesOrder>()
                .Property(so => so.TotalAmount).HasPrecision(18, 2);
            builder.Entity<SalesOrder>()
                .Property(so => so.PaidAmount).HasPrecision(18, 2);
            builder.Entity<SalesOrder>()
                .Property(so => so.BalanceAmount).HasPrecision(18, 2);
            builder.Entity<SalesOrder>()
                .Property(so => so.ExchangeRate).HasPrecision(18, 6);
            builder.Entity<SalesOrder>()
                .Ignore(so => so.Items);
            builder.Entity<SalesInvoice>()
                .Ignore(si => si.Items);


            // Configure decimal properties for PurchaseOrder
            builder.Entity<PurchaseOrder>()
                .Property(po => po.SubTotal).HasPrecision(18, 2);
            builder.Entity<PurchaseOrder>()
                .Property(po => po.DiscountAmount).HasPrecision(18, 2);
            builder.Entity<PurchaseOrder>()
                .Property(po => po.TaxAmount).HasPrecision(18, 2);
            builder.Entity<PurchaseOrder>()
                .Property(po => po.FreightAmount).HasPrecision(18, 2);
            builder.Entity<PurchaseOrder>()
                .Property(po => po.TotalAmount).HasPrecision(18, 2);
            builder.Entity<PurchaseOrder>()
                .Property(po => po.ExchangeRate).HasPrecision(18, 6);

            // Configure indexes - CustomerCode unique constraint is handled at database level
            // with filtered index to allow NULL values
            builder.Entity<Supplier>().HasIndex(s => s.SupplierCode).IsUnique();
            builder.Entity<Product>().HasIndex(p => p.ProductCode).IsUnique();
            builder.Entity<SalesOrder>().HasIndex(so => so.OrderNumber).IsUnique();
            builder.Entity<SalesInvoice>().HasIndex(si => si.InvoiceNumber).IsUnique();
            builder.Entity<PurchaseOrder>().HasIndex(po => po.PurchaseOrderNumber).IsUnique();

            // Configure table names to match SQL scripts
            builder.Entity<Customer>().ToTable("Customer");
            builder.Entity<CustomerAddress>().ToTable("CustomerAddress");
            builder.Entity<CustomerContact>().ToTable("CustomerContact");
            builder.Entity<Supplier>().ToTable("Supplier");
            builder.Entity<SupplierAddress>().ToTable("SupplierAddress");
            builder.Entity<SupplierContact>().ToTable("SupplierContact");
            builder.Entity<Product>().ToTable("Product");
            builder.Entity<ProductCategory>().ToTable("ProductCategory");
            builder.Entity<UnitOfMeasure>().ToTable("UnitOfMeasure");
            builder.Entity<Warehouse>().ToTable("Warehouse");
            builder.Entity<SalesOrder>().ToTable("SalesOrder");
            builder.Entity<SalesOrderItem>().ToTable("SalesOrderItem");
            builder.Entity<SalesInvoice>().ToTable("SalesInvoice");
            builder.Entity<SalesInvoiceItem>().ToTable("SalesInvoiceItem");
        }
    }
}