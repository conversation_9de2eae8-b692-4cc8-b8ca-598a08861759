is_global = true
build_property.TargetFramework = net8.0
build_property.TargetFramework = net8.0
build_property.TargetPlatformMinVersion = 
build_property.TargetPlatformMinVersion = 
build_property.UsingMicrosoftNETSdkWeb = true
build_property.UsingMicrosoftNETSdkWeb = true
build_property.ProjectTypeGuids = 
build_property.ProjectTypeGuids = 
build_property.InvariantGlobalization = 
build_property.InvariantGlobalization = 
build_property.PlatformNeutralAssembly = 
build_property.PlatformNeutralAssembly = 
build_property.EnforceExtendedAnalyzerRules = 
build_property.EnforceExtendedAnalyzerRules = 
build_property._SupportedPlatformList = Linux,macOS,Windows
build_property._SupportedPlatformList = Linux,macOS,Windows
build_property.RootNamespace = nCarry.Web
build_property.RootNamespace = nCarry.Web
build_property.ProjectDir = /Users/<USER>/Source/nCarry/nCarry/nCarry.Web/
build_property.EnableComHosting = 
build_property.EnableGeneratedComInterfaceComImportInterop = 
build_property.RazorLangVersion = 8.0
build_property.SupportLocalizedComponentNames = 
build_property.GenerateRazorMetadataSourceChecksumAttributes = 
build_property.MSBuildProjectDirectory = /Users/<USER>/Source/nCarry/nCarry/nCarry.Web
build_property._RazorSourceGeneratorDebug = 

[/Users/<USER>/Source/nCarry/nCarry/nCarry.Web/Views/Account/Login.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3MvQWNjb3VudC9Mb2dpbi5jc2h0bWw=
build_metadata.AdditionalFiles.CssScope = 

[/Users/<USER>/Source/nCarry/nCarry/nCarry.Web/Views/Account/Register.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3MvQWNjb3VudC9SZWdpc3Rlci5jc2h0bWw=
build_metadata.AdditionalFiles.CssScope = 

[/Users/<USER>/Source/nCarry/nCarry/nCarry.Web/Views/APIKey/Create.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3MvQVBJS2V5L0NyZWF0ZS5jc2h0bWw=
build_metadata.AdditionalFiles.CssScope = 

[/Users/<USER>/Source/nCarry/nCarry/nCarry.Web/Views/APIKey/Delete.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3MvQVBJS2V5L0RlbGV0ZS5jc2h0bWw=
build_metadata.AdditionalFiles.CssScope = 

[/Users/<USER>/Source/nCarry/nCarry/nCarry.Web/Views/APIKey/Details.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3MvQVBJS2V5L0RldGFpbHMuY3NodG1s
build_metadata.AdditionalFiles.CssScope = 

[/Users/<USER>/Source/nCarry/nCarry/nCarry.Web/Views/APIKey/Edit.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3MvQVBJS2V5L0VkaXQuY3NodG1s
build_metadata.AdditionalFiles.CssScope = 

[/Users/<USER>/Source/nCarry/nCarry/nCarry.Web/Views/APIKey/Index.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3MvQVBJS2V5L0luZGV4LmNzaHRtbA==
build_metadata.AdditionalFiles.CssScope = 

[/Users/<USER>/Source/nCarry/nCarry/nCarry.Web/Views/APIKey/TestConnection.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3MvQVBJS2V5L1Rlc3RDb25uZWN0aW9uLmNzaHRtbA==
build_metadata.AdditionalFiles.CssScope = 

[/Users/<USER>/Source/nCarry/nCarry/nCarry.Web/Views/APIKey/Usage.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3MvQVBJS2V5L1VzYWdlLmNzaHRtbA==
build_metadata.AdditionalFiles.CssScope = 

[/Users/<USER>/Source/nCarry/nCarry/nCarry.Web/Views/Branch/Create.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3MvQnJhbmNoL0NyZWF0ZS5jc2h0bWw=
build_metadata.AdditionalFiles.CssScope = 

[/Users/<USER>/Source/nCarry/nCarry/nCarry.Web/Views/Branch/Delete.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3MvQnJhbmNoL0RlbGV0ZS5jc2h0bWw=
build_metadata.AdditionalFiles.CssScope = 

[/Users/<USER>/Source/nCarry/nCarry/nCarry.Web/Views/Branch/Details.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3MvQnJhbmNoL0RldGFpbHMuY3NodG1s
build_metadata.AdditionalFiles.CssScope = 

[/Users/<USER>/Source/nCarry/nCarry/nCarry.Web/Views/Branch/Edit.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3MvQnJhbmNoL0VkaXQuY3NodG1s
build_metadata.AdditionalFiles.CssScope = 

[/Users/<USER>/Source/nCarry/nCarry/nCarry.Web/Views/Branch/Index.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3MvQnJhbmNoL0luZGV4LmNzaHRtbA==
build_metadata.AdditionalFiles.CssScope = 

[/Users/<USER>/Source/nCarry/nCarry/nCarry.Web/Views/Carrier/Create.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3MvQ2Fycmllci9DcmVhdGUuY3NodG1s
build_metadata.AdditionalFiles.CssScope = 

[/Users/<USER>/Source/nCarry/nCarry/nCarry.Web/Views/Carrier/Delete.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3MvQ2Fycmllci9EZWxldGUuY3NodG1s
build_metadata.AdditionalFiles.CssScope = 

[/Users/<USER>/Source/nCarry/nCarry/nCarry.Web/Views/Carrier/Details.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3MvQ2Fycmllci9EZXRhaWxzLmNzaHRtbA==
build_metadata.AdditionalFiles.CssScope = 

[/Users/<USER>/Source/nCarry/nCarry/nCarry.Web/Views/Carrier/Edit.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3MvQ2Fycmllci9FZGl0LmNzaHRtbA==
build_metadata.AdditionalFiles.CssScope = 

[/Users/<USER>/Source/nCarry/nCarry/nCarry.Web/Views/Carrier/Index.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3MvQ2Fycmllci9JbmRleC5jc2h0bWw=
build_metadata.AdditionalFiles.CssScope = 

[/Users/<USER>/Source/nCarry/nCarry/nCarry.Web/Views/CarrierService/Create.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3MvQ2FycmllclNlcnZpY2UvQ3JlYXRlLmNzaHRtbA==
build_metadata.AdditionalFiles.CssScope = 

[/Users/<USER>/Source/nCarry/nCarry/nCarry.Web/Views/CarrierService/Delete.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3MvQ2FycmllclNlcnZpY2UvRGVsZXRlLmNzaHRtbA==
build_metadata.AdditionalFiles.CssScope = 

[/Users/<USER>/Source/nCarry/nCarry/nCarry.Web/Views/CarrierService/Details.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3MvQ2FycmllclNlcnZpY2UvRGV0YWlscy5jc2h0bWw=
build_metadata.AdditionalFiles.CssScope = 

[/Users/<USER>/Source/nCarry/nCarry/nCarry.Web/Views/CarrierService/Edit.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3MvQ2FycmllclNlcnZpY2UvRWRpdC5jc2h0bWw=
build_metadata.AdditionalFiles.CssScope = 

[/Users/<USER>/Source/nCarry/nCarry/nCarry.Web/Views/CarrierService/Index.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3MvQ2FycmllclNlcnZpY2UvSW5kZXguY3NodG1s
build_metadata.AdditionalFiles.CssScope = 

[/Users/<USER>/Source/nCarry/nCarry/nCarry.Web/Views/Company/Create.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3MvQ29tcGFueS9DcmVhdGUuY3NodG1s
build_metadata.AdditionalFiles.CssScope = 

[/Users/<USER>/Source/nCarry/nCarry/nCarry.Web/Views/Company/Delete.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3MvQ29tcGFueS9EZWxldGUuY3NodG1s
build_metadata.AdditionalFiles.CssScope = 

[/Users/<USER>/Source/nCarry/nCarry/nCarry.Web/Views/Company/Details.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3MvQ29tcGFueS9EZXRhaWxzLmNzaHRtbA==
build_metadata.AdditionalFiles.CssScope = 

[/Users/<USER>/Source/nCarry/nCarry/nCarry.Web/Views/Company/Edit.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3MvQ29tcGFueS9FZGl0LmNzaHRtbA==
build_metadata.AdditionalFiles.CssScope = 

[/Users/<USER>/Source/nCarry/nCarry/nCarry.Web/Views/Company/Index.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3MvQ29tcGFueS9JbmRleC5jc2h0bWw=
build_metadata.AdditionalFiles.CssScope = 

[/Users/<USER>/Source/nCarry/nCarry/nCarry.Web/Views/Customer/Create.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3MvQ3VzdG9tZXIvQ3JlYXRlLmNzaHRtbA==
build_metadata.AdditionalFiles.CssScope = 

[/Users/<USER>/Source/nCarry/nCarry/nCarry.Web/Views/Customer/Delete.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3MvQ3VzdG9tZXIvRGVsZXRlLmNzaHRtbA==
build_metadata.AdditionalFiles.CssScope = 

[/Users/<USER>/Source/nCarry/nCarry/nCarry.Web/Views/Customer/Details.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3MvQ3VzdG9tZXIvRGV0YWlscy5jc2h0bWw=
build_metadata.AdditionalFiles.CssScope = 

[/Users/<USER>/Source/nCarry/nCarry/nCarry.Web/Views/Customer/Edit.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3MvQ3VzdG9tZXIvRWRpdC5jc2h0bWw=
build_metadata.AdditionalFiles.CssScope = 

[/Users/<USER>/Source/nCarry/nCarry/nCarry.Web/Views/Customer/Index.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3MvQ3VzdG9tZXIvSW5kZXguY3NodG1s
build_metadata.AdditionalFiles.CssScope = 

[/Users/<USER>/Source/nCarry/nCarry/nCarry.Web/Views/CustomerPriceList/AddItem.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3MvQ3VzdG9tZXJQcmljZUxpc3QvQWRkSXRlbS5jc2h0bWw=
build_metadata.AdditionalFiles.CssScope = 

[/Users/<USER>/Source/nCarry/nCarry/nCarry.Web/Views/CustomerPriceList/Create.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3MvQ3VzdG9tZXJQcmljZUxpc3QvQ3JlYXRlLmNzaHRtbA==
build_metadata.AdditionalFiles.CssScope = 

[/Users/<USER>/Source/nCarry/nCarry/nCarry.Web/Views/CustomerPriceList/Delete.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3MvQ3VzdG9tZXJQcmljZUxpc3QvRGVsZXRlLmNzaHRtbA==
build_metadata.AdditionalFiles.CssScope = 

[/Users/<USER>/Source/nCarry/nCarry/nCarry.Web/Views/CustomerPriceList/Details.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3MvQ3VzdG9tZXJQcmljZUxpc3QvRGV0YWlscy5jc2h0bWw=
build_metadata.AdditionalFiles.CssScope = 

[/Users/<USER>/Source/nCarry/nCarry/nCarry.Web/Views/CustomerPriceList/Edit.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3MvQ3VzdG9tZXJQcmljZUxpc3QvRWRpdC5jc2h0bWw=
build_metadata.AdditionalFiles.CssScope = 

[/Users/<USER>/Source/nCarry/nCarry/nCarry.Web/Views/CustomerPriceList/EditItem.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3MvQ3VzdG9tZXJQcmljZUxpc3QvRWRpdEl0ZW0uY3NodG1s
build_metadata.AdditionalFiles.CssScope = 

[/Users/<USER>/Source/nCarry/nCarry/nCarry.Web/Views/CustomerPriceList/Index.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3MvQ3VzdG9tZXJQcmljZUxpc3QvSW5kZXguY3NodG1s
build_metadata.AdditionalFiles.CssScope = 

[/Users/<USER>/Source/nCarry/nCarry/nCarry.Web/Views/Dashboard/Index.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3MvRGFzaGJvYXJkL0luZGV4LmNzaHRtbA==
build_metadata.AdditionalFiles.CssScope = 

[/Users/<USER>/Source/nCarry/nCarry/nCarry.Web/Views/DeliveryZone/Create.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3MvRGVsaXZlcnlab25lL0NyZWF0ZS5jc2h0bWw=
build_metadata.AdditionalFiles.CssScope = 

[/Users/<USER>/Source/nCarry/nCarry/nCarry.Web/Views/DeliveryZone/Index.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3MvRGVsaXZlcnlab25lL0luZGV4LmNzaHRtbA==
build_metadata.AdditionalFiles.CssScope = 

[/Users/<USER>/Source/nCarry/nCarry/nCarry.Web/Views/EmailTemplate/Create.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3MvRW1haWxUZW1wbGF0ZS9DcmVhdGUuY3NodG1s
build_metadata.AdditionalFiles.CssScope = 

[/Users/<USER>/Source/nCarry/nCarry/nCarry.Web/Views/EmailTemplate/Delete.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3MvRW1haWxUZW1wbGF0ZS9EZWxldGUuY3NodG1s
build_metadata.AdditionalFiles.CssScope = 

[/Users/<USER>/Source/nCarry/nCarry/nCarry.Web/Views/EmailTemplate/Details.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3MvRW1haWxUZW1wbGF0ZS9EZXRhaWxzLmNzaHRtbA==
build_metadata.AdditionalFiles.CssScope = 

[/Users/<USER>/Source/nCarry/nCarry/nCarry.Web/Views/EmailTemplate/Edit.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3MvRW1haWxUZW1wbGF0ZS9FZGl0LmNzaHRtbA==
build_metadata.AdditionalFiles.CssScope = 

[/Users/<USER>/Source/nCarry/nCarry/nCarry.Web/Views/EmailTemplate/Index.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3MvRW1haWxUZW1wbGF0ZS9JbmRleC5jc2h0bWw=
build_metadata.AdditionalFiles.CssScope = 

[/Users/<USER>/Source/nCarry/nCarry/nCarry.Web/Views/EmailTemplate/Preview.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3MvRW1haWxUZW1wbGF0ZS9QcmV2aWV3LmNzaHRtbA==
build_metadata.AdditionalFiles.CssScope = 

[/Users/<USER>/Source/nCarry/nCarry/nCarry.Web/Views/FreightRate/Calculator.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3MvRnJlaWdodFJhdGUvQ2FsY3VsYXRvci5jc2h0bWw=
build_metadata.AdditionalFiles.CssScope = 

[/Users/<USER>/Source/nCarry/nCarry/nCarry.Web/Views/FreightRate/Index.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3MvRnJlaWdodFJhdGUvSW5kZXguY3NodG1s
build_metadata.AdditionalFiles.CssScope = 

[/Users/<USER>/Source/nCarry/nCarry/nCarry.Web/Views/GoodsReceipt/Create.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3MvR29vZHNSZWNlaXB0L0NyZWF0ZS5jc2h0bWw=
build_metadata.AdditionalFiles.CssScope = 

[/Users/<USER>/Source/nCarry/nCarry/nCarry.Web/Views/GoodsReceipt/Details.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3MvR29vZHNSZWNlaXB0L0RldGFpbHMuY3NodG1s
build_metadata.AdditionalFiles.CssScope = 

[/Users/<USER>/Source/nCarry/nCarry/nCarry.Web/Views/GoodsReceipt/Index.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3MvR29vZHNSZWNlaXB0L0luZGV4LmNzaHRtbA==
build_metadata.AdditionalFiles.CssScope = 

[/Users/<USER>/Source/nCarry/nCarry/nCarry.Web/Views/GoodsReceipt/Print.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3MvR29vZHNSZWNlaXB0L1ByaW50LmNzaHRtbA==
build_metadata.AdditionalFiles.CssScope = 

[/Users/<USER>/Source/nCarry/nCarry/nCarry.Web/Views/Home/Index.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3MvSG9tZS9JbmRleC5jc2h0bWw=
build_metadata.AdditionalFiles.CssScope = 

[/Users/<USER>/Source/nCarry/nCarry/nCarry.Web/Views/Home/Privacy.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3MvSG9tZS9Qcml2YWN5LmNzaHRtbA==
build_metadata.AdditionalFiles.CssScope = 

[/Users/<USER>/Source/nCarry/nCarry/nCarry.Web/Views/Inventory/Details.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3MvSW52ZW50b3J5L0RldGFpbHMuY3NodG1s
build_metadata.AdditionalFiles.CssScope = 

[/Users/<USER>/Source/nCarry/nCarry/nCarry.Web/Views/Inventory/Index.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3MvSW52ZW50b3J5L0luZGV4LmNzaHRtbA==
build_metadata.AdditionalFiles.CssScope = 

[/Users/<USER>/Source/nCarry/nCarry/nCarry.Web/Views/Inventory/StockAdjustment.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3MvSW52ZW50b3J5L1N0b2NrQWRqdXN0bWVudC5jc2h0bWw=
build_metadata.AdditionalFiles.CssScope = 

[/Users/<USER>/Source/nCarry/nCarry/nCarry.Web/Views/Inventory/Transactions.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3MvSW52ZW50b3J5L1RyYW5zYWN0aW9ucy5jc2h0bWw=
build_metadata.AdditionalFiles.CssScope = 

[/Users/<USER>/Source/nCarry/nCarry/nCarry.Web/Views/Inventory/Transfer.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3MvSW52ZW50b3J5L1RyYW5zZmVyLmNzaHRtbA==
build_metadata.AdditionalFiles.CssScope = 

[/Users/<USER>/Source/nCarry/nCarry/nCarry.Web/Views/KPI/Configure.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3MvS1BJL0NvbmZpZ3VyZS5jc2h0bWw=
build_metadata.AdditionalFiles.CssScope = 

[/Users/<USER>/Source/nCarry/nCarry/nCarry.Web/Views/KPI/Create.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3MvS1BJL0NyZWF0ZS5jc2h0bWw=
build_metadata.AdditionalFiles.CssScope = 

[/Users/<USER>/Source/nCarry/nCarry/nCarry.Web/Views/KPI/Delete.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3MvS1BJL0RlbGV0ZS5jc2h0bWw=
build_metadata.AdditionalFiles.CssScope = 

[/Users/<USER>/Source/nCarry/nCarry/nCarry.Web/Views/KPI/Details.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3MvS1BJL0RldGFpbHMuY3NodG1s
build_metadata.AdditionalFiles.CssScope = 

[/Users/<USER>/Source/nCarry/nCarry/nCarry.Web/Views/KPI/Edit.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3MvS1BJL0VkaXQuY3NodG1s
build_metadata.AdditionalFiles.CssScope = 

[/Users/<USER>/Source/nCarry/nCarry/nCarry.Web/Views/KPI/Index.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3MvS1BJL0luZGV4LmNzaHRtbA==
build_metadata.AdditionalFiles.CssScope = 

[/Users/<USER>/Source/nCarry/nCarry/nCarry.Web/Views/NotificationTemplate/Create.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3MvTm90aWZpY2F0aW9uVGVtcGxhdGUvQ3JlYXRlLmNzaHRtbA==
build_metadata.AdditionalFiles.CssScope = 

[/Users/<USER>/Source/nCarry/nCarry/nCarry.Web/Views/NotificationTemplate/Delete.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3MvTm90aWZpY2F0aW9uVGVtcGxhdGUvRGVsZXRlLmNzaHRtbA==
build_metadata.AdditionalFiles.CssScope = 

[/Users/<USER>/Source/nCarry/nCarry/nCarry.Web/Views/NotificationTemplate/Details.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3MvTm90aWZpY2F0aW9uVGVtcGxhdGUvRGV0YWlscy5jc2h0bWw=
build_metadata.AdditionalFiles.CssScope = 

[/Users/<USER>/Source/nCarry/nCarry/nCarry.Web/Views/NotificationTemplate/Edit.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3MvTm90aWZpY2F0aW9uVGVtcGxhdGUvRWRpdC5jc2h0bWw=
build_metadata.AdditionalFiles.CssScope = 

[/Users/<USER>/Source/nCarry/nCarry/nCarry.Web/Views/NotificationTemplate/Index.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3MvTm90aWZpY2F0aW9uVGVtcGxhdGUvSW5kZXguY3NodG1s
build_metadata.AdditionalFiles.CssScope = 

[/Users/<USER>/Source/nCarry/nCarry/nCarry.Web/Views/NotificationTemplate/Preview.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3MvTm90aWZpY2F0aW9uVGVtcGxhdGUvUHJldmlldy5jc2h0bWw=
build_metadata.AdditionalFiles.CssScope = 

[/Users/<USER>/Source/nCarry/nCarry/nCarry.Web/Views/NumberSequence/Index.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3MvTnVtYmVyU2VxdWVuY2UvSW5kZXguY3NodG1s
build_metadata.AdditionalFiles.CssScope = 

[/Users/<USER>/Source/nCarry/nCarry/nCarry.Web/Views/PackingList/Create.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3MvUGFja2luZ0xpc3QvQ3JlYXRlLmNzaHRtbA==
build_metadata.AdditionalFiles.CssScope = 

[/Users/<USER>/Source/nCarry/nCarry/nCarry.Web/Views/PackingList/Details.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3MvUGFja2luZ0xpc3QvRGV0YWlscy5jc2h0bWw=
build_metadata.AdditionalFiles.CssScope = 

[/Users/<USER>/Source/nCarry/nCarry/nCarry.Web/Views/PackingList/Index.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3MvUGFja2luZ0xpc3QvSW5kZXguY3NodG1s
build_metadata.AdditionalFiles.CssScope = 

[/Users/<USER>/Source/nCarry/nCarry/nCarry.Web/Views/PackingList/PackageOptimization.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3MvUGFja2luZ0xpc3QvUGFja2FnZU9wdGltaXphdGlvbi5jc2h0bWw=
build_metadata.AdditionalFiles.CssScope = 

[/Users/<USER>/Source/nCarry/nCarry/nCarry.Web/Views/Payment/Create.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3MvUGF5bWVudC9DcmVhdGUuY3NodG1s
build_metadata.AdditionalFiles.CssScope = 

[/Users/<USER>/Source/nCarry/nCarry/nCarry.Web/Views/Payment/Delete.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3MvUGF5bWVudC9EZWxldGUuY3NodG1s
build_metadata.AdditionalFiles.CssScope = 

[/Users/<USER>/Source/nCarry/nCarry/nCarry.Web/Views/Payment/Details.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3MvUGF5bWVudC9EZXRhaWxzLmNzaHRtbA==
build_metadata.AdditionalFiles.CssScope = 

[/Users/<USER>/Source/nCarry/nCarry/nCarry.Web/Views/Payment/Edit.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3MvUGF5bWVudC9FZGl0LmNzaHRtbA==
build_metadata.AdditionalFiles.CssScope = 

[/Users/<USER>/Source/nCarry/nCarry/nCarry.Web/Views/Payment/Index.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3MvUGF5bWVudC9JbmRleC5jc2h0bWw=
build_metadata.AdditionalFiles.CssScope = 

[/Users/<USER>/Source/nCarry/nCarry/nCarry.Web/Views/PickingList/Create.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3MvUGlja2luZ0xpc3QvQ3JlYXRlLmNzaHRtbA==
build_metadata.AdditionalFiles.CssScope = 

[/Users/<USER>/Source/nCarry/nCarry/nCarry.Web/Views/PickingList/Details.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3MvUGlja2luZ0xpc3QvRGV0YWlscy5jc2h0bWw=
build_metadata.AdditionalFiles.CssScope = 

[/Users/<USER>/Source/nCarry/nCarry/nCarry.Web/Views/PickingList/Edit.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3MvUGlja2luZ0xpc3QvRWRpdC5jc2h0bWw=
build_metadata.AdditionalFiles.CssScope = 

[/Users/<USER>/Source/nCarry/nCarry/nCarry.Web/Views/PickingList/Generate.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3MvUGlja2luZ0xpc3QvR2VuZXJhdGUuY3NodG1s
build_metadata.AdditionalFiles.CssScope = 

[/Users/<USER>/Source/nCarry/nCarry/nCarry.Web/Views/PickingList/Index.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3MvUGlja2luZ0xpc3QvSW5kZXguY3NodG1s
build_metadata.AdditionalFiles.CssScope = 

[/Users/<USER>/Source/nCarry/nCarry/nCarry.Web/Views/PickingList/Pick.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3MvUGlja2luZ0xpc3QvUGljay5jc2h0bWw=
build_metadata.AdditionalFiles.CssScope = 

[/Users/<USER>/Source/nCarry/nCarry/nCarry.Web/Views/Product/Create.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3MvUHJvZHVjdC9DcmVhdGUuY3NodG1s
build_metadata.AdditionalFiles.CssScope = 

[/Users/<USER>/Source/nCarry/nCarry/nCarry.Web/Views/Product/Delete.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3MvUHJvZHVjdC9EZWxldGUuY3NodG1s
build_metadata.AdditionalFiles.CssScope = 

[/Users/<USER>/Source/nCarry/nCarry/nCarry.Web/Views/Product/Details.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3MvUHJvZHVjdC9EZXRhaWxzLmNzaHRtbA==
build_metadata.AdditionalFiles.CssScope = 

[/Users/<USER>/Source/nCarry/nCarry/nCarry.Web/Views/Product/Edit.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3MvUHJvZHVjdC9FZGl0LmNzaHRtbA==
build_metadata.AdditionalFiles.CssScope = 

[/Users/<USER>/Source/nCarry/nCarry/nCarry.Web/Views/Product/Index.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3MvUHJvZHVjdC9JbmRleC5jc2h0bWw=
build_metadata.AdditionalFiles.CssScope = 

[/Users/<USER>/Source/nCarry/nCarry/nCarry.Web/Views/ProductCategory/Create.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3MvUHJvZHVjdENhdGVnb3J5L0NyZWF0ZS5jc2h0bWw=
build_metadata.AdditionalFiles.CssScope = 

[/Users/<USER>/Source/nCarry/nCarry/nCarry.Web/Views/ProductCategory/Delete.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3MvUHJvZHVjdENhdGVnb3J5L0RlbGV0ZS5jc2h0bWw=
build_metadata.AdditionalFiles.CssScope = 

[/Users/<USER>/Source/nCarry/nCarry/nCarry.Web/Views/ProductCategory/Edit.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3MvUHJvZHVjdENhdGVnb3J5L0VkaXQuY3NodG1s
build_metadata.AdditionalFiles.CssScope = 

[/Users/<USER>/Source/nCarry/nCarry/nCarry.Web/Views/ProductCategory/Index.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3MvUHJvZHVjdENhdGVnb3J5L0luZGV4LmNzaHRtbA==
build_metadata.AdditionalFiles.CssScope = 

[/Users/<USER>/Source/nCarry/nCarry/nCarry.Web/Views/ProductImport/ImportResult.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3MvUHJvZHVjdEltcG9ydC9JbXBvcnRSZXN1bHQuY3NodG1s
build_metadata.AdditionalFiles.CssScope = 

[/Users/<USER>/Source/nCarry/nCarry/nCarry.Web/Views/ProductImport/Index.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3MvUHJvZHVjdEltcG9ydC9JbmRleC5jc2h0bWw=
build_metadata.AdditionalFiles.CssScope = 

[/Users/<USER>/Source/nCarry/nCarry/nCarry.Web/Views/PurchaseOrder/AddItem.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3MvUHVyY2hhc2VPcmRlci9BZGRJdGVtLmNzaHRtbA==
build_metadata.AdditionalFiles.CssScope = 

[/Users/<USER>/Source/nCarry/nCarry/nCarry.Web/Views/PurchaseOrder/Create.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3MvUHVyY2hhc2VPcmRlci9DcmVhdGUuY3NodG1s
build_metadata.AdditionalFiles.CssScope = 

[/Users/<USER>/Source/nCarry/nCarry/nCarry.Web/Views/PurchaseOrder/Delete.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3MvUHVyY2hhc2VPcmRlci9EZWxldGUuY3NodG1s
build_metadata.AdditionalFiles.CssScope = 

[/Users/<USER>/Source/nCarry/nCarry/nCarry.Web/Views/PurchaseOrder/Details.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3MvUHVyY2hhc2VPcmRlci9EZXRhaWxzLmNzaHRtbA==
build_metadata.AdditionalFiles.CssScope = 

[/Users/<USER>/Source/nCarry/nCarry/nCarry.Web/Views/PurchaseOrder/Edit.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3MvUHVyY2hhc2VPcmRlci9FZGl0LmNzaHRtbA==
build_metadata.AdditionalFiles.CssScope = 

[/Users/<USER>/Source/nCarry/nCarry/nCarry.Web/Views/PurchaseOrder/Index.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3MvUHVyY2hhc2VPcmRlci9JbmRleC5jc2h0bWw=
build_metadata.AdditionalFiles.CssScope = 

[/Users/<USER>/Source/nCarry/nCarry/nCarry.Web/Views/Reports/CustomerReport.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3MvUmVwb3J0cy9DdXN0b21lclJlcG9ydC5jc2h0bWw=
build_metadata.AdditionalFiles.CssScope = 

[/Users/<USER>/Source/nCarry/nCarry/nCarry.Web/Views/Reports/Index.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3MvUmVwb3J0cy9JbmRleC5jc2h0bWw=
build_metadata.AdditionalFiles.CssScope = 

[/Users/<USER>/Source/nCarry/nCarry/nCarry.Web/Views/Reports/InventoryReport.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3MvUmVwb3J0cy9JbnZlbnRvcnlSZXBvcnQuY3NodG1s
build_metadata.AdditionalFiles.CssScope = 

[/Users/<USER>/Source/nCarry/nCarry/nCarry.Web/Views/Reports/ProductReport.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3MvUmVwb3J0cy9Qcm9kdWN0UmVwb3J0LmNzaHRtbA==
build_metadata.AdditionalFiles.CssScope = 

[/Users/<USER>/Source/nCarry/nCarry/nCarry.Web/Views/Reports/ProfitLoss.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3MvUmVwb3J0cy9Qcm9maXRMb3NzLmNzaHRtbA==
build_metadata.AdditionalFiles.CssScope = 

[/Users/<USER>/Source/nCarry/nCarry/nCarry.Web/Views/Reports/SalesReport.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3MvUmVwb3J0cy9TYWxlc1JlcG9ydC5jc2h0bWw=
build_metadata.AdditionalFiles.CssScope = 

[/Users/<USER>/Source/nCarry/nCarry/nCarry.Web/Views/Role/Create.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3MvUm9sZS9DcmVhdGUuY3NodG1s
build_metadata.AdditionalFiles.CssScope = 

[/Users/<USER>/Source/nCarry/nCarry/nCarry.Web/Views/Role/Delete.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3MvUm9sZS9EZWxldGUuY3NodG1s
build_metadata.AdditionalFiles.CssScope = 

[/Users/<USER>/Source/nCarry/nCarry/nCarry.Web/Views/Role/Details.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3MvUm9sZS9EZXRhaWxzLmNzaHRtbA==
build_metadata.AdditionalFiles.CssScope = 

[/Users/<USER>/Source/nCarry/nCarry/nCarry.Web/Views/Role/Edit.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3MvUm9sZS9FZGl0LmNzaHRtbA==
build_metadata.AdditionalFiles.CssScope = 

[/Users/<USER>/Source/nCarry/nCarry/nCarry.Web/Views/Role/Index.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3MvUm9sZS9JbmRleC5jc2h0bWw=
build_metadata.AdditionalFiles.CssScope = 

[/Users/<USER>/Source/nCarry/nCarry/nCarry.Web/Views/Route/Index.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3MvUm91dGUvSW5kZXguY3NodG1s
build_metadata.AdditionalFiles.CssScope = 

[/Users/<USER>/Source/nCarry/nCarry/nCarry.Web/Views/Route/Optimize.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3MvUm91dGUvT3B0aW1pemUuY3NodG1s
build_metadata.AdditionalFiles.CssScope = 

[/Users/<USER>/Source/nCarry/nCarry/nCarry.Web/Views/SalesInvoice/Create.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3MvU2FsZXNJbnZvaWNlL0NyZWF0ZS5jc2h0bWw=
build_metadata.AdditionalFiles.CssScope = 

[/Users/<USER>/Source/nCarry/nCarry/nCarry.Web/Views/SalesInvoice/Delete.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3MvU2FsZXNJbnZvaWNlL0RlbGV0ZS5jc2h0bWw=
build_metadata.AdditionalFiles.CssScope = 

[/Users/<USER>/Source/nCarry/nCarry/nCarry.Web/Views/SalesInvoice/Details.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3MvU2FsZXNJbnZvaWNlL0RldGFpbHMuY3NodG1s
build_metadata.AdditionalFiles.CssScope = 

[/Users/<USER>/Source/nCarry/nCarry/nCarry.Web/Views/SalesInvoice/Edit.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3MvU2FsZXNJbnZvaWNlL0VkaXQuY3NodG1s
build_metadata.AdditionalFiles.CssScope = 

[/Users/<USER>/Source/nCarry/nCarry/nCarry.Web/Views/SalesInvoice/Index.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3MvU2FsZXNJbnZvaWNlL0luZGV4LmNzaHRtbA==
build_metadata.AdditionalFiles.CssScope = 

[/Users/<USER>/Source/nCarry/nCarry/nCarry.Web/Views/SalesInvoice/Print.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3MvU2FsZXNJbnZvaWNlL1ByaW50LmNzaHRtbA==
build_metadata.AdditionalFiles.CssScope = 

[/Users/<USER>/Source/nCarry/nCarry/nCarry.Web/Views/SalesOrder/Create.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3MvU2FsZXNPcmRlci9DcmVhdGUuY3NodG1s
build_metadata.AdditionalFiles.CssScope = 

[/Users/<USER>/Source/nCarry/nCarry/nCarry.Web/Views/SalesOrder/Delete.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3MvU2FsZXNPcmRlci9EZWxldGUuY3NodG1s
build_metadata.AdditionalFiles.CssScope = 

[/Users/<USER>/Source/nCarry/nCarry/nCarry.Web/Views/SalesOrder/Details.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3MvU2FsZXNPcmRlci9EZXRhaWxzLmNzaHRtbA==
build_metadata.AdditionalFiles.CssScope = 

[/Users/<USER>/Source/nCarry/nCarry/nCarry.Web/Views/SalesOrder/Edit.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3MvU2FsZXNPcmRlci9FZGl0LmNzaHRtbA==
build_metadata.AdditionalFiles.CssScope = 

[/Users/<USER>/Source/nCarry/nCarry/nCarry.Web/Views/SalesOrder/Index.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3MvU2FsZXNPcmRlci9JbmRleC5jc2h0bWw=
build_metadata.AdditionalFiles.CssScope = 

[/Users/<USER>/Source/nCarry/nCarry/nCarry.Web/Views/SeedData/Index.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3MvU2VlZERhdGEvSW5kZXguY3NodG1s
build_metadata.AdditionalFiles.CssScope = 

[/Users/<USER>/Source/nCarry/nCarry/nCarry.Web/Views/Shared/Error.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3MvU2hhcmVkL0Vycm9yLmNzaHRtbA==
build_metadata.AdditionalFiles.CssScope = 

[/Users/<USER>/Source/nCarry/nCarry/nCarry.Web/Views/Shared/_LoginPartial.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3MvU2hhcmVkL19Mb2dpblBhcnRpYWwuY3NodG1s
build_metadata.AdditionalFiles.CssScope = 

[/Users/<USER>/Source/nCarry/nCarry/nCarry.Web/Views/Shared/_ValidationScriptsPartial.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3MvU2hhcmVkL19WYWxpZGF0aW9uU2NyaXB0c1BhcnRpYWwuY3NodG1s
build_metadata.AdditionalFiles.CssScope = 

[/Users/<USER>/Source/nCarry/nCarry/nCarry.Web/Views/Shipment/BulkUpdate.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3MvU2hpcG1lbnQvQnVsa1VwZGF0ZS5jc2h0bWw=
build_metadata.AdditionalFiles.CssScope = 

[/Users/<USER>/Source/nCarry/nCarry/nCarry.Web/Views/Shipment/Create.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3MvU2hpcG1lbnQvQ3JlYXRlLmNzaHRtbA==
build_metadata.AdditionalFiles.CssScope = 

[/Users/<USER>/Source/nCarry/nCarry/nCarry.Web/Views/Shipment/Details.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3MvU2hpcG1lbnQvRGV0YWlscy5jc2h0bWw=
build_metadata.AdditionalFiles.CssScope = 

[/Users/<USER>/Source/nCarry/nCarry/nCarry.Web/Views/Shipment/Edit.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3MvU2hpcG1lbnQvRWRpdC5jc2h0bWw=
build_metadata.AdditionalFiles.CssScope = 

[/Users/<USER>/Source/nCarry/nCarry/nCarry.Web/Views/Shipment/Index.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3MvU2hpcG1lbnQvSW5kZXguY3NodG1s
build_metadata.AdditionalFiles.CssScope = 

[/Users/<USER>/Source/nCarry/nCarry/nCarry.Web/Views/Shipment/PrintLabel.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3MvU2hpcG1lbnQvUHJpbnRMYWJlbC5jc2h0bWw=
build_metadata.AdditionalFiles.CssScope = 

[/Users/<USER>/Source/nCarry/nCarry/nCarry.Web/Views/Shipment/Track.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3MvU2hpcG1lbnQvVHJhY2suY3NodG1s
build_metadata.AdditionalFiles.CssScope = 

[/Users/<USER>/Source/nCarry/nCarry/nCarry.Web/Views/Shipment/TrackingResult.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3MvU2hpcG1lbnQvVHJhY2tpbmdSZXN1bHQuY3NodG1s
build_metadata.AdditionalFiles.CssScope = 

[/Users/<USER>/Source/nCarry/nCarry/nCarry.Web/Views/Supplier/Create.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3MvU3VwcGxpZXIvQ3JlYXRlLmNzaHRtbA==
build_metadata.AdditionalFiles.CssScope = 

[/Users/<USER>/Source/nCarry/nCarry/nCarry.Web/Views/Supplier/Delete.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3MvU3VwcGxpZXIvRGVsZXRlLmNzaHRtbA==
build_metadata.AdditionalFiles.CssScope = 

[/Users/<USER>/Source/nCarry/nCarry/nCarry.Web/Views/Supplier/Details.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3MvU3VwcGxpZXIvRGV0YWlscy5jc2h0bWw=
build_metadata.AdditionalFiles.CssScope = 

[/Users/<USER>/Source/nCarry/nCarry/nCarry.Web/Views/Supplier/Edit.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3MvU3VwcGxpZXIvRWRpdC5jc2h0bWw=
build_metadata.AdditionalFiles.CssScope = 

[/Users/<USER>/Source/nCarry/nCarry/nCarry.Web/Views/Supplier/Index.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3MvU3VwcGxpZXIvSW5kZXguY3NodG1s
build_metadata.AdditionalFiles.CssScope = 

[/Users/<USER>/Source/nCarry/nCarry/nCarry.Web/Views/SupplierInvoice/Approve.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3MvU3VwcGxpZXJJbnZvaWNlL0FwcHJvdmUuY3NodG1s
build_metadata.AdditionalFiles.CssScope = 

[/Users/<USER>/Source/nCarry/nCarry/nCarry.Web/Views/SupplierInvoice/Create.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3MvU3VwcGxpZXJJbnZvaWNlL0NyZWF0ZS5jc2h0bWw=
build_metadata.AdditionalFiles.CssScope = 

[/Users/<USER>/Source/nCarry/nCarry/nCarry.Web/Views/SupplierInvoice/CreatePayment.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3MvU3VwcGxpZXJJbnZvaWNlL0NyZWF0ZVBheW1lbnQuY3NodG1s
build_metadata.AdditionalFiles.CssScope = 

[/Users/<USER>/Source/nCarry/nCarry/nCarry.Web/Views/SupplierInvoice/Details.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3MvU3VwcGxpZXJJbnZvaWNlL0RldGFpbHMuY3NodG1s
build_metadata.AdditionalFiles.CssScope = 

[/Users/<USER>/Source/nCarry/nCarry/nCarry.Web/Views/SupplierInvoice/Index.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3MvU3VwcGxpZXJJbnZvaWNlL0luZGV4LmNzaHRtbA==
build_metadata.AdditionalFiles.CssScope = 

[/Users/<USER>/Source/nCarry/nCarry/nCarry.Web/Views/SystemConfiguration/Categories.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3MvU3lzdGVtQ29uZmlndXJhdGlvbi9DYXRlZ29yaWVzLmNzaHRtbA==
build_metadata.AdditionalFiles.CssScope = 

[/Users/<USER>/Source/nCarry/nCarry/nCarry.Web/Views/SystemConfiguration/Index.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3MvU3lzdGVtQ29uZmlndXJhdGlvbi9JbmRleC5jc2h0bWw=
build_metadata.AdditionalFiles.CssScope = 

[/Users/<USER>/Source/nCarry/nCarry/nCarry.Web/Views/UnitOfMeasure/Create.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3MvVW5pdE9mTWVhc3VyZS9DcmVhdGUuY3NodG1s
build_metadata.AdditionalFiles.CssScope = 

[/Users/<USER>/Source/nCarry/nCarry/nCarry.Web/Views/UnitOfMeasure/Delete.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3MvVW5pdE9mTWVhc3VyZS9EZWxldGUuY3NodG1s
build_metadata.AdditionalFiles.CssScope = 

[/Users/<USER>/Source/nCarry/nCarry/nCarry.Web/Views/UnitOfMeasure/Details.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3MvVW5pdE9mTWVhc3VyZS9EZXRhaWxzLmNzaHRtbA==
build_metadata.AdditionalFiles.CssScope = 

[/Users/<USER>/Source/nCarry/nCarry/nCarry.Web/Views/UnitOfMeasure/Edit.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3MvVW5pdE9mTWVhc3VyZS9FZGl0LmNzaHRtbA==
build_metadata.AdditionalFiles.CssScope = 

[/Users/<USER>/Source/nCarry/nCarry/nCarry.Web/Views/UnitOfMeasure/Index.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3MvVW5pdE9mTWVhc3VyZS9JbmRleC5jc2h0bWw=
build_metadata.AdditionalFiles.CssScope = 

[/Users/<USER>/Source/nCarry/nCarry/nCarry.Web/Views/User/Create.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3MvVXNlci9DcmVhdGUuY3NodG1s
build_metadata.AdditionalFiles.CssScope = 

[/Users/<USER>/Source/nCarry/nCarry/nCarry.Web/Views/User/Delete.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3MvVXNlci9EZWxldGUuY3NodG1s
build_metadata.AdditionalFiles.CssScope = 

[/Users/<USER>/Source/nCarry/nCarry/nCarry.Web/Views/User/Details.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3MvVXNlci9EZXRhaWxzLmNzaHRtbA==
build_metadata.AdditionalFiles.CssScope = 

[/Users/<USER>/Source/nCarry/nCarry/nCarry.Web/Views/User/Edit.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3MvVXNlci9FZGl0LmNzaHRtbA==
build_metadata.AdditionalFiles.CssScope = 

[/Users/<USER>/Source/nCarry/nCarry/nCarry.Web/Views/User/Index.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3MvVXNlci9JbmRleC5jc2h0bWw=
build_metadata.AdditionalFiles.CssScope = 

[/Users/<USER>/Source/nCarry/nCarry/nCarry.Web/Views/User/ResetPassword.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3MvVXNlci9SZXNldFBhc3N3b3JkLmNzaHRtbA==
build_metadata.AdditionalFiles.CssScope = 

[/Users/<USER>/Source/nCarry/nCarry/nCarry.Web/Views/Warehouse/Create.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3MvV2FyZWhvdXNlL0NyZWF0ZS5jc2h0bWw=
build_metadata.AdditionalFiles.CssScope = 

[/Users/<USER>/Source/nCarry/nCarry/nCarry.Web/Views/Warehouse/Delete.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3MvV2FyZWhvdXNlL0RlbGV0ZS5jc2h0bWw=
build_metadata.AdditionalFiles.CssScope = 

[/Users/<USER>/Source/nCarry/nCarry/nCarry.Web/Views/Warehouse/Details.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3MvV2FyZWhvdXNlL0RldGFpbHMuY3NodG1s
build_metadata.AdditionalFiles.CssScope = 

[/Users/<USER>/Source/nCarry/nCarry/nCarry.Web/Views/Warehouse/Edit.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3MvV2FyZWhvdXNlL0VkaXQuY3NodG1s
build_metadata.AdditionalFiles.CssScope = 

[/Users/<USER>/Source/nCarry/nCarry/nCarry.Web/Views/Warehouse/Index.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3MvV2FyZWhvdXNlL0luZGV4LmNzaHRtbA==
build_metadata.AdditionalFiles.CssScope = 

[/Users/<USER>/Source/nCarry/nCarry/nCarry.Web/Views/_ViewImports.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3MvX1ZpZXdJbXBvcnRzLmNzaHRtbA==
build_metadata.AdditionalFiles.CssScope = 

[/Users/<USER>/Source/nCarry/nCarry/nCarry.Web/Views/_ViewStart.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3MvX1ZpZXdTdGFydC5jc2h0bWw=
build_metadata.AdditionalFiles.CssScope = 

[/Users/<USER>/Source/nCarry/nCarry/nCarry.Web/Views/Shared/_Layout.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3MvU2hhcmVkL19MYXlvdXQuY3NodG1s
build_metadata.AdditionalFiles.CssScope = b-4m2fi7fuog
