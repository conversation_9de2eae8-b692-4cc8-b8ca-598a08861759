2025-07-30 00:00:01.506 +03:00 [INF] HTTP GET /Dashboard responded 200 in 2645.0323 ms
2025-07-30 00:00:01.522 +03:00 [INF] HTTP GET /nCarry.Web.styles.css responded 304 in 10.0507 ms
2025-07-30 00:00:01.728 +03:00 [INF] HTTP GET /Dashboard/GetChartData responded 200 in 167.5190 ms
2025-07-30 00:00:08.461 +03:00 [INF] HTTP GET /GoodsReceipt/Create responded 200 in 1064.9707 ms
2025-07-30 00:00:32.608 +03:00 [ERR] Failed executing DbCommand (141ms) [Parameters=[@p0='?' (Precision = 18) (Scale = 2) (DbType = Decimal), @p1='?' (Size = 50), @p2='?' (DbType = DateTime2), @p3='?' (DbType = DateTime2), @p4='?' (Precision = 18) (Scale = 2) (DbType = Decimal), @p5='?' (DbType = DateTime2), @p6='?' (DbType = Int32), @p7='?' (DbType = Int32), @p8='?' (Precision = 18) (Scale = 4) (DbType = Decimal), @p9='?' (Precision = 18) (Scale = 4) (DbType = Decimal), @p10='?' (Precision = 18) (Scale = 4) (DbType = Decimal), @p11='?' (Precision = 18) (Scale = 4) (DbType = Decimal), @p12='?' (Precision = 18) (Scale = 4) (DbType = Decimal), @p13='?' (Size = 50), @p14='?' (DbType = DateTime2), @p15='?' (DbType = Int32), @p16='?' (DbType = Int32), @p17='?' (Precision = 18) (Scale = 2) (DbType = Decimal), @p18='?' (Size = 50), @p19='?' (DbType = DateTime2), @p20='?' (DbType = DateTime2), @p21='?' (Precision = 18) (Scale = 2) (DbType = Decimal), @p22='?' (DbType = DateTime2), @p23='?' (DbType = Int32), @p24='?' (DbType = Int32), @p25='?' (Precision = 18) (Scale = 4) (DbType = Decimal), @p26='?' (Precision = 18) (Scale = 4) (DbType = Decimal), @p27='?' (Precision = 18) (Scale = 4) (DbType = Decimal), @p28='?' (Precision = 18) (Scale = 4) (DbType = Decimal), @p29='?' (Precision = 18) (Scale = 4) (DbType = Decimal), @p30='?' (Size = 50), @p31='?' (DbType = DateTime2), @p32='?' (DbType = Int32), @p33='?' (DbType = Int32), @p34='?' (Size = 50), @p35='?' (DbType = Int32), @p36='?' (DbType = DateTime2), @p37='?' (DbType = Int32), @p38='?' (Size = 255), @p39='?' (DbType = Int32), @p40='?' (Precision = 18) (Scale = 4) (DbType = Decimal), @p41='?' (DbType = Int32), @p42='?' (Size = 50), @p43='?' (Size = 50), @p44='?' (Size = 50), @p45='?' (Precision = 18) (Scale = 2) (DbType = Decimal), @p46='?' (DbType = DateTime2), @p47='?' (Size = 20), @p48='?' (DbType = Int32), @p49='?' (Precision = 18) (Scale = 2) (DbType = Decimal), @p50='?' (DbType = Int32), @p51='?' (DbType = Int32), @p52='?' (Size = 50), @p53='?' (DbType = Int32), @p54='?' (DbType = DateTime2), @p55='?' (DbType = Int32), @p56='?' (Size = 255), @p57='?' (DbType = Int32), @p58='?' (Precision = 18) (Scale = 4) (DbType = Decimal), @p59='?' (DbType = Int32), @p60='?' (Size = 50), @p61='?' (Size = 50), @p62='?' (Size = 50), @p63='?' (Precision = 18) (Scale = 2) (DbType = Decimal), @p64='?' (DbType = DateTime2), @p65='?' (Size = 20), @p66='?' (DbType = Int32), @p67='?' (Precision = 18) (Scale = 2) (DbType = Decimal), @p68='?' (DbType = Int32), @p69='?' (DbType = Int32), @p71='?' (DbType = Int32), @p70='?' (Size = 20), @p74='?' (DbType = Int32), @p72='?' (Size = 20), @p73='?' (Precision = 18) (Scale = 4) (DbType = Decimal)], CommandType='"Text"', CommandTimeout='30']
SET NOCOUNT ON;
MERGE [Inventory] USING (
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10, @p11, @p12, @p13, @p14, @p15, @p16, 0),
(@p17, @p18, @p19, @p20, @p21, @p22, @p23, @p24, @p25, @p26, @p27, @p28, @p29, @p30, @p31, @p32, @p33, 1)) AS i ([AverageCost], [BatchNumber], [CreatedDate], [ExpiryDate], [LastCost], [LastCountDate], [LocationID], [ProductID], [QuantityAvailable], [QuantityInTransit], [QuantityOnHand], [QuantityOnOrder], [QuantityReserved], [SerialNumber], [UpdatedDate], [VariantID], [WarehouseID], _Position) ON 1=0
WHEN NOT MATCHED THEN
INSERT ([AverageCost], [BatchNumber], [CreatedDate], [ExpiryDate], [LastCost], [LastCountDate], [LocationID], [ProductID], [QuantityAvailable], [QuantityInTransit], [QuantityOnHand], [QuantityOnOrder], [QuantityReserved], [SerialNumber], [UpdatedDate], [VariantID], [WarehouseID])
VALUES (i.[AverageCost], i.[BatchNumber], i.[CreatedDate], i.[ExpiryDate], i.[LastCost], i.[LastCountDate], i.[LocationID], i.[ProductID], i.[QuantityAvailable], i.[QuantityInTransit], i.[QuantityOnHand], i.[QuantityOnOrder], i.[QuantityReserved], i.[SerialNumber], i.[UpdatedDate], i.[VariantID], i.[WarehouseID])
OUTPUT INSERTED.[InventoryID], i._Position;
MERGE [InventoryTransaction] USING (
VALUES (@p34, @p35, @p36, @p37, @p38, @p39, @p40, @p41, @p42, @p43, @p44, @p45, @p46, @p47, @p48, @p49, @p50, @p51, 0),
(@p52, @p53, @p54, @p55, @p56, @p57, @p58, @p59, @p60, @p61, @p62, @p63, @p64, @p65, @p66, @p67, @p68, @p69, 1)) AS i ([BatchNumber], [CreatedByUserID], [CreatedDate], [LocationID], [Notes], [ProductID], [Quantity], [ReferenceID], [ReferenceNumber], [ReferenceType], [SerialNumber], [TotalCost], [TransactionDate], [TransactionType], [UOMID], [UnitCost], [VariantID], [WarehouseID], _Position) ON 1=0
WHEN NOT MATCHED THEN
INSERT ([BatchNumber], [CreatedByUserID], [CreatedDate], [LocationID], [Notes], [ProductID], [Quantity], [ReferenceID], [ReferenceNumber], [ReferenceType], [SerialNumber], [TotalCost], [TransactionDate], [TransactionType], [UOMID], [UnitCost], [VariantID], [WarehouseID])
VALUES (i.[BatchNumber], i.[CreatedByUserID], i.[CreatedDate], i.[LocationID], i.[Notes], i.[ProductID], i.[Quantity], i.[ReferenceID], i.[ReferenceNumber], i.[ReferenceType], i.[SerialNumber], i.[TotalCost], i.[TransactionDate], i.[TransactionType], i.[UOMID], i.[UnitCost], i.[VariantID], i.[WarehouseID])
OUTPUT INSERTED.[TransactionID], i._Position;
UPDATE [PurchaseOrder] SET [OrderStatus] = @p70
OUTPUT 1
WHERE [PurchaseOrderID] = @p71;
UPDATE [PurchaseOrderItem] SET [ItemStatus] = @p72, [ReceivedQuantity] = @p73
OUTPUT 1
WHERE [PurchaseOrderItemID] = @p74;
2025-07-30 00:00:32.824 +03:00 [ERR] An exception occurred in the database while saving changes for context type 'nCarry.Web.Data.ApplicationDbContext'.
Microsoft.EntityFrameworkCore.DbUpdateException: An error occurred while saving the entity changes. See the inner exception for details.
 ---> Microsoft.Data.SqlClient.SqlException (0x80131904): Invalid column name 'UOMID'.
   at Microsoft.Data.SqlClient.SqlCommand.<>c.<ExecuteDbDataReaderAsync>b__211_0(Task`1 result)
   at System.Threading.Tasks.ContinuationResultTaskFromResultTask`2.InnerInvoke()
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
ClientConnectionId:1dd875b5-9cf1-4119-a004-c217b9e7b7cd
Error Number:207,State:1,Class:16
   --- End of inner exception stack trace ---
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.SqlServer.Update.Internal.SqlServerModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalDatabase.SaveChangesAsync(IList`1 entries, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(IList`1 entriesToSave, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(StateManager stateManager, Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.SqlServer.Storage.Internal.SqlServerExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.DbContext.SaveChangesAsync(Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
Microsoft.EntityFrameworkCore.DbUpdateException: An error occurred while saving the entity changes. See the inner exception for details.
 ---> Microsoft.Data.SqlClient.SqlException (0x80131904): Invalid column name 'UOMID'.
   at Microsoft.Data.SqlClient.SqlCommand.<>c.<ExecuteDbDataReaderAsync>b__211_0(Task`1 result)
   at System.Threading.Tasks.ContinuationResultTaskFromResultTask`2.InnerInvoke()
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
ClientConnectionId:1dd875b5-9cf1-4119-a004-c217b9e7b7cd
Error Number:207,State:1,Class:16
   --- End of inner exception stack trace ---
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.SqlServer.Update.Internal.SqlServerModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalDatabase.SaveChangesAsync(IList`1 entries, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(IList`1 entriesToSave, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(StateManager stateManager, Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.SqlServer.Storage.Internal.SqlServerExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.DbContext.SaveChangesAsync(Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
2025-07-30 00:00:33.322 +03:00 [INF] HTTP POST /GoodsReceipt/Create responded 200 in 2485.9804 ms
2025-07-30 00:01:37.045 +03:00 [INF] Starting database seeding.
2025-07-30 00:01:40.330 +03:00 [INF] Admin user already exists. Seeding is not required.
2025-07-30 00:03:26.267 +03:00 [WRN] Failed to determine the https port for redirect.
2025-07-30 00:03:26.586 +03:00 [INF] HTTP GET /Account/Login responded 200 in 324.6676 ms
2025-07-30 00:03:42.063 +03:00 [INF] HTTP POST /Account/Login responded 302 in 859.1784 ms
2025-07-30 00:03:44.465 +03:00 [INF] HTTP GET /Dashboard responded 200 in 2397.3494 ms
2025-07-30 00:03:44.723 +03:00 [INF] HTTP GET /Dashboard/GetChartData responded 200 in 215.9388 ms
2025-07-30 00:03:51.990 +03:00 [INF] HTTP GET /GoodsReceipt/Create responded 200 in 1199.3605 ms
2025-07-30 00:04:36.940 +03:00 [ERR] Failed executing DbCommand (125ms) [Parameters=[@p0='?' (Precision = 18) (Scale = 2) (DbType = Decimal), @p1='?' (Size = 50), @p2='?' (DbType = DateTime2), @p3='?' (DbType = DateTime2), @p4='?' (Precision = 18) (Scale = 2) (DbType = Decimal), @p5='?' (DbType = DateTime2), @p6='?' (DbType = Int32), @p7='?' (DbType = Int32), @p8='?' (Precision = 18) (Scale = 4) (DbType = Decimal), @p9='?' (Precision = 18) (Scale = 4) (DbType = Decimal), @p10='?' (Precision = 18) (Scale = 4) (DbType = Decimal), @p11='?' (Precision = 18) (Scale = 4) (DbType = Decimal), @p12='?' (Precision = 18) (Scale = 4) (DbType = Decimal), @p13='?' (Size = 50), @p14='?' (DbType = DateTime2), @p15='?' (DbType = Int32), @p16='?' (DbType = Int32), @p17='?' (Precision = 18) (Scale = 2) (DbType = Decimal), @p18='?' (Size = 50), @p19='?' (DbType = DateTime2), @p20='?' (DbType = DateTime2), @p21='?' (Precision = 18) (Scale = 2) (DbType = Decimal), @p22='?' (DbType = DateTime2), @p23='?' (DbType = Int32), @p24='?' (DbType = Int32), @p25='?' (Precision = 18) (Scale = 4) (DbType = Decimal), @p26='?' (Precision = 18) (Scale = 4) (DbType = Decimal), @p27='?' (Precision = 18) (Scale = 4) (DbType = Decimal), @p28='?' (Precision = 18) (Scale = 4) (DbType = Decimal), @p29='?' (Precision = 18) (Scale = 4) (DbType = Decimal), @p30='?' (Size = 50), @p31='?' (DbType = DateTime2), @p32='?' (DbType = Int32), @p33='?' (DbType = Int32), @p34='?' (Size = 50), @p35='?' (DbType = Int32), @p36='?' (DbType = DateTime2), @p37='?' (DbType = Int32), @p38='?' (Size = 255), @p39='?' (DbType = Int32), @p40='?' (Precision = 18) (Scale = 4) (DbType = Decimal), @p41='?' (DbType = Int32), @p42='?' (Size = 50), @p43='?' (Size = 50), @p44='?' (Size = 50), @p45='?' (Precision = 18) (Scale = 2) (DbType = Decimal), @p46='?' (DbType = DateTime2), @p47='?' (Size = 20), @p48='?' (DbType = Int32), @p49='?' (Precision = 18) (Scale = 2) (DbType = Decimal), @p50='?' (DbType = Int32), @p51='?' (DbType = Int32), @p52='?' (Size = 50), @p53='?' (DbType = Int32), @p54='?' (DbType = DateTime2), @p55='?' (DbType = Int32), @p56='?' (Size = 255), @p57='?' (DbType = Int32), @p58='?' (Precision = 18) (Scale = 4) (DbType = Decimal), @p59='?' (DbType = Int32), @p60='?' (Size = 50), @p61='?' (Size = 50), @p62='?' (Size = 50), @p63='?' (Precision = 18) (Scale = 2) (DbType = Decimal), @p64='?' (DbType = DateTime2), @p65='?' (Size = 20), @p66='?' (DbType = Int32), @p67='?' (Precision = 18) (Scale = 2) (DbType = Decimal), @p68='?' (DbType = Int32), @p69='?' (DbType = Int32), @p71='?' (DbType = Int32), @p70='?' (Size = 20), @p74='?' (DbType = Int32), @p72='?' (Size = 20), @p73='?' (Precision = 18) (Scale = 4) (DbType = Decimal)], CommandType='"Text"', CommandTimeout='30']
SET NOCOUNT ON;
MERGE [Inventory] USING (
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10, @p11, @p12, @p13, @p14, @p15, @p16, 0),
(@p17, @p18, @p19, @p20, @p21, @p22, @p23, @p24, @p25, @p26, @p27, @p28, @p29, @p30, @p31, @p32, @p33, 1)) AS i ([AverageCost], [BatchNumber], [CreatedDate], [ExpiryDate], [LastCost], [LastCountDate], [LocationID], [ProductID], [QuantityAvailable], [QuantityInTransit], [QuantityOnHand], [QuantityOnOrder], [QuantityReserved], [SerialNumber], [UpdatedDate], [VariantID], [WarehouseID], _Position) ON 1=0
WHEN NOT MATCHED THEN
INSERT ([AverageCost], [BatchNumber], [CreatedDate], [ExpiryDate], [LastCost], [LastCountDate], [LocationID], [ProductID], [QuantityAvailable], [QuantityInTransit], [QuantityOnHand], [QuantityOnOrder], [QuantityReserved], [SerialNumber], [UpdatedDate], [VariantID], [WarehouseID])
VALUES (i.[AverageCost], i.[BatchNumber], i.[CreatedDate], i.[ExpiryDate], i.[LastCost], i.[LastCountDate], i.[LocationID], i.[ProductID], i.[QuantityAvailable], i.[QuantityInTransit], i.[QuantityOnHand], i.[QuantityOnOrder], i.[QuantityReserved], i.[SerialNumber], i.[UpdatedDate], i.[VariantID], i.[WarehouseID])
OUTPUT INSERTED.[InventoryID], i._Position;
MERGE [InventoryTransaction] USING (
VALUES (@p34, @p35, @p36, @p37, @p38, @p39, @p40, @p41, @p42, @p43, @p44, @p45, @p46, @p47, @p48, @p49, @p50, @p51, 0),
(@p52, @p53, @p54, @p55, @p56, @p57, @p58, @p59, @p60, @p61, @p62, @p63, @p64, @p65, @p66, @p67, @p68, @p69, 1)) AS i ([BatchNumber], [CreatedByUserID], [CreatedDate], [LocationID], [Notes], [ProductID], [Quantity], [ReferenceID], [ReferenceNumber], [ReferenceType], [SerialNumber], [TotalCost], [TransactionDate], [TransactionType], [UOMID], [UnitCost], [VariantID], [WarehouseID], _Position) ON 1=0
WHEN NOT MATCHED THEN
INSERT ([BatchNumber], [CreatedByUserID], [CreatedDate], [LocationID], [Notes], [ProductID], [Quantity], [ReferenceID], [ReferenceNumber], [ReferenceType], [SerialNumber], [TotalCost], [TransactionDate], [TransactionType], [UOMID], [UnitCost], [VariantID], [WarehouseID])
VALUES (i.[BatchNumber], i.[CreatedByUserID], i.[CreatedDate], i.[LocationID], i.[Notes], i.[ProductID], i.[Quantity], i.[ReferenceID], i.[ReferenceNumber], i.[ReferenceType], i.[SerialNumber], i.[TotalCost], i.[TransactionDate], i.[TransactionType], i.[UOMID], i.[UnitCost], i.[VariantID], i.[WarehouseID])
OUTPUT INSERTED.[TransactionID], i._Position;
UPDATE [PurchaseOrder] SET [OrderStatus] = @p70
OUTPUT 1
WHERE [PurchaseOrderID] = @p71;
UPDATE [PurchaseOrderItem] SET [ItemStatus] = @p72, [ReceivedQuantity] = @p73
OUTPUT 1
WHERE [PurchaseOrderItemID] = @p74;
2025-07-30 00:04:37.053 +03:00 [ERR] An exception occurred in the database while saving changes for context type 'nCarry.Web.Data.ApplicationDbContext'.
Microsoft.EntityFrameworkCore.DbUpdateException: An error occurred while saving the entity changes. See the inner exception for details.
 ---> Microsoft.Data.SqlClient.SqlException (0x80131904): Invalid column name 'UOMID'.
   at Microsoft.Data.SqlClient.SqlCommand.<>c.<ExecuteDbDataReaderAsync>b__211_0(Task`1 result)
   at System.Threading.Tasks.ContinuationResultTaskFromResultTask`2.InnerInvoke()
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
ClientConnectionId:5d11a337-8378-414f-8dd7-7556178d1aec
Error Number:207,State:1,Class:16
   --- End of inner exception stack trace ---
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.SqlServer.Update.Internal.SqlServerModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalDatabase.SaveChangesAsync(IList`1 entries, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(IList`1 entriesToSave, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(StateManager stateManager, Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.SqlServer.Storage.Internal.SqlServerExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.DbContext.SaveChangesAsync(Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
Microsoft.EntityFrameworkCore.DbUpdateException: An error occurred while saving the entity changes. See the inner exception for details.
 ---> Microsoft.Data.SqlClient.SqlException (0x80131904): Invalid column name 'UOMID'.
   at Microsoft.Data.SqlClient.SqlCommand.<>c.<ExecuteDbDataReaderAsync>b__211_0(Task`1 result)
   at System.Threading.Tasks.ContinuationResultTaskFromResultTask`2.InnerInvoke()
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
ClientConnectionId:5d11a337-8378-414f-8dd7-7556178d1aec
Error Number:207,State:1,Class:16
   --- End of inner exception stack trace ---
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.SqlServer.Update.Internal.SqlServerModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalDatabase.SaveChangesAsync(IList`1 entries, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(IList`1 entriesToSave, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(StateManager stateManager, Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.SqlServer.Storage.Internal.SqlServerExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.DbContext.SaveChangesAsync(Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
2025-07-30 00:04:37.459 +03:00 [INF] HTTP POST /GoodsReceipt/Create responded 200 in 2161.8808 ms
2025-07-30 00:04:57.258 +03:00 [INF] HTTP GET /GoodsReceipt/Create responded 302 in 5.6790 ms
2025-07-30 00:09:05.854 +03:00 [INF] Starting database seeding.
2025-07-30 00:09:09.375 +03:00 [INF] Admin user already exists. Seeding is not required.
2025-07-30 00:10:54.846 +03:00 [WRN] Failed to determine the https port for redirect.
2025-07-30 00:10:55.149 +03:00 [INF] HTTP GET /Account/Login responded 200 in 308.6408 ms
2025-07-30 00:11:12.038 +03:00 [INF] HTTP POST /Account/Login responded 302 in 711.4302 ms
2025-07-30 00:11:14.547 +03:00 [INF] HTTP GET /Dashboard responded 200 in 2503.4679 ms
2025-07-30 00:11:14.561 +03:00 [INF] HTTP GET /nCarry.Web.styles.css responded 304 in 7.0780 ms
2025-07-30 00:11:14.745 +03:00 [INF] HTTP GET /Dashboard/GetChartData responded 200 in 158.5506 ms
2025-07-30 00:11:21.306 +03:00 [INF] HTTP GET /GoodsReceipt/Create responded 200 in 973.5643 ms
2025-07-30 00:11:59.088 +03:00 [ERR] Failed executing DbCommand (92ms) [Parameters=[@p0='?' (Precision = 18) (Scale = 2) (DbType = Decimal), @p1='?' (Size = 50), @p2='?' (DbType = DateTime2), @p3='?' (DbType = DateTime2), @p4='?' (Precision = 18) (Scale = 2) (DbType = Decimal), @p5='?' (DbType = DateTime2), @p6='?' (DbType = Int32), @p7='?' (DbType = Int32), @p8='?' (Precision = 18) (Scale = 4) (DbType = Decimal), @p9='?' (Precision = 18) (Scale = 4) (DbType = Decimal), @p10='?' (Precision = 18) (Scale = 4) (DbType = Decimal), @p11='?' (Precision = 18) (Scale = 4) (DbType = Decimal), @p12='?' (Precision = 18) (Scale = 4) (DbType = Decimal), @p13='?' (Size = 50), @p14='?' (DbType = DateTime2), @p15='?' (DbType = Int32), @p16='?' (DbType = Int32), @p17='?' (Precision = 18) (Scale = 2) (DbType = Decimal), @p18='?' (Size = 50), @p19='?' (DbType = DateTime2), @p20='?' (DbType = DateTime2), @p21='?' (Precision = 18) (Scale = 2) (DbType = Decimal), @p22='?' (DbType = DateTime2), @p23='?' (DbType = Int32), @p24='?' (DbType = Int32), @p25='?' (Precision = 18) (Scale = 4) (DbType = Decimal), @p26='?' (Precision = 18) (Scale = 4) (DbType = Decimal), @p27='?' (Precision = 18) (Scale = 4) (DbType = Decimal), @p28='?' (Precision = 18) (Scale = 4) (DbType = Decimal), @p29='?' (Precision = 18) (Scale = 4) (DbType = Decimal), @p30='?' (Size = 50), @p31='?' (DbType = DateTime2), @p32='?' (DbType = Int32), @p33='?' (DbType = Int32), @p34='?' (Size = 50), @p35='?' (DbType = Int32), @p36='?' (DbType = DateTime2), @p37='?' (DbType = Int32), @p38='?' (Size = 255), @p39='?' (DbType = Int32), @p40='?' (Precision = 18) (Scale = 4) (DbType = Decimal), @p41='?' (DbType = Int32), @p42='?' (Size = 50), @p43='?' (Size = 50), @p44='?' (Size = 50), @p45='?' (Precision = 18) (Scale = 2) (DbType = Decimal), @p46='?' (DbType = DateTime2), @p47='?' (Size = 20), @p48='?' (DbType = Int32), @p49='?' (Precision = 18) (Scale = 2) (DbType = Decimal), @p50='?' (DbType = Int32), @p51='?' (DbType = Int32), @p52='?' (Size = 50), @p53='?' (DbType = Int32), @p54='?' (DbType = DateTime2), @p55='?' (DbType = Int32), @p56='?' (Size = 255), @p57='?' (DbType = Int32), @p58='?' (Precision = 18) (Scale = 4) (DbType = Decimal), @p59='?' (DbType = Int32), @p60='?' (Size = 50), @p61='?' (Size = 50), @p62='?' (Size = 50), @p63='?' (Precision = 18) (Scale = 2) (DbType = Decimal), @p64='?' (DbType = DateTime2), @p65='?' (Size = 20), @p66='?' (DbType = Int32), @p67='?' (Precision = 18) (Scale = 2) (DbType = Decimal), @p68='?' (DbType = Int32), @p69='?' (DbType = Int32), @p71='?' (DbType = Int32), @p70='?' (Size = 20), @p74='?' (DbType = Int32), @p72='?' (Size = 20), @p73='?' (Precision = 18) (Scale = 4) (DbType = Decimal)], CommandType='"Text"', CommandTimeout='30']
SET NOCOUNT ON;
MERGE [Inventory] USING (
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10, @p11, @p12, @p13, @p14, @p15, @p16, 0),
(@p17, @p18, @p19, @p20, @p21, @p22, @p23, @p24, @p25, @p26, @p27, @p28, @p29, @p30, @p31, @p32, @p33, 1)) AS i ([AverageCost], [BatchNumber], [CreatedDate], [ExpiryDate], [LastCost], [LastCountDate], [LocationID], [ProductID], [QuantityAvailable], [QuantityInTransit], [QuantityOnHand], [QuantityOnOrder], [QuantityReserved], [SerialNumber], [UpdatedDate], [VariantID], [WarehouseID], _Position) ON 1=0
WHEN NOT MATCHED THEN
INSERT ([AverageCost], [BatchNumber], [CreatedDate], [ExpiryDate], [LastCost], [LastCountDate], [LocationID], [ProductID], [QuantityAvailable], [QuantityInTransit], [QuantityOnHand], [QuantityOnOrder], [QuantityReserved], [SerialNumber], [UpdatedDate], [VariantID], [WarehouseID])
VALUES (i.[AverageCost], i.[BatchNumber], i.[CreatedDate], i.[ExpiryDate], i.[LastCost], i.[LastCountDate], i.[LocationID], i.[ProductID], i.[QuantityAvailable], i.[QuantityInTransit], i.[QuantityOnHand], i.[QuantityOnOrder], i.[QuantityReserved], i.[SerialNumber], i.[UpdatedDate], i.[VariantID], i.[WarehouseID])
OUTPUT INSERTED.[InventoryID], i._Position;
MERGE [InventoryTransaction] USING (
VALUES (@p34, @p35, @p36, @p37, @p38, @p39, @p40, @p41, @p42, @p43, @p44, @p45, @p46, @p47, @p48, @p49, @p50, @p51, 0),
(@p52, @p53, @p54, @p55, @p56, @p57, @p58, @p59, @p60, @p61, @p62, @p63, @p64, @p65, @p66, @p67, @p68, @p69, 1)) AS i ([BatchNumber], [CreatedByUserID], [CreatedDate], [LocationID], [Notes], [ProductID], [Quantity], [ReferenceID], [ReferenceNumber], [ReferenceType], [SerialNumber], [TotalCost], [TransactionDate], [TransactionType], [UOMID], [UnitCost], [VariantID], [WarehouseID], _Position) ON 1=0
WHEN NOT MATCHED THEN
INSERT ([BatchNumber], [CreatedByUserID], [CreatedDate], [LocationID], [Notes], [ProductID], [Quantity], [ReferenceID], [ReferenceNumber], [ReferenceType], [SerialNumber], [TotalCost], [TransactionDate], [TransactionType], [UOMID], [UnitCost], [VariantID], [WarehouseID])
VALUES (i.[BatchNumber], i.[CreatedByUserID], i.[CreatedDate], i.[LocationID], i.[Notes], i.[ProductID], i.[Quantity], i.[ReferenceID], i.[ReferenceNumber], i.[ReferenceType], i.[SerialNumber], i.[TotalCost], i.[TransactionDate], i.[TransactionType], i.[UOMID], i.[UnitCost], i.[VariantID], i.[WarehouseID])
OUTPUT INSERTED.[TransactionID], i._Position;
UPDATE [PurchaseOrder] SET [OrderStatus] = @p70
OUTPUT 1
WHERE [PurchaseOrderID] = @p71;
UPDATE [PurchaseOrderItem] SET [ItemStatus] = @p72, [ReceivedQuantity] = @p73
OUTPUT 1
WHERE [PurchaseOrderItemID] = @p74;
2025-07-30 00:11:59.185 +03:00 [ERR] An exception occurred in the database while saving changes for context type 'nCarry.Web.Data.ApplicationDbContext'.
Microsoft.EntityFrameworkCore.DbUpdateException: An error occurred while saving the entity changes. See the inner exception for details.
 ---> Microsoft.Data.SqlClient.SqlException (0x80131904): Invalid column name 'UOMID'.
   at Microsoft.Data.SqlClient.SqlCommand.<>c.<ExecuteDbDataReaderAsync>b__211_0(Task`1 result)
   at System.Threading.Tasks.ContinuationResultTaskFromResultTask`2.InnerInvoke()
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
ClientConnectionId:fea86392-d351-4210-845b-9ef8be1b6bac
Error Number:207,State:1,Class:16
   --- End of inner exception stack trace ---
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.SqlServer.Update.Internal.SqlServerModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalDatabase.SaveChangesAsync(IList`1 entries, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(IList`1 entriesToSave, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(StateManager stateManager, Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.SqlServer.Storage.Internal.SqlServerExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.DbContext.SaveChangesAsync(Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
Microsoft.EntityFrameworkCore.DbUpdateException: An error occurred while saving the entity changes. See the inner exception for details.
 ---> Microsoft.Data.SqlClient.SqlException (0x80131904): Invalid column name 'UOMID'.
   at Microsoft.Data.SqlClient.SqlCommand.<>c.<ExecuteDbDataReaderAsync>b__211_0(Task`1 result)
   at System.Threading.Tasks.ContinuationResultTaskFromResultTask`2.InnerInvoke()
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
ClientConnectionId:fea86392-d351-4210-845b-9ef8be1b6bac
Error Number:207,State:1,Class:16
   --- End of inner exception stack trace ---
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.SqlServer.Update.Internal.SqlServerModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalDatabase.SaveChangesAsync(IList`1 entries, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(IList`1 entriesToSave, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(StateManager stateManager, Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.SqlServer.Storage.Internal.SqlServerExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.DbContext.SaveChangesAsync(Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
2025-07-30 00:11:59.504 +03:00 [INF] HTTP POST /GoodsReceipt/Create responded 200 in 1857.1460 ms
2025-07-30 00:12:06.404 +03:00 [INF] HTTP GET /GoodsReceipt responded 302 in 3.8350 ms
2025-07-30 08:41:39.683 +03:00 [INF] Starting database seeding.
2025-07-30 08:41:41.526 +03:00 [INF] Admin user already exists. Seeding is not required.
2025-07-30 08:41:41.571 +03:00 [ERR] Hosting failed to start
System.IO.IOException: Failed to bind to address http://127.0.0.1:5298: address already in use.
 ---> Microsoft.AspNetCore.Connections.AddressInUseException: Address already in use
 ---> System.Net.Sockets.SocketException (48): Address already in use
   at System.Net.Sockets.Socket.DoBind(EndPoint endPointSnapshot, SocketAddress socketAddress)
   at System.Net.Sockets.Socket.Bind(EndPoint localEP)
   at Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets.SocketTransportOptions.CreateDefaultBoundListenSocket(EndPoint endpoint)
   at Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets.SocketConnectionListener.Bind()
   --- End of inner exception stack trace ---
   at Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets.SocketConnectionListener.Bind()
   at Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets.SocketTransportFactory.BindAsync(EndPoint endpoint, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.Infrastructure.TransportManager.BindAsync(EndPoint endPoint, ConnectionDelegate connectionDelegate, EndpointConfig endpointConfig, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.KestrelServerImpl.<>c__DisplayClass28_0`1.<<StartAsync>g__OnBind|0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.AddressBinder.BindEndpointAsync(ListenOptions endpoint, AddressBindContext context, CancellationToken cancellationToken)
   --- End of inner exception stack trace ---
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.AddressBinder.BindEndpointAsync(ListenOptions endpoint, AddressBindContext context, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.LocalhostListenOptions.BindAsync(AddressBindContext context, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.AddressBinder.AddressesStrategy.BindAsync(AddressBindContext context, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.AddressBinder.BindAsync(ListenOptions[] listenOptions, AddressBindContext context, Func`2 useHttps, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.KestrelServerImpl.BindAsync(CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.KestrelServerImpl.StartAsync[TContext](IHttpApplication`1 application, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Hosting.GenericWebHostService.StartAsync(CancellationToken cancellationToken)
   at Microsoft.Extensions.Hosting.Internal.Host.<StartAsync>b__15_1(IHostedService service, CancellationToken token)
   at Microsoft.Extensions.Hosting.Internal.Host.ForeachService[T](IEnumerable`1 services, CancellationToken token, Boolean concurrent, Boolean abortOnFirstException, List`1 exceptions, Func`3 operation)
2025-07-30 08:41:41.578 +03:00 [FTL] Application terminated unexpectedly
System.IO.IOException: Failed to bind to address http://127.0.0.1:5298: address already in use.
 ---> Microsoft.AspNetCore.Connections.AddressInUseException: Address already in use
 ---> System.Net.Sockets.SocketException (48): Address already in use
   at System.Net.Sockets.Socket.DoBind(EndPoint endPointSnapshot, SocketAddress socketAddress)
   at System.Net.Sockets.Socket.Bind(EndPoint localEP)
   at Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets.SocketTransportOptions.CreateDefaultBoundListenSocket(EndPoint endpoint)
   at Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets.SocketConnectionListener.Bind()
   --- End of inner exception stack trace ---
   at Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets.SocketConnectionListener.Bind()
   at Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets.SocketTransportFactory.BindAsync(EndPoint endpoint, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.Infrastructure.TransportManager.BindAsync(EndPoint endPoint, ConnectionDelegate connectionDelegate, EndpointConfig endpointConfig, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.KestrelServerImpl.<>c__DisplayClass28_0`1.<<StartAsync>g__OnBind|0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.AddressBinder.BindEndpointAsync(ListenOptions endpoint, AddressBindContext context, CancellationToken cancellationToken)
   --- End of inner exception stack trace ---
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.AddressBinder.BindEndpointAsync(ListenOptions endpoint, AddressBindContext context, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.LocalhostListenOptions.BindAsync(AddressBindContext context, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.AddressBinder.AddressesStrategy.BindAsync(AddressBindContext context, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.AddressBinder.BindAsync(ListenOptions[] listenOptions, AddressBindContext context, Func`2 useHttps, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.KestrelServerImpl.BindAsync(CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.KestrelServerImpl.StartAsync[TContext](IHttpApplication`1 application, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Hosting.GenericWebHostService.StartAsync(CancellationToken cancellationToken)
   at Microsoft.Extensions.Hosting.Internal.Host.<StartAsync>b__15_1(IHostedService service, CancellationToken token)
   at Microsoft.Extensions.Hosting.Internal.Host.ForeachService[T](IEnumerable`1 services, CancellationToken token, Boolean concurrent, Boolean abortOnFirstException, List`1 exceptions, Func`3 operation)
   at Microsoft.Extensions.Hosting.Internal.Host.StartAsync(CancellationToken cancellationToken)
   at Microsoft.Extensions.Hosting.HostingAbstractionsHostExtensions.RunAsync(IHost host, CancellationToken token)
   at Microsoft.Extensions.Hosting.HostingAbstractionsHostExtensions.RunAsync(IHost host, CancellationToken token)
   at Microsoft.Extensions.Hosting.HostingAbstractionsHostExtensions.Run(IHost host)
   at Program.<Main>$(String[] args) in /Users/<USER>/Source/nCarry/nCarry/nCarry.Web/Program.cs:line 82
2025-07-30 08:44:11.561 +03:00 [INF] Starting database seeding.
2025-07-30 08:44:13.439 +03:00 [INF] Admin user already exists. Seeding is not required.
2025-07-30 08:44:13.483 +03:00 [ERR] Hosting failed to start
System.IO.IOException: Failed to bind to address http://127.0.0.1:5298: address already in use.
 ---> Microsoft.AspNetCore.Connections.AddressInUseException: Address already in use
 ---> System.Net.Sockets.SocketException (48): Address already in use
   at System.Net.Sockets.Socket.DoBind(EndPoint endPointSnapshot, SocketAddress socketAddress)
   at System.Net.Sockets.Socket.Bind(EndPoint localEP)
   at Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets.SocketTransportOptions.CreateDefaultBoundListenSocket(EndPoint endpoint)
   at Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets.SocketConnectionListener.Bind()
   --- End of inner exception stack trace ---
   at Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets.SocketConnectionListener.Bind()
   at Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets.SocketTransportFactory.BindAsync(EndPoint endpoint, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.Infrastructure.TransportManager.BindAsync(EndPoint endPoint, ConnectionDelegate connectionDelegate, EndpointConfig endpointConfig, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.KestrelServerImpl.<>c__DisplayClass28_0`1.<<StartAsync>g__OnBind|0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.AddressBinder.BindEndpointAsync(ListenOptions endpoint, AddressBindContext context, CancellationToken cancellationToken)
   --- End of inner exception stack trace ---
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.AddressBinder.BindEndpointAsync(ListenOptions endpoint, AddressBindContext context, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.LocalhostListenOptions.BindAsync(AddressBindContext context, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.AddressBinder.AddressesStrategy.BindAsync(AddressBindContext context, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.AddressBinder.BindAsync(ListenOptions[] listenOptions, AddressBindContext context, Func`2 useHttps, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.KestrelServerImpl.BindAsync(CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.KestrelServerImpl.StartAsync[TContext](IHttpApplication`1 application, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Hosting.GenericWebHostService.StartAsync(CancellationToken cancellationToken)
   at Microsoft.Extensions.Hosting.Internal.Host.<StartAsync>b__15_1(IHostedService service, CancellationToken token)
   at Microsoft.Extensions.Hosting.Internal.Host.ForeachService[T](IEnumerable`1 services, CancellationToken token, Boolean concurrent, Boolean abortOnFirstException, List`1 exceptions, Func`3 operation)
2025-07-30 08:44:13.490 +03:00 [FTL] Application terminated unexpectedly
System.IO.IOException: Failed to bind to address http://127.0.0.1:5298: address already in use.
 ---> Microsoft.AspNetCore.Connections.AddressInUseException: Address already in use
 ---> System.Net.Sockets.SocketException (48): Address already in use
   at System.Net.Sockets.Socket.DoBind(EndPoint endPointSnapshot, SocketAddress socketAddress)
   at System.Net.Sockets.Socket.Bind(EndPoint localEP)
   at Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets.SocketTransportOptions.CreateDefaultBoundListenSocket(EndPoint endpoint)
   at Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets.SocketConnectionListener.Bind()
   --- End of inner exception stack trace ---
   at Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets.SocketConnectionListener.Bind()
   at Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets.SocketTransportFactory.BindAsync(EndPoint endpoint, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.Infrastructure.TransportManager.BindAsync(EndPoint endPoint, ConnectionDelegate connectionDelegate, EndpointConfig endpointConfig, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.KestrelServerImpl.<>c__DisplayClass28_0`1.<<StartAsync>g__OnBind|0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.AddressBinder.BindEndpointAsync(ListenOptions endpoint, AddressBindContext context, CancellationToken cancellationToken)
   --- End of inner exception stack trace ---
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.AddressBinder.BindEndpointAsync(ListenOptions endpoint, AddressBindContext context, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.LocalhostListenOptions.BindAsync(AddressBindContext context, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.AddressBinder.AddressesStrategy.BindAsync(AddressBindContext context, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.AddressBinder.BindAsync(ListenOptions[] listenOptions, AddressBindContext context, Func`2 useHttps, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.KestrelServerImpl.BindAsync(CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.KestrelServerImpl.StartAsync[TContext](IHttpApplication`1 application, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Hosting.GenericWebHostService.StartAsync(CancellationToken cancellationToken)
   at Microsoft.Extensions.Hosting.Internal.Host.<StartAsync>b__15_1(IHostedService service, CancellationToken token)
   at Microsoft.Extensions.Hosting.Internal.Host.ForeachService[T](IEnumerable`1 services, CancellationToken token, Boolean concurrent, Boolean abortOnFirstException, List`1 exceptions, Func`3 operation)
   at Microsoft.Extensions.Hosting.Internal.Host.StartAsync(CancellationToken cancellationToken)
   at Microsoft.Extensions.Hosting.HostingAbstractionsHostExtensions.RunAsync(IHost host, CancellationToken token)
   at Microsoft.Extensions.Hosting.HostingAbstractionsHostExtensions.RunAsync(IHost host, CancellationToken token)
   at Microsoft.Extensions.Hosting.HostingAbstractionsHostExtensions.Run(IHost host)
   at Program.<Main>$(String[] args) in /Users/<USER>/Source/nCarry/nCarry/nCarry.Web/Program.cs:line 82
2025-07-30 08:44:28.914 +03:00 [INF] Starting database seeding.
2025-07-30 08:44:30.861 +03:00 [INF] Admin user already exists. Seeding is not required.
2025-07-30 08:44:48.150 +03:00 [WRN] Failed to determine the https port for redirect.
2025-07-30 08:44:48.191 +03:00 [INF] HTTP GET / responded 302 in 43.4474 ms
2025-07-30 08:46:51.399 +03:00 [INF] HTTP GET /Account/Login responded 200 in 203.9641 ms
2025-07-30 08:47:12.686 +03:00 [INF] HTTP POST /Account/Login responded 302 in 538.4845 ms
2025-07-30 08:47:14.695 +03:00 [INF] HTTP GET /Dashboard responded 200 in 2008.4935 ms
2025-07-30 08:47:14.707 +03:00 [INF] HTTP GET /nCarry.Web.styles.css responded 304 in 7.1299 ms
2025-07-30 08:47:14.874 +03:00 [INF] HTTP GET /Dashboard/GetChartData responded 200 in 134.5037 ms
2025-07-30 08:47:32.942 +03:00 [INF] HTTP GET /GoodsReceipt responded 200 in 298.7001 ms
2025-07-30 08:47:43.448 +03:00 [INF] HTTP GET /GoodsReceipt/Create responded 200 in 356.4930 ms
2025-07-30 08:47:55.175 +03:00 [INF] HTTP GET /GoodsReceipt/Create responded 200 in 930.3383 ms
2025-07-30 08:49:19.882 +03:00 [ERR] An exception occurred in the database while saving changes for context type 'nCarry.Web.Data.ApplicationDbContext'.
Microsoft.EntityFrameworkCore.DbUpdateException: An error occurred while saving the entity changes. See the inner exception for details.
 ---> System.InvalidCastException: Unable to cast object of type 'System.Int64' to type 'System.Int32'.
   at Microsoft.Data.SqlClient.SqlBuffer.get_Int32()
   at lambda_method3895(Closure, DbDataReader, Int32)
   at Microsoft.EntityFrameworkCore.RelationalPropertyExtensions.GetReaderFieldValue(IProperty property, RelationalDataReader relationalReader, Int32 ordinal, Boolean detailedErrorsEnabled)
   at Microsoft.EntityFrameworkCore.Update.ModificationCommand.PropagateResults(RelationalDataReader relationalReader)
   at Microsoft.EntityFrameworkCore.Update.AffectedCountModificationCommandBatch.ConsumeResultSetAsync(Int32 startCommandIndex, RelationalDataReader reader, CancellationToken cancellationToken)
   --- End of inner exception stack trace ---
   at Microsoft.EntityFrameworkCore.Update.AffectedCountModificationCommandBatch.ConsumeResultSetAsync(Int32 startCommandIndex, RelationalDataReader reader, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.AffectedCountModificationCommandBatch.ConsumeAsync(RelationalDataReader reader, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.SqlServer.Update.Internal.SqlServerModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalDatabase.SaveChangesAsync(IList`1 entries, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(IList`1 entriesToSave, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(StateManager stateManager, Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.SqlServer.Storage.Internal.SqlServerExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.DbContext.SaveChangesAsync(Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
Microsoft.EntityFrameworkCore.DbUpdateException: An error occurred while saving the entity changes. See the inner exception for details.
 ---> System.InvalidCastException: Unable to cast object of type 'System.Int64' to type 'System.Int32'.
   at Microsoft.Data.SqlClient.SqlBuffer.get_Int32()
   at lambda_method3895(Closure, DbDataReader, Int32)
   at Microsoft.EntityFrameworkCore.RelationalPropertyExtensions.GetReaderFieldValue(IProperty property, RelationalDataReader relationalReader, Int32 ordinal, Boolean detailedErrorsEnabled)
   at Microsoft.EntityFrameworkCore.Update.ModificationCommand.PropagateResults(RelationalDataReader relationalReader)
   at Microsoft.EntityFrameworkCore.Update.AffectedCountModificationCommandBatch.ConsumeResultSetAsync(Int32 startCommandIndex, RelationalDataReader reader, CancellationToken cancellationToken)
   --- End of inner exception stack trace ---
   at Microsoft.EntityFrameworkCore.Update.AffectedCountModificationCommandBatch.ConsumeResultSetAsync(Int32 startCommandIndex, RelationalDataReader reader, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.AffectedCountModificationCommandBatch.ConsumeAsync(RelationalDataReader reader, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.SqlServer.Update.Internal.SqlServerModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalDatabase.SaveChangesAsync(IList`1 entries, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(IList`1 entriesToSave, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(StateManager stateManager, Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.SqlServer.Storage.Internal.SqlServerExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.DbContext.SaveChangesAsync(Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
2025-07-30 08:49:20.274 +03:00 [INF] HTTP POST /GoodsReceipt/Create responded 200 in 2015.3641 ms
2025-07-30 08:54:23.618 +03:00 [INF] Starting database seeding.
2025-07-30 08:54:27.018 +03:00 [INF] Admin user already exists. Seeding is not required.
2025-07-30 08:54:40.298 +03:00 [WRN] Failed to determine the https port for redirect.
2025-07-30 08:54:40.342 +03:00 [INF] HTTP GET / responded 302 in 46.8875 ms
2025-07-30 08:54:55.991 +03:00 [INF] HTTP GET /GoodsReceipt/Create responded 200 in 1283.3754 ms
2025-07-30 08:55:56.559 +03:00 [ERR] An exception occurred in the database while saving changes for context type 'nCarry.Web.Data.ApplicationDbContext'.
Microsoft.EntityFrameworkCore.DbUpdateException: An error occurred while saving the entity changes. See the inner exception for details.
 ---> Microsoft.Data.SqlClient.SqlException (0x80131904): Violation of UNIQUE KEY constraint 'UQ__Inventor__E733A2BF4077D565'. Cannot insert duplicate key in object 'dbo.InventoryTransaction'. The duplicate key value is (IT202507300001).
   at Microsoft.Data.SqlClient.SqlConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.SqlInternalConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.TdsParser.ThrowExceptionAndWarning(TdsParserStateObject stateObj, Boolean callerHasConnectionLock, Boolean asyncClose)
   at Microsoft.Data.SqlClient.TdsParser.TryRun(RunBehavior runBehavior, SqlCommand cmdHandler, SqlDataReader dataStream, BulkCopySimpleResultSet bulkCopyHandler, TdsParserStateObject stateObj, Boolean& dataReady)
   at Microsoft.Data.SqlClient.SqlDataReader.TryHasMoreRows(Boolean& moreRows)
   at Microsoft.Data.SqlClient.SqlDataReader.TryReadInternal(Boolean setTimeout, Boolean& more)
   at Microsoft.Data.SqlClient.SqlDataReader.ReadAsyncExecute(Task task, Object state)
   at Microsoft.Data.SqlClient.SqlDataReader.InvokeAsyncCall[T](SqlDataReaderBaseAsyncCallContext`1 context)
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Update.AffectedCountModificationCommandBatch.ConsumeResultSetAsync(Int32 startCommandIndex, RelationalDataReader reader, CancellationToken cancellationToken)
ClientConnectionId:9888242d-7ac5-40fc-b44b-f4de9b2c0528
Error Number:2627,State:1,Class:14
   --- End of inner exception stack trace ---
   at Microsoft.EntityFrameworkCore.Update.AffectedCountModificationCommandBatch.ConsumeResultSetAsync(Int32 startCommandIndex, RelationalDataReader reader, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.AffectedCountModificationCommandBatch.ConsumeAsync(RelationalDataReader reader, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.SqlServer.Update.Internal.SqlServerModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalDatabase.SaveChangesAsync(IList`1 entries, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(IList`1 entriesToSave, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(StateManager stateManager, Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.SqlServer.Storage.Internal.SqlServerExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.DbContext.SaveChangesAsync(Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
Microsoft.EntityFrameworkCore.DbUpdateException: An error occurred while saving the entity changes. See the inner exception for details.
 ---> Microsoft.Data.SqlClient.SqlException (0x80131904): Violation of UNIQUE KEY constraint 'UQ__Inventor__E733A2BF4077D565'. Cannot insert duplicate key in object 'dbo.InventoryTransaction'. The duplicate key value is (IT202507300001).
   at Microsoft.Data.SqlClient.SqlConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.SqlInternalConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.TdsParser.ThrowExceptionAndWarning(TdsParserStateObject stateObj, Boolean callerHasConnectionLock, Boolean asyncClose)
   at Microsoft.Data.SqlClient.TdsParser.TryRun(RunBehavior runBehavior, SqlCommand cmdHandler, SqlDataReader dataStream, BulkCopySimpleResultSet bulkCopyHandler, TdsParserStateObject stateObj, Boolean& dataReady)
   at Microsoft.Data.SqlClient.SqlDataReader.TryHasMoreRows(Boolean& moreRows)
   at Microsoft.Data.SqlClient.SqlDataReader.TryReadInternal(Boolean setTimeout, Boolean& more)
   at Microsoft.Data.SqlClient.SqlDataReader.ReadAsyncExecute(Task task, Object state)
   at Microsoft.Data.SqlClient.SqlDataReader.InvokeAsyncCall[T](SqlDataReaderBaseAsyncCallContext`1 context)
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Update.AffectedCountModificationCommandBatch.ConsumeResultSetAsync(Int32 startCommandIndex, RelationalDataReader reader, CancellationToken cancellationToken)
ClientConnectionId:9888242d-7ac5-40fc-b44b-f4de9b2c0528
Error Number:2627,State:1,Class:14
   --- End of inner exception stack trace ---
   at Microsoft.EntityFrameworkCore.Update.AffectedCountModificationCommandBatch.ConsumeResultSetAsync(Int32 startCommandIndex, RelationalDataReader reader, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.AffectedCountModificationCommandBatch.ConsumeAsync(RelationalDataReader reader, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.SqlServer.Update.Internal.SqlServerModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalDatabase.SaveChangesAsync(IList`1 entries, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(IList`1 entriesToSave, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(StateManager stateManager, Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.SqlServer.Storage.Internal.SqlServerExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.DbContext.SaveChangesAsync(Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
2025-07-30 08:55:56.930 +03:00 [INF] HTTP POST /GoodsReceipt/Create responded 200 in 2124.5032 ms
2025-07-30 08:58:26.456 +03:00 [INF] Starting database seeding.
2025-07-30 08:58:29.060 +03:00 [INF] Admin user already exists. Seeding is not required.
2025-07-30 08:58:35.532 +03:00 [WRN] Failed to determine the https port for redirect.
2025-07-30 08:58:35.575 +03:00 [INF] HTTP GET / responded 302 in 45.0479 ms
2025-07-30 08:58:50.146 +03:00 [INF] HTTP GET /GoodsReceipt/Create responded 200 in 1233.9970 ms
2025-07-30 08:59:55.897 +03:00 [INF] HTTP POST /GoodsReceipt/Create responded 302 in 1690.3105 ms
2025-07-30 08:59:56.270 +03:00 [INF] HTTP GET /GoodsReceipt/Details/11 responded 200 in 370.6784 ms
2025-07-30 09:05:14.474 +03:00 [ERR] Failed executing DbCommand (35,008ms) [Parameters=[@__id_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [s0].[ReceiptID], [s0].[CreatedByUserID], [s0].[CreatedDate], [s0].[Notes], [s0].[PostedByUserID], [s0].[PostedDate], [s0].[PurchaseOrderID], [s0].[QualityCheckByUserID], [s0].[QualityCheckDate], [s0].[QualityCheckStatus], [s0].[ReceiptDate], [s0].[ReceiptNumber], [s0].[ReceiptType], [s0].[ReceivedByUserID], [s0].[ReceiptStatus], [s0].[SupplierDeliveryNote], [s0].[SupplierID], [s0].[UpdatedByUserID], [s0].[UpdatedDate], [s0].[WarehouseID], [s0].[PurchaseOrderID0], [s0].[ApprovalNotes], [s0].[ApprovalStatus], [s0].[ApprovedByUserID], [s0].[ApprovedDate], [s0].[BuyerID], [s0].[CreatedByUserID0], [s0].[CreatedDate0], [s0].[Currency], [s0].[DeletedByUserID], [s0].[DeletedDate], [s0].[DeliveryAddressID], [s0].[DeliveryInstructions], [s0].[DeliveryWarehouseID], [s0].[DiscountAmount], [s0].[ExchangeRate], [s0].[ExpectedDate], [s0].[FreightAmount], [s0].[FreightTerms], [s0].[InternalNotes], [s0].[IsDeleted], [s0].[Notes0], [s0].[OrderDate], [s0].[OrderStatus], [s0].[OrderType], [s0].[PaymentMethod], [s0].[PaymentTerms], [s0].[PromisedDate], [s0].[PurchaseOrderNumber], [s0].[RequiredDate], [s0].[RequisitionID], [s0].[ShippingMethod], [s0].[SubTotal], [s0].[SupplierID0], [s0].[SupplierReference], [s0].[TaxAmount], [s0].[TermsAndConditions], [s0].[TotalAmount], [s0].[UpdatedByUserID0], [s0].[UpdatedDate0], [s0].[SupplierID1], [s0].[APIEnabled], [s0].[BankAccountDetails], [s0].[BlockedByUserID], [s0].[BlockedDate], [s0].[BlockedReason], [s0].[CertificateExpiryDate], [s0].[CertificateNumber], [s0].[ComplianceStatus], [s0].[ContactEmail], [s0].[ContactLandline], [s0].[ContactMobile], [s0].[ContactPhone], [s0].[CreatedByUserID1], [s0].[CreatedDate1], [s0].[DeletedByUserID0], [s0].[DeletedDate0], [s0].[DeliveryDays], [s0].[DeliveryScore], [s0].[EDIEnabled], [s0].[EarlyPaymentDiscountDays], [s0].[EarlyPaymentDiscountPercent], [s0].[InsuranceExpiryDate], [s0].[InsurancePolicyNumber], [s0].[IntegrationKey], [s0].[InternalNotes0], [s0].[IsActive], [s0].[IsDeleted0], [s0].[IsPreferred], [s0].[LastOrderDate], [s0].[LeadTimeDays], [s0].[MinimumOrderValue], [s0].[Notes1], [s0].[OrderCutoffTime], [s0].[OutstandingBalance], [s0].[PaymentMethodID], [s0].[PaymentTermDays], [s0].[PaymentTerms0], [s0].[PreferredCurrency], [s0].[PriceScore], [s0].[QualityScore], [s0].[Rating], [s0].[ReferenceCode], [s0].[ResponsibleContact], [s0].[ReturnPolicy], [s0].[SupplierAddress], [s0].[SupplierAddress2], [s0].[SupplierCity], [s0].[SupplierCode], [s0].[SupplierCountry], [s0].[SupplierGroup], [s0].[SupplierName], [s0].[SupplierPostCode], [s0].[SupplierState], [s0].[SupplierStatus], [s0].[SupplierType], [s0].[TaxNumber], [s0].[TaxRate], [s0].[TotalPurchaseValue], [s0].[TradingName], [s0].[UpdatedByUserID1], [s0].[UpdatedDate1], [s0].[WarrantyTerms], [s0].[Website], [s0].[WithholdingTaxRate], [s0].[YTDPurchases], [s0].[UserID], [s0].[AccessFailedCount], [s0].[CreatedByUserID2], [s0].[CreatedDate2], [s0].[DateFormat], [s0].[DeletedByUserID1], [s0].[DeletedDate1], [s0].[Department], [s0].[DisplayName], [s0].[Email], [s0].[FirstName], [s0].[IsActive0], [s0].[IsDeleted1], [s0].[IsLocked], [s0].[JobTitle], [s0].[Language], [s0].[LastActivityDate], [s0].[LastLoginDate], [s0].[LastName], [s0].[LockoutEnd], [s0].[MobileNumber], [s0].[MustChangePassword], [s0].[PasswordChangedDate], [s0].[PasswordHash], [s0].[PasswordSalt], [s0].[PhoneNumber], [s0].[ProfilePicture], [s0].[ThemePreference], [s0].[TimeZone], [s0].[TwoFactorEnabled], [s0].[TwoFactorSecret], [s0].[UpdatedByUserID2], [s0].[UpdatedDate2], [s0].[Username], [s0].[WarehouseID0], [s0].[Address1], [s0].[Address2], [s0].[AllowNegativeStock], [s0].[City], [s0].[Country], [s0].[CreatedByUserID3], [s0].[CreatedDate3], [s0].[CurrentUtilization], [s0].[Description], [s0].[Email0], [s0].[IsActive1], [s0].[IsDefault], [s0].[ManagerName], [s0].[OperatingHours], [s0].[Phone], [s0].[PostCode], [s0].[State], [s0].[StorageCapacity], [s0].[TimeZone0], [s0].[TotalArea], [s0].[UpdatedByUserID3], [s0].[UpdatedDate3], [s0].[WarehouseCode], [s0].[WarehouseName], [s0].[WarehouseType], [s1].[ReceiptItemID], [s1].[AcceptedQuantity], [s1].[BatchNumber], [s1].[Description], [s1].[ExpiryDate], [s1].[ReceiptID], [s1].[LineNumber], [s1].[LocationID], [s1].[Notes], [s1].[ProductID], [s1].[ProductName], [s1].[PurchaseOrderItemID], [s1].[QualityStatus], [s1].[ReceivedQuantity], [s1].[RejectedQuantity], [s1].[RejectionReason], [s1].[SerialNumber], [s1].[SortOrder], [s1].[UOMID], [s1].[UnitCost], [s1].[UnitPrice], [s1].[VariantID], [s1].[ProductID0], [s1].[AllowBackorder], [s1].[AllowPreorder], [s1].[Brand], [s1].[CategoryID], [s1].[CommodityCode], [s1].[CountryOfOrigin], [s1].[CreatedByUserID], [s1].[CreatedDate], [s1].[Currency], [s1].[DeletedByUserID], [s1].[DeletedDate], [s1].[Description0], [s1].[DimensionUOMID], [s1].[HSCode], [s1].[HasVariants], [s1].[Height], [s1].[InventoryUOMID], [s1].[IsActive], [s1].[IsDeleted], [s1].[IsKit], [s1].[IsTaxExempt], [s1].[LeadTimeDays], [s1].[Length], [s1].[ListPrice], [s1].[Manufacturer], [s1].[ManufacturerPartNumber], [s1].[MaxStockLevel], [s1].[MinStockLevel], [s1].[Notes0], [s1].[ProductCode], [s1].[ProductName0], [s1].[ProductType], [s1].[PurchaseUOMID], [s1].[ReorderPoint], [s1].[ReorderQuantity], [s1].[RequiresBatch], [s1].[RequiresSerial], [s1].[SalesUOMID], [s1].[SellPrice], [s1].[ShelfLifeDays], [s1].[ShortDescription], [s1].[StandardCost], [s1].[Status], [s1].[TaxCodeID], [s1].[TrackInventory], [s1].[UpdatedByUserID], [s1].[UpdatedDate], [s1].[Volume], [s1].[VolumeUOMID], [s1].[WarrantyDays], [s1].[Weight], [s1].[WeightUOMID], [s1].[Width], [s1].[UOMID0], [s1].[BaseUnit], [s1].[ConversionFactor], [s1].[CreatedDate0], [s1].[DecimalPlaces], [s1].[IsActive0], [s1].[Symbol], [s1].[UOMCode], [s1].[UOMName], [s1].[UOMType], [s1].[UpdatedDate0]
FROM (
    SELECT TOP(1) [g].[ReceiptID], [g].[CreatedByUserID], [g].[CreatedDate], [g].[Notes], [g].[PostedByUserID], [g].[PostedDate], [g].[PurchaseOrderID], [g].[QualityCheckByUserID], [g].[QualityCheckDate], [g].[QualityCheckStatus], [g].[ReceiptDate], [g].[ReceiptNumber], [g].[ReceiptType], [g].[ReceivedByUserID], [g].[ReceiptStatus], [g].[SupplierDeliveryNote], [g].[SupplierID], [g].[UpdatedByUserID], [g].[UpdatedDate], [g].[WarehouseID], [p].[PurchaseOrderID] AS [PurchaseOrderID0], [p].[ApprovalNotes], [p].[ApprovalStatus], [p].[ApprovedByUserID], [p].[ApprovedDate], [p].[BuyerID], [p].[CreatedByUserID] AS [CreatedByUserID0], [p].[CreatedDate] AS [CreatedDate0], [p].[Currency], [p].[DeletedByUserID], [p].[DeletedDate], [p].[DeliveryAddressID], [p].[DeliveryInstructions], [p].[DeliveryWarehouseID], [p].[DiscountAmount], [p].[ExchangeRate], [p].[ExpectedDate], [p].[FreightAmount], [p].[FreightTerms], [p].[InternalNotes], [p].[IsDeleted], [p].[Notes] AS [Notes0], [p].[OrderDate], [p].[OrderStatus], [p].[OrderType], [p].[PaymentMethod], [p].[PaymentTerms], [p].[PromisedDate], [p].[PurchaseOrderNumber], [p].[RequiredDate], [p].[RequisitionID], [p].[ShippingMethod], [p].[SubTotal], [p].[SupplierID] AS [SupplierID0], [p].[SupplierReference], [p].[TaxAmount], [p].[TermsAndConditions], [p].[TotalAmount], [p].[UpdatedByUserID] AS [UpdatedByUserID0], [p].[UpdatedDate] AS [UpdatedDate0], [s].[SupplierID] AS [SupplierID1], [s].[APIEnabled], [s].[BankAccountDetails], [s].[BlockedByUserID], [s].[BlockedDate], [s].[BlockedReason], [s].[CertificateExpiryDate], [s].[CertificateNumber], [s].[ComplianceStatus], [s].[ContactEmail], [s].[ContactLandline], [s].[ContactMobile], [s].[ContactPhone], [s].[CreatedByUserID] AS [CreatedByUserID1], [s].[CreatedDate] AS [CreatedDate1], [s].[DeletedByUserID] AS [DeletedByUserID0], [s].[DeletedDate] AS [DeletedDate0], [s].[DeliveryDays], [s].[DeliveryScore], [s].[EDIEnabled], [s].[EarlyPaymentDiscountDays], [s].[EarlyPaymentDiscountPercent], [s].[InsuranceExpiryDate], [s].[InsurancePolicyNumber], [s].[IntegrationKey], [s].[InternalNotes] AS [InternalNotes0], [s].[IsActive], [s].[IsDeleted] AS [IsDeleted0], [s].[IsPreferred], [s].[LastOrderDate], [s].[LeadTimeDays], [s].[MinimumOrderValue], [s].[Notes] AS [Notes1], [s].[OrderCutoffTime], [s].[OutstandingBalance], [s].[PaymentMethodID], [s].[PaymentTermDays], [s].[PaymentTerms] AS [PaymentTerms0], [s].[PreferredCurrency], [s].[PriceScore], [s].[QualityScore], [s].[Rating], [s].[ReferenceCode], [s].[ResponsibleContact], [s].[ReturnPolicy], [s].[SupplierAddress], [s].[SupplierAddress2], [s].[SupplierCity], [s].[SupplierCode], [s].[SupplierCountry], [s].[SupplierGroup], [s].[SupplierName], [s].[SupplierPostCode], [s].[SupplierState], [s].[SupplierStatus], [s].[SupplierType], [s].[TaxNumber], [s].[TaxRate], [s].[TotalPurchaseValue], [s].[TradingName], [s].[UpdatedByUserID] AS [UpdatedByUserID1], [s].[UpdatedDate] AS [UpdatedDate1], [s].[WarrantyTerms], [s].[Website], [s].[WithholdingTaxRate], [s].[YTDPurchases], [u].[UserID], [u].[AccessFailedCount], [u].[CreatedByUserID] AS [CreatedByUserID2], [u].[CreatedDate] AS [CreatedDate2], [u].[DateFormat], [u].[DeletedByUserID] AS [DeletedByUserID1], [u].[DeletedDate] AS [DeletedDate1], [u].[Department], [u].[DisplayName], [u].[Email], [u].[FirstName], [u].[IsActive] AS [IsActive0], [u].[IsDeleted] AS [IsDeleted1], [u].[IsLocked], [u].[JobTitle], [u].[Language], [u].[LastActivityDate], [u].[LastLoginDate], [u].[LastName], [u].[LockoutEnd], [u].[MobileNumber], [u].[MustChangePassword], [u].[PasswordChangedDate], [u].[PasswordHash], [u].[PasswordSalt], [u].[PhoneNumber], [u].[ProfilePicture], [u].[ThemePreference], [u].[TimeZone], [u].[TwoFactorEnabled], [u].[TwoFactorSecret], [u].[UpdatedByUserID] AS [UpdatedByUserID2], [u].[UpdatedDate] AS [UpdatedDate2], [u].[Username], [w].[WarehouseID] AS [WarehouseID0], [w].[Address1], [w].[Address2], [w].[AllowNegativeStock], [w].[City], [w].[Country], [w].[CreatedByUserID] AS [CreatedByUserID3], [w].[CreatedDate] AS [CreatedDate3], [w].[CurrentUtilization], [w].[Description], [w].[Email] AS [Email0], [w].[IsActive] AS [IsActive1], [w].[IsDefault], [w].[ManagerName], [w].[OperatingHours], [w].[Phone], [w].[PostCode], [w].[State], [w].[StorageCapacity], [w].[TimeZone] AS [TimeZone0], [w].[TotalArea], [w].[UpdatedByUserID] AS [UpdatedByUserID3], [w].[UpdatedDate] AS [UpdatedDate3], [w].[WarehouseCode], [w].[WarehouseName], [w].[WarehouseType]
    FROM [GoodsReceipt] AS [g]
    LEFT JOIN [PurchaseOrder] AS [p] ON [g].[PurchaseOrderID] = [p].[PurchaseOrderID]
    LEFT JOIN [Supplier] AS [s] ON [p].[SupplierID] = [s].[SupplierID]
    INNER JOIN [User] AS [u] ON [g].[ReceivedByUserID] = [u].[UserID]
    INNER JOIN [Warehouse] AS [w] ON [g].[WarehouseID] = [w].[WarehouseID]
    WHERE [g].[ReceiptID] = @__id_0
) AS [s0]
LEFT JOIN (
    SELECT [g0].[ReceiptItemID], [g0].[AcceptedQuantity], [g0].[BatchNumber], [g0].[Description], [g0].[ExpiryDate], [g0].[ReceiptID], [g0].[LineNumber], [g0].[LocationID], [g0].[Notes], [g0].[ProductID], [g0].[ProductName], [g0].[PurchaseOrderItemID], [g0].[QualityStatus], [g0].[ReceivedQuantity], [g0].[RejectedQuantity], [g0].[RejectionReason], [g0].[SerialNumber], [g0].[SortOrder], [g0].[UOMID], [g0].[UnitCost], [g0].[UnitPrice], [g0].[VariantID], [p0].[ProductID] AS [ProductID0], [p0].[AllowBackorder], [p0].[AllowPreorder], [p0].[Brand], [p0].[CategoryID], [p0].[CommodityCode], [p0].[CountryOfOrigin], [p0].[CreatedByUserID], [p0].[CreatedDate], [p0].[Currency], [p0].[DeletedByUserID], [p0].[DeletedDate], [p0].[Description] AS [Description0], [p0].[DimensionUOMID], [p0].[HSCode], [p0].[HasVariants], [p0].[Height], [p0].[InventoryUOMID], [p0].[IsActive], [p0].[IsDeleted], [p0].[IsKit], [p0].[IsTaxExempt], [p0].[LeadTimeDays], [p0].[Length], [p0].[ListPrice], [p0].[Manufacturer], [p0].[ManufacturerPartNumber], [p0].[MaxStockLevel], [p0].[MinStockLevel], [p0].[Notes] AS [Notes0], [p0].[ProductCode], [p0].[ProductName] AS [ProductName0], [p0].[ProductType], [p0].[PurchaseUOMID], [p0].[ReorderPoint], [p0].[ReorderQuantity], [p0].[RequiresBatch], [p0].[RequiresSerial], [p0].[SalesUOMID], [p0].[SellPrice], [p0].[ShelfLifeDays], [p0].[ShortDescription], [p0].[StandardCost], [p0].[Status], [p0].[TaxCodeID], [p0].[TrackInventory], [p0].[UpdatedByUserID], [p0].[UpdatedDate], [p0].[Volume], [p0].[VolumeUOMID], [p0].[WarrantyDays], [p0].[Weight], [p0].[WeightUOMID], [p0].[Width], [u0].[UOMID] AS [UOMID0], [u0].[BaseUnit], [u0].[ConversionFactor], [u0].[CreatedDate] AS [CreatedDate0], [u0].[DecimalPlaces], [u0].[IsActive] AS [IsActive0], [u0].[Symbol], [u0].[UOMCode], [u0].[UOMName], [u0].[UOMType], [u0].[UpdatedDate] AS [UpdatedDate0]
    FROM [GoodsReceiptItem] AS [g0]
    INNER JOIN [Product] AS [p0] ON [g0].[ProductID] = [p0].[ProductID]
    INNER JOIN [UnitOfMeasure] AS [u0] ON [g0].[UOMID] = [u0].[UOMID]
) AS [s1] ON [s0].[ReceiptID] = [s1].[ReceiptID]
ORDER BY [s0].[ReceiptID], [s0].[PurchaseOrderID0], [s0].[SupplierID1], [s0].[UserID], [s0].[WarehouseID0], [s1].[ReceiptItemID], [s1].[ProductID0]
2025-07-30 09:05:14.488 +03:00 [ERR] An exception occurred while iterating over the results of a query for context type 'nCarry.Web.Data.ApplicationDbContext'.
Microsoft.Data.SqlClient.SqlException (0x80131904): Execution Timeout Expired.  The timeout period elapsed prior to completion of the operation or the server is not responding.
 ---> System.ComponentModel.Win32Exception (258): Unknown error: 258
   at Microsoft.Data.SqlClient.SqlCommand.<>c.<ExecuteDbDataReaderAsync>b__211_0(Task`1 result)
   at System.Threading.Tasks.ContinuationResultTaskFromResultTask`2.InnerInvoke()
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.InitializeReaderAsync(AsyncEnumerator enumerator, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.SqlServer.Storage.Internal.SqlServerExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
ClientConnectionId:079b7ad0-9dd3-4b6d-a636-bc59570e5ead
Error Number:-2,State:0,Class:11
Microsoft.Data.SqlClient.SqlException (0x80131904): Execution Timeout Expired.  The timeout period elapsed prior to completion of the operation or the server is not responding.
 ---> System.ComponentModel.Win32Exception (258): Unknown error: 258
   at Microsoft.Data.SqlClient.SqlCommand.<>c.<ExecuteDbDataReaderAsync>b__211_0(Task`1 result)
   at System.Threading.Tasks.ContinuationResultTaskFromResultTask`2.InnerInvoke()
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.InitializeReaderAsync(AsyncEnumerator enumerator, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.SqlServer.Storage.Internal.SqlServerExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
ClientConnectionId:079b7ad0-9dd3-4b6d-a636-bc59570e5ead
Error Number:-2,State:0,Class:11
2025-07-30 09:05:14.490 +03:00 [ERR] HTTP GET /GoodsReceipt/Print/11 responded 500 in 35065.7022 ms
Microsoft.Data.SqlClient.SqlException (0x80131904): Execution Timeout Expired.  The timeout period elapsed prior to completion of the operation or the server is not responding.
 ---> System.ComponentModel.Win32Exception (258): Unknown error: 258
   at Microsoft.Data.SqlClient.SqlCommand.<>c.<ExecuteDbDataReaderAsync>b__211_0(Task`1 result)
   at System.Threading.Tasks.ContinuationResultTaskFromResultTask`2.InnerInvoke()
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.InitializeReaderAsync(AsyncEnumerator enumerator, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.SqlServer.Storage.Internal.SqlServerExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
   at Microsoft.EntityFrameworkCore.Query.ShapedQueryCompilingExpressionVisitor.SingleOrDefaultAsync[TSource](IAsyncEnumerable`1 asyncEnumerable, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.ShapedQueryCompilingExpressionVisitor.SingleOrDefaultAsync[TSource](IAsyncEnumerable`1 asyncEnumerable, CancellationToken cancellationToken)
   at nCarry.Web.Controllers.GoodsReceiptController.Print(Int32 id) in /Users/<USER>/Source/nCarry/nCarry/nCarry.Web/Controllers/GoodsReceiptController.cs:line 367
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.TaskOfIActionResultExecutor.Execute(ActionContext actionContext, IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeActionMethodAsync>g__Awaited|12_0(ControllerActionInvoker invoker, ValueTask`1 actionResultValueTask)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeInnerFilterAsync>g__Awaited|13_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextResourceFilter>g__Awaited|25_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.Rethrow(ResourceExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeFilterPipelineAsync>g__Awaited|20_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Session.SessionMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Session.SessionMiddleware.Invoke(HttpContext context)
   at Serilog.AspNetCore.RequestLoggingMiddleware.Invoke(HttpContext httpContext)
ClientConnectionId:079b7ad0-9dd3-4b6d-a636-bc59570e5ead
Error Number:-2,State:0,Class:11
2025-07-30 09:05:14.496 +03:00 [ERR] An unhandled exception has occurred while executing the request.
Microsoft.Data.SqlClient.SqlException (0x80131904): Execution Timeout Expired.  The timeout period elapsed prior to completion of the operation or the server is not responding.
 ---> System.ComponentModel.Win32Exception (258): Unknown error: 258
   at Microsoft.Data.SqlClient.SqlCommand.<>c.<ExecuteDbDataReaderAsync>b__211_0(Task`1 result)
   at System.Threading.Tasks.ContinuationResultTaskFromResultTask`2.InnerInvoke()
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.InitializeReaderAsync(AsyncEnumerator enumerator, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.SqlServer.Storage.Internal.SqlServerExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
   at Microsoft.EntityFrameworkCore.Query.ShapedQueryCompilingExpressionVisitor.SingleOrDefaultAsync[TSource](IAsyncEnumerable`1 asyncEnumerable, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.ShapedQueryCompilingExpressionVisitor.SingleOrDefaultAsync[TSource](IAsyncEnumerable`1 asyncEnumerable, CancellationToken cancellationToken)
   at nCarry.Web.Controllers.GoodsReceiptController.Print(Int32 id) in /Users/<USER>/Source/nCarry/nCarry/nCarry.Web/Controllers/GoodsReceiptController.cs:line 367
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.TaskOfIActionResultExecutor.Execute(ActionContext actionContext, IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeActionMethodAsync>g__Awaited|12_0(ControllerActionInvoker invoker, ValueTask`1 actionResultValueTask)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeInnerFilterAsync>g__Awaited|13_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextResourceFilter>g__Awaited|25_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.Rethrow(ResourceExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeFilterPipelineAsync>g__Awaited|20_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Session.SessionMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Session.SessionMiddleware.Invoke(HttpContext context)
   at Serilog.AspNetCore.RequestLoggingMiddleware.Invoke(HttpContext httpContext)
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
ClientConnectionId:079b7ad0-9dd3-4b6d-a636-bc59570e5ead
Error Number:-2,State:0,Class:11
2025-07-30 09:09:08.981 +03:00 [INF] Starting database seeding.
2025-07-30 09:09:10.955 +03:00 [INF] Admin user already exists. Seeding is not required.
2025-07-30 09:09:18.410 +03:00 [WRN] Failed to determine the https port for redirect.
2025-07-30 09:09:18.454 +03:00 [INF] HTTP GET / responded 302 in 46.2600 ms
2025-07-30 09:09:30.028 +03:00 [INF] HTTP GET /GoodsReceipt/Create responded 200 in 1335.1237 ms
2025-07-30 09:16:40.564 +03:00 [INF] HTTP GET /GoodsReceipt/Create responded 200 in 939.4445 ms
2025-07-30 09:17:09.590 +03:00 [INF] HTTP GET /GoodsReceipt/Create responded 200 in 438.7347 ms
2025-07-30 09:18:11.121 +03:00 [ERR] An exception occurred in the database while saving changes for context type 'nCarry.Web.Data.ApplicationDbContext'.
Microsoft.EntityFrameworkCore.DbUpdateException: An error occurred while saving the entity changes. See the inner exception for details.
 ---> Microsoft.Data.SqlClient.SqlException (0x80131904): Violation of UNIQUE KEY constraint 'UQ__Inventor__E733A2BF4077D565'. Cannot insert duplicate key in object 'dbo.InventoryTransaction'. The duplicate key value is (IT202507300001-001).
   at Microsoft.Data.SqlClient.SqlConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.SqlInternalConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.TdsParser.ThrowExceptionAndWarning(TdsParserStateObject stateObj, Boolean callerHasConnectionLock, Boolean asyncClose)
   at Microsoft.Data.SqlClient.TdsParser.TryRun(RunBehavior runBehavior, SqlCommand cmdHandler, SqlDataReader dataStream, BulkCopySimpleResultSet bulkCopyHandler, TdsParserStateObject stateObj, Boolean& dataReady)
   at Microsoft.Data.SqlClient.SqlDataReader.TryHasMoreRows(Boolean& moreRows)
   at Microsoft.Data.SqlClient.SqlDataReader.TryReadInternal(Boolean setTimeout, Boolean& more)
   at Microsoft.Data.SqlClient.SqlDataReader.ReadAsyncExecute(Task task, Object state)
   at Microsoft.Data.SqlClient.SqlDataReader.InvokeAsyncCall[T](SqlDataReaderBaseAsyncCallContext`1 context)
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Update.AffectedCountModificationCommandBatch.ConsumeResultSetAsync(Int32 startCommandIndex, RelationalDataReader reader, CancellationToken cancellationToken)
ClientConnectionId:5d29b504-a981-46c6-9ead-54e2fdb738b6
Error Number:2627,State:1,Class:14
   --- End of inner exception stack trace ---
   at Microsoft.EntityFrameworkCore.Update.AffectedCountModificationCommandBatch.ConsumeResultSetAsync(Int32 startCommandIndex, RelationalDataReader reader, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.AffectedCountModificationCommandBatch.ConsumeAsync(RelationalDataReader reader, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.SqlServer.Update.Internal.SqlServerModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalDatabase.SaveChangesAsync(IList`1 entries, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(IList`1 entriesToSave, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(StateManager stateManager, Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.SqlServer.Storage.Internal.SqlServerExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.DbContext.SaveChangesAsync(Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
Microsoft.EntityFrameworkCore.DbUpdateException: An error occurred while saving the entity changes. See the inner exception for details.
 ---> Microsoft.Data.SqlClient.SqlException (0x80131904): Violation of UNIQUE KEY constraint 'UQ__Inventor__E733A2BF4077D565'. Cannot insert duplicate key in object 'dbo.InventoryTransaction'. The duplicate key value is (IT202507300001-001).
   at Microsoft.Data.SqlClient.SqlConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.SqlInternalConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.TdsParser.ThrowExceptionAndWarning(TdsParserStateObject stateObj, Boolean callerHasConnectionLock, Boolean asyncClose)
   at Microsoft.Data.SqlClient.TdsParser.TryRun(RunBehavior runBehavior, SqlCommand cmdHandler, SqlDataReader dataStream, BulkCopySimpleResultSet bulkCopyHandler, TdsParserStateObject stateObj, Boolean& dataReady)
   at Microsoft.Data.SqlClient.SqlDataReader.TryHasMoreRows(Boolean& moreRows)
   at Microsoft.Data.SqlClient.SqlDataReader.TryReadInternal(Boolean setTimeout, Boolean& more)
   at Microsoft.Data.SqlClient.SqlDataReader.ReadAsyncExecute(Task task, Object state)
   at Microsoft.Data.SqlClient.SqlDataReader.InvokeAsyncCall[T](SqlDataReaderBaseAsyncCallContext`1 context)
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Update.AffectedCountModificationCommandBatch.ConsumeResultSetAsync(Int32 startCommandIndex, RelationalDataReader reader, CancellationToken cancellationToken)
ClientConnectionId:5d29b504-a981-46c6-9ead-54e2fdb738b6
Error Number:2627,State:1,Class:14
   --- End of inner exception stack trace ---
   at Microsoft.EntityFrameworkCore.Update.AffectedCountModificationCommandBatch.ConsumeResultSetAsync(Int32 startCommandIndex, RelationalDataReader reader, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.AffectedCountModificationCommandBatch.ConsumeAsync(RelationalDataReader reader, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.SqlServer.Update.Internal.SqlServerModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalDatabase.SaveChangesAsync(IList`1 entries, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(IList`1 entriesToSave, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(StateManager stateManager, Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.SqlServer.Storage.Internal.SqlServerExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.DbContext.SaveChangesAsync(Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
2025-07-30 09:18:11.546 +03:00 [INF] HTTP POST /GoodsReceipt/Create responded 200 in 1958.9685 ms
2025-07-30 09:19:22.525 +03:00 [INF] HTTP GET /GoodsReceipt responded 200 in 222.3442 ms
2025-07-30 09:19:32.254 +03:00 [INF] HTTP GET /GoodsReceipt/Details/11 responded 200 in 212.8368 ms
2025-07-30 09:22:03.849 +03:00 [INF] HTTP GET /GoodsReceipt/Create responded 200 in 435.9859 ms
2025-07-30 09:22:29.522 +03:00 [INF] HTTP GET /GoodsReceipt/Create responded 200 in 1181.6360 ms
2025-07-30 09:25:33.669 +03:00 [ERR] An exception occurred while iterating over the results of a query for context type 'nCarry.Web.Data.ApplicationDbContext'.
System.Data.SqlTypes.SqlNullValueException: Data is Null. This method or property cannot be called on Null values.
   at Microsoft.Data.SqlClient.SqlDataReader.GetString(Int32 i)
   at lambda_method2180(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
System.Data.SqlTypes.SqlNullValueException: Data is Null. This method or property cannot be called on Null values.
   at Microsoft.Data.SqlClient.SqlDataReader.GetString(Int32 i)
   at lambda_method2180(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
2025-07-30 09:25:34.041 +03:00 [INF] HTTP POST /GoodsReceipt/Create responded 200 in 1009.6965 ms
2025-07-30 09:27:06.081 +03:00 [INF] HTTP GET /GoodsReceipt/Create responded 200 in 521.5704 ms
2025-07-30 09:28:07.920 +03:00 [ERR] An exception occurred while iterating over the results of a query for context type 'nCarry.Web.Data.ApplicationDbContext'.
System.Data.SqlTypes.SqlNullValueException: Data is Null. This method or property cannot be called on Null values.
   at Microsoft.Data.SqlClient.SqlDataReader.GetString(Int32 i)
   at lambda_method2180(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
System.Data.SqlTypes.SqlNullValueException: Data is Null. This method or property cannot be called on Null values.
   at Microsoft.Data.SqlClient.SqlDataReader.GetString(Int32 i)
   at lambda_method2180(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
2025-07-30 09:28:08.320 +03:00 [INF] HTTP POST /GoodsReceipt/Create responded 200 in 1050.8219 ms
2025-07-30 09:30:45.323 +03:00 [INF] HTTP GET /GoodsReceipt/Create responded 200 in 385.0235 ms
2025-07-30 10:24:35.413 +03:00 [INF] HTTP GET /SalesInvoice responded 200 in 722.7494 ms
2025-07-30 10:24:35.426 +03:00 [INF] HTTP GET /nCarry.Web.styles.css responded 304 in 5.9423 ms
2025-07-30 10:24:41.739 +03:00 [INF] HTTP GET /SalesInvoice/Edit/2 responded 404 in 1.3381 ms
2025-07-30 10:25:16.541 +03:00 [INF] HTTP GET /SalesInvoice responded 200 in 119.2725 ms
2025-07-30 10:25:30.173 +03:00 [INF] HTTP GET /SalesInvoice/Edit/2 responded 404 in 2.1365 ms
gin responded 302 in 477.1364 ms
2025-07-30 09:41:16.726 +03:00 [INF] HTTP GET /Dashboard responded 200 in 1872.3177 ms
2025-07-30 09:41:16.741 +03:00 [INF] HTTP GET /nCarry.Web.styles.css responded 200 in 9.1409 ms
2025-07-30 09:41:16.905 +03:00 [INF] HTTP GET /Dashboard/GetChartData responded 200 in 145.9943 ms
2025-07-30 09:41:30.157 +03:00 [INF] HTTP GET /User responded 200 in 118.2789 ms
2025-07-30 09:42:24.276 +03:00 [INF] HTTP GET /User/Create responded 200 in 148.0368 ms
2025-07-30 09:43:34.073 +03:00 [INF] HTTP POST /User/Create responded 302 in 684.1955 ms
2025-07-30 09:43:34.186 +03:00 [INF] HTTP GET /User responded 200 in 111.5195 ms
2025-07-30 09:43:50.225 +03:00 [INF] HTTP GET /User/Edit/12 responded 200 in 235.6777 ms
2025-07-30 09:44:15.426 +03:00 [INF] HTTP POST /User/Edit/12 responded 302 in 436.6915 ms
2025-07-30 09:44:15.541 +03:00 [INF] HTTP GET /User responded 200 in 111.7183 ms
2025-07-30 09:44:30.659 +03:00 [INF] HTTP GET /User/Details/12 responded 200 in 135.3490 ms
2025-07-30 09:44:45.711 +03:00 [INF] HTTP GET /User responded 200 in 87.0528 ms
2025-07-30 09:44:52.573 +03:00 [INF] HTTP GET /User/Delete/12 responded 200 in 101.8469 ms
2025-07-30 09:44:59.740 +03:00 [INF] HTTP GET /User responded 200 in 101.4592 ms
2025-07-30 09:45:07.239 +03:00 [INF] HTTP GET /User/Delete/12 responded 200 in 119.8290 ms
2025-07-30 09:45:14.338 +03:00 [INF] HTTP POST /User/Delete/12 responded 302 in 220.8060 ms
2025-07-30 09:45:14.471 +03:00 [INF] HTTP GET /User responded 200 in 130.0948 ms
2025-07-30 09:45:33.237 +03:00 [INF] HTTP GET /User/ResetPassword/9 responded 200 in 112.2814 ms
2025-07-30 09:46:39.901 +03:00 [INF] HTTP GET /User responded 200 in 115.0312 ms
2025-07-30 09:50:01.785 +03:00 [INF] HTTP GET / responded 302 in 0.5344 ms
2025-07-30 09:50:01.791 +03:00 [INF] HTTP GET /Account/Login responded 200 in 2.1933 ms
2025-07-30 09:50:18.314 +03:00 [INF] HTTP POST /Account/Login responded 302 in 266.5143 ms
2025-07-30 09:50:19.834 +03:00 [INF] HTTP GET /Dashboard responded 200 in 1518.2478 ms
2025-07-30 09:50:19.954 +03:00 [INF] HTTP GET /Dashboard/GetChartData responded 200 in 91.7940 ms
2025-07-30 09:50:34.703 +03:00 [INF] HTTP GET /GoodsReceipt responded 200 in 269.2543 ms
2025-07-30 09:50:57.505 +03:00 [INF] HTTP GET /GoodsReceipt responded 200 in 125.0871 ms
2025-07-30 09:51:10.757 +03:00 [INF] HTTP GET /GoodsReceipt responded 200 in 107.9654 ms
2025-07-30 09:51:27.429 +03:00 [INF] HTTP GET /GoodsReceipt/Details/11 responded 200 in 244.2189 ms
2025-07-30 09:51:46.671 +03:00 [INF] HTTP GET /GoodsReceipt/Print/11 responded 200 in 145.6612 ms
2025-07-30 09:53:06.645 +03:00 [INF] HTTP GET /GoodsReceipt responded 200 in 126.7390 ms
2025-07-30 09:53:13.945 +03:00 [INF] HTTP GET /GoodsReceipt/Create responded 200 in 359.1062 ms
2025-07-30 09:53:32.450 +03:00 [INF] HTTP GET /GoodsReceipt/Create responded 200 in 466.4040 ms
2025-07-30 09:55:08.638 +03:00 [INF] HTTP GET /GoodsReceipt responded 200 in 99.0330 ms
2025-07-30 09:57:41.390 +03:00 [INF] HTTP GET /GoodsReceipt/Details/11 responded 200 in 114.9096 ms
2025-07-30 11:16:14.785 +03:00 [INF] HTTP GET /SalesInvoice responded 200 in 693.2554 ms
2025-07-30 11:16:14.795 +03:00 [INF] HTTP GET /nCarry.Web.styles.css responded 304 in 1.9672 ms
2025-07-30 11:16:26.793 +03:00 [INF] HTTP GET /SalesOrder responded 200 in 159.8210 ms
2025-07-30 11:16:36.713 +03:00 [INF] HTTP GET /SalesOrder/Details/29 responded 200 in 227.5697 ms
2025-07-30 11:16:44.230 +03:00 [INF] HTTP GET /SalesInvoice/CreateFromOrder responded 200 in 311.8901 ms
2025-07-30 11:16:57.512 +03:00 [INF] HTTP POST /Account/Logout responded 302 in 4.3429 ms
2025-07-30 11:16:57.516 +03:00 [INF] HTTP GET /Account/Login responded 200 in 1.8724 ms
2025-07-30 11:17:09.326 +03:00 [INF] HTTP POST /Account/Login responded 302 in 321.4866 ms
2025-07-30 11:17:11.011 +03:00 [INF] HTTP GET /Dashboard responded 200 in 1680.0686 ms
2025-07-30 11:17:11.155 +03:00 [INF] HTTP GET /Dashboard/GetChartData responded 200 in 100.1721 ms
2025-07-30 11:17:17.126 +03:00 [INF] HTTP GET /SalesInvoice responded 200 in 108.1403 ms
2025-07-30 11:17:30.493 +03:00 [INF] HTTP GET /SalesInvoice/Create responded 200 in 5.3923 ms
2025-07-30 11:19:45.600 +03:00 [INF] HTTP GET /SalesInvoice responded 200 in 120.5468 ms
2025-07-30 11:19:52.424 +03:00 [INF] HTTP GET /SalesInvoice/Edit/2 responded 404 in 1.0371 ms
2025-07-30 11:20:32.750 +03:00 [INF] HTTP GET /SalesInvoice responded 200 in 107.3620 ms
2025-07-30 11:21:08.992 +03:00 [INF] HTTP GET /SalesInvoice responded 200 in 112.9277 ms
2025-07-30 11:23:14.437 +03:00 [INF] HTTP GET /SalesInvoice/Create responded 200 in 2.4076 ms
2025-07-30 11:23:38.975 +03:00 [INF] HTTP GET /SalesInvoice responded 200 in 129.1359 ms
2025-07-30 11:23:41.501 +03:00 [INF] HTTP GET /SalesOrder responded 200 in 141.3133 ms
2025-07-30 11:23:47.516 +03:00 [INF] HTTP GET /SalesOrder/Details/29 responded 200 in 140.5626 ms
2025-07-30 11:23:51.966 +03:00 [INF] HTTP GET /SalesInvoice/CreateFromOrder responded 200 in 237.0611 ms
2025-07-30 11:24:55.293 +03:00 [INF] HTTP GET /SalesOrder responded 200 in 128.2189 ms
2025-07-30 11:25:05.522 +03:00 [INF] HTTP GET /SalesOrder/Details/18 responded 200 in 171.7555 ms
2025-07-30 11:25:14.837 +03:00 [INF] HTTP GET /SalesInvoice/CreateFromOrder responded 200 in 223.5794 ms
2025-07-30 11:25:23.510 +03:00 [INF] HTTP POST /Account/Logout responded 302 in 0.7435 ms
2025-07-30 11:25:23.513 +03:00 [INF] HTTP GET /Account/Login responded 200 in 1.1025 ms
2025-07-30 11:25:38.367 +03:00 [INF] HTTP POST /Account/Login responded 302 in 1081.3507 ms
2025-07-30 11:25:45.920 +03:00 [INF] HTTP GET /Dashboard responded 200 in 7548.8292 ms
2025-07-30 11:25:46.060 +03:00 [INF] HTTP GET /Dashboard/GetChartData responded 200 in 96.4505 ms
2025-07-30 11:26:02.989 +03:00 [INF] HTTP GET /SalesInvoice responded 200 in 924.4150 ms
2025-07-30 11:26:19.586 +03:00 [INF] HTTP GET /SalesOrder responded 200 in 136.6138 ms
2025-07-30 11:26:30.417 +03:00 [INF] HTTP GET /SalesOrder/Details/6 responded 200 in 147.1850 ms
2025-07-30 11:26:39.471 +03:00 [INF] HTTP GET /SalesInvoice/CreateFromOrder responded 200 in 220.5532 ms
2025-07-30 11:26:49.047 +03:00 [INF] HTTP POST /Account/Logout responded 302 in 0.5849 ms
2025-07-30 11:26:49.050 +03:00 [INF] HTTP GET /Account/Login responded 200 in 1.0353 ms
2025-07-30 11:27:03.939 +03:00 [INF] HTTP POST /Account/Login responded 302 in 301.4828 ms
2025-07-30 11:27:05.592 +03:00 [INF] HTTP GET /Dashboard responded 200 in 1650.2491 ms
2025-07-30 11:27:05.729 +03:00 [INF] HTTP GET /Dashboard/GetChartData responded 200 in 97.5615 ms
2025-07-30 11:28:13.011 +032025-07-30 11:37:51.756 +03:00 [INF] HTTP GET /ProductImport responded 404 in 2.7532 ms
3:00 [WRN] Compiling a query which loads related collections for more than one collection navigation, either via 'Include' or through projection, but no 'QuerySplittingBehavior' has been configured. By default, Entity Framework will use 'QuerySplittingBehavior.SingleQuery', which can potentially result in slow query performance. See https://go.microsoft.com/fwlink/?linkid=2134277 for more information. To identify the query that's triggering this warning call 'ConfigureWarnings(w => w.Throw(RelationalEventId.MultipleCollectionIncludeWarning))'.
2025-07-30 11:28:20.358 +03:00 [WRN] The query uses the 'First'/'FirstOrDefault' operator without 'OrderBy' and filter operators. This may lead to unpredictable results.
2025-07-30 11:28:20.493 +03:00 [INF] HTTP GET /SalesInvoice/Print/1 responded 200 in 385.7260 ms
2025-07-30 11:29:10.023 +03:00 [INF] HTTP GET /SalesInvoice/Print/2 responded 200 in 228.7862 ms
2025-07-30 11:37:56.581 +03:00 [INF] HTTP GET /ProductImport responded 404 in 0.7535 ms
oft.AspNetCore.Hosting.GenericWebHostService.StartAsync(CancellationToken cancellationToken)
   at Microsoft.Extensions.Hosting.Internal.Host.<StartAsync>b__15_1(IHostedService service, CancellationToken token)
   at Microsoft.Extensions.Hosting.Internal.Host.ForeachService[T](IEnumerable`1 services, CancellationToken token, Boolean concurrent, Boolean abortOnFirstException, List`1 exceptions, Func`3 operation)
2025-07-30 10:09:59.887 +03:00 [FTL] Application terminated unexpectedly
System.IO.IOException: Failed to bind to address http://127.0.0.1:5015: address already in use.
 ---> Microsoft.AspNetCore.Connections.AddressInUseException: Address already in use
 ---> System.Net.Sockets.SocketException (48): Address already in use
   at System.Net.Sockets.Socket.DoBind(EndPoint endPointSnapshot, SocketAddress socketAddress)
   at System.Net.Sockets.Socket.Bind(EndPoint localEP)
   at Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets.SocketTransportOptions.CreateDefaultBoundListenSocket(EndPoint endpoint)
   at Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets.SocketConnectionListener.Bind()
   --- End of inner exception stack trace ---
   at Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets.SocketConnectionListener.Bind()
   at Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets.SocketTransportFactory.BindAsync(EndPoint endpoint, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.Infrastructure.TransportManager.BindAsync(EndPoint endPoint, ConnectionDelegate connectionDelegate, EndpointConfig endpointConfig, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.KestrelServerImpl.<>c__DisplayClass28_0`1.<<StartAsync>g__OnBind|0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.AddressBinder.BindEndpointAsync(ListenOptions endpoint, AddressBindContext context, CancellationToken cancellationToken)
   --- End of inner exception stack trace ---
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.AddressBinder.BindEndpointAsync(ListenOptions endpoint, AddressBindContext context, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.LocalhostListenOptions.BindAsync(AddressBindContext context, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.AddressBinder.AddressesStrategy.BindAsync(AddressBindContext context, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.AddressBinder.BindAsync(ListenOptions[] listenOptions, AddressBindContext context, Func`2 useHttps, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.KestrelServerImpl.BindAsync(CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.KestrelServerImpl.StartAsync[TContext](IHttpApplication`1 application, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Hosting.GenericWebHostService.StartAsync(CancellationToken cancellationToken)
   at Microsoft.Extensions.Hosting.Internal.Host.<StartAsync>b__15_1(IHostedService service, CancellationToken token)
   at Microsoft.Extensions.Hosting.Internal.Host.ForeachService[T](IEnumerable`1 services, CancellationToken token, Boolean concurrent, Boolean abortOnFirstException, List`1 exceptions, Func`3 operation)
   at Microsoft.Extensions.Hosting.Internal.Host.StartAsync(CancellationToken cancellationToken)
   at Microsoft.Extensions.Hosting.HostingAbstractionsHostExtensions.RunAsync(IHost host, CancellationToken token)
   at Microsoft.Extensions.Hosting.HostingAbstractionsHostExtensions.RunAsync(IHost host, CancellationToken token)
   at Microsoft.Extensions.Hosting.HostingAbstractionsHostExtensions.Run(IHost host)
   at Program.<Main>$(String[] args) in /Users/<USER>/Source/nCarry/nCarry/nCarry.Web/Program.cs:line 82
2025-07-30 10:10:43.780 +03:00 [INF] Starting database seeding.
2025-07-30 10:10:45.404 +03:00 [INF] Admin user already exists. Seeding is not required.
2025-07-30 10:10:45.440 +03:00 [ERR] Hosting failed to start
System.IO.IOException: Failed to bind to address http://127.0.0.1:5015: address already in use.
 ---> Microsoft.AspNetCore.Connections.AddressInUseException: Address already in use
 ---> System.Net.Sockets.SocketException (48): Address already in use
   at System.Net.Sockets.Socket.DoBind(EndPoint endPointSnapshot, SocketAddress socketAddress)
   at System.Net.Sockets.Socket.Bind(EndPoint localEP)
   at Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets.SocketTransportOptions.CreateDefaultBoundListenSocket(EndPoint endpoint)
   at Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets.SocketConnectionListener.Bind()
   --- End of inner exception stack trace ---
   at Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets.SocketConnectionListener.Bind()
   at Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets.SocketTransportFactory.BindAsync(EndPoint endpoint, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.Infrastructure.TransportManager.BindAsync(EndPoint endPoint, ConnectionDelegate connectionDelegate, EndpointConfig endpointConfig, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.KestrelServerImpl.<>c__DisplayClass28_0`1.<<StartAsync>g__OnBind|0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.AddressBinder.BindEndpointAsync(ListenOptions endpoint, AddressBindContext context, CancellationToken cancellationToken)
   --- End of inner exception stack trace ---
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.AddressBinder.BindEndpointAsync(ListenOptions endpoint, AddressBindContext context, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.LocalhostListenOptions.BindAsync(AddressBindContext context, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.AddressBinder.AddressesStrategy.BindAsync(AddressBindContext context, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.AddressBinder.BindAsync(ListenOptions[] listenOptions, AddressBindContext context, Func`2 useHttps, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.KestrelServerImpl.BindAsync(CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.KestrelServerImpl.StartAsync[TContext](IHttpApplication`1 application, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Hosting.GenericWebHostService.StartAsync(CancellationToken cancellationToken)
   at Microsoft.Extensions.Hosting.Internal.Host.<StartAsync>b__15_1(IHostedService service, CancellationToken token)
   at Microsoft.Extensions.Hosting.Internal.Host.ForeachService[T](IEnumerable`1 services, CancellationToken token, Boolean concurrent, Boolean abortOnFirstException, List`1 exceptions, Func`3 operation)
2025-07-30 10:10:45.444 +03:00 [FTL] Application terminated unexpectedly
System.IO.IOException: Failed to bind to address http://127.0.0.1:5015: address already in use.
 ---> Microsoft.AspNetCore.Connections.AddressInUseException: Address already in use
 ---> System.Net.Sockets.SocketException (48): Address already in use
   at System.Net.Sockets.Socket.DoBind(EndPoint endPointSnapshot, SocketAddress socketAddress)
   at System.Net.Sockets.Socket.Bind(EndPoint localEP)
   at Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets.SocketTransportOptions.CreateDefaultBoundListenSocket(EndPoint endpoint)
   at Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets.SocketConnectionListener.Bind()
   --- End of inner exception stack trace ---
   at Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets.SocketConnectionListener.Bind()
   at Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets.SocketTransportFactory.BindAsync(EndPoint endpoint, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.Infrastructure.TransportManager.BindAsync(EndPoint endPoint, ConnectionDelegate connectionDelegate, EndpointConfig endpointConfig, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.KestrelServerImpl.<>c__DisplayClass28_0`1.<<StartAsync>g__OnBind|0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.AddressBinder.BindEndpointAsync(ListenOptions endpoint, AddressBindContext context, CancellationToken cancellationToken)
   --- End of inner exception stack trace ---
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.AddressBinder.BindEndpointAsync(ListenOptions endpoint, AddressBindContext context, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.LocalhostListenOptions.BindAsync(AddressBindContext context, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.AddressBinder.AddressesStrategy.BindAsync(AddressBindContext context, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.AddressBinder.BindAsync(ListenOptions[] listenOptions, AddressBindContext context, Func`2 useHttps, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.KestrelServerImpl.BindAsync(CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.KestrelServerImpl.StartAsync[TContext](IHttpApplication`1 application, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Hosting.GenericWebHostService.StartAsync(CancellationToken cancellationToken)
   at Microsoft.Extensions.Hosting.Internal.Host.<StartAsync>b__15_1(IHostedService service, CancellationToken token)
   at Microsoft.Extensions.Hosting.Internal.Host.ForeachService[T](IEnumerable`1 services, CancellationToken token, Boolean concurrent, Boolean abortOnFirstException, List`1 exceptions, Func`3 operation)
   at Microsoft.Extensions.Hosting.Internal.Host.StartAsync(CancellationToken cancellationToken)
   at Microsoft.Extensions.Hosting.HostingAbstractionsHostExtensions.RunAsync(IHost host, CancellationToken token)
   at Microsoft.Extensions.Hosting.HostingAbstractionsHostExtensions.RunAsync(IHost host, CancellationToken token)
   at Microsoft.Extensions.Hosting.HostingAbstractionsHostExtensions.Run(IHost host)
   at Program.<Main>$(String[] args) in /Users/<USER>/Source/nCarry/nCarry/nCarry.Web/Program.cs:line 82
2025-07-30 10:24:26.826 +03:00 [INF] Starting database seeding.
2025-07-30 10:24:29.014 +03:00 [INF] Admin user already exists. Seeding is not required.
2025-07-30 10:24:29.048 +03:00 [ERR] Hosting failed to start
System.IO.IOException: Failed to bind to address http://127.0.0.1:5298: address already in use.
 ---> Microsoft.AspNetCore.Connections.AddressInUseException: Address already in use
 ---> System.Net.Sockets.SocketException (48): Address already in use
   at System.Net.Sockets.Socket.DoBind(EndPoint endPointSnapshot, SocketAddress socketAddress)
   at System.Net.Sockets.Socket.Bind(EndPoint localEP)
   at Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets.SocketTransportOptions.CreateDefaultBoundListenSocket(EndPoint endpoint)
   at Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets.SocketConnectionListener.Bind()
   --- End of inner exception stack trace ---
   at Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets.SocketConnectionListener.Bind()
   at Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets.SocketTransportFactory.BindAsync(EndPoint endpoint, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.Infrastructure.TransportManager.BindAsync(EndPoint endPoint, ConnectionDelegate connectionDelegate, EndpointConfig endpointConfig, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.KestrelServerImpl.<>c__DisplayClass28_0`1.<<StartAsync>g__OnBind|0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.AddressBinder.BindEndpointAsync(ListenOptions endpoint, AddressBindContext context, CancellationToken cancellationToken)
   --- End of inner exception stack trace ---
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.AddressBinder.BindEndpointAsync(ListenOptions endpoint, AddressBindContext context, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.LocalhostListenOptions.BindAsync(AddressBindContext context, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.AddressBinder.AddressesStrategy.BindAsync(AddressBindContext context, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.AddressBinder.BindAsync(ListenOptions[] listenOptions, AddressBindContext context, Func`2 useHttps, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.KestrelServerImpl.BindAsync(CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.KestrelServerImpl.StartAsync[TContext](IHttpApplication`1 application, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Hosting.GenericWebHostService.StartAsync(CancellationToken cancellationToken)
   at Microsoft.Extensions.Hosting.Internal.Host.<StartAsync>b__15_1(IHostedService service, CancellationToken token)
   at Microsoft.Extensions.Hosting.Internal.Host.ForeachService[T](IEnumerable`1 services, CancellationToken token, Boolean concurrent, Boolean abortOnFirstException, List`1 exceptions, Func`3 operation)
2025-07-30 10:24:29.053 +03:00 [FTL] Application terminated unexpectedly
System.IO.IOException: Failed to bind to address http://127.0.0.1:5298: address already in use.
 ---> Microsoft.AspNetCore.Connections.AddressInUseException: Address already in use
 ---> System.Net.Sockets.SocketException (48): Address already in use
   at System.Net.Sockets.Socket.DoBind(EndPoint endPointSnapshot, SocketAddress socketAddress)
   at System.Net.Sockets.Socket.Bind(EndPoint localEP)
   at Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets.SocketTransportOptions.CreateDefaultBoundListenSocket(EndPoint endpoint)
   at Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets.SocketConnectionListener.Bind()
   --- End of inner exception stack trace ---
   at Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets.SocketConnectionListener.Bind()
   at Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets.SocketTransportFactory.BindAsync(EndPoint endpoint, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.Infrastructure.TransportManager.BindAsync(EndPoint endPoint, ConnectionDelegate connectionDelegate, EndpointConfig endpointConfig, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.KestrelServerImpl.<>c__DisplayClass28_0`1.<<StartAsync>g__OnBind|0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.AddressBinder.BindEndpointAsync(ListenOptions endpoint, AddressBindContext context, CancellationToken cancellationToken)
   --- End of inner exception stack trace ---
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.AddressBinder.BindEndpointAsync(ListenOptions endpoint, AddressBindContext context, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.LocalhostListenOptions.BindAsync(AddressBindContext context, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.AddressBinder.AddressesStrategy.BindAsync(AddressBindContext context, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.AddressBinder.BindAsync(ListenOptions[] listenOptions, AddressBindContext context, Func`2 useHttps, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.KestrelServerImpl.BindAsync(CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.KestrelServerImpl.StartAsync[TContext](IHttpApplication`1 application, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Hosting.GenericWebHostService.StartAsync(CancellationToken cancellationToken)
   at Microsoft.Extensions.Hosting.Internal.Host.<StartAsync>b__15_1(IHostedService service, CancellationToken token)
   at Microsoft.Extensions.Hosting.Internal.Host.ForeachService[T](IEnumerable`1 services, CancellationToken token, Boolean concurrent, Boolean abortOnFirstException, List`1 exceptions, Func`3 operation)
   at Microsoft.Extensions.Hosting.Internal.Host.StartAsync(CancellationToken cancellationToken)
   at Microsoft.Extensions.Hosting.HostingAbstractionsHostExtensions.RunAsync(IHost host, CancellationToken token)
   at Microsoft.Extensions.Hosting.HostingAbstractionsHostExtensions.RunAsync(IHost host, CancellationToken token)
   at Microsoft.Extensions.Hosting.HostingAbstractionsHostExtensions.Run(IHost host)
   at Program.<Main>$(String[] args) in /Users/<USER>/Source/nCarry/nCarry/nCarry.Web/Program.cs:line 82
2025-07-30 10:25:05.101 +03:00 [INF] Starting database seeding.
2025-07-30 10:25:06.700 +03:00 [INF] Admin user already exists. Seeding is not required.
2025-07-30 10:25:06.735 +03:00 [ERR] Hosting failed to start
System.IO.IOException: Failed to bind to address http://127.0.0.1:5298: address already in use.
 ---> Microsoft.AspNetCore.Connections.AddressInUseException: Address already in use
 ---> System.Net.Sockets.SocketException (48): Address already in use
   at System.Net.Sockets.Socket.DoBind(EndPoint endPointSnapshot, SocketAddress socketAddress)
   at System.Net.Sockets.Socket.Bind(EndPoint localEP)
   at Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets.SocketTransportOptions.CreateDefaultBoundListenSocket(EndPoint endpoint)
   at Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets.SocketConnectionListener.Bind()
   --- End of inner exception stack trace ---
   at Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets.SocketConnectionListener.Bind()
   at Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets.SocketTransportFactory.BindAsync(EndPoint endpoint, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.Infrastructure.TransportManager.BindAsync(EndPoint endPoint, ConnectionDelegate connectionDelegate, EndpointConfig endpointConfig, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.KestrelServerImpl.<>c__DisplayClass28_0`1.<<StartAsync>g__OnBind|0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.AddressBinder.BindEndpointAsync(ListenOptions endpoint, AddressBindContext context, CancellationToken cancellationToken)
   --- End of inner exception stack trace ---
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.AddressBinder.BindEndpointAsync(ListenOptions endpoint, AddressBindContext context, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.LocalhostListenOptions.BindAsync(AddressBindContext context, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.AddressBinder.AddressesStrategy.BindAsync(AddressBindContext context, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.AddressBinder.BindAsync(ListenOptions[] listenOptions, AddressBindContext context, Func`2 useHttps, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.KestrelServerImpl.BindAsync(CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.KestrelServerImpl.StartAsync[TContext](IHttpApplication`1 application, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Hosting.GenericWebHostService.StartAsync(CancellationToken cancellationToken)
   at Microsoft.Extensions.Hosting.Internal.Host.<StartAsync>b__15_1(IHostedService service, CancellationToken token)
   at Microsoft.Extensions.Hosting.Internal.Host.ForeachService[T](IEnumerable`1 services, CancellationToken token, Boolean concurrent, Boolean abortOnFirstException, List`1 exceptions, Func`3 operation)
2025-07-30 10:25:06.739 +03:00 [FTL] Application terminated unexpectedly
System.IO.IOException: Failed to bind to address http://127.0.0.1:5298: address already in use.
 ---> Microsoft.AspNetCore.Connections.AddressInUseException: Address already in use
 ---> System.Net.Sockets.SocketException (48): Address already in use
   at System.Net.Sockets.Socket.DoBind(EndPoint endPointSnapshot, SocketAddress socketAddress)
   at System.Net.Sockets.Socket.Bind(EndPoint localEP)
   at Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets.SocketTransportOptions.CreateDefaultBoundListenSocket(EndPoint endpoint)
   at Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets.SocketConnectionListener.Bind()
   --- End of inner exception stack trace ---
   at Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets.SocketConnectionListener.Bind()
   at Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets.SocketTransportFactory.BindAsync(EndPoint endpoint, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.Infrastructure.TransportManager.BindAsync(EndPoint endPoint, ConnectionDelegate connectionDelegate, EndpointConfig endpointConfig, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.KestrelServerImpl.<>c__DisplayClass28_0`1.<<StartAsync>g__OnBind|0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.AddressBinder.BindEndpointAsync(ListenOptions endpoint, AddressBindContext context, CancellationToken cancellationToken)
   --- End of inner exception stack trace ---
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.AddressBinder.BindEndpointAsync(ListenOptions endpoint, AddressBindContext context, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.LocalhostListenOptions.BindAsync(AddressBindContext context, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.AddressBinder.AddressesStrategy.BindAsync(AddressBindContext context, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.AddressBinder.BindAsync(ListenOptions[] listenOptions, AddressBindContext context, Func`2 useHttps, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.KestrelServerImpl.BindAsync(CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.KestrelServerImpl.StartAsync[TContext](IHttpApplication`1 application, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Hosting.GenericWebHostService.StartAsync(CancellationToken cancellationToken)
   at Microsoft.Extensions.Hosting.Internal.Host.<StartAsync>b__15_1(IHostedService service, CancellationToken token)
   at Microsoft.Extensions.Hosting.Internal.Host.ForeachService[T](IEnumerable`1 services, CancellationToken token, Boolean concurrent, Boolean abortOnFirstException, List`1 exceptions, Func`3 operation)
   at Microsoft.Extensions.Hosting.Internal.Host.StartAsync(CancellationToken cancellationToken)
   at Microsoft.Extensions.Hosting.HostingAbstractionsHostExtensions.RunAsync(IHost host, CancellationToken token)
   at Microsoft.Extensions.Hosting.HostingAbstractionsHostExtensions.RunAsync(IHost host, CancellationToken token)
   at Microsoft.Extensions.Hosting.HostingAbstractionsHostExtensions.Run(IHost host)
   at Program.<Main>$(String[] args) in /Users/<USER>/Source/nCarry/nCarry/nCarry.Web/Program.cs:line 82
2025-07-30 10:25:47.374 +03:00 [INF] Starting database seeding.
2025-07-30 10:25:49.026 +03:00 [INF] Admin user already exists. Seeding is not required.
2025-07-30 10:27:52.073 +03:00 [WRN] Failed to determine the https port for redirect.
2025-07-30 10:27:52.474 +03:00 [INF] HTTP GET /SalesInvoice responded 200 in 399.1815 ms
2025-07-30 10:27:58.272 +03:00 [INF] HTTP GET /SalesInvoice/Edit/2 responded 200 in 233.2239 ms
2025-07-30 10:28:04.862 +03:00 [INF] HTTP GET /SalesInvoice responded 200 in 126.8673 ms
2025-07-30 10:28:10.419 +03:00 [INF] HTTP GET /SalesInvoice/Delete/2 responded 200 in 116.4787 ms
2025-07-30 10:28:17.675 +03:00 [INF] HTTP GET /SalesInvoice/Create responded 200 in 126.9468 ms
2025-07-30 10:29:03.303 +03:00 [ERR] An exception occurred in the database while saving changes for context type 'nCarry.Web.Data.ApplicationDbContext'.
Microsoft.EntityFrameworkCore.DbUpdateException: An error occurred while saving the entity changes. See the inner exception for details.
 ---> Microsoft.Data.SqlClient.SqlException (0x80131904): The INSERT statement conflicted with the FOREIGN KEY constraint "FK_SalesInvoice_Customer". The conflict occurred in database "nCarryDB", table "dbo.Customer", column 'CustomerID'.
   at Microsoft.Data.SqlClient.SqlConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.SqlInternalConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.TdsParser.ThrowExceptionAndWarning(TdsParserStateObject stateObj, Boolean callerHasConnectionLock, Boolean asyncClose)
   at Microsoft.Data.SqlClient.TdsParser.TryRun(RunBehavior runBehavior, SqlCommand cmdHandler, SqlDataReader dataStream, BulkCopySimpleResultSet bulkCopyHandler, TdsParserStateObject stateObj, Boolean& dataReady)
   at Microsoft.Data.SqlClient.SqlDataReader.TryHasMoreRows(Boolean& moreRows)
   at Microsoft.Data.SqlClient.SqlDataReader.TryReadInternal(Boolean setTimeout, Boolean& more)
   at Microsoft.Data.SqlClient.SqlDataReader.ReadAsyncExecute(Task task, Object state)
   at Microsoft.Data.SqlClient.SqlDataReader.InvokeAsyncCall[T](SqlDataReaderBaseAsyncCallContext`1 context)
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Update.AffectedCountModificationCommandBatch.ConsumeResultSetAsync(Int32 startCommandIndex, RelationalDataReader reader, CancellationToken cancellationToken)
ClientConnectionId:84972cd4-94a1-49ad-939d-11519305337e
Error Number:547,State:0,Class:16
   --- End of inner exception stack trace ---
   at Microsoft.EntityFrameworkCore.Update.AffectedCountModificationCommandBatch.ConsumeResultSetAsync(Int32 startCommandIndex, RelationalDataReader reader, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.AffectedCountModificationCommandBatch.ConsumeAsync(RelationalDataReader reader, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.SqlServer.Update.Internal.SqlServerModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalDatabase.SaveChangesAsync(IList`1 entries, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(IList`1 entriesToSave, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(StateManager stateManager, Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.SqlServer.Storage.Internal.SqlServerExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.DbContext.SaveChangesAsync(Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
Microsoft.EntityFrameworkCore.DbUpdateException: An error occurred while saving the entity changes. See the inner exception for details.
 ---> Microsoft.Data.SqlClient.SqlException (0x80131904): The INSERT statement conflicted with the FOREIGN KEY constraint "FK_SalesInvoice_Customer". The conflict occurred in database "nCarryDB", table "dbo.Customer", column 'CustomerID'.
   at Microsoft.Data.SqlClient.SqlConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.SqlInternalConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.TdsParser.ThrowExceptionAndWarning(TdsParserStateObject stateObj, Boolean callerHasConnectionLock, Boolean asyncClose)
   at Microsoft.Data.SqlClient.TdsParser.TryRun(RunBehavior runBehavior, SqlCommand cmdHandler, SqlDataReader dataStream, BulkCopySimpleResultSet bulkCopyHandler, TdsParserStateObject stateObj, Boolean& dataReady)
   at Microsoft.Data.SqlClient.SqlDataReader.TryHasMoreRows(Boolean& moreRows)
   at Microsoft.Data.SqlClient.SqlDataReader.TryReadInternal(Boolean setTimeout, Boolean& more)
   at Microsoft.Data.SqlClient.SqlDataReader.ReadAsyncExecute(Task task, Object state)
   at Microsoft.Data.SqlClient.SqlDataReader.InvokeAsyncCall[T](SqlDataReaderBaseAsyncCallContext`1 context)
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Update.AffectedCountModificationCommandBatch.ConsumeResultSetAsync(Int32 startCommandIndex, RelationalDataReader reader, CancellationToken cancellationToken)
ClientConnectionId:84972cd4-94a1-49ad-939d-11519305337e
Error Number:547,State:0,Class:16
   --- End of inner exception stack trace ---
   at Microsoft.EntityFrameworkCore.Update.AffectedCountModificationCommandBatch.ConsumeResultSetAsync(Int32 startCommandIndex, RelationalDataReader reader, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.AffectedCountModificationCommandBatch.ConsumeAsync(RelationalDataReader reader, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.SqlServer.Update.Internal.SqlServerModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalDatabase.SaveChangesAsync(IList`1 entries, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(IList`1 entriesToSave, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(StateManager stateManager, Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.SqlServer.Storage.Internal.SqlServerExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.DbContext.SaveChangesAsync(Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
2025-07-30 10:29:03.492 +03:00 [INF] HTTP POST /SalesInvoice/Create responded 200 in 738.7703 ms
2025-07-30 10:30:06.966 +03:00 [INF] HTTP GET /SalesOrder responded 200 in 174.5437 ms
2025-07-30 10:30:13.972 +03:00 [INF] HTTP GET /SalesOrder/Details/29 responded 200 in 233.5615 ms
2025-07-30 10:30:21.466 +03:00 [INF] HTTP GET /SalesInvoice/CreateFromOrder responded 200 in 226.4027 ms
2025-07-30 10:53:50.667 +03:00 [ERR] An exception occurred in the database while saving changes for context type 'nCarry.Web.Data.ApplicationDbContext'.
Microsoft.EntityFrameworkCore.DbUpdateException: An error occurred while saving the entity changes. See the inner exception for details.
 ---> Microsoft.Data.SqlClient.SqlException (0x80131904): Cannot insert the value NULL into column 'SortOrder', table 'nCarryDB.dbo.SalesInvoiceItem'; column does not allow nulls. UPDATE fails.
   at Microsoft.Data.SqlClient.SqlConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.SqlInternalConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.TdsParser.ThrowExceptionAndWarning(TdsParserStateObject stateObj, Boolean callerHasConnectionLock, Boolean asyncClose)
   at Microsoft.Data.SqlClient.TdsParser.TryRun(RunBehavior runBehavior, SqlCommand cmdHandler, SqlDataReader dataStream, BulkCopySimpleResultSet bulkCopyHandler, TdsParserStateObject stateObj, Boolean& dataReady)
   at Microsoft.Data.SqlClient.SqlDataReader.TryHasMoreRows(Boolean& moreRows)
   at Microsoft.Data.SqlClient.SqlDataReader.TryReadInternal(Boolean setTimeout, Boolean& more)
   at Microsoft.Data.SqlClient.SqlDataReader.ReadAsyncExecute(Task task, Object state)
   at Microsoft.Data.SqlClient.SqlDataReader.InvokeAsyncCall[T](SqlDataReaderBaseAsyncCallContext`1 context)
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Update.AffectedCountModificationCommandBatch.ConsumeResultSetAsync(Int32 startCommandIndex, RelationalDataReader reader, CancellationToken cancellationToken)
ClientConnectionId:c3f4970f-ec78-4b19-9378-d4c1f083bd3d
Error Number:515,State:2,Class:16
   --- End of inner exception stack trace ---
   at Microsoft.EntityFrameworkCore.Update.AffectedCountModificationCommandBatch.ConsumeResultSetAsync(Int32 startCommandIndex, RelationalDataReader reader, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.AffectedCountModificationCommandBatch.ConsumeAsync(RelationalDataReader reader, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.SqlServer.Update.Internal.SqlServerModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalDatabase.SaveChangesAsync(IList`1 entries, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(IList`1 entriesToSave, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(StateManager stateManager, Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.SqlServer.Storage.Internal.SqlServerExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.DbContext.SaveChangesAsync(Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
Microsoft.EntityFrameworkCore.DbUpdateException: An error occurred while saving the entity changes. See the inner exception for details.
 ---> Microsoft.Data.SqlClient.SqlException (0x80131904): Cannot insert the value NULL into column 'SortOrder', table 'nCarryDB.dbo.SalesInvoiceItem'; column does not allow nulls. UPDATE fails.
   at Microsoft.Data.SqlClient.SqlConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.SqlInternalConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.TdsParser.ThrowExceptionAndWarning(TdsParserStateObject stateObj, Boolean callerHasConnectionLock, Boolean asyncClose)
   at Microsoft.Data.SqlClient.TdsParser.TryRun(RunBehavior runBehavior, SqlCommand cmdHandler, SqlDataReader dataStream, BulkCopySimpleResultSet bulkCopyHandler, TdsParserStateObject stateObj, Boolean& dataReady)
   at Microsoft.Data.SqlClient.SqlDataReader.TryHasMoreRows(Boolean& moreRows)
   at Microsoft.Data.SqlClient.SqlDataReader.TryReadInternal(Boolean setTimeout, Boolean& more)
   at Microsoft.Data.SqlClient.SqlDataReader.ReadAsyncExecute(Task task, Object state)
   at Microsoft.Data.SqlClient.SqlDataReader.InvokeAsyncCall[T](SqlDataReaderBaseAsyncCallContext`1 context)
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Update.AffectedCountModificationCommandBatch.ConsumeResultSetAsync(Int32 startCommandIndex, RelationalDataReader reader, CancellationToken cancellationToken)
ClientConnectionId:c3f4970f-ec78-4b19-9378-d4c1f083bd3d
Error Number:515,State:2,Class:16
   --- End of inner exception stack trace ---
   at Microsoft.EntityFrameworkCore.Update.AffectedCountModificationCommandBatch.ConsumeResultSetAsync(Int32 startCommandIndex, RelationalDataReader reader, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.AffectedCountModificationCommandBatch.ConsumeAsync(RelationalDataReader reader, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.SqlServer.Update.Internal.SqlServerModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalDatabase.SaveChangesAsync(IList`1 entries, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(IList`1 entriesToSave, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(StateManager stateManager, Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.SqlServer.Storage.Internal.SqlServerExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.DbContext.SaveChangesAsync(Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
2025-07-30 10:53:50.753 +03:00 [INF] HTTP POST /SalesInvoice/Create responded 200 in 1146.6985 ms
2025-07-30 10:55:23.611 +03:00 [INF] Starting database seeding.
2025-07-30 10:55:25.076 +03:00 [INF] Admin user already exists. Seeding is not required.
2025-07-30 10:55:25.106 +03:00 [ERR] Hosting failed to start
System.IO.IOException: Failed to bind to address http://127.0.0.1:5298: address already in use.
 ---> Microsoft.AspNetCore.Connections.AddressInUseException: Address already in use
 ---> System.Net.Sockets.SocketException (48): Address already in use
   at System.Net.Sockets.Socket.DoBind(EndPoint endPointSnapshot, SocketAddress socketAddress)
   at System.Net.Sockets.Socket.Bind(EndPoint localEP)
   at Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets.SocketTransportOptions.CreateDefaultBoundListenSocket(EndPoint endpoint)
   at Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets.SocketConnectionListener.Bind()
   --- End of inner exception stack trace ---
   at Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets.SocketConnectionListener.Bind()
   at Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets.SocketTransportFactory.BindAsync(EndPoint endpoint, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.Infrastructure.TransportManager.BindAsync(EndPoint endPoint, ConnectionDelegate connectionDelegate, EndpointConfig endpointConfig, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.KestrelServerImpl.<>c__DisplayClass28_0`1.<<StartAsync>g__OnBind|0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.AddressBinder.BindEndpointAsync(ListenOptions endpoint, AddressBindContext context, CancellationToken cancellationToken)
   --- End of inner exception stack trace ---
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.AddressBinder.BindEndpointAsync(ListenOptions endpoint, AddressBindContext context, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.LocalhostListenOptions.BindAsync(AddressBindContext context, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.AddressBinder.AddressesStrategy.BindAsync(AddressBindContext context, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.AddressBinder.BindAsync(ListenOptions[] listenOptions, AddressBindContext context, Func`2 useHttps, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.KestrelServerImpl.BindAsync(CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.KestrelServerImpl.StartAsync[TContext](IHttpApplication`1 application, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Hosting.GenericWebHostService.StartAsync(CancellationToken cancellationToken)
   at Microsoft.Extensions.Hosting.Internal.Host.<StartAsync>b__15_1(IHostedService service, CancellationToken token)
   at Microsoft.Extensions.Hosting.Internal.Host.ForeachService[T](IEnumerable`1 services, CancellationToken token, Boolean concurrent, Boolean abortOnFirstException, List`1 exceptions, Func`3 operation)
2025-07-30 10:55:25.111 +03:00 [FTL] Application terminated unexpectedly
System.IO.IOException: Failed to bind to address http://127.0.0.1:5298: address already in use.
 ---> Microsoft.AspNetCore.Connections.AddressInUseException: Address already in use
 ---> System.Net.Sockets.SocketException (48): Address already in use
   at System.Net.Sockets.Socket.DoBind(EndPoint endPointSnapshot, SocketAddress socketAddress)
   at System.Net.Sockets.Socket.Bind(EndPoint localEP)
   at Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets.SocketTransportOptions.CreateDefaultBoundListenSocket(EndPoint endpoint)
   at Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets.SocketConnectionListener.Bind()
   --- End of inner exception stack trace ---
   at Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets.SocketConnectionListener.Bind()
   at Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets.SocketTransportFactory.BindAsync(EndPoint endpoint, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.Infrastructure.TransportManager.BindAsync(EndPoint endPoint, ConnectionDelegate connectionDelegate, EndpointConfig endpointConfig, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.KestrelServerImpl.<>c__DisplayClass28_0`1.<<StartAsync>g__OnBind|0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.AddressBinder.BindEndpointAsync(ListenOptions endpoint, AddressBindContext context, CancellationToken cancellationToken)
   --- End of inner exception stack trace ---
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.AddressBinder.BindEndpointAsync(ListenOptions endpoint, AddressBindContext context, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.LocalhostListenOptions.BindAsync(AddressBindContext context, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.AddressBinder.AddressesStrategy.BindAsync(AddressBindContext context, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.AddressBinder.BindAsync(ListenOptions[] listenOptions, AddressBindContext context, Func`2 useHttps, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.KestrelServerImpl.BindAsync(CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.KestrelServerImpl.StartAsync[TContext](IHttpApplication`1 application, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Hosting.GenericWebHostService.StartAsync(CancellationToken cancellationToken)
   at Microsoft.Extensions.Hosting.Internal.Host.<StartAsync>b__15_1(IHostedService service, CancellationToken token)
   at Microsoft.Extensions.Hosting.Internal.Host.ForeachService[T](IEnumerable`1 services, CancellationToken token, Boolean concurrent, Boolean abortOnFirstException, List`1 exceptions, Func`3 operation)
   at Microsoft.Extensions.Hosting.Internal.Host.StartAsync(CancellationToken cancellationToken)
   at Microsoft.Extensions.Hosting.HostingAbstractionsHostExtensions.RunAsync(IHost host, CancellationToken token)
   at Microsoft.Extensions.Hosting.HostingAbstractionsHostExtensions.RunAsync(IHost host, CancellationToken token)
   at Microsoft.Extensions.Hosting.HostingAbstractionsHostExtensions.Run(IHost host)
   at Program.<Main>$(String[] args) in /Users/<USER>/Source/nCarry/nCarry/nCarry.Web/Program.cs:line 82
2025-07-30 10:55:41.916 +03:00 [INF] Starting database seeding.
2025-07-30 10:55:43.422 +03:00 [INF] Admin user already exists. Seeding is not required.
2025-07-30 10:57:46.474 +03:00 [WRN] Failed to determine the https port for redirect.
2025-07-30 10:57:47.177 +03:00 [INF] HTTP GET /SalesInvoice/CreateFromOrder responded 200 in 703.4566 ms
2025-07-30 10:58:08.813 +03:00 [ERR] An exception occurred in the database while saving changes for context type 'nCarry.Web.Data.ApplicationDbContext'.
Microsoft.EntityFrameworkCore.DbUpdateException: An error occurred while saving the entity changes. See the inner exception for details.
 ---> Microsoft.Data.SqlClient.SqlException (0x80131904): Cannot insert the value NULL into column 'SortOrder', table 'nCarryDB.dbo.SalesInvoiceItem'; column does not allow nulls. UPDATE fails.
   at Microsoft.Data.SqlClient.SqlConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.SqlInternalConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.TdsParser.ThrowExceptionAndWarning(TdsParserStateObject stateObj, Boolean callerHasConnectionLock, Boolean asyncClose)
   at Microsoft.Data.SqlClient.TdsParser.TryRun(RunBehavior runBehavior, SqlCommand cmdHandler, SqlDataReader dataStream, BulkCopySimpleResultSet bulkCopyHandler, TdsParserStateObject stateObj, Boolean& dataReady)
   at Microsoft.Data.SqlClient.SqlDataReader.TryHasMoreRows(Boolean& moreRows)
   at Microsoft.Data.SqlClient.SqlDataReader.TryReadInternal(Boolean setTimeout, Boolean& more)
   at Microsoft.Data.SqlClient.SqlDataReader.ReadAsyncExecute(Task task, Object state)
   at Microsoft.Data.SqlClient.SqlDataReader.InvokeAsyncCall[T](SqlDataReaderBaseAsyncCallContext`1 context)
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Update.AffectedCountModificationCommandBatch.ConsumeResultSetAsync(Int32 startCommandIndex, RelationalDataReader reader, CancellationToken cancellationToken)
ClientConnectionId:0dcc6cdf-e47c-489b-b770-5b652e60fb9f
Error Number:515,State:2,Class:16
   --- End of inner exception stack trace ---
   at Microsoft.EntityFrameworkCore.Update.AffectedCountModificationCommandBatch.ConsumeResultSetAsync(Int32 startCommandIndex, RelationalDataReader reader, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.AffectedCountModificationCommandBatch.ConsumeAsync(RelationalDataReader reader, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.SqlServer.Update.Internal.SqlServerModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalDatabase.SaveChangesAsync(IList`1 entries, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(IList`1 entriesToSave, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(StateManager stateManager, Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.SqlServer.Storage.Internal.SqlServerExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.DbContext.SaveChangesAsync(Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
Microsoft.EntityFrameworkCore.DbUpdateException: An error occurred while saving the entity changes. See the inner exception for details.
 ---> Microsoft.Data.SqlClient.SqlException (0x80131904): Cannot insert the value NULL into column 'SortOrder', table 'nCarryDB.dbo.SalesInvoiceItem'; column does not allow nulls. UPDATE fails.
   at Microsoft.Data.SqlClient.SqlConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.SqlInternalConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.TdsParser.ThrowExceptionAndWarning(TdsParserStateObject stateObj, Boolean callerHasConnectionLock, Boolean asyncClose)
   at Microsoft.Data.SqlClient.TdsParser.TryRun(RunBehavior runBehavior, SqlCommand cmdHandler, SqlDataReader dataStream, BulkCopySimpleResultSet bulkCopyHandler, TdsParserStateObject stateObj, Boolean& dataReady)
   at Microsoft.Data.SqlClient.SqlDataReader.TryHasMoreRows(Boolean& moreRows)
   at Microsoft.Data.SqlClient.SqlDataReader.TryReadInternal(Boolean setTimeout, Boolean& more)
   at Microsoft.Data.SqlClient.SqlDataReader.ReadAsyncExecute(Task task, Object state)
   at Microsoft.Data.SqlClient.SqlDataReader.InvokeAsyncCall[T](SqlDataReaderBaseAsyncCallContext`1 context)
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Update.AffectedCountModificationCommandBatch.ConsumeResultSetAsync(Int32 startCommandIndex, RelationalDataReader reader, CancellationToken cancellationToken)
ClientConnectionId:0dcc6cdf-e47c-489b-b770-5b652e60fb9f
Error Number:515,State:2,Class:16
   --- End of inner exception stack trace ---
   at Microsoft.EntityFrameworkCore.Update.AffectedCountModificationCommandBatch.ConsumeResultSetAsync(Int32 startCommandIndex, RelationalDataReader reader, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.AffectedCountModificationCommandBatch.ConsumeAsync(RelationalDataReader reader, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.SqlServer.Update.Internal.SqlServerModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalDatabase.SaveChangesAsync(IList`1 entries, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(IList`1 entriesToSave, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(StateManager stateManager, Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.SqlServer.Storage.Internal.SqlServerExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.DbContext.SaveChangesAsync(Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
2025-07-30 10:58:08.902 +03:00 [INF] HTTP POST /SalesInvoice/Create responded 200 in 833.9163 ms
2025-07-30 10:58:55.124 +03:00 [INF] HTTP GET /SalesInvoice/CreateFromOrder responded 200 in 166.0483 ms
2025-07-30 10:59:02.419 +03:00 [ERR] An exception occurred in the database while saving changes for context type 'nCarry.Web.Data.ApplicationDbContext'.
Microsoft.EntityFrameworkCore.DbUpdateException: An error occurred while saving the entity changes. See the inner exception for details.
 ---> Microsoft.Data.SqlClient.SqlException (0x80131904): Cannot insert the value NULL into column 'SortOrder', table 'nCarryDB.dbo.SalesInvoiceItem'; column does not allow nulls. UPDATE fails.
   at Microsoft.Data.SqlClient.SqlConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.SqlInternalConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.TdsParser.ThrowExceptionAndWarning(TdsParserStateObject stateObj, Boolean callerHasConnectionLock, Boolean asyncClose)
   at Microsoft.Data.SqlClient.TdsParser.TryRun(RunBehavior runBehavior, SqlCommand cmdHandler, SqlDataReader dataStream, BulkCopySimpleResultSet bulkCopyHandler, TdsParserStateObject stateObj, Boolean& dataReady)
   at Microsoft.Data.SqlClient.SqlDataReader.TryHasMoreRows(Boolean& moreRows)
   at Microsoft.Data.SqlClient.SqlDataReader.TryReadInternal(Boolean setTimeout, Boolean& more)
   at Microsoft.Data.SqlClient.SqlDataReader.ReadAsyncExecute(Task task, Object state)
   at Microsoft.Data.SqlClient.SqlDataReader.InvokeAsyncCall[T](SqlDataReaderBaseAsyncCallContext`1 context)
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Update.AffectedCountModificationCommandBatch.ConsumeResultSetAsync(Int32 startCommandIndex, RelationalDataReader reader, CancellationToken cancellationToken)
ClientConnectionId:0dcc6cdf-e47c-489b-b770-5b652e60fb9f
Error Number:515,State:2,Class:16
   --- End of inner exception stack trace ---
   at Microsoft.EntityFrameworkCore.Update.AffectedCountModificationCommandBatch.ConsumeResultSetAsync(Int32 startCommandIndex, RelationalDataReader reader, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.AffectedCountModificationCommandBatch.ConsumeAsync(RelationalDataReader reader, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.SqlServer.Update.Internal.SqlServerModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalDatabase.SaveChangesAsync(IList`1 entries, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(IList`1 entriesToSave, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(StateManager stateManager, Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.SqlServer.Storage.Internal.SqlServerExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.DbContext.SaveChangesAsync(Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
Microsoft.EntityFrameworkCore.DbUpdateException: An error occurred while saving the entity changes. See the inner exception for details.
 ---> Microsoft.Data.SqlClient.SqlException (0x80131904): Cannot insert the value NULL into column 'SortOrder', table 'nCarryDB.dbo.SalesInvoiceItem'; column does not allow nulls. UPDATE fails.
   at Microsoft.Data.SqlClient.SqlConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.SqlInternalConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.TdsParser.ThrowExceptionAndWarning(TdsParserStateObject stateObj, Boolean callerHasConnectionLock, Boolean asyncClose)
   at Microsoft.Data.SqlClient.TdsParser.TryRun(RunBehavior runBehavior, SqlCommand cmdHandler, SqlDataReader dataStream, BulkCopySimpleResultSet bulkCopyHandler, TdsParserStateObject stateObj, Boolean& dataReady)
   at Microsoft.Data.SqlClient.SqlDataReader.TryHasMoreRows(Boolean& moreRows)
   at Microsoft.Data.SqlClient.SqlDataReader.TryReadInternal(Boolean setTimeout, Boolean& more)
   at Microsoft.Data.SqlClient.SqlDataReader.ReadAsyncExecute(Task task, Object state)
   at Microsoft.Data.SqlClient.SqlDataReader.InvokeAsyncCall[T](SqlDataReaderBaseAsyncCallContext`1 context)
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Update.AffectedCountModificationCommandBatch.ConsumeResultSetAsync(Int32 startCommandIndex, RelationalDataReader reader, CancellationToken cancellationToken)
ClientConnectionId:0dcc6cdf-e47c-489b-b770-5b652e60fb9f
Error Number:515,State:2,Class:16
   --- End of inner exception stack trace ---
   at Microsoft.EntityFrameworkCore.Update.AffectedCountModificationCommandBatch.ConsumeResultSetAsync(Int32 startCommandIndex, RelationalDataReader reader, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.AffectedCountModificationCommandBatch.ConsumeAsync(RelationalDataReader reader, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.SqlServer.Update.Internal.SqlServerModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalDatabase.SaveChangesAsync(IList`1 entries, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(IList`1 entriesToSave, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(StateManager stateManager, Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.SqlServer.Storage.Internal.SqlServerExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.DbContext.SaveChangesAsync(Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
2025-07-30 10:59:02.490 +03:00 [INF] HTTP POST /SalesInvoice/Create responded 200 in 574.8924 ms
2025-07-30 11:00:03.850 +03:00 [INF] HTTP GET /SalesInvoice/CreateFromOrder responded 200 in 191.0877 ms
2025-07-30 11:08:02.515 +03:00 [ERR] An exception occurred in the database while saving changes for context type 'nCarry.Web.Data.ApplicationDbContext'.
Microsoft.EntityFrameworkCore.DbUpdateException: An error occurred while saving the entity changes. See the inner exception for details.
 ---> Microsoft.Data.SqlClient.SqlException (0x80131904): Cannot insert the value NULL into column 'SortOrder', table 'nCarryDB.dbo.SalesInvoiceItem'; column does not allow nulls. UPDATE fails.
   at Microsoft.Data.SqlClient.SqlConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.SqlInternalConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.TdsParser.ThrowExceptionAndWarning(TdsParserStateObject stateObj, Boolean callerHasConnectionLock, Boolean asyncClose)
   at Microsoft.Data.SqlClient.TdsParser.TryRun(RunBehavior runBehavior, SqlCommand cmdHandler, SqlDataReader dataStream, BulkCopySimpleResultSet bulkCopyHandler, TdsParserStateObject stateObj, Boolean& dataReady)
   at Microsoft.Data.SqlClient.SqlDataReader.TryHasMoreRows(Boolean& moreRows)
   at Microsoft.Data.SqlClient.SqlDataReader.TryReadInternal(Boolean setTimeout, Boolean& more)
   at Microsoft.Data.SqlClient.SqlDataReader.ReadAsyncExecute(Task task, Object state)
   at Microsoft.Data.SqlClient.SqlDataReader.InvokeAsyncCall[T](SqlDataReaderBaseAsyncCallContext`1 context)
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Update.AffectedCountModificationCommandBatch.ConsumeResultSetAsync(Int32 startCommandIndex, RelationalDataReader reader, CancellationToken cancellationToken)
ClientConnectionId:55d1d4f8-5867-4908-9484-4d0dc961c171
Error Number:515,State:2,Class:16
   --- End of inner exception stack trace ---
   at Microsoft.EntityFrameworkCore.Update.AffectedCountModificationCommandBatch.ConsumeResultSetAsync(Int32 startCommandIndex, RelationalDataReader reader, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.AffectedCountModificationCommandBatch.ConsumeAsync(RelationalDataReader reader, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.SqlServer.Update.Internal.SqlServerModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalDatabase.SaveChangesAsync(IList`1 entries, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(IList`1 entriesToSave, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(StateManager stateManager, Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.SqlServer.Storage.Internal.SqlServerExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.DbContext.SaveChangesAsync(Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
Microsoft.EntityFrameworkCore.DbUpdateException: An error occurred while saving the entity changes. See the inner exception for details.
 ---> Microsoft.Data.SqlClient.SqlException (0x80131904): Cannot insert the value NULL into column 'SortOrder', table 'nCarryDB.dbo.SalesInvoiceItem'; column does not allow nulls. UPDATE fails.
   at Microsoft.Data.SqlClient.SqlConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.SqlInternalConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.TdsParser.ThrowExceptionAndWarning(TdsParserStateObject stateObj, Boolean callerHasConnectionLock, Boolean asyncClose)
   at Microsoft.Data.SqlClient.TdsParser.TryRun(RunBehavior runBehavior, SqlCommand cmdHandler, SqlDataReader dataStream, BulkCopySimpleResultSet bulkCopyHandler, TdsParserStateObject stateObj, Boolean& dataReady)
   at Microsoft.Data.SqlClient.SqlDataReader.TryHasMoreRows(Boolean& moreRows)
   at Microsoft.Data.SqlClient.SqlDataReader.TryReadInternal(Boolean setTimeout, Boolean& more)
   at Microsoft.Data.SqlClient.SqlDataReader.ReadAsyncExecute(Task task, Object state)
   at Microsoft.Data.SqlClient.SqlDataReader.InvokeAsyncCall[T](SqlDataReaderBaseAsyncCallContext`1 context)
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Update.AffectedCountModificationCommandBatch.ConsumeResultSetAsync(Int32 startCommandIndex, RelationalDataReader reader, CancellationToken cancellationToken)
ClientConnectionId:55d1d4f8-5867-4908-9484-4d0dc961c171
Error Number:515,State:2,Class:16
   --- End of inner exception stack trace ---
   at Microsoft.EntityFrameworkCore.Update.AffectedCountModificationCommandBatch.ConsumeResultSetAsync(Int32 startCommandIndex, RelationalDataReader reader, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.AffectedCountModificationCommandBatch.ConsumeAsync(RelationalDataReader reader, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.SqlServer.Update.Internal.SqlServerModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalDatabase.SaveChangesAsync(IList`1 entries, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(IList`1 entriesToSave, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(StateManager stateManager, Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.SqlServer.Storage.Internal.SqlServerExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.DbContext.SaveChangesAsync(Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
2025-07-30 11:08:02.606 +03:00 [INF] HTTP POST /SalesInvoice/Create responded 200 in 1254.1806 ms
2025-07-30 11:08:35.342 +03:00 [INF] Starting database seeding.
2025-07-30 11:08:37.811 +03:00 [INF] Admin user already exists. Seeding is not required.
2025-07-30 11:10:30.942 +03:00 [WRN] Failed to determine the https port for redirect.
2025-07-30 11:10:31.988 +03:00 [INF] HTTP GET /SalesInvoice/CreateFromOrder responded 200 in 1047.3077 ms
2025-07-30 11:10:55.457 +03:00 [ERR] An exception occurred in the database while saving changes for context type 'nCarry.Web.Data.ApplicationDbContext'.
Microsoft.EntityFrameworkCore.DbUpdateException: An error occurred while saving the entity changes. See the inner exception for details.
 ---> Microsoft.Data.SqlClient.SqlException (0x80131904): Cannot insert the value NULL into column 'SortOrder', table 'nCarryDB.dbo.SalesInvoiceItem'; column does not allow nulls. UPDATE fails.
   at Microsoft.Data.SqlClient.SqlConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.SqlInternalConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.TdsParser.ThrowExceptionAndWarning(TdsParserStateObject stateObj, Boolean callerHasConnectionLock, Boolean asyncClose)
   at Microsoft.Data.SqlClient.TdsParser.TryRun(RunBehavior runBehavior, SqlCommand cmdHandler, SqlDataReader dataStream, BulkCopySimpleResultSet bulkCopyHandler, TdsParserStateObject stateObj, Boolean& dataReady)
   at Microsoft.Data.SqlClient.SqlDataReader.TryHasMoreRows(Boolean& moreRows)
   at Microsoft.Data.SqlClient.SqlDataReader.TryReadInternal(Boolean setTimeout, Boolean& more)
   at Microsoft.Data.SqlClient.SqlDataReader.ReadAsyncExecute(Task task, Object state)
   at Microsoft.Data.SqlClient.SqlDataReader.InvokeAsyncCall[T](SqlDataReaderBaseAsyncCallContext`1 context)
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Update.AffectedCountModificationCommandBatch.ConsumeResultSetAsync(Int32 startCommandIndex, RelationalDataReader reader, CancellationToken cancellationToken)
ClientConnectionId:1fc61c4b-5de4-48f2-862c-4182ebb7f2f7
Error Number:515,State:2,Class:16
   --- End of inner exception stack trace ---
   at Microsoft.EntityFrameworkCore.Update.AffectedCountModificationCommandBatch.ConsumeResultSetAsync(Int32 startCommandIndex, RelationalDataReader reader, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.AffectedCountModificationCommandBatch.ConsumeAsync(RelationalDataReader reader, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.SqlServer.Update.Internal.SqlServerModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalDatabase.SaveChangesAsync(IList`1 entries, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(IList`1 entriesToSave, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(StateManager stateManager, Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.SqlServer.Storage.Internal.SqlServerExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.DbContext.SaveChangesAsync(Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
Microsoft.EntityFrameworkCore.DbUpdateException: An error occurred while saving the entity changes. See the inner exception for details.
 ---> Microsoft.Data.SqlClient.SqlException (0x80131904): Cannot insert the value NULL into column 'SortOrder', table 'nCarryDB.dbo.SalesInvoiceItem'; column does not allow nulls. UPDATE fails.
   at Microsoft.Data.SqlClient.SqlConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.SqlInternalConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.TdsParser.ThrowExceptionAndWarning(TdsParserStateObject stateObj, Boolean callerHasConnectionLock, Boolean asyncClose)
   at Microsoft.Data.SqlClient.TdsParser.TryRun(RunBehavior runBehavior, SqlCommand cmdHandler, SqlDataReader dataStream, BulkCopySimpleResultSet bulkCopyHandler, TdsParserStateObject stateObj, Boolean& dataReady)
   at Microsoft.Data.SqlClient.SqlDataReader.TryHasMoreRows(Boolean& moreRows)
   at Microsoft.Data.SqlClient.SqlDataReader.TryReadInternal(Boolean setTimeout, Boolean& more)
   at Microsoft.Data.SqlClient.SqlDataReader.ReadAsyncExecute(Task task, Object state)
   at Microsoft.Data.SqlClient.SqlDataReader.InvokeAsyncCall[T](SqlDataReaderBaseAsyncCallContext`1 context)
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Update.AffectedCountModificationCommandBatch.ConsumeResultSetAsync(Int32 startCommandIndex, RelationalDataReader reader, CancellationToken cancellationToken)
ClientConnectionId:1fc61c4b-5de4-48f2-862c-4182ebb7f2f7
Error Number:515,State:2,Class:16
   --- End of inner exception stack trace ---
   at Microsoft.EntityFrameworkCore.Update.AffectedCountModificationCommandBatch.ConsumeResultSetAsync(Int32 startCommandIndex, RelationalDataReader reader, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.AffectedCountModificationCommandBatch.ConsumeAsync(RelationalDataReader reader, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.SqlServer.Update.Internal.SqlServerModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalDatabase.SaveChangesAsync(IList`1 entries, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(IList`1 entriesToSave, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(StateManager stateManager, Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.SqlServer.Storage.Internal.SqlServerExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.DbContext.SaveChangesAsync(Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
2025-07-30 11:10:55.575 +03:00 [INF] HTTP POST /SalesInvoice/Create responded 200 in 1039.4474 ms
2025-07-30 11:11:38.187 +03:00 [INF] Starting database seeding.
2025-07-30 11:11:40.993 +03:00 [INF] Admin user already exists. Seeding is not required.
2025-07-30 11:13:35.688 +03:00 [WRN] Failed to determine the https port for redirect.
2025-07-30 11:13:36.707 +03:00 [INF] HTTP GET /SalesInvoice/CreateFromOrder responded 200 in 1020.3061 ms
2025-07-30 11:13:44.065 +03:00 [ERR] An exception occurred in the database while saving changes for context type 'nCarry.Web.Data.ApplicationDbContext'.
Microsoft.EntityFrameworkCore.DbUpdateException: An error occurred while saving the entity changes. See the inner exception for details.
 ---> Microsoft.Data.SqlClient.SqlException (0x80131904): Cannot insert the value NULL into column 'SortOrder', table 'nCarryDB.dbo.SalesInvoiceItem'; column does not allow nulls. UPDATE fails.
   at Microsoft.Data.SqlClient.SqlConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.SqlInternalConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.TdsParser.ThrowExceptionAndWarning(TdsParserStateObject stateObj, Boolean callerHasConnectionLock, Boolean asyncClose)
   at Microsoft.Data.SqlClient.TdsParser.TryRun(RunBehavior runBehavior, SqlCommand cmdHandler, SqlDataReader dataStream, BulkCopySimpleResultSet bulkCopyHandler, TdsParserStateObject stateObj, Boolean& dataReady)
   at Microsoft.Data.SqlClient.SqlDataReader.TryHasMoreRows(Boolean& moreRows)
   at Microsoft.Data.SqlClient.SqlDataReader.TryReadInternal(Boolean setTimeout, Boolean& more)
   at Microsoft.Data.SqlClient.SqlDataReader.ReadAsyncExecute(Task task, Object state)
   at Microsoft.Data.SqlClient.SqlDataReader.InvokeAsyncCall[T](SqlDataReaderBaseAsyncCallContext`1 context)
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Update.AffectedCountModificationCommandBatch.ConsumeResultSetAsync(Int32 startCommandIndex, RelationalDataReader reader, CancellationToken cancellationToken)
ClientConnectionId:ea3b91e7-f435-4dcb-b7d3-83f2bd0b1371
Error Number:515,State:2,Class:16
   --- End of inner exception stack trace ---
   at Microsoft.EntityFrameworkCore.Update.AffectedCountModificationCommandBatch.ConsumeResultSetAsync(Int32 startCommandIndex, RelationalDataReader reader, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.AffectedCountModificationCommandBatch.ConsumeAsync(RelationalDataReader reader, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.SqlServer.Update.Internal.SqlServerModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalDatabase.SaveChangesAsync(IList`1 entries, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(IList`1 entriesToSave, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(StateManager stateManager, Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.SqlServer.Storage.Internal.SqlServerExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.DbContext.SaveChangesAsync(Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
Microsoft.EntityFrameworkCore.DbUpdateException: An error occurred while saving the entity changes. See the inner exception for details.
 ---> Microsoft.Data.SqlClient.SqlException (0x80131904): Cannot insert the value NULL into column 'SortOrder', table 'nCarryDB.dbo.SalesInvoiceItem'; column does not allow nulls. UPDATE fails.
   at Microsoft.Data.SqlClient.SqlConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.SqlInternalConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.TdsParser.ThrowExceptionAndWarning(TdsParserStateObject stateObj, Boolean callerHasConnectionLock, Boolean asyncClose)
   at Microsoft.Data.SqlClient.TdsParser.TryRun(RunBehavior runBehavior, SqlCommand cmdHandler, SqlDataReader dataStream, BulkCopySimpleResultSet bulkCopyHandler, TdsParserStateObject stateObj, Boolean& dataReady)
   at Microsoft.Data.SqlClient.SqlDataReader.TryHasMoreRows(Boolean& moreRows)
   at Microsoft.Data.SqlClient.SqlDataReader.TryReadInternal(Boolean setTimeout, Boolean& more)
   at Microsoft.Data.SqlClient.SqlDataReader.ReadAsyncExecute(Task task, Object state)
   at Microsoft.Data.SqlClient.SqlDataReader.InvokeAsyncCall[T](SqlDataReaderBaseAsyncCallContext`1 context)
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Update.AffectedCountModificationCommandBatch.ConsumeResultSetAsync(Int32 startCommandIndex, RelationalDataReader reader, CancellationToken cancellationToken)
ClientConnectionId:ea3b91e7-f435-4dcb-b7d3-83f2bd0b1371
Error Number:515,State:2,Class:16
   --- End of inner exception stack trace ---
   at Microsoft.EntityFrameworkCore.Update.AffectedCountModificationCommandBatch.ConsumeResultSetAsync(Int32 startCommandIndex, RelationalDataReader reader, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.AffectedCountModificationCommandBatch.ConsumeAsync(RelationalDataReader reader, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.SqlServer.Update.Internal.SqlServerModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalDatabase.SaveChangesAsync(IList`1 entries, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(IList`1 entriesToSave, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(StateManager stateManager, Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.SqlServer.Storage.Internal.SqlServerExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.DbContext.SaveChangesAsync(Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
2025-07-30 11:13:44.168 +03:00 [INF] HTTP POST /SalesInvoice/Create responded 200 in 1025.5561 ms
2025-07-30 11:38:09.354 +03:00 [INF] Starting database seeding.
2025-07-30 11:38:11.375 +03:00 [INF] Admin user already exists. Seeding is not required.
2025-07-30 11:38:23.881 +03:00 [WRN] Failed to determine the https port for redirect.
2025-07-30 11:38:23.922 +03:00 [INF] HTTP GET / responded 302 in 43.0791 ms
2025-07-30 11:38:28.964 +03:00 [INF] HTTP GET /Account/Login responded 200 in 136.7460 ms
2025-07-30 11:38:28.987 +03:00 [INF] HTTP GET /lib/jquery-validation/dist/jquery.validate.min.js responded 200 in 13.8734 ms
2025-07-30 11:38:28.987 +03:00 [INF] HTTP GET /lib/jquery-validation-unobtrusive/jquery.validate.unobtrusive.min.js responded 200 in 10.7344 ms
2025-07-30 11:38:28.987 +03:00 [INF] HTTP GET /css/site.css responded 200 in 14.4038 ms
2025-07-30 11:38:28.988 +03:00 [INF] HTTP GET /lib/bootstrap/dist/js/bootstrap.bundle.min.js responded 200 in 14.1278 ms
2025-07-30 11:38:28.988 +03:00 [INF] HTTP GET /lib/jquery/dist/jquery.min.js responded 200 in 15.9762 ms
2025-07-30 11:38:28.989 +03:00 [INF] HTTP GET /lib/bootstrap/dist/css/bootstrap.min.css responded 200 in 17.1235 ms
2025-07-30 11:38:29.027 +03:00 [INF] HTTP GET /favicon.ico responded 200 in 0.6243 ms
2025-07-30 11:38:46.257 +03:00 [INF] HTTP POST /Account/Login responded 302 in 492.7639 ms
2025-07-30 11:38:47.944 +03:00 [INF] HTTP GET /Dashboard responded 200 in 1684.6538 ms
2025-07-30 11:38:47.950 +03:00 [INF] HTTP GET /js/site.js responded 200 in 0.7026 ms
2025-07-30 11:38:47.950 +03:00 [INF] HTTP GET /nCarry.Web.styles.css responded 200 in 0.6413 ms
2025-07-30 11:38:48.086 +03:00 [INF] HTTP GET /Dashboard/GetChartData responded 200 in 101.6716 ms
2025-07-30 11:38:54.080 +03:00 [INF] HTTP GET /ProductImport responded 200 in 8.4831 ms
2025-07-30 11:39:06.958 +03:00 [INF] HTTP POST /ProductImport/ImportFrozenSeafood responded 200 in 6452.8928 ms
2025-07-30 11:39:27.852 +03:00 [INF] HTTP GET /PurchaseOrder/Create responded 200 in 721.4238 ms
2025-07-30 11:41:17.353 +03:00 [INF] HTTP POST /PurchaseOrder/Create responded 302 in 1064.5571 ms
2025-07-30 11:41:17.544 +03:00 [INF] HTTP GET /PurchaseOrder/Details/8 responded 200 in 188.1081 ms
2025-07-30 11:41:35.455 +03:00 [INF] HTTP GET /GoodsReceipt/Create responded 200 in 379.7902 ms
2025-07-30 11:42:15.092 +03:00 [INF] HTTP POST /Account/Logout responded 302 in 4.3461 ms
2025-07-30 11:42:15.096 +03:00 [INF] HTTP GET /Account/Login responded 200 in 1.8988 ms
2025-07-30 11:42:19.979 +03:00 [INF] HTTP GET /Account/Login responded 200 in 2.4552 ms
2025-07-30 11:42:38.453 +03:00 [INF] HTTP POST /Account/Login responded 302 in 203.2517 ms
2025-07-30 11:42:39.645 +03:00 [INF] HTTP GET /Dashboard responded 200 in 1189.3441 ms
2025-07-30 11:42:39.764 +03:00 [INF] HTTP GET /Dashboard/GetChartData responded 200 in 69.4535 ms
2025-07-30 11:42:46.711 +03:00 [INF] HTTP GET /Inventory responded 200 in 311.4684 ms
2025-07-30 11:42:55.200 +03:00 [INF] HTTP GET /GoodsReceipt responded 200 in 141.1768 ms
2025-07-30 11:43:02.392 +03:00 [INF] HTTP GET /GoodsReceipt/Create responded 200 in 208.8370 ms
2025-07-30 11:43:11.243 +03:00 [INF] HTTP GET /GoodsReceipt/Create responded 200 in 314.2738 ms
2025-07-30 11:43:51.429 +03:00 [INF] HTTP POST /GoodsReceipt/Create responded 302 in 1208.5030 ms
2025-07-30 11:43:51.589 +03:00 [INF] HTTP GET /GoodsReceipt/Details/15 responded 200 in 157.8057 ms
2025-07-30 11:44:14.064 +03:00 [INF] HTTP GET /Inventory responded 200 in 241.3982 ms
2025-07-30 11:44:23.453 +03:00 [INF] HTTP GET /SalesOrder/Create responded 200 in 278.8572 ms
2025-07-30 11:44:56.290 +03:00 [INF] HTTP POST /SalesOrder/Create responded 302 in 168.5777 ms
2025-07-30 11:44:56.395 +03:00 [INF] HTTP GET /SalesOrder responded 200 in 104.0272 ms
2025-07-30 11:45:05.388 +03:00 [INF] HTTP GET /SalesOrder/Details/31 responded 200 in 148.2875 ms
2025-07-30 11:46:22.061 +03:00 [INF] Starting database seeding.
2025-07-30 11:46:24.058 +03:00 [INF] Admin user already exists. Seeding is not required.
2025-07-30 11:48:45.001 +03:00 [INF] Starting database seeding.
2025-07-30 11:48:46.979 +03:00 [INF] Admin user already exists. Seeding is not required.
2025-07-30 11:48:51.834 +03:00 [WRN] Failed to determine the https port for redirect.
2025-07-30 11:48:51.874 +03:00 [INF] HTTP GET / responded 302 in 43.0868 ms
2025-07-30 11:48:55.207 +03:00 [INF] HTTP GET /Account/Login responded 200 in 140.5793 ms
2025-07-30 11:49:08.411 +03:00 [INF] HTTP POST /Account/Login responded 302 in 482.3079 ms
2025-07-30 11:49:10.020 +03:00 [INF] HTTP GET /Dashboard responded 200 in 1606.4573 ms
2025-07-30 11:49:10.031 +03:00 [INF] HTTP GET /nCarry.Web.styles.css responded 304 in 4.9167 ms
2025-07-30 11:49:10.153 +03:00 [INF] HTTP GET /Dashboard/GetChartData responded 200 in 91.5310 ms
2025-07-30 11:49:17.174 +03:00 [INF] HTTP GET /SalesOrder/Details/31 responded 200 in 180.8395 ms
2025-07-30 11:49:23.243 +03:00 [INF] HTTP GET /SalesOrder/Edit/31 responded 200 in 502.8382 ms
2025-07-30 11:53:16.517 +03:00 [INF] HTTP GET /SalesOrder/Edit/31 responded 200 in 353.6095 ms
2025-07-30 11:53:54.305 +03:00 [INF] Starting database seeding.
2025-07-30 11:53:56.297 +03:00 [INF] Admin user already exists. Seeding is not required.
2025-07-30 11:54:01.702 +03:00 [WRN] Failed to determine the https port for redirect.
2025-07-30 11:54:01.745 +03:00 [INF] HTTP GET / responded 302 in 46.0600 ms
2025-07-30 11:54:06.273 +03:00 [INF] HTTP GET /Account/Login responded 200 in 131.2093 ms
2025-07-30 11:54:20.877 +03:00 [INF] HTTP POST /Account/Login responded 302 in 476.8547 ms
2025-07-30 11:54:22.507 +03:00 [INF] HTTP GET /Dashboard responded 200 in 1627.8405 ms
2025-07-30 11:54:22.623 +03:00 [INF] HTTP GET /Dashboard/GetChartData responded 200 in 92.2922 ms
2025-07-30 11:54:27.475 +03:00 [ERR] HTTP GET /SalesOrder/Edit/31 responded 500 in 9.1589 ms
System.InvalidOperationException: The expression 's.Items' is invalid inside an 'Include' operation, since it does not represent a property access: 't => t.MyProperty'. To target navigations declared on derived types, use casting ('t => ((Derived)t).MyProperty') or the 'as' operator ('t => (t as Derived).MyProperty'). Collection navigation access can be filtered by composing Where, OrderBy(Descending), ThenBy(Descending), Skip or Take operations. For more information on including related data, see https://go.microsoft.com/fwlink/?LinkID=746393.
   at Microsoft.EntityFrameworkCore.Query.Internal.NavigationExpandingExpressionVisitor.PopulateIncludeTree(IncludeTreeNode includeTreeNode, Expression expression, Boolean setLoaded)
   at Microsoft.EntityFrameworkCore.Query.Internal.NavigationExpandingExpressionVisitor.ProcessInclude(NavigationExpansionExpression source, Expression expression, Boolean thenInclude, Boolean setLoaded)
   at Microsoft.EntityFrameworkCore.Query.Internal.NavigationExpandingExpressionVisitor.VisitMethodCall(MethodCallExpression methodCallExpression)
   at Microsoft.EntityFrameworkCore.Query.Internal.NavigationExpandingExpressionVisitor.VisitMethodCall(MethodCallExpression methodCallExpression)
   at Microsoft.EntityFrameworkCore.Query.Internal.NavigationExpandingExpressionVisitor.VisitMethodCall(MethodCallExpression methodCallExpression)
   at Microsoft.EntityFrameworkCore.Query.Internal.NavigationExpandingExpressionVisitor.VisitMethodCall(MethodCallExpression methodCallExpression)
   at Microsoft.EntityFrameworkCore.Query.Internal.NavigationExpandingExpressionVisitor.VisitMethodCall(MethodCallExpression methodCallExpression)
   at Microsoft.EntityFrameworkCore.Query.Internal.NavigationExpandingExpressionVisitor.Expand(Expression query)
   at Microsoft.EntityFrameworkCore.Query.QueryTranslationPreprocessor.Process(Expression query)
   at Microsoft.EntityFrameworkCore.Query.QueryCompilationContext.CreateQueryExecutorExpression[TResult](Expression query)
   at Microsoft.EntityFrameworkCore.Query.QueryCompilationContext.CreateQueryExecutor[TResult](Expression query)
   at Microsoft.EntityFrameworkCore.Storage.Database.CompileQuery[TResult](Expression query, Boolean async)
   at Microsoft.EntityFrameworkCore.Query.Internal.QueryCompiler.CompileQueryCore[TResult](IDatabase database, Expression query, IModel model, Boolean async)
   at Microsoft.EntityFrameworkCore.Query.Internal.QueryCompiler.<>c__DisplayClass11_0`1.<ExecuteCore>b__0()
   at Microsoft.EntityFrameworkCore.Query.Internal.CompiledQueryCache.GetOrAddQuery[TResult](Object cacheKey, Func`1 compiler)
   at Microsoft.EntityFrameworkCore.Query.Internal.QueryCompiler.ExecuteCore[TResult](Expression query, Boolean async, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.QueryCompiler.ExecuteAsync[TResult](Expression query, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.EntityQueryProvider.ExecuteAsync[TResult](Expression expression, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ExecuteAsync[TSource,TResult](MethodInfo operatorMethodInfo, IQueryable`1 source, Expression expression, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ExecuteAsync[TSource,TResult](MethodInfo operatorMethodInfo, IQueryable`1 source, LambdaExpression expression, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.FirstOrDefaultAsync[TSource](IQueryable`1 source, Expression`1 predicate, CancellationToken cancellationToken)
   at nCarry.Web.Controllers.SalesOrderController.Edit(Nullable`1 id) in /Users/<USER>/Source/nCarry/nCarry/nCarry.Web/Controllers/SalesOrderController.cs:line 125
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.TaskOfIActionResultExecutor.Execute(ActionContext actionContext, IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeActionMethodAsync>g__Awaited|12_0(ControllerActionInvoker invoker, ValueTask`1 actionResultValueTask)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeInnerFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextResourceFilter>g__Awaited|25_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.Rethrow(ResourceExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.InvokeFilterPipelineAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Session.SessionMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Session.SessionMiddleware.Invoke(HttpContext context)
   at Serilog.AspNetCore.RequestLoggingMiddleware.Invoke(HttpContext httpContext)
2025-07-30 11:54:27.482 +03:00 [ERR] An unhandled exception has occurred while executing the request.
System.InvalidOperationException: The expression 's.Items' is invalid inside an 'Include' operation, since it does not represent a property access: 't => t.MyProperty'. To target navigations declared on derived types, use casting ('t => ((Derived)t).MyProperty') or the 'as' operator ('t => (t as Derived).MyProperty'). Collection navigation access can be filtered by composing Where, OrderBy(Descending), ThenBy(Descending), Skip or Take operations. For more information on including related data, see https://go.microsoft.com/fwlink/?LinkID=746393.
   at Microsoft.EntityFrameworkCore.Query.Internal.NavigationExpandingExpressionVisitor.PopulateIncludeTree(IncludeTreeNode includeTreeNode, Expression expression, Boolean setLoaded)
   at Microsoft.EntityFrameworkCore.Query.Internal.NavigationExpandingExpressionVisitor.ProcessInclude(NavigationExpansionExpression source, Expression expression, Boolean thenInclude, Boolean setLoaded)
   at Microsoft.EntityFrameworkCore.Query.Internal.NavigationExpandingExpressionVisitor.VisitMethodCall(MethodCallExpression methodCallExpression)
   at Microsoft.EntityFrameworkCore.Query.Internal.NavigationExpandingExpressionVisitor.VisitMethodCall(MethodCallExpression methodCallExpression)
   at Microsoft.EntityFrameworkCore.Query.Internal.NavigationExpandingExpressionVisitor.VisitMethodCall(MethodCallExpression methodCallExpression)
   at Microsoft.EntityFrameworkCore.Query.Internal.NavigationExpandingExpressionVisitor.VisitMethodCall(MethodCallExpression methodCallExpression)
   at Microsoft.EntityFrameworkCore.Query.Internal.NavigationExpandingExpressionVisitor.VisitMethodCall(MethodCallExpression methodCallExpression)
   at Microsoft.EntityFrameworkCore.Query.Internal.NavigationExpandingExpressionVisitor.Expand(Expression query)
   at Microsoft.EntityFrameworkCore.Query.QueryTranslationPreprocessor.Process(Expression query)
   at Microsoft.EntityFrameworkCore.Query.QueryCompilationContext.CreateQueryExecutorExpression[TResult](Expression query)
   at Microsoft.EntityFrameworkCore.Query.QueryCompilationContext.CreateQueryExecutor[TResult](Expression query)
   at Microsoft.EntityFrameworkCore.Storage.Database.CompileQuery[TResult](Expression query, Boolean async)
   at Microsoft.EntityFrameworkCore.Query.Internal.QueryCompiler.CompileQueryCore[TResult](IDatabase database, Expression query, IModel model, Boolean async)
   at Microsoft.EntityFrameworkCore.Query.Internal.QueryCompiler.<>c__DisplayClass11_0`1.<ExecuteCore>b__0()
   at Microsoft.EntityFrameworkCore.Query.Internal.CompiledQueryCache.GetOrAddQuery[TResult](Object cacheKey, Func`1 compiler)
   at Microsoft.EntityFrameworkCore.Query.Internal.QueryCompiler.ExecuteCore[TResult](Expression query, Boolean async, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.QueryCompiler.ExecuteAsync[TResult](Expression query, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.EntityQueryProvider.ExecuteAsync[TResult](Expression expression, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ExecuteAsync[TSource,TResult](MethodInfo operatorMethodInfo, IQueryable`1 source, Expression expression, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ExecuteAsync[TSource,TResult](MethodInfo operatorMethodInfo, IQueryable`1 source, LambdaExpression expression, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.FirstOrDefaultAsync[TSource](IQueryable`1 source, Expression`1 predicate, CancellationToken cancellationToken)
   at nCarry.Web.Controllers.SalesOrderController.Edit(Nullable`1 id) in /Users/<USER>/Source/nCarry/nCarry/nCarry.Web/Controllers/SalesOrderController.cs:line 125
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.TaskOfIActionResultExecutor.Execute(ActionContext actionContext, IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeActionMethodAsync>g__Awaited|12_0(ControllerActionInvoker invoker, ValueTask`1 actionResultValueTask)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeInnerFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextResourceFilter>g__Awaited|25_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.Rethrow(ResourceExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.InvokeFilterPipelineAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Session.SessionMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Session.SessionMiddleware.Invoke(HttpContext context)
   at Serilog.AspNetCore.RequestLoggingMiddleware.Invoke(HttpContext httpContext)
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
2025-07-30 11:55:19.123 +03:00 [INF] Starting database seeding.
2025-07-30 11:55:21.170 +03:00 [INF] Admin user already exists. Seeding is not required.
2025-07-30 11:55:26.323 +03:00 [WRN] Failed to determine the https port for redirect.
2025-07-30 11:55:26.365 +03:00 [INF] HTTP GET / responded 302 in 44.4834 ms
2025-07-30 11:55:32.697 +03:00 [INF] HTTP GET /SalesOrder/Edit/31 responded 200 in 1347.9924 ms
2025-07-30 11:56:25.420 +03:00 [INF] HTTP GET / responded 302 in 3.3384 ms
2025-07-30 11:56:26.969 +03:00 [INF] HTTP GET /Dashboard responded 200 in 1547.1182 ms
2025-07-30 11:56:27.083 +03:00 [INF] HTTP GET /Dashboard/GetChartData responded 200 in 95.7886 ms
2025-07-30 11:56:29.235 +03:00 [INF] HTTP GET /Dashboard responded 200 in 1173.7434 ms
2025-07-30 11:56:29.324 +03:00 [INF] HTTP GET /Dashboard/GetChartData responded 200 in 69.8181 ms
2025-07-30 11:56:35.090 +03:00 [INF] HTTP GET /Product responded 200 in 201.9439 ms
2025-07-30 11:56:40.121 +03:00 [INF] HTTP GET /Product/Create responded 200 in 475.7103 ms
2025-07-30 11:56:43.844 +03:00 [INF] HTTP GET /Product responded 200 in 135.3740 ms
2025-07-30 11:56:50.926 +03:00 [INF] HTTP GET /SalesOrder responded 200 in 113.3956 ms
2025-07-30 11:56:54.651 +03:00 [INF] HTTP GET /SalesOrder/Create responded 200 in 303.2810 ms
2025-07-30 11:57:02.134 +03:00 [INF] HTTP GET /SalesOrder responded 200 in 90.6484 ms
2025-07-30 11:57:03.599 +03:00 [INF] HTTP GET /SalesOrder/Details/31 responded 200 in 150.8991 ms
2025-07-30 11:57:09.124 +03:00 [INF] HTTP GET /SalesOrder/Edit/31 responded 200 in 528.7999 ms
2025-07-30 11:59:05.522 +03:00 [INF] Starting database seeding.
2025-07-30 11:59:07.537 +03:00 [INF] Admin user already exists. Seeding is not required.
2025-07-30 11:59:12.699 +03:00 [WRN] Failed to determine the https port for redirect.
2025-07-30 11:59:12.742 +03:00 [INF] HTTP GET / responded 302 in 45.7763 ms
2025-07-30 11:59:19.168 +03:00 [INF] HTTP GET /SalesOrder/Edit/31 responded 200 in 1283.0168 ms
2025-07-30 12:00:00.586 +03:00 [INF] HTTP POST /SalesOrder/Edit/31 responded 302 in 304.5653 ms
2025-07-30 12:00:00.707 +03:00 [INF] HTTP GET /SalesOrder responded 200 in 119.5979 ms
2025-07-30 12:00:10.102 +03:00 [INF] HTTP GET /SalesOrder/Details/31 responded 200 in 146.6836 ms
2025-07-30 12:04:58.690 +03:00 [INF] Starting database seeding.
2025-07-30 12:05:01.232 +03:00 [INF] Admin user already exists. Seeding is not required.
2025-07-30 12:07:12.252 +03:00 [INF] Starting database seeding.
2025-07-30 12:07:14.256 +03:00 [INF] Admin user already exists. Seeding is not required.
2025-07-30 12:09:26.429 +03:00 [INF] Starting database seeding.
2025-07-30 12:09:28.774 +03:00 [INF] Admin user already exists. Seeding is not required.
2025-07-30 12:11:36.871 +03:00 [WRN] Failed to determine the https port for redirect.
2025-07-30 12:11:36.918 +03:00 [INF] HTTP GET / responded 302 in 50.3433 ms
2025-07-30 12:11:38.784 +03:00 [INF] HTTP GET /Dashboard responded 200 in 1859.8686 ms
2025-07-30 12:11:38.986 +03:00 [INF] HTTP GET /Dashboard/GetChartData responded 200 in 154.1833 ms
2025-07-30 12:13:24.711 +03:00 [INF] HTTP GET /SalesOrder responded 200 in 132.3165 ms
2025-07-30 12:13:32.934 +03:00 [INF] HTTP GET /SalesOrder/Edit/31 responded 200 in 858.4495 ms
2025-07-30 15:14:10.055 +03:00 [INF] HTTP POST /SalesOrder/Edit/31 responded 302 in 1323.3478 ms
2025-07-30 15:14:10.213 +03:00 [INF] HTTP GET /SalesOrder responded 200 in 154.2813 ms
2025-07-30 15:14:10.225 +03:00 [INF] HTTP GET /nCarry.Web.styles.css responded 304 in 5.7976 ms
2025-07-30 15:15:38.442 +03:00 [INF] HTTP GET /SalesInvoice responded 200 in 195.5319 ms
2025-07-30 15:15:49.500 +03:00 [INF] HTTP GET /SalesInvoice/Edit/2 responded 200 in 351.5103 ms
2025-07-30 15:15:57.738 +03:00 [INF] HTTP GET /SalesInvoice responded 200 in 137.3165 ms
2025-07-30 15:16:05.884 +03:00 [INF] HTTP GET /SalesInvoice/Delete/2 responded 200 in 222.4435 ms
2025-07-30 16:11:51.570 +03:00 [INF] HTTP GET / responded 302 in 2.8587 ms
2025-07-30 16:11:51.602 +03:00 [INF] HTTP GET /Account/Login responded 200 in 27.5020 ms
2025-07-30 16:12:15.857 +03:00 [INF] HTTP POST /Account/Login responded 302 in 922.1976 ms
2025-07-30 16:12:17.737 +03:00 [INF] HTTP GET /Dashboard responded 200 in 1877.4570 ms
2025-07-30 16:12:18.039 +03:00 [INF] HTTP GET /Dashboard/GetChartData responded 200 in 249.3945 ms
2025-07-30 16:12:37.453 +03:00 [INF] HTTP GET /Product responded 200 in 239.2175 ms
2025-07-30 17:53:09.154 +03:00 [INF] HTTP GET /Product responded 200 in 794.3488 ms
2025-07-30 17:53:09.164 +03:00 [INF] HTTP GET /nCarry.Web.styles.css responded 304 in 1.3345 ms
2025-07-30 17:58:30.125 +03:00 [INF] HTTP GET /Product responded 200 in 162.8958 ms
2025-07-30 17:58:30.715 +03:00 [INF] HTTP GET /Product responded 200 in 199.9766 ms
2025-07-30 18:00:19.883 +03:00 [INF] HTTP GET /Product responded 200 in 286.6730 ms
2025-07-30 18:00:36.278 +03:00 [INF] HTTP GET /Product responded 200 in 139.2186 ms
2025-07-30 18:00:37.228 +03:00 [INF] HTTP GET /Product responded 200 in 147.1927 ms
2025-07-30 18:00:45.949 +03:00 [INF] HTTP GET /Shipment responded 200 in 1166.2900 ms
2025-07-30 18:00:53.068 +03:00 [INF] HTTP GET /SalesInvoice responded 200 in 163.9834 ms
2025-07-30 18:01:03.716 +03:00 [INF] HTTP GET / responded 302 in 0.3109 ms
2025-07-30 18:01:06.635 +03:00 [INF] HTTP GET /Shipment responded 200 in 350.4015 ms
2025-07-30 18:01:09.287 +03:00 [INF] HTTP GET /Shipment/Create responded 200 in 921.7835 ms
2025-07-30 18:01:10.513 +03:00 [INF] HTTP GET /Shipment/Create responded 200 in 1499.1060 ms
2025-07-30 18:01:11.174 +03:00 [INF] HTTP GET /Shipment/Create responded 200 in 1054.7975 ms
2025-07-30 18:01:11.504 +03:00 [INF] HTTP GET /Shipment/Create responded 200 in 869.8928 ms
2025-07-30 18:01:12.103 +03:00 [INF] HTTP GET /Shipment/Create responded 200 in 1272.7327 ms
2025-07-30 18:01:12.103 +03:00 [INF] HTTP GET /Shipment/Create responded 200 in 1115.2084 ms
2025-07-30 18:01:16.422 +03:00 [INF] HTTP GET /Shipment responded 200 in 282.5625 ms
2025-07-30 18:01:46.699 +03:00 [INF] HTTP GET /Shipment responded 200 in 475.0283 ms
2025-07-30 18:01:50.186 +03:00 [INF] HTTP GET /Shipment responded 200 in 316.7670 ms
2025-07-30 18:01:52.166 +03:00 [INF] HTTP GET /Shipment responded 200 in 343.9534 ms
2025-07-30 18:08:45.248 +03:00 [INF] HTTP GET /Shipment responded 200 in 4534.7268 ms
2025-07-30 18:08:48.149 +03:00 [INF] HTTP GET / responded 302 in 0.4747 ms
2025-07-30 18:08:57.094 +03:00 [INF] HTTP GET /Dashboard responded 200 in 8942.7992 ms
2025-07-30 18:09:04.511 +03:00 [INF] HTTP GET /Dashboard responded 200 in 6296.8435 ms
2025-07-30 18:09:04.665 +03:00 [INF] HTTP GET /Dashboard/GetChartData responded 200 in 116.3195 ms
2025-07-30 18:09:05.413 +03:00 [INF] HTTP GET /Dashboard responded 200 in 8616.5863 ms
2025-07-30 18:09:57.293 +03:00 [INF] HTTP GET /Dashboard responded 200 in 9998.3626 ms
2025-07-30 18:09:57.573 +03:00 [INF] HTTP GET /Dashboard/GetChartData responded 200 in 245.5346 ms
2025-07-30 19:30:28.897 +03:00 [INF] HTTP GET / responded 302 in 2.5805 ms
2025-07-30 19:30:31.625 +03:00 [INF] HTTP GET /Dashboard responded 200 in 2722.1464 ms
2025-07-30 19:30:31.849 +03:00 [INF] HTTP GET /Dashboard/GetChartData responded 200 in 155.0716 ms
2025-07-30 19:30:52.302 +03:00 [INF] HTTP GET /Dashboard responded 200 in 2149.0840 ms
2025-07-30 19:30:52.449 +03:00 [INF] HTTP GET /Dashboard/GetChartData responded 200 in 116.6198 ms
2025-07-30 19:39:57.658 +03:00 [INF] HTTP GET /Dashboard responded 302 in 5.3590 ms
2025-07-30 19:39:57.667 +03:00 [INF] HTTP GET /Account/Login responded 200 in 6.6306 ms
2025-07-30 19:40:25.385 +03:00 [INF] HTTP POST /Account/Login responded 302 in 1239.7056 ms
2025-07-30 19:40:27.355 +03:00 [INF] HTTP GET /Dashboard responded 200 in 1964.7084 ms
2025-07-30 19:40:27.552 +03:00 [INF] HTTP GET /Dashboard/GetChartData responded 200 in 142.3892 ms
2025-07-30 19:43:52.723 +03:00 [INF] HTTP GET / responded 302 in 2.3244 ms
2025-07-30 19:43:52.733 +03:00 [INF] HTTP GET /Account/Login responded 200 in 5.2012 ms
2025-07-30 19:43:52.888 +03:00 [INF] HTTP GET /css/site.css responded 200 in 25.7642 ms
2025-07-30 19:43:52.891 +03:00 [INF] HTTP GET /lib/bootstrap/dist/js/bootstrap.bundle.min.js responded 200 in 95.2700 ms
2025-07-30 19:43:52.891 +03:00 [INF] HTTP GET /lib/jquery-validation-unobtrusive/jquery.validate.unobtrusive.min.js responded 200 in 28.6498 ms
2025-07-30 19:43:52.910 +03:00 [INF] HTTP GET /lib/jquery/dist/jquery.min.js responded 200 in 117.0158 ms
2025-07-30 19:43:52.913 +03:00 [INF] HTTP GET /lib/jquery-validation/dist/jquery.validate.min.js responded 200 in 100.2998 ms
2025-07-30 19:43:52.927 +03:00 [INF] HTTP GET /lib/bootstrap/dist/css/bootstrap.min.css responded 200 in 139.7825 ms
2025-07-30 19:43:53.525 +03:00 [INF] HTTP GET /favicon.ico responded 200 in 0.9213 ms
2025-07-30 19:43:59.172 +03:00 [INF] HTTP POST /Account/Login responded 302 in 498.8705 ms
2025-07-30 19:44:01.388 +03:00 [INF] HTTP GET /Dashboard responded 200 in 2206.7357 ms
2025-07-30 19:44:01.411 +03:00 [INF] HTTP GET /nCarry.Web.styles.css responded 200 in 0.6242 ms
2025-07-30 19:44:01.411 +03:00 [INF] HTTP GET /js/site.js responded 200 in 0.5477 ms
2025-07-30 19:44:02.455 +03:00 [INF] HTTP GET /Dashboard/GetChartData responded 200 in 110.6890 ms
2025-07-30 19:44:10.277 +03:00 [INF] HTTP GET /Dashboard responded 200 in 2182.5790 ms
2025-07-30 19:44:10.301 +03:00 [INF] HTTP GET /css/site.css responded 200 in 0.7093 ms
2025-07-30 19:44:10.301 +03:00 [INF] HTTP GET /js/site.js responded 200 in 0.7185 ms
2025-07-30 19:44:10.305 +03:00 [INF] HTTP GET /lib/jquery/dist/jquery.min.js responded 200 in 5.0156 ms
2025-07-30 19:44:10.306 +03:00 [INF] HTTP GET /lib/bootstrap/dist/js/bootstrap.bundle.min.js responded 200 in 5.6586 ms
2025-07-30 19:44:10.308 +03:00 [INF] HTTP GET /nCarry.Web.styles.css responded 200 in 1.6865 ms
2025-07-30 19:44:10.309 +03:00 [INF] HTTP GET /lib/bootstrap/dist/css/bootstrap.min.css responded 200 in 8.4189 ms
2025-07-30 19:44:11.390 +03:00 [INF] HTTP GET /favicon.ico responded 200 in 1.5433 ms
2025-07-30 19:44:11.514 +03:00 [INF] HTTP GET /Dashboard/GetChartData responded 200 in 198.5528 ms
2025-07-30 19:44:14.187 +03:00 [INF] HTTP GET /Dashboard responded 200 in 1923.4440 ms
2025-07-30 19:44:14.218 +03:00 [INF] HTTP GET /lib/bootstrap/dist/css/bootstrap.min.css responded 200 in 4.1914 ms
2025-07-30 19:44:14.219 +03:00 [INF] HTTP GET /css/site.css responded 200 in 0.4148 ms
2025-07-30 19:44:14.219 +03:00 [INF] HTTP GET /js/site.js responded 200 in 0.1469 ms
2025-07-30 19:44:14.220 +03:00 [INF] HTTP GET /nCarry.Web.styles.css responded 200 in 1.0738 ms
2025-07-30 19:44:14.223 +03:00 [INF] HTTP GET /lib/bootstrap/dist/js/bootstrap.bundle.min.js responded 200 in 4.2793 ms
2025-07-30 19:44:14.225 +03:00 [INF] HTTP GET /lib/jquery/dist/jquery.min.js responded 200 in 5.7588 ms
2025-07-30 19:44:14.718 +03:00 [INF] HTTP GET /favicon.ico responded 200 in 0.5937 ms
2025-07-30 19:44:14.905 +03:00 [INF] HTTP GET /Dashboard/GetChartData responded 200 in 250.3182 ms
2025-07-30 19:44:21.914 +03:00 [INF] HTTP GET / responded 302 in 0.4572 ms
2025-07-30 19:44:21.919 +03:00 [INF] HTTP GET /Account/Login responded 200 in 1.8207 ms
2025-07-30 19:44:21.935 +03:00 [INF] HTTP GET /css/site.css responded 200 in 0.6093 ms
2025-07-30 19:44:21.935 +03:00 [INF] HTTP GET /lib/jquery-validation/dist/jquery.validate.min.js responded 200 in 0.5269 ms
2025-07-30 19:44:21.936 +03:00 [INF] HTTP GET /lib/jquery-validation-unobtrusive/jquery.validate.unobtrusive.min.js responded 200 in 0.5488 ms
2025-07-30 19:44:21.936 +03:00 [INF] HTTP GET /lib/bootstrap/dist/css/bootstrap.min.css responded 200 in 3.4630 ms
2025-07-30 19:44:21.936 +03:00 [INF] HTTP GET /lib/jquery/dist/jquery.min.js responded 200 in 1.6703 ms
2025-07-30 19:44:21.936 +03:00 [INF] HTTP GET /lib/bootstrap/dist/js/bootstrap.bundle.min.js responded 200 in 1.0146 ms
2025-07-30 19:44:21.982 +03:00 [INF] HTTP GET /favicon.ico responded 200 in 0.3823 ms
2025-07-30 19:44:27.065 +03:00 [INF] HTTP POST /Account/Login responded 302 in 384.6673 ms
2025-07-30 19:44:28.947 +03:00 [INF] HTTP GET /Dashboard responded 200 in 1878.1180 ms
2025-07-30 19:44:28.973 +03:00 [INF] HTTP GET /nCarry.Web.styles.css responded 200 in 0.5154 ms
2025-07-30 19:44:28.973 +03:00 [INF] HTTP GET /js/site.js responded 200 in 0.1401 ms
2025-07-30 19:44:29.954 +03:00 [INF] HTTP GET /Dashboard/GetChartData responded 200 in 108.8940 ms
2025-07-30 19:44:42.277 +03:00 [INF] HTTP GET /Dashboard responded 200 in 2061.9467 ms
2025-07-30 19:46:39.090 +03:00 [INF] HTTP GET /Dashboard responded 200 in 4498.3061 ms
2025-07-30 19:46:39.111 +03:00 [INF] HTTP GET /nCarry.Web.styles.css responded 304 in 0.2853 ms
2025-07-30 19:46:39.276 +03:00 [INF] HTTP GET /Dashboard/GetChartData responded 200 in 141.7854 ms
CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.KestrelServerImpl.<>c__DisplayClass28_0`1.<<StartAsync>g__OnBind|0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.AddressBinder.BindEndpointAsync(ListenOptions endpoint, AddressBindContext context, CancellationToken cancellationToken)
   --- End of inner exception stack trace ---
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.AddressBinder.BindEndpointAsync(ListenOptions endpoint, AddressBindContext context, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.LocalhostListenOptions.BindAsync(AddressBindContext context, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.AddressBinder.AddressesStrategy.BindAsync(AddressBindContext context, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.AddressBinder.BindAsync(ListenOptions[] listenOptions, AddressBindContext context, Func`2 useHttps, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.KestrelServerImpl.BindAsync(CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.KestrelServerImpl.StartAsync[TContext](IHttpApplication`1 application, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Hosting.GenericWebHostService.StartAsync(CancellationToken cancellationToken)
   at Microsoft.Extensions.Hosting.Internal.Host.<StartAsync>b__15_1(IHostedService service, CancellationToken token)
   at Microsoft.Extensions.Hosting.Internal.Host.ForeachService[T](IEnumerable`1 services, CancellationToken token, Boolean concurrent, Boolean abortOnFirstException, List`1 exceptions, Func`3 operation)
2025-07-30 19:31:57.420 +03:00 [FTL] Application terminated unexpectedly
System.IO.IOException: Failed to bind to address http://127.0.0.1:5298: address already in use.
 ---> Microsoft.AspNetCore.Connections.AddressInUseException: Address already in use
 ---> System.Net.Sockets.SocketException (48): Address already in use
   at System.Net.Sockets.Socket.DoBind(EndPoint endPointSnapshot, SocketAddress socketAddress)
   at System.Net.Sockets.Socket.Bind(EndPoint localEP)
   at Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets.SocketTransportOptions.CreateDefaultBoundListenSocket(EndPoint endpoint)
   at Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets.SocketConnectionListener.Bind()
   --- End of inner exception stack trace ---
   at Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets.SocketConnectionListener.Bind()
   at Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets.SocketTransportFactory.BindAsync(EndPoint endpoint, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.Infrastructure.TransportManager.BindAsync(EndPoint endPoint, ConnectionDelegate connectionDelegate, EndpointConfig endpointConfig, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.KestrelServerImpl.<>c__DisplayClass28_0`1.<<StartAsync>g__OnBind|0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.AddressBinder.BindEndpointAsync(ListenOptions endpoint, AddressBindContext context, CancellationToken cancellationToken)
   --- End of inner exception stack trace ---
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.AddressBinder.BindEndpointAsync(ListenOptions endpoint, AddressBindContext context, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.LocalhostListenOptions.BindAsync(AddressBindContext context, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.AddressBinder.AddressesStrategy.BindAsync(AddressBindContext context, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.AddressBinder.BindAsync(ListenOptions[] listenOptions, AddressBindContext context, Func`2 useHttps, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.KestrelServerImpl.BindAsync(CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.KestrelServerImpl.StartAsync[TContext](IHttpApplication`1 application, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Hosting.GenericWebHostService.StartAsync(CancellationToken cancellationToken)
   at Microsoft.Extensions.Hosting.Internal.Host.<StartAsync>b__15_1(IHostedService service, CancellationToken token)
   at Microsoft.Extensions.Hosting.Internal.Host.ForeachService[T](IEnumerable`1 services, CancellationToken token, Boolean concurrent, Boolean abortOnFirstException, List`1 exceptions, Func`3 operation)
   at Microsoft.Extensions.Hosting.Internal.Host.StartAsync(CancellationToken cancellationToken)
   at Microsoft.Extensions.Hosting.HostingAbstractionsHostExtensions.RunAsync(IHost host, CancellationToken token)
   at Microsoft.Extensions.Hosting.HostingAbstractionsHostExtensions.RunAsync(IHost host, CancellationToken token)
   at Microsoft.Extensions.Hosting.HostingAbstractionsHostExtensions.Run(IHost host)
   at Program.<Main>$(String[] args) in /Users/<USER>/Source/nCarry/nCarry/nCarry.Web/Program.cs:line 82
2025-07-30 19:39:06.716 +03:00 [INF] Starting database seeding.
2025-07-30 19:39:09.025 +03:00 [INF] Admin user already exists. Seeding is not required.
2025-07-30 19:39:09.071 +03:00 [ERR] Hosting failed to start
System.IO.IOException: Failed to bind to address http://127.0.0.1:5298: address already in use.
 ---> Microsoft.AspNetCore.Connections.AddressInUseException: Address already in use
 ---> System.Net.Sockets.SocketException (48): Address already in use
   at System.Net.Sockets.Socket.DoBind(EndPoint endPointSnapshot, SocketAddress socketAddress)
   at System.Net.Sockets.Socket.Bind(EndPoint localEP)
   at Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets.SocketTransportOptions.CreateDefaultBoundListenSocket(EndPoint endpoint)
   at Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets.SocketConnectionListener.Bind()
   --- End of inner exception stack trace ---
   at Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets.SocketConnectionListener.Bind()
   at Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets.SocketTransportFactory.BindAsync(EndPoint endpoint, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.Infrastructure.TransportManager.BindAsync(EndPoint endPoint, ConnectionDelegate connectionDelegate, EndpointConfig endpointConfig, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.KestrelServerImpl.<>c__DisplayClass28_0`1.<<StartAsync>g__OnBind|0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.AddressBinder.BindEndpointAsync(ListenOptions endpoint, AddressBindContext context, CancellationToken cancellationToken)
   --- End of inner exception stack trace ---
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.AddressBinder.BindEndpointAsync(ListenOptions endpoint, AddressBindContext context, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.LocalhostListenOptions.BindAsync(AddressBindContext context, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.AddressBinder.AddressesStrategy.BindAsync(AddressBindContext context, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.AddressBinder.BindAsync(ListenOptions[] listenOptions, AddressBindContext context, Func`2 useHttps, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.KestrelServerImpl.BindAsync(CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.KestrelServerImpl.StartAsync[TContext](IHttpApplication`1 application, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Hosting.GenericWebHostService.StartAsync(CancellationToken cancellationToken)
   at Microsoft.Extensions.Hosting.Internal.Host.<StartAsync>b__15_1(IHostedService service, CancellationToken token)
   at Microsoft.Extensions.Hosting.Internal.Host.ForeachService[T](IEnumerable`1 services, CancellationToken token, Boolean concurrent, Boolean abortOnFirstException, List`1 exceptions, Func`3 operation)
2025-07-30 19:39:09.078 +03:00 [FTL] Application terminated unexpectedly
System.IO.IOException: Failed to bind to address http://127.0.0.1:5298: address already in use.
 ---> Microsoft.AspNetCore.Connections.AddressInUseException: Address already in use
 ---> System.Net.Sockets.SocketException (48): Address already in use
   at System.Net.Sockets.Socket.DoBind(EndPoint endPointSnapshot, SocketAddress socketAddress)
   at System.Net.Sockets.Socket.Bind(EndPoint localEP)
   at Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets.SocketTransportOptions.CreateDefaultBoundListenSocket(EndPoint endpoint)
   at Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets.SocketConnectionListener.Bind()
   --- End of inner exception stack trace ---
   at Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets.SocketConnectionListener.Bind()
   at Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets.SocketTransportFactory.BindAsync(EndPoint endpoint, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.Infrastructure.TransportManager.BindAsync(EndPoint endPoint, ConnectionDelegate connectionDelegate, EndpointConfig endpointConfig, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.KestrelServerImpl.<>c__DisplayClass28_0`1.<<StartAsync>g__OnBind|0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.AddressBinder.BindEndpointAsync(ListenOptions endpoint, AddressBindContext context, CancellationToken cancellationToken)
   --- End of inner exception stack trace ---
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.AddressBinder.BindEndpointAsync(ListenOptions endpoint, AddressBindContext context, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.LocalhostListenOptions.BindAsync(AddressBindContext context, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.AddressBinder.AddressesStrategy.BindAsync(AddressBindContext context, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.AddressBinder.BindAsync(ListenOptions[] listenOptions, AddressBindContext context, Func`2 useHttps, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.KestrelServerImpl.BindAsync(CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.KestrelServerImpl.StartAsync[TContext](IHttpApplication`1 application, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Hosting.GenericWebHostService.StartAsync(CancellationToken cancellationToken)
   at Microsoft.Extensions.Hosting.Internal.Host.<StartAsync>b__15_1(IHostedService service, CancellationToken token)
   at Microsoft.Extensions.Hosting.Internal.Host.ForeachService[T](IEnumerable`1 services, CancellationToken token, Boolean concurrent, Boolean abortOnFirstException, List`1 exceptions, Func`3 operation)
   at Microsoft.Extensions.Hosting.Internal.Host.StartAsync(CancellationToken cancellationToken)
   at Microsoft.Extensions.Hosting.HostingAbstractionsHostExtensions.RunAsync(IHost host, CancellationToken token)
   at Microsoft.Extensions.Hosting.HostingAbstractionsHostExtensions.RunAsync(IHost host, CancellationToken token)
   at Microsoft.Extensions.Hosting.HostingAbstractionsHostExtensions.Run(IHost host)
   at Program.<Main>$(String[] args) in /Users/<USER>/Source/nCarry/nCarry/nCarry.Web/Program.cs:line 82
2025-07-30 19:50:19.665 +03:00 [INF] Starting database seeding.
2025-07-30 19:54:53.495 +03:00 [INF] Starting database seeding.
2025-07-30 19:54:55.771 +03:00 [INF] Admin user already exists. Seeding is not required.
