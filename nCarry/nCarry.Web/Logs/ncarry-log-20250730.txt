2025-07-30 00:00:01.506 +03:00 [INF] HTTP GET /Dashboard responded 200 in 2645.0323 ms
2025-07-30 00:00:01.522 +03:00 [INF] HTTP GET /nCarry.Web.styles.css responded 304 in 10.0507 ms
2025-07-30 00:00:01.728 +03:00 [INF] HTTP GET /Dashboard/GetChartData responded 200 in 167.5190 ms
2025-07-30 00:00:08.461 +03:00 [INF] HTTP GET /GoodsReceipt/Create responded 200 in 1064.9707 ms
2025-07-30 00:00:32.608 +03:00 [ERR] Failed executing DbCommand (141ms) [Parameters=[@p0='?' (Precision = 18) (Scale = 2) (DbType = Decimal), @p1='?' (Size = 50), @p2='?' (DbType = DateTime2), @p3='?' (DbType = DateTime2), @p4='?' (Precision = 18) (Scale = 2) (DbType = Decimal), @p5='?' (DbType = DateTime2), @p6='?' (DbType = Int32), @p7='?' (DbType = Int32), @p8='?' (Precision = 18) (Scale = 4) (DbType = Decimal), @p9='?' (Precision = 18) (Scale = 4) (DbType = Decimal), @p10='?' (Precision = 18) (Scale = 4) (DbType = Decimal), @p11='?' (Precision = 18) (Scale = 4) (DbType = Decimal), @p12='?' (Precision = 18) (Scale = 4) (DbType = Decimal), @p13='?' (Size = 50), @p14='?' (DbType = DateTime2), @p15='?' (DbType = Int32), @p16='?' (DbType = Int32), @p17='?' (Precision = 18) (Scale = 2) (DbType = Decimal), @p18='?' (Size = 50), @p19='?' (DbType = DateTime2), @p20='?' (DbType = DateTime2), @p21='?' (Precision = 18) (Scale = 2) (DbType = Decimal), @p22='?' (DbType = DateTime2), @p23='?' (DbType = Int32), @p24='?' (DbType = Int32), @p25='?' (Precision = 18) (Scale = 4) (DbType = Decimal), @p26='?' (Precision = 18) (Scale = 4) (DbType = Decimal), @p27='?' (Precision = 18) (Scale = 4) (DbType = Decimal), @p28='?' (Precision = 18) (Scale = 4) (DbType = Decimal), @p29='?' (Precision = 18) (Scale = 4) (DbType = Decimal), @p30='?' (Size = 50), @p31='?' (DbType = DateTime2), @p32='?' (DbType = Int32), @p33='?' (DbType = Int32), @p34='?' (Size = 50), @p35='?' (DbType = Int32), @p36='?' (DbType = DateTime2), @p37='?' (DbType = Int32), @p38='?' (Size = 255), @p39='?' (DbType = Int32), @p40='?' (Precision = 18) (Scale = 4) (DbType = Decimal), @p41='?' (DbType = Int32), @p42='?' (Size = 50), @p43='?' (Size = 50), @p44='?' (Size = 50), @p45='?' (Precision = 18) (Scale = 2) (DbType = Decimal), @p46='?' (DbType = DateTime2), @p47='?' (Size = 20), @p48='?' (DbType = Int32), @p49='?' (Precision = 18) (Scale = 2) (DbType = Decimal), @p50='?' (DbType = Int32), @p51='?' (DbType = Int32), @p52='?' (Size = 50), @p53='?' (DbType = Int32), @p54='?' (DbType = DateTime2), @p55='?' (DbType = Int32), @p56='?' (Size = 255), @p57='?' (DbType = Int32), @p58='?' (Precision = 18) (Scale = 4) (DbType = Decimal), @p59='?' (DbType = Int32), @p60='?' (Size = 50), @p61='?' (Size = 50), @p62='?' (Size = 50), @p63='?' (Precision = 18) (Scale = 2) (DbType = Decimal), @p64='?' (DbType = DateTime2), @p65='?' (Size = 20), @p66='?' (DbType = Int32), @p67='?' (Precision = 18) (Scale = 2) (DbType = Decimal), @p68='?' (DbType = Int32), @p69='?' (DbType = Int32), @p71='?' (DbType = Int32), @p70='?' (Size = 20), @p74='?' (DbType = Int32), @p72='?' (Size = 20), @p73='?' (Precision = 18) (Scale = 4) (DbType = Decimal)], CommandType='"Text"', CommandTimeout='30']
SET NOCOUNT ON;
MERGE [Inventory] USING (
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10, @p11, @p12, @p13, @p14, @p15, @p16, 0),
(@p17, @p18, @p19, @p20, @p21, @p22, @p23, @p24, @p25, @p26, @p27, @p28, @p29, @p30, @p31, @p32, @p33, 1)) AS i ([AverageCost], [BatchNumber], [CreatedDate], [ExpiryDate], [LastCost], [LastCountDate], [LocationID], [ProductID], [QuantityAvailable], [QuantityInTransit], [QuantityOnHand], [QuantityOnOrder], [QuantityReserved], [SerialNumber], [UpdatedDate], [VariantID], [WarehouseID], _Position) ON 1=0
WHEN NOT MATCHED THEN
INSERT ([AverageCost], [BatchNumber], [CreatedDate], [ExpiryDate], [LastCost], [LastCountDate], [LocationID], [ProductID], [QuantityAvailable], [QuantityInTransit], [QuantityOnHand], [QuantityOnOrder], [QuantityReserved], [SerialNumber], [UpdatedDate], [VariantID], [WarehouseID])
VALUES (i.[AverageCost], i.[BatchNumber], i.[CreatedDate], i.[ExpiryDate], i.[LastCost], i.[LastCountDate], i.[LocationID], i.[ProductID], i.[QuantityAvailable], i.[QuantityInTransit], i.[QuantityOnHand], i.[QuantityOnOrder], i.[QuantityReserved], i.[SerialNumber], i.[UpdatedDate], i.[VariantID], i.[WarehouseID])
OUTPUT INSERTED.[InventoryID], i._Position;
MERGE [InventoryTransaction] USING (
VALUES (@p34, @p35, @p36, @p37, @p38, @p39, @p40, @p41, @p42, @p43, @p44, @p45, @p46, @p47, @p48, @p49, @p50, @p51, 0),
(@p52, @p53, @p54, @p55, @p56, @p57, @p58, @p59, @p60, @p61, @p62, @p63, @p64, @p65, @p66, @p67, @p68, @p69, 1)) AS i ([BatchNumber], [CreatedByUserID], [CreatedDate], [LocationID], [Notes], [ProductID], [Quantity], [ReferenceID], [ReferenceNumber], [ReferenceType], [SerialNumber], [TotalCost], [TransactionDate], [TransactionType], [UOMID], [UnitCost], [VariantID], [WarehouseID], _Position) ON 1=0
WHEN NOT MATCHED THEN
INSERT ([BatchNumber], [CreatedByUserID], [CreatedDate], [LocationID], [Notes], [ProductID], [Quantity], [ReferenceID], [ReferenceNumber], [ReferenceType], [SerialNumber], [TotalCost], [TransactionDate], [TransactionType], [UOMID], [UnitCost], [VariantID], [WarehouseID])
VALUES (i.[BatchNumber], i.[CreatedByUserID], i.[CreatedDate], i.[LocationID], i.[Notes], i.[ProductID], i.[Quantity], i.[ReferenceID], i.[ReferenceNumber], i.[ReferenceType], i.[SerialNumber], i.[TotalCost], i.[TransactionDate], i.[TransactionType], i.[UOMID], i.[UnitCost], i.[VariantID], i.[WarehouseID])
OUTPUT INSERTED.[TransactionID], i._Position;
UPDATE [PurchaseOrder] SET [OrderStatus] = @p70
OUTPUT 1
WHERE [PurchaseOrderID] = @p71;
UPDATE [PurchaseOrderItem] SET [ItemStatus] = @p72, [ReceivedQuantity] = @p73
OUTPUT 1
WHERE [PurchaseOrderItemID] = @p74;
2025-07-30 00:00:32.824 +03:00 [ERR] An exception occurred in the database while saving changes for context type 'nCarry.Web.Data.ApplicationDbContext'.
Microsoft.EntityFrameworkCore.DbUpdateException: An error occurred while saving the entity changes. See the inner exception for details.
 ---> Microsoft.Data.SqlClient.SqlException (0x80131904): Invalid column name 'UOMID'.
   at Microsoft.Data.SqlClient.SqlCommand.<>c.<ExecuteDbDataReaderAsync>b__211_0(Task`1 result)
   at System.Threading.Tasks.ContinuationResultTaskFromResultTask`2.InnerInvoke()
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
ClientConnectionId:1dd875b5-9cf1-4119-a004-c217b9e7b7cd
Error Number:207,State:1,Class:16
   --- End of inner exception stack trace ---
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.SqlServer.Update.Internal.SqlServerModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalDatabase.SaveChangesAsync(IList`1 entries, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(IList`1 entriesToSave, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(StateManager stateManager, Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.SqlServer.Storage.Internal.SqlServerExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.DbContext.SaveChangesAsync(Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
Microsoft.EntityFrameworkCore.DbUpdateException: An error occurred while saving the entity changes. See the inner exception for details.
 ---> Microsoft.Data.SqlClient.SqlException (0x80131904): Invalid column name 'UOMID'.
   at Microsoft.Data.SqlClient.SqlCommand.<>c.<ExecuteDbDataReaderAsync>b__211_0(Task`1 result)
   at System.Threading.Tasks.ContinuationResultTaskFromResultTask`2.InnerInvoke()
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
ClientConnectionId:1dd875b5-9cf1-4119-a004-c217b9e7b7cd
Error Number:207,State:1,Class:16
   --- End of inner exception stack trace ---
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.SqlServer.Update.Internal.SqlServerModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalDatabase.SaveChangesAsync(IList`1 entries, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(IList`1 entriesToSave, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(StateManager stateManager, Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.SqlServer.Storage.Internal.SqlServerExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.DbContext.SaveChangesAsync(Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
2025-07-30 00:00:33.322 +03:00 [INF] HTTP POST /GoodsReceipt/Create responded 200 in 2485.9804 ms
2025-07-30 00:01:37.045 +03:00 [INF] Starting database seeding.
2025-07-30 00:01:40.330 +03:00 [INF] Admin user already exists. Seeding is not required.
2025-07-30 00:03:26.267 +03:00 [WRN] Failed to determine the https port for redirect.
2025-07-30 00:03:26.586 +03:00 [INF] HTTP GET /Account/Login responded 200 in 324.6676 ms
2025-07-30 00:03:42.063 +03:00 [INF] HTTP POST /Account/Login responded 302 in 859.1784 ms
2025-07-30 00:03:44.465 +03:00 [INF] HTTP GET /Dashboard responded 200 in 2397.3494 ms
2025-07-30 00:03:44.723 +03:00 [INF] HTTP GET /Dashboard/GetChartData responded 200 in 215.9388 ms
2025-07-30 00:03:51.990 +03:00 [INF] HTTP GET /GoodsReceipt/Create responded 200 in 1199.3605 ms
2025-07-30 00:04:36.940 +03:00 [ERR] Failed executing DbCommand (125ms) [Parameters=[@p0='?' (Precision = 18) (Scale = 2) (DbType = Decimal), @p1='?' (Size = 50), @p2='?' (DbType = DateTime2), @p3='?' (DbType = DateTime2), @p4='?' (Precision = 18) (Scale = 2) (DbType = Decimal), @p5='?' (DbType = DateTime2), @p6='?' (DbType = Int32), @p7='?' (DbType = Int32), @p8='?' (Precision = 18) (Scale = 4) (DbType = Decimal), @p9='?' (Precision = 18) (Scale = 4) (DbType = Decimal), @p10='?' (Precision = 18) (Scale = 4) (DbType = Decimal), @p11='?' (Precision = 18) (Scale = 4) (DbType = Decimal), @p12='?' (Precision = 18) (Scale = 4) (DbType = Decimal), @p13='?' (Size = 50), @p14='?' (DbType = DateTime2), @p15='?' (DbType = Int32), @p16='?' (DbType = Int32), @p17='?' (Precision = 18) (Scale = 2) (DbType = Decimal), @p18='?' (Size = 50), @p19='?' (DbType = DateTime2), @p20='?' (DbType = DateTime2), @p21='?' (Precision = 18) (Scale = 2) (DbType = Decimal), @p22='?' (DbType = DateTime2), @p23='?' (DbType = Int32), @p24='?' (DbType = Int32), @p25='?' (Precision = 18) (Scale = 4) (DbType = Decimal), @p26='?' (Precision = 18) (Scale = 4) (DbType = Decimal), @p27='?' (Precision = 18) (Scale = 4) (DbType = Decimal), @p28='?' (Precision = 18) (Scale = 4) (DbType = Decimal), @p29='?' (Precision = 18) (Scale = 4) (DbType = Decimal), @p30='?' (Size = 50), @p31='?' (DbType = DateTime2), @p32='?' (DbType = Int32), @p33='?' (DbType = Int32), @p34='?' (Size = 50), @p35='?' (DbType = Int32), @p36='?' (DbType = DateTime2), @p37='?' (DbType = Int32), @p38='?' (Size = 255), @p39='?' (DbType = Int32), @p40='?' (Precision = 18) (Scale = 4) (DbType = Decimal), @p41='?' (DbType = Int32), @p42='?' (Size = 50), @p43='?' (Size = 50), @p44='?' (Size = 50), @p45='?' (Precision = 18) (Scale = 2) (DbType = Decimal), @p46='?' (DbType = DateTime2), @p47='?' (Size = 20), @p48='?' (DbType = Int32), @p49='?' (Precision = 18) (Scale = 2) (DbType = Decimal), @p50='?' (DbType = Int32), @p51='?' (DbType = Int32), @p52='?' (Size = 50), @p53='?' (DbType = Int32), @p54='?' (DbType = DateTime2), @p55='?' (DbType = Int32), @p56='?' (Size = 255), @p57='?' (DbType = Int32), @p58='?' (Precision = 18) (Scale = 4) (DbType = Decimal), @p59='?' (DbType = Int32), @p60='?' (Size = 50), @p61='?' (Size = 50), @p62='?' (Size = 50), @p63='?' (Precision = 18) (Scale = 2) (DbType = Decimal), @p64='?' (DbType = DateTime2), @p65='?' (Size = 20), @p66='?' (DbType = Int32), @p67='?' (Precision = 18) (Scale = 2) (DbType = Decimal), @p68='?' (DbType = Int32), @p69='?' (DbType = Int32), @p71='?' (DbType = Int32), @p70='?' (Size = 20), @p74='?' (DbType = Int32), @p72='?' (Size = 20), @p73='?' (Precision = 18) (Scale = 4) (DbType = Decimal)], CommandType='"Text"', CommandTimeout='30']
SET NOCOUNT ON;
MERGE [Inventory] USING (
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10, @p11, @p12, @p13, @p14, @p15, @p16, 0),
(@p17, @p18, @p19, @p20, @p21, @p22, @p23, @p24, @p25, @p26, @p27, @p28, @p29, @p30, @p31, @p32, @p33, 1)) AS i ([AverageCost], [BatchNumber], [CreatedDate], [ExpiryDate], [LastCost], [LastCountDate], [LocationID], [ProductID], [QuantityAvailable], [QuantityInTransit], [QuantityOnHand], [QuantityOnOrder], [QuantityReserved], [SerialNumber], [UpdatedDate], [VariantID], [WarehouseID], _Position) ON 1=0
WHEN NOT MATCHED THEN
INSERT ([AverageCost], [BatchNumber], [CreatedDate], [ExpiryDate], [LastCost], [LastCountDate], [LocationID], [ProductID], [QuantityAvailable], [QuantityInTransit], [QuantityOnHand], [QuantityOnOrder], [QuantityReserved], [SerialNumber], [UpdatedDate], [VariantID], [WarehouseID])
VALUES (i.[AverageCost], i.[BatchNumber], i.[CreatedDate], i.[ExpiryDate], i.[LastCost], i.[LastCountDate], i.[LocationID], i.[ProductID], i.[QuantityAvailable], i.[QuantityInTransit], i.[QuantityOnHand], i.[QuantityOnOrder], i.[QuantityReserved], i.[SerialNumber], i.[UpdatedDate], i.[VariantID], i.[WarehouseID])
OUTPUT INSERTED.[InventoryID], i._Position;
MERGE [InventoryTransaction] USING (
VALUES (@p34, @p35, @p36, @p37, @p38, @p39, @p40, @p41, @p42, @p43, @p44, @p45, @p46, @p47, @p48, @p49, @p50, @p51, 0),
(@p52, @p53, @p54, @p55, @p56, @p57, @p58, @p59, @p60, @p61, @p62, @p63, @p64, @p65, @p66, @p67, @p68, @p69, 1)) AS i ([BatchNumber], [CreatedByUserID], [CreatedDate], [LocationID], [Notes], [ProductID], [Quantity], [ReferenceID], [ReferenceNumber], [ReferenceType], [SerialNumber], [TotalCost], [TransactionDate], [TransactionType], [UOMID], [UnitCost], [VariantID], [WarehouseID], _Position) ON 1=0
WHEN NOT MATCHED THEN
INSERT ([BatchNumber], [CreatedByUserID], [CreatedDate], [LocationID], [Notes], [ProductID], [Quantity], [ReferenceID], [ReferenceNumber], [ReferenceType], [SerialNumber], [TotalCost], [TransactionDate], [TransactionType], [UOMID], [UnitCost], [VariantID], [WarehouseID])
VALUES (i.[BatchNumber], i.[CreatedByUserID], i.[CreatedDate], i.[LocationID], i.[Notes], i.[ProductID], i.[Quantity], i.[ReferenceID], i.[ReferenceNumber], i.[ReferenceType], i.[SerialNumber], i.[TotalCost], i.[TransactionDate], i.[TransactionType], i.[UOMID], i.[UnitCost], i.[VariantID], i.[WarehouseID])
OUTPUT INSERTED.[TransactionID], i._Position;
UPDATE [PurchaseOrder] SET [OrderStatus] = @p70
OUTPUT 1
WHERE [PurchaseOrderID] = @p71;
UPDATE [PurchaseOrderItem] SET [ItemStatus] = @p72, [ReceivedQuantity] = @p73
OUTPUT 1
WHERE [PurchaseOrderItemID] = @p74;
2025-07-30 00:04:37.053 +03:00 [ERR] An exception occurred in the database while saving changes for context type 'nCarry.Web.Data.ApplicationDbContext'.
Microsoft.EntityFrameworkCore.DbUpdateException: An error occurred while saving the entity changes. See the inner exception for details.
 ---> Microsoft.Data.SqlClient.SqlException (0x80131904): Invalid column name 'UOMID'.
   at Microsoft.Data.SqlClient.SqlCommand.<>c.<ExecuteDbDataReaderAsync>b__211_0(Task`1 result)
   at System.Threading.Tasks.ContinuationResultTaskFromResultTask`2.InnerInvoke()
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
ClientConnectionId:5d11a337-8378-414f-8dd7-7556178d1aec
Error Number:207,State:1,Class:16
   --- End of inner exception stack trace ---
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.SqlServer.Update.Internal.SqlServerModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalDatabase.SaveChangesAsync(IList`1 entries, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(IList`1 entriesToSave, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(StateManager stateManager, Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.SqlServer.Storage.Internal.SqlServerExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.DbContext.SaveChangesAsync(Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
Microsoft.EntityFrameworkCore.DbUpdateException: An error occurred while saving the entity changes. See the inner exception for details.
 ---> Microsoft.Data.SqlClient.SqlException (0x80131904): Invalid column name 'UOMID'.
   at Microsoft.Data.SqlClient.SqlCommand.<>c.<ExecuteDbDataReaderAsync>b__211_0(Task`1 result)
   at System.Threading.Tasks.ContinuationResultTaskFromResultTask`2.InnerInvoke()
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
ClientConnectionId:5d11a337-8378-414f-8dd7-7556178d1aec
Error Number:207,State:1,Class:16
   --- End of inner exception stack trace ---
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.SqlServer.Update.Internal.SqlServerModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalDatabase.SaveChangesAsync(IList`1 entries, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(IList`1 entriesToSave, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(StateManager stateManager, Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.SqlServer.Storage.Internal.SqlServerExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.DbContext.SaveChangesAsync(Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
2025-07-30 00:04:37.459 +03:00 [INF] HTTP POST /GoodsReceipt/Create responded 200 in 2161.8808 ms
2025-07-30 00:04:57.258 +03:00 [INF] HTTP GET /GoodsReceipt/Create responded 302 in 5.6790 ms
2025-07-30 00:09:05.854 +03:00 [INF] Starting database seeding.
2025-07-30 00:09:09.375 +03:00 [INF] Admin user already exists. Seeding is not required.
2025-07-30 00:10:54.846 +03:00 [WRN] Failed to determine the https port for redirect.
2025-07-30 00:10:55.149 +03:00 [INF] HTTP GET /Account/Login responded 200 in 308.6408 ms
2025-07-30 00:11:12.038 +03:00 [INF] HTTP POST /Account/Login responded 302 in 711.4302 ms
2025-07-30 00:11:14.547 +03:00 [INF] HTTP GET /Dashboard responded 200 in 2503.4679 ms
2025-07-30 00:11:14.561 +03:00 [INF] HTTP GET /nCarry.Web.styles.css responded 304 in 7.0780 ms
2025-07-30 00:11:14.745 +03:00 [INF] HTTP GET /Dashboard/GetChartData responded 200 in 158.5506 ms
2025-07-30 00:11:21.306 +03:00 [INF] HTTP GET /GoodsReceipt/Create responded 200 in 973.5643 ms
2025-07-30 00:11:59.088 +03:00 [ERR] Failed executing DbCommand (92ms) [Parameters=[@p0='?' (Precision = 18) (Scale = 2) (DbType = Decimal), @p1='?' (Size = 50), @p2='?' (DbType = DateTime2), @p3='?' (DbType = DateTime2), @p4='?' (Precision = 18) (Scale = 2) (DbType = Decimal), @p5='?' (DbType = DateTime2), @p6='?' (DbType = Int32), @p7='?' (DbType = Int32), @p8='?' (Precision = 18) (Scale = 4) (DbType = Decimal), @p9='?' (Precision = 18) (Scale = 4) (DbType = Decimal), @p10='?' (Precision = 18) (Scale = 4) (DbType = Decimal), @p11='?' (Precision = 18) (Scale = 4) (DbType = Decimal), @p12='?' (Precision = 18) (Scale = 4) (DbType = Decimal), @p13='?' (Size = 50), @p14='?' (DbType = DateTime2), @p15='?' (DbType = Int32), @p16='?' (DbType = Int32), @p17='?' (Precision = 18) (Scale = 2) (DbType = Decimal), @p18='?' (Size = 50), @p19='?' (DbType = DateTime2), @p20='?' (DbType = DateTime2), @p21='?' (Precision = 18) (Scale = 2) (DbType = Decimal), @p22='?' (DbType = DateTime2), @p23='?' (DbType = Int32), @p24='?' (DbType = Int32), @p25='?' (Precision = 18) (Scale = 4) (DbType = Decimal), @p26='?' (Precision = 18) (Scale = 4) (DbType = Decimal), @p27='?' (Precision = 18) (Scale = 4) (DbType = Decimal), @p28='?' (Precision = 18) (Scale = 4) (DbType = Decimal), @p29='?' (Precision = 18) (Scale = 4) (DbType = Decimal), @p30='?' (Size = 50), @p31='?' (DbType = DateTime2), @p32='?' (DbType = Int32), @p33='?' (DbType = Int32), @p34='?' (Size = 50), @p35='?' (DbType = Int32), @p36='?' (DbType = DateTime2), @p37='?' (DbType = Int32), @p38='?' (Size = 255), @p39='?' (DbType = Int32), @p40='?' (Precision = 18) (Scale = 4) (DbType = Decimal), @p41='?' (DbType = Int32), @p42='?' (Size = 50), @p43='?' (Size = 50), @p44='?' (Size = 50), @p45='?' (Precision = 18) (Scale = 2) (DbType = Decimal), @p46='?' (DbType = DateTime2), @p47='?' (Size = 20), @p48='?' (DbType = Int32), @p49='?' (Precision = 18) (Scale = 2) (DbType = Decimal), @p50='?' (DbType = Int32), @p51='?' (DbType = Int32), @p52='?' (Size = 50), @p53='?' (DbType = Int32), @p54='?' (DbType = DateTime2), @p55='?' (DbType = Int32), @p56='?' (Size = 255), @p57='?' (DbType = Int32), @p58='?' (Precision = 18) (Scale = 4) (DbType = Decimal), @p59='?' (DbType = Int32), @p60='?' (Size = 50), @p61='?' (Size = 50), @p62='?' (Size = 50), @p63='?' (Precision = 18) (Scale = 2) (DbType = Decimal), @p64='?' (DbType = DateTime2), @p65='?' (Size = 20), @p66='?' (DbType = Int32), @p67='?' (Precision = 18) (Scale = 2) (DbType = Decimal), @p68='?' (DbType = Int32), @p69='?' (DbType = Int32), @p71='?' (DbType = Int32), @p70='?' (Size = 20), @p74='?' (DbType = Int32), @p72='?' (Size = 20), @p73='?' (Precision = 18) (Scale = 4) (DbType = Decimal)], CommandType='"Text"', CommandTimeout='30']
SET NOCOUNT ON;
MERGE [Inventory] USING (
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10, @p11, @p12, @p13, @p14, @p15, @p16, 0),
(@p17, @p18, @p19, @p20, @p21, @p22, @p23, @p24, @p25, @p26, @p27, @p28, @p29, @p30, @p31, @p32, @p33, 1)) AS i ([AverageCost], [BatchNumber], [CreatedDate], [ExpiryDate], [LastCost], [LastCountDate], [LocationID], [ProductID], [QuantityAvailable], [QuantityInTransit], [QuantityOnHand], [QuantityOnOrder], [QuantityReserved], [SerialNumber], [UpdatedDate], [VariantID], [WarehouseID], _Position) ON 1=0
WHEN NOT MATCHED THEN
INSERT ([AverageCost], [BatchNumber], [CreatedDate], [ExpiryDate], [LastCost], [LastCountDate], [LocationID], [ProductID], [QuantityAvailable], [QuantityInTransit], [QuantityOnHand], [QuantityOnOrder], [QuantityReserved], [SerialNumber], [UpdatedDate], [VariantID], [WarehouseID])
VALUES (i.[AverageCost], i.[BatchNumber], i.[CreatedDate], i.[ExpiryDate], i.[LastCost], i.[LastCountDate], i.[LocationID], i.[ProductID], i.[QuantityAvailable], i.[QuantityInTransit], i.[QuantityOnHand], i.[QuantityOnOrder], i.[QuantityReserved], i.[SerialNumber], i.[UpdatedDate], i.[VariantID], i.[WarehouseID])
OUTPUT INSERTED.[InventoryID], i._Position;
MERGE [InventoryTransaction] USING (
VALUES (@p34, @p35, @p36, @p37, @p38, @p39, @p40, @p41, @p42, @p43, @p44, @p45, @p46, @p47, @p48, @p49, @p50, @p51, 0),
(@p52, @p53, @p54, @p55, @p56, @p57, @p58, @p59, @p60, @p61, @p62, @p63, @p64, @p65, @p66, @p67, @p68, @p69, 1)) AS i ([BatchNumber], [CreatedByUserID], [CreatedDate], [LocationID], [Notes], [ProductID], [Quantity], [ReferenceID], [ReferenceNumber], [ReferenceType], [SerialNumber], [TotalCost], [TransactionDate], [TransactionType], [UOMID], [UnitCost], [VariantID], [WarehouseID], _Position) ON 1=0
WHEN NOT MATCHED THEN
INSERT ([BatchNumber], [CreatedByUserID], [CreatedDate], [LocationID], [Notes], [ProductID], [Quantity], [ReferenceID], [ReferenceNumber], [ReferenceType], [SerialNumber], [TotalCost], [TransactionDate], [TransactionType], [UOMID], [UnitCost], [VariantID], [WarehouseID])
VALUES (i.[BatchNumber], i.[CreatedByUserID], i.[CreatedDate], i.[LocationID], i.[Notes], i.[ProductID], i.[Quantity], i.[ReferenceID], i.[ReferenceNumber], i.[ReferenceType], i.[SerialNumber], i.[TotalCost], i.[TransactionDate], i.[TransactionType], i.[UOMID], i.[UnitCost], i.[VariantID], i.[WarehouseID])
OUTPUT INSERTED.[TransactionID], i._Position;
UPDATE [PurchaseOrder] SET [OrderStatus] = @p70
OUTPUT 1
WHERE [PurchaseOrderID] = @p71;
UPDATE [PurchaseOrderItem] SET [ItemStatus] = @p72, [ReceivedQuantity] = @p73
OUTPUT 1
WHERE [PurchaseOrderItemID] = @p74;
2025-07-30 00:11:59.185 +03:00 [ERR] An exception occurred in the database while saving changes for context type 'nCarry.Web.Data.ApplicationDbContext'.
Microsoft.EntityFrameworkCore.DbUpdateException: An error occurred while saving the entity changes. See the inner exception for details.
 ---> Microsoft.Data.SqlClient.SqlException (0x80131904): Invalid column name 'UOMID'.
   at Microsoft.Data.SqlClient.SqlCommand.<>c.<ExecuteDbDataReaderAsync>b__211_0(Task`1 result)
   at System.Threading.Tasks.ContinuationResultTaskFromResultTask`2.InnerInvoke()
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
ClientConnectionId:fea86392-d351-4210-845b-9ef8be1b6bac
Error Number:207,State:1,Class:16
   --- End of inner exception stack trace ---
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.SqlServer.Update.Internal.SqlServerModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalDatabase.SaveChangesAsync(IList`1 entries, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(IList`1 entriesToSave, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(StateManager stateManager, Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.SqlServer.Storage.Internal.SqlServerExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.DbContext.SaveChangesAsync(Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
Microsoft.EntityFrameworkCore.DbUpdateException: An error occurred while saving the entity changes. See the inner exception for details.
 ---> Microsoft.Data.SqlClient.SqlException (0x80131904): Invalid column name 'UOMID'.
   at Microsoft.Data.SqlClient.SqlCommand.<>c.<ExecuteDbDataReaderAsync>b__211_0(Task`1 result)
   at System.Threading.Tasks.ContinuationResultTaskFromResultTask`2.InnerInvoke()
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
ClientConnectionId:fea86392-d351-4210-845b-9ef8be1b6bac
Error Number:207,State:1,Class:16
   --- End of inner exception stack trace ---
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.SqlServer.Update.Internal.SqlServerModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalDatabase.SaveChangesAsync(IList`1 entries, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(IList`1 entriesToSave, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(StateManager stateManager, Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.SqlServer.Storage.Internal.SqlServerExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.DbContext.SaveChangesAsync(Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
2025-07-30 00:11:59.504 +03:00 [INF] HTTP POST /GoodsReceipt/Create responded 200 in 1857.1460 ms
2025-07-30 00:12:06.404 +03:00 [INF] HTTP GET /GoodsReceipt responded 302 in 3.8350 ms
2025-07-30 08:41:39.683 +03:00 [INF] Starting database seeding.
2025-07-30 08:41:41.526 +03:00 [INF] Admin user already exists. Seeding is not required.
2025-07-30 08:41:41.571 +03:00 [ERR] Hosting failed to start
System.IO.IOException: Failed to bind to address http://127.0.0.1:5298: address already in use.
 ---> Microsoft.AspNetCore.Connections.AddressInUseException: Address already in use
 ---> System.Net.Sockets.SocketException (48): Address already in use
   at System.Net.Sockets.Socket.DoBind(EndPoint endPointSnapshot, SocketAddress socketAddress)
   at System.Net.Sockets.Socket.Bind(EndPoint localEP)
   at Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets.SocketTransportOptions.CreateDefaultBoundListenSocket(EndPoint endpoint)
   at Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets.SocketConnectionListener.Bind()
   --- End of inner exception stack trace ---
   at Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets.SocketConnectionListener.Bind()
   at Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets.SocketTransportFactory.BindAsync(EndPoint endpoint, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.Infrastructure.TransportManager.BindAsync(EndPoint endPoint, ConnectionDelegate connectionDelegate, EndpointConfig endpointConfig, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.KestrelServerImpl.<>c__DisplayClass28_0`1.<<StartAsync>g__OnBind|0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.AddressBinder.BindEndpointAsync(ListenOptions endpoint, AddressBindContext context, CancellationToken cancellationToken)
   --- End of inner exception stack trace ---
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.AddressBinder.BindEndpointAsync(ListenOptions endpoint, AddressBindContext context, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.LocalhostListenOptions.BindAsync(AddressBindContext context, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.AddressBinder.AddressesStrategy.BindAsync(AddressBindContext context, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.AddressBinder.BindAsync(ListenOptions[] listenOptions, AddressBindContext context, Func`2 useHttps, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.KestrelServerImpl.BindAsync(CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.KestrelServerImpl.StartAsync[TContext](IHttpApplication`1 application, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Hosting.GenericWebHostService.StartAsync(CancellationToken cancellationToken)
   at Microsoft.Extensions.Hosting.Internal.Host.<StartAsync>b__15_1(IHostedService service, CancellationToken token)
   at Microsoft.Extensions.Hosting.Internal.Host.ForeachService[T](IEnumerable`1 services, CancellationToken token, Boolean concurrent, Boolean abortOnFirstException, List`1 exceptions, Func`3 operation)
2025-07-30 08:41:41.578 +03:00 [FTL] Application terminated unexpectedly
System.IO.IOException: Failed to bind to address http://127.0.0.1:5298: address already in use.
 ---> Microsoft.AspNetCore.Connections.AddressInUseException: Address already in use
 ---> System.Net.Sockets.SocketException (48): Address already in use
   at System.Net.Sockets.Socket.DoBind(EndPoint endPointSnapshot, SocketAddress socketAddress)
   at System.Net.Sockets.Socket.Bind(EndPoint localEP)
   at Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets.SocketTransportOptions.CreateDefaultBoundListenSocket(EndPoint endpoint)
   at Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets.SocketConnectionListener.Bind()
   --- End of inner exception stack trace ---
   at Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets.SocketConnectionListener.Bind()
   at Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets.SocketTransportFactory.BindAsync(EndPoint endpoint, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.Infrastructure.TransportManager.BindAsync(EndPoint endPoint, ConnectionDelegate connectionDelegate, EndpointConfig endpointConfig, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.KestrelServerImpl.<>c__DisplayClass28_0`1.<<StartAsync>g__OnBind|0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.AddressBinder.BindEndpointAsync(ListenOptions endpoint, AddressBindContext context, CancellationToken cancellationToken)
   --- End of inner exception stack trace ---
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.AddressBinder.BindEndpointAsync(ListenOptions endpoint, AddressBindContext context, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.LocalhostListenOptions.BindAsync(AddressBindContext context, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.AddressBinder.AddressesStrategy.BindAsync(AddressBindContext context, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.AddressBinder.BindAsync(ListenOptions[] listenOptions, AddressBindContext context, Func`2 useHttps, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.KestrelServerImpl.BindAsync(CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.KestrelServerImpl.StartAsync[TContext](IHttpApplication`1 application, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Hosting.GenericWebHostService.StartAsync(CancellationToken cancellationToken)
   at Microsoft.Extensions.Hosting.Internal.Host.<StartAsync>b__15_1(IHostedService service, CancellationToken token)
   at Microsoft.Extensions.Hosting.Internal.Host.ForeachService[T](IEnumerable`1 services, CancellationToken token, Boolean concurrent, Boolean abortOnFirstException, List`1 exceptions, Func`3 operation)
   at Microsoft.Extensions.Hosting.Internal.Host.StartAsync(CancellationToken cancellationToken)
   at Microsoft.Extensions.Hosting.HostingAbstractionsHostExtensions.RunAsync(IHost host, CancellationToken token)
   at Microsoft.Extensions.Hosting.HostingAbstractionsHostExtensions.RunAsync(IHost host, CancellationToken token)
   at Microsoft.Extensions.Hosting.HostingAbstractionsHostExtensions.Run(IHost host)
   at Program.<Main>$(String[] args) in /Users/<USER>/Source/nCarry/nCarry/nCarry.Web/Program.cs:line 82
2025-07-30 08:44:11.561 +03:00 [INF] Starting database seeding.
2025-07-30 08:44:13.439 +03:00 [INF] Admin user already exists. Seeding is not required.
2025-07-30 08:44:13.483 +03:00 [ERR] Hosting failed to start
System.IO.IOException: Failed to bind to address http://127.0.0.1:5298: address already in use.
 ---> Microsoft.AspNetCore.Connections.AddressInUseException: Address already in use
 ---> System.Net.Sockets.SocketException (48): Address already in use
   at System.Net.Sockets.Socket.DoBind(EndPoint endPointSnapshot, SocketAddress socketAddress)
   at System.Net.Sockets.Socket.Bind(EndPoint localEP)
   at Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets.SocketTransportOptions.CreateDefaultBoundListenSocket(EndPoint endpoint)
   at Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets.SocketConnectionListener.Bind()
   --- End of inner exception stack trace ---
   at Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets.SocketConnectionListener.Bind()
   at Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets.SocketTransportFactory.BindAsync(EndPoint endpoint, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.Infrastructure.TransportManager.BindAsync(EndPoint endPoint, ConnectionDelegate connectionDelegate, EndpointConfig endpointConfig, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.KestrelServerImpl.<>c__DisplayClass28_0`1.<<StartAsync>g__OnBind|0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.AddressBinder.BindEndpointAsync(ListenOptions endpoint, AddressBindContext context, CancellationToken cancellationToken)
   --- End of inner exception stack trace ---
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.AddressBinder.BindEndpointAsync(ListenOptions endpoint, AddressBindContext context, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.LocalhostListenOptions.BindAsync(AddressBindContext context, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.AddressBinder.AddressesStrategy.BindAsync(AddressBindContext context, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.AddressBinder.BindAsync(ListenOptions[] listenOptions, AddressBindContext context, Func`2 useHttps, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.KestrelServerImpl.BindAsync(CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.KestrelServerImpl.StartAsync[TContext](IHttpApplication`1 application, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Hosting.GenericWebHostService.StartAsync(CancellationToken cancellationToken)
   at Microsoft.Extensions.Hosting.Internal.Host.<StartAsync>b__15_1(IHostedService service, CancellationToken token)
   at Microsoft.Extensions.Hosting.Internal.Host.ForeachService[T](IEnumerable`1 services, CancellationToken token, Boolean concurrent, Boolean abortOnFirstException, List`1 exceptions, Func`3 operation)
2025-07-30 08:44:13.490 +03:00 [FTL] Application terminated unexpectedly
System.IO.IOException: Failed to bind to address http://127.0.0.1:5298: address already in use.
 ---> Microsoft.AspNetCore.Connections.AddressInUseException: Address already in use
 ---> System.Net.Sockets.SocketException (48): Address already in use
   at System.Net.Sockets.Socket.DoBind(EndPoint endPointSnapshot, SocketAddress socketAddress)
   at System.Net.Sockets.Socket.Bind(EndPoint localEP)
   at Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets.SocketTransportOptions.CreateDefaultBoundListenSocket(EndPoint endpoint)
   at Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets.SocketConnectionListener.Bind()
   --- End of inner exception stack trace ---
   at Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets.SocketConnectionListener.Bind()
   at Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets.SocketTransportFactory.BindAsync(EndPoint endpoint, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.Infrastructure.TransportManager.BindAsync(EndPoint endPoint, ConnectionDelegate connectionDelegate, EndpointConfig endpointConfig, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.KestrelServerImpl.<>c__DisplayClass28_0`1.<<StartAsync>g__OnBind|0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.AddressBinder.BindEndpointAsync(ListenOptions endpoint, AddressBindContext context, CancellationToken cancellationToken)
   --- End of inner exception stack trace ---
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.AddressBinder.BindEndpointAsync(ListenOptions endpoint, AddressBindContext context, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.LocalhostListenOptions.BindAsync(AddressBindContext context, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.AddressBinder.AddressesStrategy.BindAsync(AddressBindContext context, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.AddressBinder.BindAsync(ListenOptions[] listenOptions, AddressBindContext context, Func`2 useHttps, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.KestrelServerImpl.BindAsync(CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.KestrelServerImpl.StartAsync[TContext](IHttpApplication`1 application, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Hosting.GenericWebHostService.StartAsync(CancellationToken cancellationToken)
   at Microsoft.Extensions.Hosting.Internal.Host.<StartAsync>b__15_1(IHostedService service, CancellationToken token)
   at Microsoft.Extensions.Hosting.Internal.Host.ForeachService[T](IEnumerable`1 services, CancellationToken token, Boolean concurrent, Boolean abortOnFirstException, List`1 exceptions, Func`3 operation)
   at Microsoft.Extensions.Hosting.Internal.Host.StartAsync(CancellationToken cancellationToken)
   at Microsoft.Extensions.Hosting.HostingAbstractionsHostExtensions.RunAsync(IHost host, CancellationToken token)
   at Microsoft.Extensions.Hosting.HostingAbstractionsHostExtensions.RunAsync(IHost host, CancellationToken token)
   at Microsoft.Extensions.Hosting.HostingAbstractionsHostExtensions.Run(IHost host)
   at Program.<Main>$(String[] args) in /Users/<USER>/Source/nCarry/nCarry/nCarry.Web/Program.cs:line 82
2025-07-30 08:44:28.914 +03:00 [INF] Starting database seeding.
2025-07-30 08:44:30.861 +03:00 [INF] Admin user already exists. Seeding is not required.
2025-07-30 08:44:48.150 +03:00 [WRN] Failed to determine the https port for redirect.
2025-07-30 08:44:48.191 +03:00 [INF] HTTP GET / responded 302 in 43.4474 ms
2025-07-30 08:46:51.399 +03:00 [INF] HTTP GET /Account/Login responded 200 in 203.9641 ms
2025-07-30 08:47:12.686 +03:00 [INF] HTTP POST /Account/Login responded 302 in 538.4845 ms
2025-07-30 08:47:14.695 +03:00 [INF] HTTP GET /Dashboard responded 200 in 2008.4935 ms
2025-07-30 08:47:14.707 +03:00 [INF] HTTP GET /nCarry.Web.styles.css responded 304 in 7.1299 ms
2025-07-30 08:47:14.874 +03:00 [INF] HTTP GET /Dashboard/GetChartData responded 200 in 134.5037 ms
2025-07-30 08:47:32.942 +03:00 [INF] HTTP GET /GoodsReceipt responded 200 in 298.7001 ms
2025-07-30 08:47:43.448 +03:00 [INF] HTTP GET /GoodsReceipt/Create responded 200 in 356.4930 ms
2025-07-30 08:47:55.175 +03:00 [INF] HTTP GET /GoodsReceipt/Create responded 200 in 930.3383 ms
2025-07-30 08:49:19.882 +03:00 [ERR] An exception occurred in the database while saving changes for context type 'nCarry.Web.Data.ApplicationDbContext'.
Microsoft.EntityFrameworkCore.DbUpdateException: An error occurred while saving the entity changes. See the inner exception for details.
 ---> System.InvalidCastException: Unable to cast object of type 'System.Int64' to type 'System.Int32'.
   at Microsoft.Data.SqlClient.SqlBuffer.get_Int32()
   at lambda_method3895(Closure, DbDataReader, Int32)
   at Microsoft.EntityFrameworkCore.RelationalPropertyExtensions.GetReaderFieldValue(IProperty property, RelationalDataReader relationalReader, Int32 ordinal, Boolean detailedErrorsEnabled)
   at Microsoft.EntityFrameworkCore.Update.ModificationCommand.PropagateResults(RelationalDataReader relationalReader)
   at Microsoft.EntityFrameworkCore.Update.AffectedCountModificationCommandBatch.ConsumeResultSetAsync(Int32 startCommandIndex, RelationalDataReader reader, CancellationToken cancellationToken)
   --- End of inner exception stack trace ---
   at Microsoft.EntityFrameworkCore.Update.AffectedCountModificationCommandBatch.ConsumeResultSetAsync(Int32 startCommandIndex, RelationalDataReader reader, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.AffectedCountModificationCommandBatch.ConsumeAsync(RelationalDataReader reader, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.SqlServer.Update.Internal.SqlServerModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalDatabase.SaveChangesAsync(IList`1 entries, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(IList`1 entriesToSave, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(StateManager stateManager, Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.SqlServer.Storage.Internal.SqlServerExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.DbContext.SaveChangesAsync(Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
Microsoft.EntityFrameworkCore.DbUpdateException: An error occurred while saving the entity changes. See the inner exception for details.
 ---> System.InvalidCastException: Unable to cast object of type 'System.Int64' to type 'System.Int32'.
   at Microsoft.Data.SqlClient.SqlBuffer.get_Int32()
   at lambda_method3895(Closure, DbDataReader, Int32)
   at Microsoft.EntityFrameworkCore.RelationalPropertyExtensions.GetReaderFieldValue(IProperty property, RelationalDataReader relationalReader, Int32 ordinal, Boolean detailedErrorsEnabled)
   at Microsoft.EntityFrameworkCore.Update.ModificationCommand.PropagateResults(RelationalDataReader relationalReader)
   at Microsoft.EntityFrameworkCore.Update.AffectedCountModificationCommandBatch.ConsumeResultSetAsync(Int32 startCommandIndex, RelationalDataReader reader, CancellationToken cancellationToken)
   --- End of inner exception stack trace ---
   at Microsoft.EntityFrameworkCore.Update.AffectedCountModificationCommandBatch.ConsumeResultSetAsync(Int32 startCommandIndex, RelationalDataReader reader, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.AffectedCountModificationCommandBatch.ConsumeAsync(RelationalDataReader reader, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.SqlServer.Update.Internal.SqlServerModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalDatabase.SaveChangesAsync(IList`1 entries, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(IList`1 entriesToSave, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(StateManager stateManager, Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.SqlServer.Storage.Internal.SqlServerExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.DbContext.SaveChangesAsync(Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
2025-07-30 08:49:20.274 +03:00 [INF] HTTP POST /GoodsReceipt/Create responded 200 in 2015.3641 ms
2025-07-30 08:54:23.618 +03:00 [INF] Starting database seeding.
2025-07-30 08:54:27.018 +03:00 [INF] Admin user already exists. Seeding is not required.
2025-07-30 08:54:40.298 +03:00 [WRN] Failed to determine the https port for redirect.
2025-07-30 08:54:40.342 +03:00 [INF] HTTP GET / responded 302 in 46.8875 ms
2025-07-30 08:54:55.991 +03:00 [INF] HTTP GET /GoodsReceipt/Create responded 200 in 1283.3754 ms
2025-07-30 08:55:56.559 +03:00 [ERR] An exception occurred in the database while saving changes for context type 'nCarry.Web.Data.ApplicationDbContext'.
Microsoft.EntityFrameworkCore.DbUpdateException: An error occurred while saving the entity changes. See the inner exception for details.
 ---> Microsoft.Data.SqlClient.SqlException (0x80131904): Violation of UNIQUE KEY constraint 'UQ__Inventor__E733A2BF4077D565'. Cannot insert duplicate key in object 'dbo.InventoryTransaction'. The duplicate key value is (IT202507300001).
   at Microsoft.Data.SqlClient.SqlConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.SqlInternalConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.TdsParser.ThrowExceptionAndWarning(TdsParserStateObject stateObj, Boolean callerHasConnectionLock, Boolean asyncClose)
   at Microsoft.Data.SqlClient.TdsParser.TryRun(RunBehavior runBehavior, SqlCommand cmdHandler, SqlDataReader dataStream, BulkCopySimpleResultSet bulkCopyHandler, TdsParserStateObject stateObj, Boolean& dataReady)
   at Microsoft.Data.SqlClient.SqlDataReader.TryHasMoreRows(Boolean& moreRows)
   at Microsoft.Data.SqlClient.SqlDataReader.TryReadInternal(Boolean setTimeout, Boolean& more)
   at Microsoft.Data.SqlClient.SqlDataReader.ReadAsyncExecute(Task task, Object state)
   at Microsoft.Data.SqlClient.SqlDataReader.InvokeAsyncCall[T](SqlDataReaderBaseAsyncCallContext`1 context)
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Update.AffectedCountModificationCommandBatch.ConsumeResultSetAsync(Int32 startCommandIndex, RelationalDataReader reader, CancellationToken cancellationToken)
ClientConnectionId:9888242d-7ac5-40fc-b44b-f4de9b2c0528
Error Number:2627,State:1,Class:14
   --- End of inner exception stack trace ---
   at Microsoft.EntityFrameworkCore.Update.AffectedCountModificationCommandBatch.ConsumeResultSetAsync(Int32 startCommandIndex, RelationalDataReader reader, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.AffectedCountModificationCommandBatch.ConsumeAsync(RelationalDataReader reader, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.SqlServer.Update.Internal.SqlServerModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalDatabase.SaveChangesAsync(IList`1 entries, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(IList`1 entriesToSave, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(StateManager stateManager, Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.SqlServer.Storage.Internal.SqlServerExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.DbContext.SaveChangesAsync(Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
Microsoft.EntityFrameworkCore.DbUpdateException: An error occurred while saving the entity changes. See the inner exception for details.
 ---> Microsoft.Data.SqlClient.SqlException (0x80131904): Violation of UNIQUE KEY constraint 'UQ__Inventor__E733A2BF4077D565'. Cannot insert duplicate key in object 'dbo.InventoryTransaction'. The duplicate key value is (IT202507300001).
   at Microsoft.Data.SqlClient.SqlConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.SqlInternalConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.TdsParser.ThrowExceptionAndWarning(TdsParserStateObject stateObj, Boolean callerHasConnectionLock, Boolean asyncClose)
   at Microsoft.Data.SqlClient.TdsParser.TryRun(RunBehavior runBehavior, SqlCommand cmdHandler, SqlDataReader dataStream, BulkCopySimpleResultSet bulkCopyHandler, TdsParserStateObject stateObj, Boolean& dataReady)
   at Microsoft.Data.SqlClient.SqlDataReader.TryHasMoreRows(Boolean& moreRows)
   at Microsoft.Data.SqlClient.SqlDataReader.TryReadInternal(Boolean setTimeout, Boolean& more)
   at Microsoft.Data.SqlClient.SqlDataReader.ReadAsyncExecute(Task task, Object state)
   at Microsoft.Data.SqlClient.SqlDataReader.InvokeAsyncCall[T](SqlDataReaderBaseAsyncCallContext`1 context)
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Update.AffectedCountModificationCommandBatch.ConsumeResultSetAsync(Int32 startCommandIndex, RelationalDataReader reader, CancellationToken cancellationToken)
ClientConnectionId:9888242d-7ac5-40fc-b44b-f4de9b2c0528
Error Number:2627,State:1,Class:14
   --- End of inner exception stack trace ---
   at Microsoft.EntityFrameworkCore.Update.AffectedCountModificationCommandBatch.ConsumeResultSetAsync(Int32 startCommandIndex, RelationalDataReader reader, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.AffectedCountModificationCommandBatch.ConsumeAsync(RelationalDataReader reader, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.SqlServer.Update.Internal.SqlServerModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalDatabase.SaveChangesAsync(IList`1 entries, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(IList`1 entriesToSave, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(StateManager stateManager, Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.SqlServer.Storage.Internal.SqlServerExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.DbContext.SaveChangesAsync(Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
2025-07-30 08:55:56.930 +03:00 [INF] HTTP POST /GoodsReceipt/Create responded 200 in 2124.5032 ms
2025-07-30 08:58:26.456 +03:00 [INF] Starting database seeding.
2025-07-30 08:58:29.060 +03:00 [INF] Admin user already exists. Seeding is not required.
2025-07-30 08:58:35.532 +03:00 [WRN] Failed to determine the https port for redirect.
2025-07-30 08:58:35.575 +03:00 [INF] HTTP GET / responded 302 in 45.0479 ms
2025-07-30 08:58:50.146 +03:00 [INF] HTTP GET /GoodsReceipt/Create responded 200 in 1233.9970 ms
2025-07-30 08:59:55.897 +03:00 [INF] HTTP POST /GoodsReceipt/Create responded 302 in 1690.3105 ms
2025-07-30 08:59:56.270 +03:00 [INF] HTTP GET /GoodsReceipt/Details/11 responded 200 in 370.6784 ms
2025-07-30 09:05:14.474 +03:00 [ERR] Failed executing DbCommand (35,008ms) [Parameters=[@__id_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [s0].[ReceiptID], [s0].[CreatedByUserID], [s0].[CreatedDate], [s0].[Notes], [s0].[PostedByUserID], [s0].[PostedDate], [s0].[PurchaseOrderID], [s0].[QualityCheckByUserID], [s0].[QualityCheckDate], [s0].[QualityCheckStatus], [s0].[ReceiptDate], [s0].[ReceiptNumber], [s0].[ReceiptType], [s0].[ReceivedByUserID], [s0].[ReceiptStatus], [s0].[SupplierDeliveryNote], [s0].[SupplierID], [s0].[UpdatedByUserID], [s0].[UpdatedDate], [s0].[WarehouseID], [s0].[PurchaseOrderID0], [s0].[ApprovalNotes], [s0].[ApprovalStatus], [s0].[ApprovedByUserID], [s0].[ApprovedDate], [s0].[BuyerID], [s0].[CreatedByUserID0], [s0].[CreatedDate0], [s0].[Currency], [s0].[DeletedByUserID], [s0].[DeletedDate], [s0].[DeliveryAddressID], [s0].[DeliveryInstructions], [s0].[DeliveryWarehouseID], [s0].[DiscountAmount], [s0].[ExchangeRate], [s0].[ExpectedDate], [s0].[FreightAmount], [s0].[FreightTerms], [s0].[InternalNotes], [s0].[IsDeleted], [s0].[Notes0], [s0].[OrderDate], [s0].[OrderStatus], [s0].[OrderType], [s0].[PaymentMethod], [s0].[PaymentTerms], [s0].[PromisedDate], [s0].[PurchaseOrderNumber], [s0].[RequiredDate], [s0].[RequisitionID], [s0].[ShippingMethod], [s0].[SubTotal], [s0].[SupplierID0], [s0].[SupplierReference], [s0].[TaxAmount], [s0].[TermsAndConditions], [s0].[TotalAmount], [s0].[UpdatedByUserID0], [s0].[UpdatedDate0], [s0].[SupplierID1], [s0].[APIEnabled], [s0].[BankAccountDetails], [s0].[BlockedByUserID], [s0].[BlockedDate], [s0].[BlockedReason], [s0].[CertificateExpiryDate], [s0].[CertificateNumber], [s0].[ComplianceStatus], [s0].[ContactEmail], [s0].[ContactLandline], [s0].[ContactMobile], [s0].[ContactPhone], [s0].[CreatedByUserID1], [s0].[CreatedDate1], [s0].[DeletedByUserID0], [s0].[DeletedDate0], [s0].[DeliveryDays], [s0].[DeliveryScore], [s0].[EDIEnabled], [s0].[EarlyPaymentDiscountDays], [s0].[EarlyPaymentDiscountPercent], [s0].[InsuranceExpiryDate], [s0].[InsurancePolicyNumber], [s0].[IntegrationKey], [s0].[InternalNotes0], [s0].[IsActive], [s0].[IsDeleted0], [s0].[IsPreferred], [s0].[LastOrderDate], [s0].[LeadTimeDays], [s0].[MinimumOrderValue], [s0].[Notes1], [s0].[OrderCutoffTime], [s0].[OutstandingBalance], [s0].[PaymentMethodID], [s0].[PaymentTermDays], [s0].[PaymentTerms0], [s0].[PreferredCurrency], [s0].[PriceScore], [s0].[QualityScore], [s0].[Rating], [s0].[ReferenceCode], [s0].[ResponsibleContact], [s0].[ReturnPolicy], [s0].[SupplierAddress], [s0].[SupplierAddress2], [s0].[SupplierCity], [s0].[SupplierCode], [s0].[SupplierCountry], [s0].[SupplierGroup], [s0].[SupplierName], [s0].[SupplierPostCode], [s0].[SupplierState], [s0].[SupplierStatus], [s0].[SupplierType], [s0].[TaxNumber], [s0].[TaxRate], [s0].[TotalPurchaseValue], [s0].[TradingName], [s0].[UpdatedByUserID1], [s0].[UpdatedDate1], [s0].[WarrantyTerms], [s0].[Website], [s0].[WithholdingTaxRate], [s0].[YTDPurchases], [s0].[UserID], [s0].[AccessFailedCount], [s0].[CreatedByUserID2], [s0].[CreatedDate2], [s0].[DateFormat], [s0].[DeletedByUserID1], [s0].[DeletedDate1], [s0].[Department], [s0].[DisplayName], [s0].[Email], [s0].[FirstName], [s0].[IsActive0], [s0].[IsDeleted1], [s0].[IsLocked], [s0].[JobTitle], [s0].[Language], [s0].[LastActivityDate], [s0].[LastLoginDate], [s0].[LastName], [s0].[LockoutEnd], [s0].[MobileNumber], [s0].[MustChangePassword], [s0].[PasswordChangedDate], [s0].[PasswordHash], [s0].[PasswordSalt], [s0].[PhoneNumber], [s0].[ProfilePicture], [s0].[ThemePreference], [s0].[TimeZone], [s0].[TwoFactorEnabled], [s0].[TwoFactorSecret], [s0].[UpdatedByUserID2], [s0].[UpdatedDate2], [s0].[Username], [s0].[WarehouseID0], [s0].[Address1], [s0].[Address2], [s0].[AllowNegativeStock], [s0].[City], [s0].[Country], [s0].[CreatedByUserID3], [s0].[CreatedDate3], [s0].[CurrentUtilization], [s0].[Description], [s0].[Email0], [s0].[IsActive1], [s0].[IsDefault], [s0].[ManagerName], [s0].[OperatingHours], [s0].[Phone], [s0].[PostCode], [s0].[State], [s0].[StorageCapacity], [s0].[TimeZone0], [s0].[TotalArea], [s0].[UpdatedByUserID3], [s0].[UpdatedDate3], [s0].[WarehouseCode], [s0].[WarehouseName], [s0].[WarehouseType], [s1].[ReceiptItemID], [s1].[AcceptedQuantity], [s1].[BatchNumber], [s1].[Description], [s1].[ExpiryDate], [s1].[ReceiptID], [s1].[LineNumber], [s1].[LocationID], [s1].[Notes], [s1].[ProductID], [s1].[ProductName], [s1].[PurchaseOrderItemID], [s1].[QualityStatus], [s1].[ReceivedQuantity], [s1].[RejectedQuantity], [s1].[RejectionReason], [s1].[SerialNumber], [s1].[SortOrder], [s1].[UOMID], [s1].[UnitCost], [s1].[UnitPrice], [s1].[VariantID], [s1].[ProductID0], [s1].[AllowBackorder], [s1].[AllowPreorder], [s1].[Brand], [s1].[CategoryID], [s1].[CommodityCode], [s1].[CountryOfOrigin], [s1].[CreatedByUserID], [s1].[CreatedDate], [s1].[Currency], [s1].[DeletedByUserID], [s1].[DeletedDate], [s1].[Description0], [s1].[DimensionUOMID], [s1].[HSCode], [s1].[HasVariants], [s1].[Height], [s1].[InventoryUOMID], [s1].[IsActive], [s1].[IsDeleted], [s1].[IsKit], [s1].[IsTaxExempt], [s1].[LeadTimeDays], [s1].[Length], [s1].[ListPrice], [s1].[Manufacturer], [s1].[ManufacturerPartNumber], [s1].[MaxStockLevel], [s1].[MinStockLevel], [s1].[Notes0], [s1].[ProductCode], [s1].[ProductName0], [s1].[ProductType], [s1].[PurchaseUOMID], [s1].[ReorderPoint], [s1].[ReorderQuantity], [s1].[RequiresBatch], [s1].[RequiresSerial], [s1].[SalesUOMID], [s1].[SellPrice], [s1].[ShelfLifeDays], [s1].[ShortDescription], [s1].[StandardCost], [s1].[Status], [s1].[TaxCodeID], [s1].[TrackInventory], [s1].[UpdatedByUserID], [s1].[UpdatedDate], [s1].[Volume], [s1].[VolumeUOMID], [s1].[WarrantyDays], [s1].[Weight], [s1].[WeightUOMID], [s1].[Width], [s1].[UOMID0], [s1].[BaseUnit], [s1].[ConversionFactor], [s1].[CreatedDate0], [s1].[DecimalPlaces], [s1].[IsActive0], [s1].[Symbol], [s1].[UOMCode], [s1].[UOMName], [s1].[UOMType], [s1].[UpdatedDate0]
FROM (
    SELECT TOP(1) [g].[ReceiptID], [g].[CreatedByUserID], [g].[CreatedDate], [g].[Notes], [g].[PostedByUserID], [g].[PostedDate], [g].[PurchaseOrderID], [g].[QualityCheckByUserID], [g].[QualityCheckDate], [g].[QualityCheckStatus], [g].[ReceiptDate], [g].[ReceiptNumber], [g].[ReceiptType], [g].[ReceivedByUserID], [g].[ReceiptStatus], [g].[SupplierDeliveryNote], [g].[SupplierID], [g].[UpdatedByUserID], [g].[UpdatedDate], [g].[WarehouseID], [p].[PurchaseOrderID] AS [PurchaseOrderID0], [p].[ApprovalNotes], [p].[ApprovalStatus], [p].[ApprovedByUserID], [p].[ApprovedDate], [p].[BuyerID], [p].[CreatedByUserID] AS [CreatedByUserID0], [p].[CreatedDate] AS [CreatedDate0], [p].[Currency], [p].[DeletedByUserID], [p].[DeletedDate], [p].[DeliveryAddressID], [p].[DeliveryInstructions], [p].[DeliveryWarehouseID], [p].[DiscountAmount], [p].[ExchangeRate], [p].[ExpectedDate], [p].[FreightAmount], [p].[FreightTerms], [p].[InternalNotes], [p].[IsDeleted], [p].[Notes] AS [Notes0], [p].[OrderDate], [p].[OrderStatus], [p].[OrderType], [p].[PaymentMethod], [p].[PaymentTerms], [p].[PromisedDate], [p].[PurchaseOrderNumber], [p].[RequiredDate], [p].[RequisitionID], [p].[ShippingMethod], [p].[SubTotal], [p].[SupplierID] AS [SupplierID0], [p].[SupplierReference], [p].[TaxAmount], [p].[TermsAndConditions], [p].[TotalAmount], [p].[UpdatedByUserID] AS [UpdatedByUserID0], [p].[UpdatedDate] AS [UpdatedDate0], [s].[SupplierID] AS [SupplierID1], [s].[APIEnabled], [s].[BankAccountDetails], [s].[BlockedByUserID], [s].[BlockedDate], [s].[BlockedReason], [s].[CertificateExpiryDate], [s].[CertificateNumber], [s].[ComplianceStatus], [s].[ContactEmail], [s].[ContactLandline], [s].[ContactMobile], [s].[ContactPhone], [s].[CreatedByUserID] AS [CreatedByUserID1], [s].[CreatedDate] AS [CreatedDate1], [s].[DeletedByUserID] AS [DeletedByUserID0], [s].[DeletedDate] AS [DeletedDate0], [s].[DeliveryDays], [s].[DeliveryScore], [s].[EDIEnabled], [s].[EarlyPaymentDiscountDays], [s].[EarlyPaymentDiscountPercent], [s].[InsuranceExpiryDate], [s].[InsurancePolicyNumber], [s].[IntegrationKey], [s].[InternalNotes] AS [InternalNotes0], [s].[IsActive], [s].[IsDeleted] AS [IsDeleted0], [s].[IsPreferred], [s].[LastOrderDate], [s].[LeadTimeDays], [s].[MinimumOrderValue], [s].[Notes] AS [Notes1], [s].[OrderCutoffTime], [s].[OutstandingBalance], [s].[PaymentMethodID], [s].[PaymentTermDays], [s].[PaymentTerms] AS [PaymentTerms0], [s].[PreferredCurrency], [s].[PriceScore], [s].[QualityScore], [s].[Rating], [s].[ReferenceCode], [s].[ResponsibleContact], [s].[ReturnPolicy], [s].[SupplierAddress], [s].[SupplierAddress2], [s].[SupplierCity], [s].[SupplierCode], [s].[SupplierCountry], [s].[SupplierGroup], [s].[SupplierName], [s].[SupplierPostCode], [s].[SupplierState], [s].[SupplierStatus], [s].[SupplierType], [s].[TaxNumber], [s].[TaxRate], [s].[TotalPurchaseValue], [s].[TradingName], [s].[UpdatedByUserID] AS [UpdatedByUserID1], [s].[UpdatedDate] AS [UpdatedDate1], [s].[WarrantyTerms], [s].[Website], [s].[WithholdingTaxRate], [s].[YTDPurchases], [u].[UserID], [u].[AccessFailedCount], [u].[CreatedByUserID] AS [CreatedByUserID2], [u].[CreatedDate] AS [CreatedDate2], [u].[DateFormat], [u].[DeletedByUserID] AS [DeletedByUserID1], [u].[DeletedDate] AS [DeletedDate1], [u].[Department], [u].[DisplayName], [u].[Email], [u].[FirstName], [u].[IsActive] AS [IsActive0], [u].[IsDeleted] AS [IsDeleted1], [u].[IsLocked], [u].[JobTitle], [u].[Language], [u].[LastActivityDate], [u].[LastLoginDate], [u].[LastName], [u].[LockoutEnd], [u].[MobileNumber], [u].[MustChangePassword], [u].[PasswordChangedDate], [u].[PasswordHash], [u].[PasswordSalt], [u].[PhoneNumber], [u].[ProfilePicture], [u].[ThemePreference], [u].[TimeZone], [u].[TwoFactorEnabled], [u].[TwoFactorSecret], [u].[UpdatedByUserID] AS [UpdatedByUserID2], [u].[UpdatedDate] AS [UpdatedDate2], [u].[Username], [w].[WarehouseID] AS [WarehouseID0], [w].[Address1], [w].[Address2], [w].[AllowNegativeStock], [w].[City], [w].[Country], [w].[CreatedByUserID] AS [CreatedByUserID3], [w].[CreatedDate] AS [CreatedDate3], [w].[CurrentUtilization], [w].[Description], [w].[Email] AS [Email0], [w].[IsActive] AS [IsActive1], [w].[IsDefault], [w].[ManagerName], [w].[OperatingHours], [w].[Phone], [w].[PostCode], [w].[State], [w].[StorageCapacity], [w].[TimeZone] AS [TimeZone0], [w].[TotalArea], [w].[UpdatedByUserID] AS [UpdatedByUserID3], [w].[UpdatedDate] AS [UpdatedDate3], [w].[WarehouseCode], [w].[WarehouseName], [w].[WarehouseType]
    FROM [GoodsReceipt] AS [g]
    LEFT JOIN [PurchaseOrder] AS [p] ON [g].[PurchaseOrderID] = [p].[PurchaseOrderID]
    LEFT JOIN [Supplier] AS [s] ON [p].[SupplierID] = [s].[SupplierID]
    INNER JOIN [User] AS [u] ON [g].[ReceivedByUserID] = [u].[UserID]
    INNER JOIN [Warehouse] AS [w] ON [g].[WarehouseID] = [w].[WarehouseID]
    WHERE [g].[ReceiptID] = @__id_0
) AS [s0]
LEFT JOIN (
    SELECT [g0].[ReceiptItemID], [g0].[AcceptedQuantity], [g0].[BatchNumber], [g0].[Description], [g0].[ExpiryDate], [g0].[ReceiptID], [g0].[LineNumber], [g0].[LocationID], [g0].[Notes], [g0].[ProductID], [g0].[ProductName], [g0].[PurchaseOrderItemID], [g0].[QualityStatus], [g0].[ReceivedQuantity], [g0].[RejectedQuantity], [g0].[RejectionReason], [g0].[SerialNumber], [g0].[SortOrder], [g0].[UOMID], [g0].[UnitCost], [g0].[UnitPrice], [g0].[VariantID], [p0].[ProductID] AS [ProductID0], [p0].[AllowBackorder], [p0].[AllowPreorder], [p0].[Brand], [p0].[CategoryID], [p0].[CommodityCode], [p0].[CountryOfOrigin], [p0].[CreatedByUserID], [p0].[CreatedDate], [p0].[Currency], [p0].[DeletedByUserID], [p0].[DeletedDate], [p0].[Description] AS [Description0], [p0].[DimensionUOMID], [p0].[HSCode], [p0].[HasVariants], [p0].[Height], [p0].[InventoryUOMID], [p0].[IsActive], [p0].[IsDeleted], [p0].[IsKit], [p0].[IsTaxExempt], [p0].[LeadTimeDays], [p0].[Length], [p0].[ListPrice], [p0].[Manufacturer], [p0].[ManufacturerPartNumber], [p0].[MaxStockLevel], [p0].[MinStockLevel], [p0].[Notes] AS [Notes0], [p0].[ProductCode], [p0].[ProductName] AS [ProductName0], [p0].[ProductType], [p0].[PurchaseUOMID], [p0].[ReorderPoint], [p0].[ReorderQuantity], [p0].[RequiresBatch], [p0].[RequiresSerial], [p0].[SalesUOMID], [p0].[SellPrice], [p0].[ShelfLifeDays], [p0].[ShortDescription], [p0].[StandardCost], [p0].[Status], [p0].[TaxCodeID], [p0].[TrackInventory], [p0].[UpdatedByUserID], [p0].[UpdatedDate], [p0].[Volume], [p0].[VolumeUOMID], [p0].[WarrantyDays], [p0].[Weight], [p0].[WeightUOMID], [p0].[Width], [u0].[UOMID] AS [UOMID0], [u0].[BaseUnit], [u0].[ConversionFactor], [u0].[CreatedDate] AS [CreatedDate0], [u0].[DecimalPlaces], [u0].[IsActive] AS [IsActive0], [u0].[Symbol], [u0].[UOMCode], [u0].[UOMName], [u0].[UOMType], [u0].[UpdatedDate] AS [UpdatedDate0]
    FROM [GoodsReceiptItem] AS [g0]
    INNER JOIN [Product] AS [p0] ON [g0].[ProductID] = [p0].[ProductID]
    INNER JOIN [UnitOfMeasure] AS [u0] ON [g0].[UOMID] = [u0].[UOMID]
) AS [s1] ON [s0].[ReceiptID] = [s1].[ReceiptID]
ORDER BY [s0].[ReceiptID], [s0].[PurchaseOrderID0], [s0].[SupplierID1], [s0].[UserID], [s0].[WarehouseID0], [s1].[ReceiptItemID], [s1].[ProductID0]
2025-07-30 09:05:14.488 +03:00 [ERR] An exception occurred while iterating over the results of a query for context type 'nCarry.Web.Data.ApplicationDbContext'.
Microsoft.Data.SqlClient.SqlException (0x80131904): Execution Timeout Expired.  The timeout period elapsed prior to completion of the operation or the server is not responding.
 ---> System.ComponentModel.Win32Exception (258): Unknown error: 258
   at Microsoft.Data.SqlClient.SqlCommand.<>c.<ExecuteDbDataReaderAsync>b__211_0(Task`1 result)
   at System.Threading.Tasks.ContinuationResultTaskFromResultTask`2.InnerInvoke()
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.InitializeReaderAsync(AsyncEnumerator enumerator, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.SqlServer.Storage.Internal.SqlServerExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
ClientConnectionId:079b7ad0-9dd3-4b6d-a636-bc59570e5ead
Error Number:-2,State:0,Class:11
Microsoft.Data.SqlClient.SqlException (0x80131904): Execution Timeout Expired.  The timeout period elapsed prior to completion of the operation or the server is not responding.
 ---> System.ComponentModel.Win32Exception (258): Unknown error: 258
   at Microsoft.Data.SqlClient.SqlCommand.<>c.<ExecuteDbDataReaderAsync>b__211_0(Task`1 result)
   at System.Threading.Tasks.ContinuationResultTaskFromResultTask`2.InnerInvoke()
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.InitializeReaderAsync(AsyncEnumerator enumerator, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.SqlServer.Storage.Internal.SqlServerExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
ClientConnectionId:079b7ad0-9dd3-4b6d-a636-bc59570e5ead
Error Number:-2,State:0,Class:11
2025-07-30 09:05:14.490 +03:00 [ERR] HTTP GET /GoodsReceipt/Print/11 responded 500 in 35065.7022 ms
Microsoft.Data.SqlClient.SqlException (0x80131904): Execution Timeout Expired.  The timeout period elapsed prior to completion of the operation or the server is not responding.
 ---> System.ComponentModel.Win32Exception (258): Unknown error: 258
   at Microsoft.Data.SqlClient.SqlCommand.<>c.<ExecuteDbDataReaderAsync>b__211_0(Task`1 result)
   at System.Threading.Tasks.ContinuationResultTaskFromResultTask`2.InnerInvoke()
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.InitializeReaderAsync(AsyncEnumerator enumerator, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.SqlServer.Storage.Internal.SqlServerExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
   at Microsoft.EntityFrameworkCore.Query.ShapedQueryCompilingExpressionVisitor.SingleOrDefaultAsync[TSource](IAsyncEnumerable`1 asyncEnumerable, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.ShapedQueryCompilingExpressionVisitor.SingleOrDefaultAsync[TSource](IAsyncEnumerable`1 asyncEnumerable, CancellationToken cancellationToken)
   at nCarry.Web.Controllers.GoodsReceiptController.Print(Int32 id) in /Users/<USER>/Source/nCarry/nCarry/nCarry.Web/Controllers/GoodsReceiptController.cs:line 367
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.TaskOfIActionResultExecutor.Execute(ActionContext actionContext, IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeActionMethodAsync>g__Awaited|12_0(ControllerActionInvoker invoker, ValueTask`1 actionResultValueTask)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeInnerFilterAsync>g__Awaited|13_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextResourceFilter>g__Awaited|25_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.Rethrow(ResourceExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeFilterPipelineAsync>g__Awaited|20_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Session.SessionMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Session.SessionMiddleware.Invoke(HttpContext context)
   at Serilog.AspNetCore.RequestLoggingMiddleware.Invoke(HttpContext httpContext)
ClientConnectionId:079b7ad0-9dd3-4b6d-a636-bc59570e5ead
Error Number:-2,State:0,Class:11
2025-07-30 09:05:14.496 +03:00 [ERR] An unhandled exception has occurred while executing the request.
Microsoft.Data.SqlClient.SqlException (0x80131904): Execution Timeout Expired.  The timeout period elapsed prior to completion of the operation or the server is not responding.
 ---> System.ComponentModel.Win32Exception (258): Unknown error: 258
   at Microsoft.Data.SqlClient.SqlCommand.<>c.<ExecuteDbDataReaderAsync>b__211_0(Task`1 result)
   at System.Threading.Tasks.ContinuationResultTaskFromResultTask`2.InnerInvoke()
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.InitializeReaderAsync(AsyncEnumerator enumerator, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.SqlServer.Storage.Internal.SqlServerExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
   at Microsoft.EntityFrameworkCore.Query.ShapedQueryCompilingExpressionVisitor.SingleOrDefaultAsync[TSource](IAsyncEnumerable`1 asyncEnumerable, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.ShapedQueryCompilingExpressionVisitor.SingleOrDefaultAsync[TSource](IAsyncEnumerable`1 asyncEnumerable, CancellationToken cancellationToken)
   at nCarry.Web.Controllers.GoodsReceiptController.Print(Int32 id) in /Users/<USER>/Source/nCarry/nCarry/nCarry.Web/Controllers/GoodsReceiptController.cs:line 367
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.TaskOfIActionResultExecutor.Execute(ActionContext actionContext, IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeActionMethodAsync>g__Awaited|12_0(ControllerActionInvoker invoker, ValueTask`1 actionResultValueTask)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeInnerFilterAsync>g__Awaited|13_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextResourceFilter>g__Awaited|25_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.Rethrow(ResourceExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeFilterPipelineAsync>g__Awaited|20_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Session.SessionMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Session.SessionMiddleware.Invoke(HttpContext context)
   at Serilog.AspNetCore.RequestLoggingMiddleware.Invoke(HttpContext httpContext)
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
ClientConnectionId:079b7ad0-9dd3-4b6d-a636-bc59570e5ead
Error Number:-2,State:0,Class:11
2025-07-30 09:09:08.981 +03:00 [INF] Starting database seeding.
2025-07-30 09:09:10.955 +03:00 [INF] Admin user already exists. Seeding is not required.
2025-07-30 09:09:18.410 +03:00 [WRN] Failed to determine the https port for redirect.
2025-07-30 09:09:18.454 +03:00 [INF] HTTP GET / responded 302 in 46.2600 ms
2025-07-30 09:09:30.028 +03:00 [INF] HTTP GET /GoodsReceipt/Create responded 200 in 1335.1237 ms
2025-07-30 09:16:40.564 +03:00 [INF] HTTP GET /GoodsReceipt/Create responded 200 in 939.4445 ms
2025-07-30 09:17:09.590 +03:00 [INF] HTTP GET /GoodsReceipt/Create responded 200 in 438.7347 ms
2025-07-30 09:18:11.121 +03:00 [ERR] An exception occurred in the database while saving changes for context type 'nCarry.Web.Data.ApplicationDbContext'.
Microsoft.EntityFrameworkCore.DbUpdateException: An error occurred while saving the entity changes. See the inner exception for details.
 ---> Microsoft.Data.SqlClient.SqlException (0x80131904): Violation of UNIQUE KEY constraint 'UQ__Inventor__E733A2BF4077D565'. Cannot insert duplicate key in object 'dbo.InventoryTransaction'. The duplicate key value is (IT202507300001-001).
   at Microsoft.Data.SqlClient.SqlConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.SqlInternalConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.TdsParser.ThrowExceptionAndWarning(TdsParserStateObject stateObj, Boolean callerHasConnectionLock, Boolean asyncClose)
   at Microsoft.Data.SqlClient.TdsParser.TryRun(RunBehavior runBehavior, SqlCommand cmdHandler, SqlDataReader dataStream, BulkCopySimpleResultSet bulkCopyHandler, TdsParserStateObject stateObj, Boolean& dataReady)
   at Microsoft.Data.SqlClient.SqlDataReader.TryHasMoreRows(Boolean& moreRows)
   at Microsoft.Data.SqlClient.SqlDataReader.TryReadInternal(Boolean setTimeout, Boolean& more)
   at Microsoft.Data.SqlClient.SqlDataReader.ReadAsyncExecute(Task task, Object state)
   at Microsoft.Data.SqlClient.SqlDataReader.InvokeAsyncCall[T](SqlDataReaderBaseAsyncCallContext`1 context)
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Update.AffectedCountModificationCommandBatch.ConsumeResultSetAsync(Int32 startCommandIndex, RelationalDataReader reader, CancellationToken cancellationToken)
ClientConnectionId:5d29b504-a981-46c6-9ead-54e2fdb738b6
Error Number:2627,State:1,Class:14
   --- End of inner exception stack trace ---
   at Microsoft.EntityFrameworkCore.Update.AffectedCountModificationCommandBatch.ConsumeResultSetAsync(Int32 startCommandIndex, RelationalDataReader reader, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.AffectedCountModificationCommandBatch.ConsumeAsync(RelationalDataReader reader, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.SqlServer.Update.Internal.SqlServerModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalDatabase.SaveChangesAsync(IList`1 entries, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(IList`1 entriesToSave, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(StateManager stateManager, Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.SqlServer.Storage.Internal.SqlServerExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.DbContext.SaveChangesAsync(Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
Microsoft.EntityFrameworkCore.DbUpdateException: An error occurred while saving the entity changes. See the inner exception for details.
 ---> Microsoft.Data.SqlClient.SqlException (0x80131904): Violation of UNIQUE KEY constraint 'UQ__Inventor__E733A2BF4077D565'. Cannot insert duplicate key in object 'dbo.InventoryTransaction'. The duplicate key value is (IT202507300001-001).
   at Microsoft.Data.SqlClient.SqlConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.SqlInternalConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.TdsParser.ThrowExceptionAndWarning(TdsParserStateObject stateObj, Boolean callerHasConnectionLock, Boolean asyncClose)
   at Microsoft.Data.SqlClient.TdsParser.TryRun(RunBehavior runBehavior, SqlCommand cmdHandler, SqlDataReader dataStream, BulkCopySimpleResultSet bulkCopyHandler, TdsParserStateObject stateObj, Boolean& dataReady)
   at Microsoft.Data.SqlClient.SqlDataReader.TryHasMoreRows(Boolean& moreRows)
   at Microsoft.Data.SqlClient.SqlDataReader.TryReadInternal(Boolean setTimeout, Boolean& more)
   at Microsoft.Data.SqlClient.SqlDataReader.ReadAsyncExecute(Task task, Object state)
   at Microsoft.Data.SqlClient.SqlDataReader.InvokeAsyncCall[T](SqlDataReaderBaseAsyncCallContext`1 context)
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Update.AffectedCountModificationCommandBatch.ConsumeResultSetAsync(Int32 startCommandIndex, RelationalDataReader reader, CancellationToken cancellationToken)
ClientConnectionId:5d29b504-a981-46c6-9ead-54e2fdb738b6
Error Number:2627,State:1,Class:14
   --- End of inner exception stack trace ---
   at Microsoft.EntityFrameworkCore.Update.AffectedCountModificationCommandBatch.ConsumeResultSetAsync(Int32 startCommandIndex, RelationalDataReader reader, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.AffectedCountModificationCommandBatch.ConsumeAsync(RelationalDataReader reader, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.SqlServer.Update.Internal.SqlServerModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalDatabase.SaveChangesAsync(IList`1 entries, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(IList`1 entriesToSave, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(StateManager stateManager, Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.SqlServer.Storage.Internal.SqlServerExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.DbContext.SaveChangesAsync(Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
2025-07-30 09:18:11.546 +03:00 [INF] HTTP POST /GoodsReceipt/Create responded 200 in 1958.9685 ms
2025-07-30 09:19:22.525 +03:00 [INF] HTTP GET /GoodsReceipt responded 200 in 222.3442 ms
2025-07-30 09:19:32.254 +03:00 [INF] HTTP GET /GoodsReceipt/Details/11 responded 200 in 212.8368 ms
2025-07-30 09:22:03.849 +03:00 [INF] HTTP GET /GoodsReceipt/Create responded 200 in 435.9859 ms
2025-07-30 09:22:29.522 +03:00 [INF] HTTP GET /GoodsReceipt/Create responded 200 in 1181.6360 ms
2025-07-30 09:25:33.669 +03:00 [ERR] An exception occurred while iterating over the results of a query for context type 'nCarry.Web.Data.ApplicationDbContext'.
System.Data.SqlTypes.SqlNullValueException: Data is Null. This method or property cannot be called on Null values.
   at Microsoft.Data.SqlClient.SqlDataReader.GetString(Int32 i)
   at lambda_method2180(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
System.Data.SqlTypes.SqlNullValueException: Data is Null. This method or property cannot be called on Null values.
   at Microsoft.Data.SqlClient.SqlDataReader.GetString(Int32 i)
   at lambda_method2180(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
2025-07-30 09:25:34.041 +03:00 [INF] HTTP POST /GoodsReceipt/Create responded 200 in 1009.6965 ms
2025-07-30 09:27:06.081 +03:00 [INF] HTTP GET /GoodsReceipt/Create responded 200 in 521.5704 ms
2025-07-30 09:28:07.920 +03:00 [ERR] An exception occurred while iterating over the results of a query for context type 'nCarry.Web.Data.ApplicationDbContext'.
System.Data.SqlTypes.SqlNullValueException: Data is Null. This method or property cannot be called on Null values.
   at Microsoft.Data.SqlClient.SqlDataReader.GetString(Int32 i)
   at lambda_method2180(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
System.Data.SqlTypes.SqlNullValueException: Data is Null. This method or property cannot be called on Null values.
   at Microsoft.Data.SqlClient.SqlDataReader.GetString(Int32 i)
   at lambda_method2180(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
2025-07-30 09:28:08.320 +03:00 [INF] HTTP POST /GoodsReceipt/Create responded 200 in 1050.8219 ms
2025-07-30 09:30:45.323 +03:00 [INF] HTTP GET /GoodsReceipt/Create responded 200 in 385.0235 ms
