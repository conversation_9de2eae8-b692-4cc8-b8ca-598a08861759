2025-07-29 10:37:15.191 +03:00 [INF] HTTP GET /Product/Edit/35 responded 302 in 23.5999 ms
2025-07-29 10:37:15.223 +03:00 [INF] HTTP GET /Account/Lo<PERSON> responded 200 in 19.6842 ms
2025-07-29 10:38:05.051 +03:00 [INF] HTTP POST /Account/Login responded 302 in 544.9928 ms
2025-07-29 10:38:05.935 +03:00 [INF] HTTP GET /Product/Edit/35 responded 200 in 881.5392 ms
2025-07-29 10:38:05.948 +03:00 [INF] HTTP GET /nCarry.Web.styles.css responded 304 in 4.2648 ms
2025-07-29 10:39:07.196 +03:00 [INF] HTTP POST /Product/Edit/35 responded 302 in 174.2387 ms
2025-07-29 10:39:07.275 +03:00 [INF] HTTP GET /Product responded 200 in 76.0805 ms
2025-07-29 10:45:57.728 +03:00 [INF] HTTP GET /CustomerPriceList responded 200 in 492.0907 ms
2025-07-29 10:47:04.484 +03:00 [INF] HTTP GET /CustomerPriceList/Create responded 200 in 214.1237 ms
2025-07-29 10:48:10.915 +03:00 [INF] HTTP POST /CustomerPriceList/Create responded 200 in 180.0110 ms
2025-07-29 10:48:34.765 +03:00 [INF] HTTP POST /CustomerPriceList/Create responded 200 in 145.5460 ms
2025-07-29 10:49:01.999 +03:00 [INF] HTTP POST /CustomerPriceList/Create responded 200 in 163.3599 ms
2025-07-29 10:49:24.935 +03:00 [INF] HTTP GET /CustomerPriceList responded 200 in 77.8318 ms
2025-07-29 10:49:52.818 +03:00 [INF] HTTP GET /SalesOrder responded 200 in 112.5265 ms
2025-07-29 10:50:02.758 +03:00 [ERR] Failed executing DbCommand (80ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [w].[WarehouseID], [w].[Address1], [w].[Address2], [w].[AllowNegativeStock], [w].[City], [w].[Country], [w].[CreatedByUserID], [w].[CreatedDate], [w].[Currency], [w].[CurrentUtilization], [w].[Description], [w].[Email], [w].[IsActive], [w].[IsDefault], [w].[ManagerName], [w].[OperatingHours], [w].[Phone], [w].[PostCode], [w].[State], [w].[StorageCapacity], [w].[TimeZone], [w].[TotalArea], [w].[UpdatedByUserID], [w].[UpdatedDate], [w].[WarehouseCode], [w].[WarehouseName], [w].[WarehouseType]
FROM [Warehouse] AS [w]
WHERE [w].[IsActive] = CAST(1 AS bit)
ORDER BY [w].[WarehouseName]
2025-07-29 10:50:02.781 +03:00 [ERR] An exception occurred while iterating over the results of a query for context type 'nCarry.Web.Data.ApplicationDbContext'.
Microsoft.Data.SqlClient.SqlException (0x80131904): Invalid column name 'Currency'.
   at System.Threading.Tasks.ContinuationResultTaskFromResultTask`2.InnerInvoke()
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.InitializeReaderAsync(AsyncEnumerator enumerator, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.SqlServer.Storage.Internal.SqlServerExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
ClientConnectionId:575333d0-278a-49ac-9a66-daa293ee8e08
Error Number:207,State:1,Class:16
Microsoft.Data.SqlClient.SqlException (0x80131904): Invalid column name 'Currency'.
   at System.Threading.Tasks.ContinuationResultTaskFromResultTask`2.InnerInvoke()
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.InitializeReaderAsync(AsyncEnumerator enumerator, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.SqlServer.Storage.Internal.SqlServerExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
ClientConnectionId:575333d0-278a-49ac-9a66-daa293ee8e08
Error Number:207,State:1,Class:16
2025-07-29 10:50:02.783 +03:00 [ERR] HTTP GET /SalesOrder/Edit/18 responded 500 in 476.7860 ms
Microsoft.Data.SqlClient.SqlException (0x80131904): Invalid column name 'Currency'.
   at System.Threading.Tasks.ContinuationResultTaskFromResultTask`2.InnerInvoke()
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.InitializeReaderAsync(AsyncEnumerator enumerator, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.SqlServer.Storage.Internal.SqlServerExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at nCarry.Web.Controllers.SalesOrderController.PopulateDropdownsAsync(SalesOrderEditViewModel viewModel, Nullable`1 customerID) in /Users/<USER>/Source/nCarry/nCarry/nCarry.Web/Controllers/SalesOrderController.cs:line 392
   at nCarry.Web.Controllers.SalesOrderController.Edit(Nullable`1 id) in /Users/<USER>/Source/nCarry/nCarry/nCarry.Web/Controllers/SalesOrderController.cs:line 161
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.TaskOfIActionResultExecutor.Execute(ActionContext actionContext, IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeActionMethodAsync>g__Awaited|12_0(ControllerActionInvoker invoker, ValueTask`1 actionResultValueTask)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeInnerFilterAsync>g__Awaited|13_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextResourceFilter>g__Awaited|25_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.Rethrow(ResourceExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeFilterPipelineAsync>g__Awaited|20_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at Serilog.AspNetCore.RequestLoggingMiddleware.Invoke(HttpContext httpContext)
ClientConnectionId:575333d0-278a-49ac-9a66-daa293ee8e08
Error Number:207,State:1,Class:16
2025-07-29 10:50:02.791 +03:00 [ERR] An unhandled exception has occurred while executing the request.
Microsoft.Data.SqlClient.SqlException (0x80131904): Invalid column name 'Currency'.
   at System.Threading.Tasks.ContinuationResultTaskFromResultTask`2.InnerInvoke()
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.InitializeReaderAsync(AsyncEnumerator enumerator, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.SqlServer.Storage.Internal.SqlServerExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at nCarry.Web.Controllers.SalesOrderController.PopulateDropdownsAsync(SalesOrderEditViewModel viewModel, Nullable`1 customerID) in /Users/<USER>/Source/nCarry/nCarry/nCarry.Web/Controllers/SalesOrderController.cs:line 392
   at nCarry.Web.Controllers.SalesOrderController.Edit(Nullable`1 id) in /Users/<USER>/Source/nCarry/nCarry/nCarry.Web/Controllers/SalesOrderController.cs:line 161
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.TaskOfIActionResultExecutor.Execute(ActionContext actionContext, IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeActionMethodAsync>g__Awaited|12_0(ControllerActionInvoker invoker, ValueTask`1 actionResultValueTask)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeInnerFilterAsync>g__Awaited|13_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextResourceFilter>g__Awaited|25_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.Rethrow(ResourceExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeFilterPipelineAsync>g__Awaited|20_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at Serilog.AspNetCore.RequestLoggingMiddleware.Invoke(HttpContext httpContext)
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
ClientConnectionId:575333d0-278a-49ac-9a66-daa293ee8e08
Error Number:207,State:1,Class:16
2025-07-29 10:51:21.124 +03:00 [INF] HTTP GET /SalesOrder responded 200 in 83.4424 ms
2025-07-29 10:51:30.930 +03:00 [ERR] Failed executing DbCommand (73ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [w].[WarehouseID], [w].[Address1], [w].[Address2], [w].[AllowNegativeStock], [w].[City], [w].[Country], [w].[CreatedByUserID], [w].[CreatedDate], [w].[Currency], [w].[CurrentUtilization], [w].[Description], [w].[Email], [w].[IsActive], [w].[IsDefault], [w].[ManagerName], [w].[OperatingHours], [w].[Phone], [w].[PostCode], [w].[State], [w].[StorageCapacity], [w].[TimeZone], [w].[TotalArea], [w].[UpdatedByUserID], [w].[UpdatedDate], [w].[WarehouseCode], [w].[WarehouseName], [w].[WarehouseType]
FROM [Warehouse] AS [w]
WHERE [w].[IsActive] = CAST(1 AS bit)
ORDER BY [w].[WarehouseName]
2025-07-29 10:51:30.932 +03:00 [ERR] An exception occurred while iterating over the results of a query for context type 'nCarry.Web.Data.ApplicationDbContext'.
Microsoft.Data.SqlClient.SqlException (0x80131904): Invalid column name 'Currency'.
   at System.Threading.Tasks.ContinuationResultTaskFromResultTask`2.InnerInvoke()
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.InitializeReaderAsync(AsyncEnumerator enumerator, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.SqlServer.Storage.Internal.SqlServerExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
ClientConnectionId:575333d0-278a-49ac-9a66-daa293ee8e08
Error Number:207,State:1,Class:16
Microsoft.Data.SqlClient.SqlException (0x80131904): Invalid column name 'Currency'.
   at System.Threading.Tasks.ContinuationResultTaskFromResultTask`2.InnerInvoke()
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.InitializeReaderAsync(AsyncEnumerator enumerator, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.SqlServer.Storage.Internal.SqlServerExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
ClientConnectionId:575333d0-278a-49ac-9a66-daa293ee8e08
Error Number:207,State:1,Class:16
2025-07-29 10:51:30.934 +03:00 [ERR] HTTP GET /SalesOrder/Edit/18 responded 500 in 371.9225 ms
Microsoft.Data.SqlClient.SqlException (0x80131904): Invalid column name 'Currency'.
   at System.Threading.Tasks.ContinuationResultTaskFromResultTask`2.InnerInvoke()
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.InitializeReaderAsync(AsyncEnumerator enumerator, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.SqlServer.Storage.Internal.SqlServerExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at nCarry.Web.Controllers.SalesOrderController.PopulateDropdownsAsync(SalesOrderEditViewModel viewModel, Nullable`1 customerID) in /Users/<USER>/Source/nCarry/nCarry/nCarry.Web/Controllers/SalesOrderController.cs:line 392
   at nCarry.Web.Controllers.SalesOrderController.Edit(Nullable`1 id) in /Users/<USER>/Source/nCarry/nCarry/nCarry.Web/Controllers/SalesOrderController.cs:line 161
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.TaskOfIActionResultExecutor.Execute(ActionContext actionContext, IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeActionMethodAsync>g__Awaited|12_0(ControllerActionInvoker invoker, ValueTask`1 actionResultValueTask)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeInnerFilterAsync>g__Awaited|13_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextResourceFilter>g__Awaited|25_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.Rethrow(ResourceExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeFilterPipelineAsync>g__Awaited|20_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at Serilog.AspNetCore.RequestLoggingMiddleware.Invoke(HttpContext httpContext)
ClientConnectionId:575333d0-278a-49ac-9a66-daa293ee8e08
Error Number:207,State:1,Class:16
2025-07-29 10:51:30.939 +03:00 [ERR] An unhandled exception has occurred while executing the request.
Microsoft.Data.SqlClient.SqlException (0x80131904): Invalid column name 'Currency'.
   at System.Threading.Tasks.ContinuationResultTaskFromResultTask`2.InnerInvoke()
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.InitializeReaderAsync(AsyncEnumerator enumerator, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.SqlServer.Storage.Internal.SqlServerExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at nCarry.Web.Controllers.SalesOrderController.PopulateDropdownsAsync(SalesOrderEditViewModel viewModel, Nullable`1 customerID) in /Users/<USER>/Source/nCarry/nCarry/nCarry.Web/Controllers/SalesOrderController.cs:line 392
   at nCarry.Web.Controllers.SalesOrderController.Edit(Nullable`1 id) in /Users/<USER>/Source/nCarry/nCarry/nCarry.Web/Controllers/SalesOrderController.cs:line 161
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.TaskOfIActionResultExecutor.Execute(ActionContext actionContext, IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeActionMethodAsync>g__Awaited|12_0(ControllerActionInvoker invoker, ValueTask`1 actionResultValueTask)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeInnerFilterAsync>g__Awaited|13_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextResourceFilter>g__Awaited|25_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.Rethrow(ResourceExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeFilterPipelineAsync>g__Awaited|20_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at Serilog.AspNetCore.RequestLoggingMiddleware.Invoke(HttpContext httpContext)
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
ClientConnectionId:575333d0-278a-49ac-9a66-daa293ee8e08
Error Number:207,State:1,Class:16
2025-07-29 10:55:52.996 +03:00 [INF] HTTP GET /SalesOrder responded 200 in 509.6927 ms
2025-07-29 10:56:03.817 +03:00 [INF] HTTP GET /Dashboard responded 200 in 1288.3904 ms
2025-07-29 10:56:03.942 +03:00 [INF] HTTP GET /Dashboard/GetChartData responded 200 in 74.7952 ms
2025-07-29 11:17:51.862 +03:00 [INF] HTTP GET /CustomerPriceList responded 200 in 523.2375 ms
2025-07-29 11:17:59.871 +03:00 [INF] HTTP GET /CustomerPriceList/Create responded 200 in 177.6632 ms
2025-07-29 11:19:07.270 +03:00 [INF] HTTP POST /CustomerPriceList/Create responded 200 in 144.4807 ms
2025-07-29 11:19:34.160 +03:00 [INF] HTTP POST /CustomerPriceList/Create responded 200 in 153.7408 ms
2025-07-29 11:19:43.359 +03:00 [INF] HTTP GET /CustomerPriceList responded 200 in 79.9027 ms
2025-07-29 11:28:03.898 +03:00 [INF] HTTP GET /CustomerPriceList responded 200 in 43968.2256 ms
2025-07-29 11:43:07.420 +03:00 [INF] HTTP GET /CustomerPriceList/Create responded 200 in 489.1280 ms
2025-07-29 11:44:15.208 +03:00 [INF] HTTP POST /CustomerPriceList/Create responded 200 in 139.0063 ms
2025-07-29 11:51:22.329 +03:00 [INF] Starting database seeding.
2025-07-29 11:51:24.371 +03:00 [INF] Admin user already exists. Seeding is not required.
2025-07-29 11:51:45.081 +03:00 [INF] HTTP GET / responded 200 in 24.2802 ms
2025-07-29 11:51:45.138 +03:00 [INF] HTTP GET /nCarry.Web.styles.css responded 304 in 2.5710 ms
2025-07-29 11:52:09.216 +03:00 [ERR] Failed executing DbCommand (86ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [c].[PriceListID], [c].[CreatedByUserID], [c].[CreatedDate], [c].[Currency], [c].[CustomerGroupID], [c].[CustomerID], [c].[Description], [c].[DiscountPercent], [c].[EffectiveFrom], [c].[EffectiveTo], [c].[IsActive], [c].[MarkupPercent], [c].[PriceListCode], [c].[PriceListName], [c].[Priority], [c].[UpdatedByUserID], [c].[UpdatedDate], [c0].[CustomerID], [c0].[BlockedByUserID], [c0].[BlockedDate], [c0].[BlockedReason], [c0].[ContactEmail], [c0].[ContactLandline], [c0].[ContactMobile], [c0].[ContactPhone], [c0].[CreatedByUserID], [c0].[CreatedDate], [c0].[CreditLimit], [c0].[CreditReviewDate], [c0].[CreditStatus], [c0].[CurrencyCode], [c0].[CustomerAddress], [c0].[CustomerAddress2], [c0].[CustomerCity], [c0].[CustomerCode], [c0].[CustomerCountry], [c0].[CustomerGroup], [c0].[CustomerName], [c0].[CustomerPostCode], [c0].[CustomerSince], [c0].[CustomerState], [c0].[CustomerStatus], [c0].[CustomerType], [c0].[DeletedByUserID], [c0].[DeletedDate], [c0].[DeliveryInstructions], [c0].[DeliveryTimePreference], [c0].[DiscountPercent], [c0].[InternalNotes], [c0].[IsActive], [c0].[IsDeleted], [c0].[LastOrderDate], [c0].[LeadSource], [c0].[LoyaltyPoints], [c0].[Notes], [c0].[OutstandingBalance], [c0].[PaymentMethodID], [c0].[PaymentTerm], [c0].[PreferredCarrierID], [c0].[PriceListID], [c0].[Rating], [c0].[ReferenceCode], [c0].[ResponsibleContact], [c0].[SalesRepID], [c0].[TaxExempt], [c0].[TaxNumber], [c0].[TerritoryID], [c0].[TotalOrderValue], [c0].[TradingName], [c0].[UpdatedByUserID], [c0].[UpdatedDate], [c0].[Website], [c0].[YTDSales], [c1].[GroupID], [c1].[CreatedByUserID], [c1].[CreatedDate], [c1].[Description], [c1].[DiscountPercent], [c1].[GroupName], [c1].[IsActive], [c1].[PriceListID], [c1].[UpdatedByUserID], [c1].[UpdatedDate]
FROM [CustomerPriceList] AS [c]
LEFT JOIN [Customer] AS [c0] ON [c].[CustomerID] = [c0].[CustomerID]
LEFT JOIN [CustomerGroup] AS [c1] ON [c].[CustomerGroupID] = [c1].[GroupID]
ORDER BY [c].[CreatedDate] DESC
2025-07-29 11:52:09.218 +03:00 [ERR] An exception occurred while iterating over the results of a query for context type 'nCarry.Web.Data.ApplicationDbContext'.
Microsoft.Data.SqlClient.SqlException (0x80131904): Invalid column name 'CreatedByUserID'.
Invalid column name 'PriceListID'.
Invalid column name 'UpdatedByUserID'.
   at System.Threading.Tasks.ContinuationResultTaskFromResultTask`2.InnerInvoke()
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.InitializeReaderAsync(AsyncEnumerator enumerator, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.SqlServer.Storage.Internal.SqlServerExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
ClientConnectionId:6366cb60-d075-422f-8574-d1941b81369a
Error Number:207,State:1,Class:16
Microsoft.Data.SqlClient.SqlException (0x80131904): Invalid column name 'CreatedByUserID'.
Invalid column name 'PriceListID'.
Invalid column name 'UpdatedByUserID'.
   at System.Threading.Tasks.ContinuationResultTaskFromResultTask`2.InnerInvoke()
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.InitializeReaderAsync(AsyncEnumerator enumerator, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.SqlServer.Storage.Internal.SqlServerExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
ClientConnectionId:6366cb60-d075-422f-8574-d1941b81369a
Error Number:207,State:1,Class:16
2025-07-29 11:52:09.219 +03:00 [ERR] HTTP GET /CustomerPriceList responded 500 in 455.5538 ms
Microsoft.Data.SqlClient.SqlException (0x80131904): Invalid column name 'CreatedByUserID'.
Invalid column name 'PriceListID'.
Invalid column name 'UpdatedByUserID'.
   at System.Threading.Tasks.ContinuationResultTaskFromResultTask`2.InnerInvoke()
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.InitializeReaderAsync(AsyncEnumerator enumerator, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.SqlServer.Storage.Internal.SqlServerExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at nCarry.Web.Controllers.CustomerPriceListController.Index() in /Users/<USER>/Source/nCarry/nCarry/nCarry.Web/Controllers/CustomerPriceListController.cs:line 24
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.TaskOfIActionResultExecutor.Execute(ActionContext actionContext, IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeActionMethodAsync>g__Awaited|12_0(ControllerActionInvoker invoker, ValueTask`1 actionResultValueTask)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeInnerFilterAsync>g__Awaited|13_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextResourceFilter>g__Awaited|25_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.Rethrow(ResourceExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeFilterPipelineAsync>g__Awaited|20_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at Serilog.AspNetCore.RequestLoggingMiddleware.Invoke(HttpContext httpContext)
ClientConnectionId:6366cb60-d075-422f-8574-d1941b81369a
Error Number:207,State:1,Class:16
2025-07-29 11:52:09.225 +03:00 [ERR] An unhandled exception has occurred while executing the request.
Microsoft.Data.SqlClient.SqlException (0x80131904): Invalid column name 'CreatedByUserID'.
Invalid column name 'PriceListID'.
Invalid column name 'UpdatedByUserID'.
   at System.Threading.Tasks.ContinuationResultTaskFromResultTask`2.InnerInvoke()
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.InitializeReaderAsync(AsyncEnumerator enumerator, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.SqlServer.Storage.Internal.SqlServerExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at nCarry.Web.Controllers.CustomerPriceListController.Index() in /Users/<USER>/Source/nCarry/nCarry/nCarry.Web/Controllers/CustomerPriceListController.cs:line 24
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.TaskOfIActionResultExecutor.Execute(ActionContext actionContext, IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeActionMethodAsync>g__Awaited|12_0(ControllerActionInvoker invoker, ValueTask`1 actionResultValueTask)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeInnerFilterAsync>g__Awaited|13_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextResourceFilter>g__Awaited|25_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.Rethrow(ResourceExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeFilterPipelineAsync>g__Awaited|20_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at Serilog.AspNetCore.RequestLoggingMiddleware.Invoke(HttpContext httpContext)
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
ClientConnectionId:6366cb60-d075-422f-8574-d1941b81369a
Error Number:207,State:1,Class:16
2025-07-29 11:52:34.924 +03:00 [INF] HTTP GET /Dashboard responded 200 in 1190.7402 ms
2025-07-29 11:52:35.082 +03:00 [INF] HTTP GET /Dashboard/GetChartData responded 200 in 72.0700 ms
2025-07-29 11:59:50.340 +03:00 [ERR] Failed executing DbCommand (71ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [c].[PriceListID], [c].[CreatedByUserID], [c].[CreatedDate], [c].[Currency], [c].[CustomerGroupID], [c].[CustomerID], [c].[Description], [c].[DiscountPercent], [c].[EffectiveFrom], [c].[EffectiveTo], [c].[IsActive], [c].[MarkupPercent], [c].[PriceListCode], [c].[PriceListName], [c].[Priority], [c].[UpdatedByUserID], [c].[UpdatedDate], [c0].[CustomerID], [c0].[BlockedByUserID], [c0].[BlockedDate], [c0].[BlockedReason], [c0].[ContactEmail], [c0].[ContactLandline], [c0].[ContactMobile], [c0].[ContactPhone], [c0].[CreatedByUserID], [c0].[CreatedDate], [c0].[CreditLimit], [c0].[CreditReviewDate], [c0].[CreditStatus], [c0].[CurrencyCode], [c0].[CustomerAddress], [c0].[CustomerAddress2], [c0].[CustomerCity], [c0].[CustomerCode], [c0].[CustomerCountry], [c0].[CustomerGroup], [c0].[CustomerName], [c0].[CustomerPostCode], [c0].[CustomerSince], [c0].[CustomerState], [c0].[CustomerStatus], [c0].[CustomerType], [c0].[DeletedByUserID], [c0].[DeletedDate], [c0].[DeliveryInstructions], [c0].[DeliveryTimePreference], [c0].[DiscountPercent], [c0].[InternalNotes], [c0].[IsActive], [c0].[IsDeleted], [c0].[LastOrderDate], [c0].[LeadSource], [c0].[LoyaltyPoints], [c0].[Notes], [c0].[OutstandingBalance], [c0].[PaymentMethodID], [c0].[PaymentTerm], [c0].[PreferredCarrierID], [c0].[PriceListID], [c0].[Rating], [c0].[ReferenceCode], [c0].[ResponsibleContact], [c0].[SalesRepID], [c0].[TaxExempt], [c0].[TaxNumber], [c0].[TerritoryID], [c0].[TotalOrderValue], [c0].[TradingName], [c0].[UpdatedByUserID], [c0].[UpdatedDate], [c0].[Website], [c0].[YTDSales], [c1].[GroupID], [c1].[CreatedByUserID], [c1].[CreatedDate], [c1].[Description], [c1].[DiscountPercent], [c1].[GroupName], [c1].[IsActive], [c1].[PriceListID], [c1].[UpdatedByUserID], [c1].[UpdatedDate]
FROM [CustomerPriceList] AS [c]
LEFT JOIN [Customer] AS [c0] ON [c].[CustomerID] = [c0].[CustomerID]
LEFT JOIN [CustomerGroup] AS [c1] ON [c].[CustomerGroupID] = [c1].[GroupID]
ORDER BY [c].[CreatedDate] DESC
2025-07-29 11:59:50.344 +03:00 [ERR] An exception occurred while iterating over the results of a query for context type 'nCarry.Web.Data.ApplicationDbContext'.
Microsoft.Data.SqlClient.SqlException (0x80131904): Invalid column name 'CreatedByUserID'.
Invalid column name 'PriceListID'.
Invalid column name 'UpdatedByUserID'.
   at System.Threading.Tasks.ContinuationResultTaskFromResultTask`2.InnerInvoke()
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.InitializeReaderAsync(AsyncEnumerator enumerator, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.SqlServer.Storage.Internal.SqlServerExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
ClientConnectionId:0b233e16-b51e-45e3-a630-c13d18540c06
Error Number:207,State:1,Class:16
Microsoft.Data.SqlClient.SqlException (0x80131904): Invalid column name 'CreatedByUserID'.
Invalid column name 'PriceListID'.
Invalid column name 'UpdatedByUserID'.
   at System.Threading.Tasks.ContinuationResultTaskFromResultTask`2.InnerInvoke()
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.InitializeReaderAsync(AsyncEnumerator enumerator, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.SqlServer.Storage.Internal.SqlServerExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
ClientConnectionId:0b233e16-b51e-45e3-a630-c13d18540c06
Error Number:207,State:1,Class:16
2025-07-29 11:59:50.347 +03:00 [ERR] HTTP GET /CustomerPriceList responded 500 in 435.6304 ms
Microsoft.Data.SqlClient.SqlException (0x80131904): Invalid column name 'CreatedByUserID'.
Invalid column name 'PriceListID'.
Invalid column name 'UpdatedByUserID'.
   at System.Threading.Tasks.ContinuationResultTaskFromResultTask`2.InnerInvoke()
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.InitializeReaderAsync(AsyncEnumerator enumerator, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.SqlServer.Storage.Internal.SqlServerExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at nCarry.Web.Controllers.CustomerPriceListController.Index() in /Users/<USER>/Source/nCarry/nCarry/nCarry.Web/Controllers/CustomerPriceListController.cs:line 24
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.TaskOfIActionResultExecutor.Execute(ActionContext actionContext, IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeActionMethodAsync>g__Awaited|12_0(ControllerActionInvoker invoker, ValueTask`1 actionResultValueTask)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeInnerFilterAsync>g__Awaited|13_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextResourceFilter>g__Awaited|25_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.Rethrow(ResourceExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeFilterPipelineAsync>g__Awaited|20_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at Serilog.AspNetCore.RequestLoggingMiddleware.Invoke(HttpContext httpContext)
ClientConnectionId:0b233e16-b51e-45e3-a630-c13d18540c06
Error Number:207,State:1,Class:16
2025-07-29 11:59:50.351 +03:00 [ERR] An unhandled exception has occurred while executing the request.
Microsoft.Data.SqlClient.SqlException (0x80131904): Invalid column name 'CreatedByUserID'.
Invalid column name 'PriceListID'.
Invalid column name 'UpdatedByUserID'.
   at System.Threading.Tasks.ContinuationResultTaskFromResultTask`2.InnerInvoke()
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.InitializeReaderAsync(AsyncEnumerator enumerator, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.SqlServer.Storage.Internal.SqlServerExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at nCarry.Web.Controllers.CustomerPriceListController.Index() in /Users/<USER>/Source/nCarry/nCarry/nCarry.Web/Controllers/CustomerPriceListController.cs:line 24
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.TaskOfIActionResultExecutor.Execute(ActionContext actionContext, IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeActionMethodAsync>g__Awaited|12_0(ControllerActionInvoker invoker, ValueTask`1 actionResultValueTask)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeInnerFilterAsync>g__Awaited|13_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextResourceFilter>g__Awaited|25_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.Rethrow(ResourceExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeFilterPipelineAsync>g__Awaited|20_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at Serilog.AspNetCore.RequestLoggingMiddleware.Invoke(HttpContext httpContext)
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
ClientConnectionId:0b233e16-b51e-45e3-a630-c13d18540c06
Error Number:207,State:1,Class:16
2025-07-29 12:00:08.537 +03:00 [INF] Starting database seeding.
2025-07-29 14:37:31.161 +03:00 [INF] Starting database seeding.
2025-07-29 14:37:33.519 +03:00 [INF] Admin user already exists. Seeding is not required.
2025-07-29 15:35:04.147 +03:00 [INF] Starting database seeding.
2025-07-29 15:35:05.817 +03:00 [INF] Admin user already exists. Seeding is not required.
2025-07-29 15:36:25.551 +03:00 [INF] Starting database seeding.
2025-07-29 15:36:27.122 +03:00 [INF] Admin user already exists. Seeding is not required.
2025-07-29 15:37:52.307 +03:00 [INF] Starting database seeding.
2025-07-29 15:37:54.037 +03:00 [INF] Admin user already exists. Seeding is not required.
2025-07-29 15:38:31.235 +03:00 [WRN] Failed to determine the https port for redirect.
2025-07-29 15:38:31.385 +03:00 [INF] HTTP GET / responded 200 in 151.7757 ms
2025-07-29 15:38:31.405 +03:00 [INF] HTTP GET /nCarry.Web.styles.css responded 200 in 5.5793 ms
2025-07-29 15:38:31.409 +03:00 [INF] HTTP GET /js/site.js responded 200 in 4.5305 ms
2025-07-29 15:38:31.409 +03:00 [INF] HTTP GET /css/site.css responded 200 in 10.7920 ms
2025-07-29 15:38:31.412 +03:00 [INF] HTTP GET /lib/bootstrap/dist/js/bootstrap.bundle.min.js responded 200 in 11.9714 ms
2025-07-29 15:38:31.412 +03:00 [INF] HTTP GET /lib/jquery/dist/jquery.min.js responded 200 in 12.6759 ms
2025-07-29 15:38:31.412 +03:00 [INF] HTTP GET /lib/bootstrap/dist/css/bootstrap.min.css responded 200 in 16.5520 ms
2025-07-29 15:38:31.482 +03:00 [INF] HTTP GET /favicon.ico responded 200 in 0.6448 ms
2025-07-29 15:38:39.345 +03:00 [INF] HTTP GET /Account/Login responded 200 in 41.3437 ms
2025-07-29 15:38:39.351 +03:00 [INF] HTTP GET /lib/jquery-validation/dist/jquery.validate.min.js responded 200 in 2.4654 ms
2025-07-29 15:38:39.351 +03:00 [INF] HTTP GET /lib/jquery-validation-unobtrusive/jquery.validate.unobtrusive.min.js responded 200 in 1.7986 ms
2025-07-29 15:39:02.118 +03:00 [INF] HTTP POST /Account/Login responded 302 in 459.5721 ms
2025-07-29 15:39:03.859 +03:00 [INF] HTTP GET /Dashboard responded 200 in 1737.2198 ms
2025-07-29 15:39:04.001 +03:00 [INF] HTTP GET /Dashboard/GetChartData responded 200 in 99.9632 ms
2025-07-29 15:39:20.310 +03:00 [ERR] Failed executing DbCommand (70ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [c].[PriceListID], [c].[CreatedByUserID], [c].[CreatedDate], [c].[Currency], [c].[CustomerGroupID], [c].[CustomerID], [c].[Description], [c].[DiscountPercent], [c].[EffectiveFrom], [c].[EffectiveTo], [c].[IsActive], [c].[MarkupPercent], [c].[PriceListCode], [c].[PriceListName], [c].[Priority], [c].[UpdatedByUserID], [c].[UpdatedDate], [c0].[CustomerID], [c0].[BlockedByUserID], [c0].[BlockedDate], [c0].[BlockedReason], [c0].[ContactEmail], [c0].[ContactLandline], [c0].[ContactMobile], [c0].[ContactPhone], [c0].[CreatedByUserID], [c0].[CreatedDate], [c0].[CreditLimit], [c0].[CreditReviewDate], [c0].[CreditStatus], [c0].[CurrencyCode], [c0].[CustomerAddress], [c0].[CustomerAddress2], [c0].[CustomerCity], [c0].[CustomerCode], [c0].[CustomerCountry], [c0].[CustomerGroup], [c0].[CustomerName], [c0].[CustomerPostCode], [c0].[CustomerSince], [c0].[CustomerState], [c0].[CustomerStatus], [c0].[CustomerType], [c0].[DeletedByUserID], [c0].[DeletedDate], [c0].[DeliveryInstructions], [c0].[DeliveryTimePreference], [c0].[DiscountPercent], [c0].[InternalNotes], [c0].[IsActive], [c0].[IsDeleted], [c0].[LastOrderDate], [c0].[LeadSource], [c0].[LoyaltyPoints], [c0].[Notes], [c0].[OutstandingBalance], [c0].[PaymentMethodID], [c0].[PaymentTerm], [c0].[PreferredCarrierID], [c0].[PriceListID], [c0].[Rating], [c0].[ReferenceCode], [c0].[ResponsibleContact], [c0].[SalesRepID], [c0].[TaxExempt], [c0].[TaxNumber], [c0].[TerritoryID], [c0].[TotalOrderValue], [c0].[TradingName], [c0].[UpdatedByUserID], [c0].[UpdatedDate], [c0].[Website], [c0].[YTDSales], [c1].[CustomerGroupID], [c1].[CreatedDate], [c1].[CreditLimit], [c1].[Description], [c1].[DiscountPercent], [c1].[GroupCode], [c1].[GroupName], [c1].[IsActive], [c1].[ParentGroupID], [c1].[PaymentTermDays], [c1].[UpdatedDate]
FROM [CustomerPriceList] AS [c]
LEFT JOIN [Customer] AS [c0] ON [c].[CustomerID] = [c0].[CustomerID]
LEFT JOIN [CustomerGroup] AS [c1] ON [c].[CustomerGroupID] = [c1].[CustomerGroupID]
ORDER BY [c].[CreatedDate] DESC
2025-07-29 15:39:20.323 +03:00 [ERR] An exception occurred while iterating over the results of a query for context type 'nCarry.Web.Data.ApplicationDbContext'.
Microsoft.Data.SqlClient.SqlException (0x80131904): Invalid column name 'CustomerGroupID'.
Invalid column name 'CustomerGroupID'.
   at Microsoft.Data.SqlClient.SqlCommand.<>c.<ExecuteDbDataReaderAsync>b__211_0(Task`1 result)
   at System.Threading.Tasks.ContinuationResultTaskFromResultTask`2.InnerInvoke()
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.InitializeReaderAsync(AsyncEnumerator enumerator, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.SqlServer.Storage.Internal.SqlServerExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
ClientConnectionId:70c07173-9ba5-4144-b030-c010d3d9dc20
Error Number:207,State:1,Class:16
Microsoft.Data.SqlClient.SqlException (0x80131904): Invalid column name 'CustomerGroupID'.
Invalid column name 'CustomerGroupID'.
   at Microsoft.Data.SqlClient.SqlCommand.<>c.<ExecuteDbDataReaderAsync>b__211_0(Task`1 result)
   at System.Threading.Tasks.ContinuationResultTaskFromResultTask`2.InnerInvoke()
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.InitializeReaderAsync(AsyncEnumerator enumerator, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.SqlServer.Storage.Internal.SqlServerExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
ClientConnectionId:70c07173-9ba5-4144-b030-c010d3d9dc20
Error Number:207,State:1,Class:16
2025-07-29 15:39:20.324 +03:00 [ERR] HTTP GET /CustomerPriceList responded 500 in 112.1660 ms
Microsoft.Data.SqlClient.SqlException (0x80131904): Invalid column name 'CustomerGroupID'.
Invalid column name 'CustomerGroupID'.
   at Microsoft.Data.SqlClient.SqlCommand.<>c.<ExecuteDbDataReaderAsync>b__211_0(Task`1 result)
   at System.Threading.Tasks.ContinuationResultTaskFromResultTask`2.InnerInvoke()
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.InitializeReaderAsync(AsyncEnumerator enumerator, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.SqlServer.Storage.Internal.SqlServerExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at nCarry.Web.Controllers.CustomerPriceListController.Index() in /Users/<USER>/Source/nCarry/nCarry/nCarry.Web/Controllers/CustomerPriceListController.cs:line 26
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.TaskOfIActionResultExecutor.Execute(ActionContext actionContext, IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeActionMethodAsync>g__Awaited|12_0(ControllerActionInvoker invoker, ValueTask`1 actionResultValueTask)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeInnerFilterAsync>g__Awaited|13_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextResourceFilter>g__Awaited|25_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.Rethrow(ResourceExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeFilterPipelineAsync>g__Awaited|20_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at Serilog.AspNetCore.RequestLoggingMiddleware.Invoke(HttpContext httpContext)
ClientConnectionId:70c07173-9ba5-4144-b030-c010d3d9dc20
Error Number:207,State:1,Class:16
2025-07-29 15:39:20.330 +03:00 [ERR] An unhandled exception has occurred while executing the request.
Microsoft.Data.SqlClient.SqlException (0x80131904): Invalid column name 'CustomerGroupID'.
Invalid column name 'CustomerGroupID'.
   at Microsoft.Data.SqlClient.SqlCommand.<>c.<ExecuteDbDataReaderAsync>b__211_0(Task`1 result)
   at System.Threading.Tasks.ContinuationResultTaskFromResultTask`2.InnerInvoke()
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.InitializeReaderAsync(AsyncEnumerator enumerator, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.SqlServer.Storage.Internal.SqlServerExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at nCarry.Web.Controllers.CustomerPriceListController.Index() in /Users/<USER>/Source/nCarry/nCarry/nCarry.Web/Controllers/CustomerPriceListController.cs:line 26
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.TaskOfIActionResultExecutor.Execute(ActionContext actionContext, IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeActionMethodAsync>g__Awaited|12_0(ControllerActionInvoker invoker, ValueTask`1 actionResultValueTask)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeInnerFilterAsync>g__Awaited|13_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextResourceFilter>g__Awaited|25_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.Rethrow(ResourceExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeFilterPipelineAsync>g__Awaited|20_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at Serilog.AspNetCore.RequestLoggingMiddleware.Invoke(HttpContext httpContext)
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
ClientConnectionId:70c07173-9ba5-4144-b030-c010d3d9dc20
Error Number:207,State:1,Class:16
2025-07-29 15:45:06.349 +03:00 [INF] Starting database seeding.
2025-07-29 15:45:08.023 +03:00 [INF] Admin user already exists. Seeding is not required.
2025-07-29 15:45:49.036 +03:00 [WRN] Failed to determine the https port for redirect.
2025-07-29 15:45:49.368 +03:00 [INF] HTTP GET /CustomerPriceList responded 200 in 333.7052 ms
2025-07-29 15:45:57.125 +03:00 [ERR] Failed executing DbCommand (72ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [c].[CustomerGroupID], [c].[GroupName]
FROM [CustomerGroup] AS [c]
WHERE [c].[IsActive] = CAST(1 AS bit)
2025-07-29 15:45:57.138 +03:00 [ERR] An exception occurred while iterating over the results of a query for context type 'nCarry.Web.Data.ApplicationDbContext'.
Microsoft.Data.SqlClient.SqlException (0x80131904): Invalid column name 'CustomerGroupID'.
   at Microsoft.Data.SqlClient.SqlConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.SqlInternalConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.TdsParser.ThrowExceptionAndWarning(TdsParserStateObject stateObj, Boolean callerHasConnectionLock, Boolean asyncClose)
   at Microsoft.Data.SqlClient.TdsParser.TryRun(RunBehavior runBehavior, SqlCommand cmdHandler, SqlDataReader dataStream, BulkCopySimpleResultSet bulkCopyHandler, TdsParserStateObject stateObj, Boolean& dataReady)
   at Microsoft.Data.SqlClient.SqlDataReader.TryConsumeMetaData()
   at Microsoft.Data.SqlClient.SqlDataReader.get_MetaData()
   at Microsoft.Data.SqlClient.SqlCommand.FinishExecuteReader(SqlDataReader ds, RunBehavior runBehavior, String resetOptionsString, Boolean isInternal, Boolean forDescribeParameterEncryption, Boolean shouldCacheForAlwaysEncrypted)
   at Microsoft.Data.SqlClient.SqlCommand.RunExecuteReaderTds(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, Boolean isAsync, Int32 timeout, Task& task, Boolean asyncWrite, Boolean inRetry, SqlDataReader ds, Boolean describeParameterEncryptionRequest)
   at Microsoft.Data.SqlClient.SqlCommand.RunExecuteReader(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, TaskCompletionSource`1 completion, Int32 timeout, Task& task, Boolean& usedCache, Boolean asyncWrite, Boolean inRetry, String method)
   at Microsoft.Data.SqlClient.SqlCommand.RunExecuteReader(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, String method)
   at Microsoft.Data.SqlClient.SqlCommand.ExecuteReader(CommandBehavior behavior)
   at Microsoft.Data.SqlClient.SqlCommand.ExecuteDbDataReader(CommandBehavior behavior)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReader(RelationalCommandParameterObject parameterObject)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.Enumerator.InitializeReader(Enumerator enumerator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.Enumerator.<>c.<MoveNext>b__21_0(DbContext _, Enumerator enumerator)
   at Microsoft.EntityFrameworkCore.SqlServer.Storage.Internal.SqlServerExecutionStrategy.Execute[TState,TResult](TState state, Func`3 operation, Func`3 verifySucceeded)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.Enumerator.MoveNext()
ClientConnectionId:bba04637-44e0-4a73-9b1c-e6efc82cf536
Error Number:207,State:1,Class:16
Microsoft.Data.SqlClient.SqlException (0x80131904): Invalid column name 'CustomerGroupID'.
   at Microsoft.Data.SqlClient.SqlConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.SqlInternalConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.TdsParser.ThrowExceptionAndWarning(TdsParserStateObject stateObj, Boolean callerHasConnectionLock, Boolean asyncClose)
   at Microsoft.Data.SqlClient.TdsParser.TryRun(RunBehavior runBehavior, SqlCommand cmdHandler, SqlDataReader dataStream, BulkCopySimpleResultSet bulkCopyHandler, TdsParserStateObject stateObj, Boolean& dataReady)
   at Microsoft.Data.SqlClient.SqlDataReader.TryConsumeMetaData()
   at Microsoft.Data.SqlClient.SqlDataReader.get_MetaData()
   at Microsoft.Data.SqlClient.SqlCommand.FinishExecuteReader(SqlDataReader ds, RunBehavior runBehavior, String resetOptionsString, Boolean isInternal, Boolean forDescribeParameterEncryption, Boolean shouldCacheForAlwaysEncrypted)
   at Microsoft.Data.SqlClient.SqlCommand.RunExecuteReaderTds(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, Boolean isAsync, Int32 timeout, Task& task, Boolean asyncWrite, Boolean inRetry, SqlDataReader ds, Boolean describeParameterEncryptionRequest)
   at Microsoft.Data.SqlClient.SqlCommand.RunExecuteReader(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, TaskCompletionSource`1 completion, Int32 timeout, Task& task, Boolean& usedCache, Boolean asyncWrite, Boolean inRetry, String method)
   at Microsoft.Data.SqlClient.SqlCommand.RunExecuteReader(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, String method)
   at Microsoft.Data.SqlClient.SqlCommand.ExecuteReader(CommandBehavior behavior)
   at Microsoft.Data.SqlClient.SqlCommand.ExecuteDbDataReader(CommandBehavior behavior)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReader(RelationalCommandParameterObject parameterObject)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.Enumerator.InitializeReader(Enumerator enumerator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.Enumerator.<>c.<MoveNext>b__21_0(DbContext _, Enumerator enumerator)
   at Microsoft.EntityFrameworkCore.SqlServer.Storage.Internal.SqlServerExecutionStrategy.Execute[TState,TResult](TState state, Func`3 operation, Func`3 verifySucceeded)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.Enumerator.MoveNext()
ClientConnectionId:bba04637-44e0-4a73-9b1c-e6efc82cf536
Error Number:207,State:1,Class:16
2025-07-29 15:45:57.139 +03:00 [ERR] HTTP GET /CustomerPriceList/Create responded 500 in 519.9004 ms
Microsoft.Data.SqlClient.SqlException (0x80131904): Invalid column name 'CustomerGroupID'.
   at Microsoft.Data.SqlClient.SqlConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.SqlInternalConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.TdsParser.ThrowExceptionAndWarning(TdsParserStateObject stateObj, Boolean callerHasConnectionLock, Boolean asyncClose)
   at Microsoft.Data.SqlClient.TdsParser.TryRun(RunBehavior runBehavior, SqlCommand cmdHandler, SqlDataReader dataStream, BulkCopySimpleResultSet bulkCopyHandler, TdsParserStateObject stateObj, Boolean& dataReady)
   at Microsoft.Data.SqlClient.SqlDataReader.TryConsumeMetaData()
   at Microsoft.Data.SqlClient.SqlDataReader.get_MetaData()
   at Microsoft.Data.SqlClient.SqlCommand.FinishExecuteReader(SqlDataReader ds, RunBehavior runBehavior, String resetOptionsString, Boolean isInternal, Boolean forDescribeParameterEncryption, Boolean shouldCacheForAlwaysEncrypted)
   at Microsoft.Data.SqlClient.SqlCommand.RunExecuteReaderTds(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, Boolean isAsync, Int32 timeout, Task& task, Boolean asyncWrite, Boolean inRetry, SqlDataReader ds, Boolean describeParameterEncryptionRequest)
   at Microsoft.Data.SqlClient.SqlCommand.RunExecuteReader(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, TaskCompletionSource`1 completion, Int32 timeout, Task& task, Boolean& usedCache, Boolean asyncWrite, Boolean inRetry, String method)
   at Microsoft.Data.SqlClient.SqlCommand.RunExecuteReader(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, String method)
   at Microsoft.Data.SqlClient.SqlCommand.ExecuteReader(CommandBehavior behavior)
   at Microsoft.Data.SqlClient.SqlCommand.ExecuteDbDataReader(CommandBehavior behavior)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReader(RelationalCommandParameterObject parameterObject)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.Enumerator.InitializeReader(Enumerator enumerator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.Enumerator.<>c.<MoveNext>b__21_0(DbContext _, Enumerator enumerator)
   at Microsoft.EntityFrameworkCore.SqlServer.Storage.Internal.SqlServerExecutionStrategy.Execute[TState,TResult](TState state, Func`3 operation, Func`3 verifySucceeded)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.Enumerator.MoveNext()
   at System.Collections.Generic.List`1..ctor(IEnumerable`1 collection)
   at System.Linq.Enumerable.ToList[TSource](IEnumerable`1 source)
   at nCarry.Web.Controllers.CustomerPriceListController.PopulateDropdowns(CustomerPriceList priceList) in /Users/<USER>/Source/nCarry/nCarry/nCarry.Web/Controllers/CustomerPriceListController.cs:line 446
   at nCarry.Web.Controllers.CustomerPriceListController.Create() in /Users/<USER>/Source/nCarry/nCarry/nCarry.Web/Controllers/CustomerPriceListController.cs:line 61
   at lambda_method120(Closure, Object, Object[])
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.SyncActionResultExecutor.Execute(ActionContext actionContext, IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeActionMethodAsync()
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeNextActionFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeInnerFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextResourceFilter>g__Awaited|25_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.Rethrow(ResourceExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.InvokeFilterPipelineAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at Serilog.AspNetCore.RequestLoggingMiddleware.Invoke(HttpContext httpContext)
ClientConnectionId:bba04637-44e0-4a73-9b1c-e6efc82cf536
Error Number:207,State:1,Class:16
2025-07-29 15:45:57.144 +03:00 [ERR] An unhandled exception has occurred while executing the request.
Microsoft.Data.SqlClient.SqlException (0x80131904): Invalid column name 'CustomerGroupID'.
   at Microsoft.Data.SqlClient.SqlConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.SqlInternalConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.TdsParser.ThrowExceptionAndWarning(TdsParserStateObject stateObj, Boolean callerHasConnectionLock, Boolean asyncClose)
   at Microsoft.Data.SqlClient.TdsParser.TryRun(RunBehavior runBehavior, SqlCommand cmdHandler, SqlDataReader dataStream, BulkCopySimpleResultSet bulkCopyHandler, TdsParserStateObject stateObj, Boolean& dataReady)
   at Microsoft.Data.SqlClient.SqlDataReader.TryConsumeMetaData()
   at Microsoft.Data.SqlClient.SqlDataReader.get_MetaData()
   at Microsoft.Data.SqlClient.SqlCommand.FinishExecuteReader(SqlDataReader ds, RunBehavior runBehavior, String resetOptionsString, Boolean isInternal, Boolean forDescribeParameterEncryption, Boolean shouldCacheForAlwaysEncrypted)
   at Microsoft.Data.SqlClient.SqlCommand.RunExecuteReaderTds(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, Boolean isAsync, Int32 timeout, Task& task, Boolean asyncWrite, Boolean inRetry, SqlDataReader ds, Boolean describeParameterEncryptionRequest)
   at Microsoft.Data.SqlClient.SqlCommand.RunExecuteReader(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, TaskCompletionSource`1 completion, Int32 timeout, Task& task, Boolean& usedCache, Boolean asyncWrite, Boolean inRetry, String method)
   at Microsoft.Data.SqlClient.SqlCommand.RunExecuteReader(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, String method)
   at Microsoft.Data.SqlClient.SqlCommand.ExecuteReader(CommandBehavior behavior)
   at Microsoft.Data.SqlClient.SqlCommand.ExecuteDbDataReader(CommandBehavior behavior)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReader(RelationalCommandParameterObject parameterObject)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.Enumerator.InitializeReader(Enumerator enumerator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.Enumerator.<>c.<MoveNext>b__21_0(DbContext _, Enumerator enumerator)
   at Microsoft.EntityFrameworkCore.SqlServer.Storage.Internal.SqlServerExecutionStrategy.Execute[TState,TResult](TState state, Func`3 operation, Func`3 verifySucceeded)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.Enumerator.MoveNext()
   at System.Collections.Generic.List`1..ctor(IEnumerable`1 collection)
   at System.Linq.Enumerable.ToList[TSource](IEnumerable`1 source)
   at nCarry.Web.Controllers.CustomerPriceListController.PopulateDropdowns(CustomerPriceList priceList) in /Users/<USER>/Source/nCarry/nCarry/nCarry.Web/Controllers/CustomerPriceListController.cs:line 446
   at nCarry.Web.Controllers.CustomerPriceListController.Create() in /Users/<USER>/Source/nCarry/nCarry/nCarry.Web/Controllers/CustomerPriceListController.cs:line 61
   at lambda_method120(Closure, Object, Object[])
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.SyncActionResultExecutor.Execute(ActionContext actionContext, IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeActionMethodAsync()
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeNextActionFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeInnerFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextResourceFilter>g__Awaited|25_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.Rethrow(ResourceExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.InvokeFilterPipelineAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at Serilog.AspNetCore.RequestLoggingMiddleware.Invoke(HttpContext httpContext)
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
ClientConnectionId:bba04637-44e0-4a73-9b1c-e6efc82cf536
Error Number:207,State:1,Class:16
2025-07-29 15:50:26.498 +03:00 [ERR] Failed executing DbCommand (69ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [c].[CustomerGroupID], [c].[GroupName]
FROM [CustomerGroup] AS [c]
WHERE [c].[IsActive] = CAST(1 AS bit)
2025-07-29 15:50:26.504 +03:00 [ERR] An exception occurred while iterating over the results of a query for context type 'nCarry.Web.Data.ApplicationDbContext'.
Microsoft.Data.SqlClient.SqlException (0x80131904): Invalid column name 'CustomerGroupID'.
   at Microsoft.Data.SqlClient.SqlConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.SqlInternalConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.TdsParser.ThrowExceptionAndWarning(TdsParserStateObject stateObj, Boolean callerHasConnectionLock, Boolean asyncClose)
   at Microsoft.Data.SqlClient.TdsParser.TryRun(RunBehavior runBehavior, SqlCommand cmdHandler, SqlDataReader dataStream, BulkCopySimpleResultSet bulkCopyHandler, TdsParserStateObject stateObj, Boolean& dataReady)
   at Microsoft.Data.SqlClient.SqlDataReader.TryConsumeMetaData()
   at Microsoft.Data.SqlClient.SqlDataReader.get_MetaData()
   at Microsoft.Data.SqlClient.SqlCommand.FinishExecuteReader(SqlDataReader ds, RunBehavior runBehavior, String resetOptionsString, Boolean isInternal, Boolean forDescribeParameterEncryption, Boolean shouldCacheForAlwaysEncrypted)
   at Microsoft.Data.SqlClient.SqlCommand.RunExecuteReaderTds(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, Boolean isAsync, Int32 timeout, Task& task, Boolean asyncWrite, Boolean inRetry, SqlDataReader ds, Boolean describeParameterEncryptionRequest)
   at Microsoft.Data.SqlClient.SqlCommand.RunExecuteReader(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, TaskCompletionSource`1 completion, Int32 timeout, Task& task, Boolean& usedCache, Boolean asyncWrite, Boolean inRetry, String method)
   at Microsoft.Data.SqlClient.SqlCommand.RunExecuteReader(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, String method)
   at Microsoft.Data.SqlClient.SqlCommand.ExecuteReader(CommandBehavior behavior)
   at Microsoft.Data.SqlClient.SqlCommand.ExecuteDbDataReader(CommandBehavior behavior)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReader(RelationalCommandParameterObject parameterObject)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.Enumerator.InitializeReader(Enumerator enumerator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.Enumerator.<>c.<MoveNext>b__21_0(DbContext _, Enumerator enumerator)
   at Microsoft.EntityFrameworkCore.SqlServer.Storage.Internal.SqlServerExecutionStrategy.Execute[TState,TResult](TState state, Func`3 operation, Func`3 verifySucceeded)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.Enumerator.MoveNext()
ClientConnectionId:bba04637-44e0-4a73-9b1c-e6efc82cf536
Error Number:207,State:1,Class:16
Microsoft.Data.SqlClient.SqlException (0x80131904): Invalid column name 'CustomerGroupID'.
   at Microsoft.Data.SqlClient.SqlConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.SqlInternalConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.TdsParser.ThrowExceptionAndWarning(TdsParserStateObject stateObj, Boolean callerHasConnectionLock, Boolean asyncClose)
   at Microsoft.Data.SqlClient.TdsParser.TryRun(RunBehavior runBehavior, SqlCommand cmdHandler, SqlDataReader dataStream, BulkCopySimpleResultSet bulkCopyHandler, TdsParserStateObject stateObj, Boolean& dataReady)
   at Microsoft.Data.SqlClient.SqlDataReader.TryConsumeMetaData()
   at Microsoft.Data.SqlClient.SqlDataReader.get_MetaData()
   at Microsoft.Data.SqlClient.SqlCommand.FinishExecuteReader(SqlDataReader ds, RunBehavior runBehavior, String resetOptionsString, Boolean isInternal, Boolean forDescribeParameterEncryption, Boolean shouldCacheForAlwaysEncrypted)
   at Microsoft.Data.SqlClient.SqlCommand.RunExecuteReaderTds(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, Boolean isAsync, Int32 timeout, Task& task, Boolean asyncWrite, Boolean inRetry, SqlDataReader ds, Boolean describeParameterEncryptionRequest)
   at Microsoft.Data.SqlClient.SqlCommand.RunExecuteReader(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, TaskCompletionSource`1 completion, Int32 timeout, Task& task, Boolean& usedCache, Boolean asyncWrite, Boolean inRetry, String method)
   at Microsoft.Data.SqlClient.SqlCommand.RunExecuteReader(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, String method)
   at Microsoft.Data.SqlClient.SqlCommand.ExecuteReader(CommandBehavior behavior)
   at Microsoft.Data.SqlClient.SqlCommand.ExecuteDbDataReader(CommandBehavior behavior)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReader(RelationalCommandParameterObject parameterObject)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.Enumerator.InitializeReader(Enumerator enumerator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.Enumerator.<>c.<MoveNext>b__21_0(DbContext _, Enumerator enumerator)
   at Microsoft.EntityFrameworkCore.SqlServer.Storage.Internal.SqlServerExecutionStrategy.Execute[TState,TResult](TState state, Func`3 operation, Func`3 verifySucceeded)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.Enumerator.MoveNext()
ClientConnectionId:bba04637-44e0-4a73-9b1c-e6efc82cf536
Error Number:207,State:1,Class:16
2025-07-29 15:50:26.506 +03:00 [ERR] HTTP GET /CustomerPriceList/Create responded 500 in 182.1218 ms
Microsoft.Data.SqlClient.SqlException (0x80131904): Invalid column name 'CustomerGroupID'.
   at Microsoft.Data.SqlClient.SqlConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.SqlInternalConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.TdsParser.ThrowExceptionAndWarning(TdsParserStateObject stateObj, Boolean callerHasConnectionLock, Boolean asyncClose)
   at Microsoft.Data.SqlClient.TdsParser.TryRun(RunBehavior runBehavior, SqlCommand cmdHandler, SqlDataReader dataStream, BulkCopySimpleResultSet bulkCopyHandler, TdsParserStateObject stateObj, Boolean& dataReady)
   at Microsoft.Data.SqlClient.SqlDataReader.TryConsumeMetaData()
   at Microsoft.Data.SqlClient.SqlDataReader.get_MetaData()
   at Microsoft.Data.SqlClient.SqlCommand.FinishExecuteReader(SqlDataReader ds, RunBehavior runBehavior, String resetOptionsString, Boolean isInternal, Boolean forDescribeParameterEncryption, Boolean shouldCacheForAlwaysEncrypted)
   at Microsoft.Data.SqlClient.SqlCommand.RunExecuteReaderTds(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, Boolean isAsync, Int32 timeout, Task& task, Boolean asyncWrite, Boolean inRetry, SqlDataReader ds, Boolean describeParameterEncryptionRequest)
   at Microsoft.Data.SqlClient.SqlCommand.RunExecuteReader(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, TaskCompletionSource`1 completion, Int32 timeout, Task& task, Boolean& usedCache, Boolean asyncWrite, Boolean inRetry, String method)
   at Microsoft.Data.SqlClient.SqlCommand.RunExecuteReader(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, String method)
   at Microsoft.Data.SqlClient.SqlCommand.ExecuteReader(CommandBehavior behavior)
   at Microsoft.Data.SqlClient.SqlCommand.ExecuteDbDataReader(CommandBehavior behavior)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReader(RelationalCommandParameterObject parameterObject)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.Enumerator.InitializeReader(Enumerator enumerator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.Enumerator.<>c.<MoveNext>b__21_0(DbContext _, Enumerator enumerator)
   at Microsoft.EntityFrameworkCore.SqlServer.Storage.Internal.SqlServerExecutionStrategy.Execute[TState,TResult](TState state, Func`3 operation, Func`3 verifySucceeded)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.Enumerator.MoveNext()
   at System.Collections.Generic.List`1..ctor(IEnumerable`1 collection)
   at System.Linq.Enumerable.ToList[TSource](IEnumerable`1 source)
   at nCarry.Web.Controllers.CustomerPriceListController.PopulateDropdowns(CustomerPriceList priceList) in /Users/<USER>/Source/nCarry/nCarry/nCarry.Web/Controllers/CustomerPriceListController.cs:line 446
   at nCarry.Web.Controllers.CustomerPriceListController.Create() in /Users/<USER>/Source/nCarry/nCarry/nCarry.Web/Controllers/CustomerPriceListController.cs:line 61
   at lambda_method120(Closure, Object, Object[])
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.SyncActionResultExecutor.Execute(ActionContext actionContext, IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeActionMethodAsync()
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeNextActionFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeInnerFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextResourceFilter>g__Awaited|25_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.Rethrow(ResourceExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.InvokeFilterPipelineAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at Serilog.AspNetCore.RequestLoggingMiddleware.Invoke(HttpContext httpContext)
ClientConnectionId:bba04637-44e0-4a73-9b1c-e6efc82cf536
Error Number:207,State:1,Class:16
2025-07-29 15:50:26.512 +03:00 [ERR] An unhandled exception has occurred while executing the request.
Microsoft.Data.SqlClient.SqlException (0x80131904): Invalid column name 'CustomerGroupID'.
   at Microsoft.Data.SqlClient.SqlConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.SqlInternalConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.TdsParser.ThrowExceptionAndWarning(TdsParserStateObject stateObj, Boolean callerHasConnectionLock, Boolean asyncClose)
   at Microsoft.Data.SqlClient.TdsParser.TryRun(RunBehavior runBehavior, SqlCommand cmdHandler, SqlDataReader dataStream, BulkCopySimpleResultSet bulkCopyHandler, TdsParserStateObject stateObj, Boolean& dataReady)
   at Microsoft.Data.SqlClient.SqlDataReader.TryConsumeMetaData()
   at Microsoft.Data.SqlClient.SqlDataReader.get_MetaData()
   at Microsoft.Data.SqlClient.SqlCommand.FinishExecuteReader(SqlDataReader ds, RunBehavior runBehavior, String resetOptionsString, Boolean isInternal, Boolean forDescribeParameterEncryption, Boolean shouldCacheForAlwaysEncrypted)
   at Microsoft.Data.SqlClient.SqlCommand.RunExecuteReaderTds(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, Boolean isAsync, Int32 timeout, Task& task, Boolean asyncWrite, Boolean inRetry, SqlDataReader ds, Boolean describeParameterEncryptionRequest)
   at Microsoft.Data.SqlClient.SqlCommand.RunExecuteReader(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, TaskCompletionSource`1 completion, Int32 timeout, Task& task, Boolean& usedCache, Boolean asyncWrite, Boolean inRetry, String method)
   at Microsoft.Data.SqlClient.SqlCommand.RunExecuteReader(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, String method)
   at Microsoft.Data.SqlClient.SqlCommand.ExecuteReader(CommandBehavior behavior)
   at Microsoft.Data.SqlClient.SqlCommand.ExecuteDbDataReader(CommandBehavior behavior)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReader(RelationalCommandParameterObject parameterObject)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.Enumerator.InitializeReader(Enumerator enumerator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.Enumerator.<>c.<MoveNext>b__21_0(DbContext _, Enumerator enumerator)
   at Microsoft.EntityFrameworkCore.SqlServer.Storage.Internal.SqlServerExecutionStrategy.Execute[TState,TResult](TState state, Func`3 operation, Func`3 verifySucceeded)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.Enumerator.MoveNext()
   at System.Collections.Generic.List`1..ctor(IEnumerable`1 collection)
   at System.Linq.Enumerable.ToList[TSource](IEnumerable`1 source)
   at nCarry.Web.Controllers.CustomerPriceListController.PopulateDropdowns(CustomerPriceList priceList) in /Users/<USER>/Source/nCarry/nCarry/nCarry.Web/Controllers/CustomerPriceListController.cs:line 446
   at nCarry.Web.Controllers.CustomerPriceListController.Create() in /Users/<USER>/Source/nCarry/nCarry/nCarry.Web/Controllers/CustomerPriceListController.cs:line 61
   at lambda_method120(Closure, Object, Object[])
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.SyncActionResultExecutor.Execute(ActionContext actionContext, IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeActionMethodAsync()
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeNextActionFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeInnerFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextResourceFilter>g__Awaited|25_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.Rethrow(ResourceExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.InvokeFilterPipelineAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at Serilog.AspNetCore.RequestLoggingMiddleware.Invoke(HttpContext httpContext)
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
ClientConnectionId:bba04637-44e0-4a73-9b1c-e6efc82cf536
Error Number:207,State:1,Class:16
2025-07-29 15:51:00.315 +03:00 [INF] Starting database seeding.
2025-07-29 15:51:01.958 +03:00 [INF] Admin user already exists. Seeding is not required.
2025-07-29 15:51:24.963 +03:00 [WRN] Failed to determine the https port for redirect.
2025-07-29 15:51:25.236 +03:00 [INF] HTTP GET /CustomerPriceList/Create responded 200 in 275.2143 ms
2025-07-29 15:54:14.618 +03:00 [INF] Creating price list - Code: PL-TC1-001, CustomerID: 28, Priority: 50
2025-07-29 15:54:14.723 +03:00 [ERR] Error creating price list
System.InvalidOperationException: Session has not been configured for this application or request.
   at Microsoft.AspNetCore.Http.DefaultHttpContext.get_Session()
   at nCarry.Web.Controllers.CustomerPriceListController.Create(CustomerPriceList priceList) in /Users/<USER>/Source/nCarry/nCarry/nCarry.Web/Controllers/CustomerPriceListController.cs:line 98
2025-07-29 15:54:14.815 +03:00 [INF] HTTP POST /CustomerPriceList/Create responded 200 in 260.2497 ms
2025-07-29 15:56:31.851 +03:00 [INF] Starting database seeding.
2025-07-29 15:56:34.437 +03:00 [INF] Admin user already exists. Seeding is not required.
2025-07-29 16:00:08.075 +03:00 [WRN] Failed to determine the https port for redirect.
2025-07-29 16:00:08.379 +03:00 [INF] HTTP GET /CustomerPriceList/Create responded 200 in 308.6681 ms
2025-07-29 16:01:06.210 +03:00 [INF] Creating price list - Code: PL-TC1-001, CustomerID: 28, Priority: 50
2025-07-29 16:01:06.502 +03:00 [INF] HTTP POST /CustomerPriceList/Create responded 302 in 340.9165 ms
2025-07-29 16:01:06.735 +03:00 [INF] HTTP GET /CustomerPriceList/Details/1 responded 200 in 228.6783 ms
2025-07-29 16:18:54.882 +03:00 [INF] HTTP GET / responded 200 in 15.6969 ms
2025-07-29 16:19:00.326 +03:00 [INF] HTTP GET /Dashboard responded 200 in 2568.5880 ms
2025-07-29 16:19:00.843 +03:00 [INF] HTTP GET /Customer responded 200 in 739.8240 ms
2025-07-29 16:19:03.790 +03:00 [INF] HTTP GET /CustomerPriceList responded 200 in 111.1913 ms
2025-07-29 16:19:07.910 +03:00 [INF] HTTP GET /Supplier responded 200 in 181.4253 ms
2025-07-29 16:19:10.668 +03:00 [INF] HTTP GET /ProductCategory responded 200 in 141.8804 ms
2025-07-29 16:19:12.320 +03:00 [INF] HTTP GET /SalesOrder responded 200 in 111.0528 ms
2025-07-29 16:19:14.992 +03:00 [INF] HTTP GET /SalesOrder responded 200 in 86.4626 ms
2025-07-29 16:19:17.161 +03:00 [INF] HTTP GET /PurchaseOrder responded 200 in 216.2928 ms
2025-07-29 16:19:19.487 +03:00 [ERR] Failed executing DbCommand (78ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [g].[ReceiptID], [g].[CreatedByUserID], [g].[CreatedDate], [g].[Currency], [g].[Notes], [g].[PostedByUserID], [g].[PostedDate], [g].[PurchaseOrderID], [g].[QualityCheckByUserID], [g].[QualityCheckDate], [g].[QualityCheckStatus], [g].[ReceiptDate], [g].[ReceiptNumber], [g].[ReceiptType], [g].[ReceivedByUserID], [g].[ReceiptStatus], [g].[SupplierDeliveryNote], [g].[SupplierID], [g].[UpdatedByUserID], [g].[UpdatedDate], [g].[WarehouseID], [p].[PurchaseOrderID], [p].[ApprovalNotes], [p].[ApprovalStatus], [p].[ApprovedByUserID], [p].[ApprovedDate], [p].[BuyerID], [p].[CreatedByUserID], [p].[CreatedDate], [p].[Currency], [p].[DeletedByUserID], [p].[DeletedDate], [p].[DeliveryAddressID], [p].[DeliveryInstructions], [p].[DeliveryWarehouseID], [p].[DiscountAmount], [p].[ExchangeRate], [p].[ExpectedDate], [p].[FreightAmount], [p].[FreightTerms], [p].[InternalNotes], [p].[IsDeleted], [p].[Notes], [p].[OrderDate], [p].[OrderStatus], [p].[OrderType], [p].[PaymentMethod], [p].[PaymentTerms], [p].[PromisedDate], [p].[PurchaseOrderNumber], [p].[RequiredDate], [p].[RequisitionID], [p].[ShippingMethod], [p].[SubTotal], [p].[SupplierID], [p].[SupplierReference], [p].[TaxAmount], [p].[TermsAndConditions], [p].[TotalAmount], [p].[UpdatedByUserID], [p].[UpdatedDate], [s].[SupplierID], [s].[APIEnabled], [s].[BankAccountDetails], [s].[BlockedByUserID], [s].[BlockedDate], [s].[BlockedReason], [s].[CertificateExpiryDate], [s].[CertificateNumber], [s].[ComplianceStatus], [s].[ContactEmail], [s].[ContactLandline], [s].[ContactMobile], [s].[ContactPhone], [s].[CreatedByUserID], [s].[CreatedDate], [s].[DeletedByUserID], [s].[DeletedDate], [s].[DeliveryDays], [s].[DeliveryScore], [s].[EDIEnabled], [s].[EarlyPaymentDiscountDays], [s].[EarlyPaymentDiscountPercent], [s].[InsuranceExpiryDate], [s].[InsurancePolicyNumber], [s].[IntegrationKey], [s].[InternalNotes], [s].[IsActive], [s].[IsDeleted], [s].[IsPreferred], [s].[LastOrderDate], [s].[LeadTimeDays], [s].[MinimumOrderValue], [s].[Notes], [s].[OrderCutoffTime], [s].[OutstandingBalance], [s].[PaymentMethodID], [s].[PaymentTermDays], [s].[PaymentTerms], [s].[PreferredCurrency], [s].[PriceScore], [s].[QualityScore], [s].[Rating], [s].[ReferenceCode], [s].[ResponsibleContact], [s].[ReturnPolicy], [s].[SupplierAddress], [s].[SupplierAddress2], [s].[SupplierCity], [s].[SupplierCode], [s].[SupplierCountry], [s].[SupplierGroup], [s].[SupplierName], [s].[SupplierPostCode], [s].[SupplierState], [s].[SupplierStatus], [s].[SupplierType], [s].[TaxNumber], [s].[TaxRate], [s].[TotalPurchaseValue], [s].[TradingName], [s].[UpdatedByUserID], [s].[UpdatedDate], [s].[WarrantyTerms], [s].[Website], [s].[WithholdingTaxRate], [s].[YTDPurchases], [u].[UserID], [u].[AccessFailedCount], [u].[CreatedByUserID], [u].[CreatedDate], [u].[DateFormat], [u].[DeletedByUserID], [u].[DeletedDate], [u].[Department], [u].[DisplayName], [u].[Email], [u].[FirstName], [u].[IsActive], [u].[IsDeleted], [u].[IsLocked], [u].[JobTitle], [u].[Language], [u].[LastActivityDate], [u].[LastLoginDate], [u].[LastName], [u].[LockoutEnd], [u].[MobileNumber], [u].[MustChangePassword], [u].[PasswordChangedDate], [u].[PasswordHash], [u].[PasswordSalt], [u].[PhoneNumber], [u].[ProfilePicture], [u].[ThemePreference], [u].[TimeZone], [u].[TwoFactorEnabled], [u].[TwoFactorSecret], [u].[UpdatedByUserID], [u].[UpdatedDate], [u].[Username], [w].[WarehouseID], [w].[Address1], [w].[Address2], [w].[AllowNegativeStock], [w].[City], [w].[Country], [w].[CreatedByUserID], [w].[CreatedDate], [w].[CurrentUtilization], [w].[Description], [w].[Email], [w].[IsActive], [w].[IsDefault], [w].[ManagerName], [w].[OperatingHours], [w].[Phone], [w].[PostCode], [w].[State], [w].[StorageCapacity], [w].[TimeZone], [w].[TotalArea], [w].[UpdatedByUserID], [w].[UpdatedDate], [w].[WarehouseCode], [w].[WarehouseName], [w].[WarehouseType]
FROM [GoodsReceipt] AS [g]
LEFT JOIN [PurchaseOrder] AS [p] ON [g].[PurchaseOrderID] = [p].[PurchaseOrderID]
LEFT JOIN [Supplier] AS [s] ON [p].[SupplierID] = [s].[SupplierID]
INNER JOIN [User] AS [u] ON [g].[ReceivedByUserID] = [u].[UserID]
INNER JOIN [Warehouse] AS [w] ON [g].[WarehouseID] = [w].[WarehouseID]
ORDER BY [g].[ReceiptDate] DESC
2025-07-29 16:19:19.501 +03:00 [ERR] An exception occurred while iterating over the results of a query for context type 'nCarry.Web.Data.ApplicationDbContext'.
Microsoft.Data.SqlClient.SqlException (0x80131904): Invalid column name 'Currency'.
   at Microsoft.Data.SqlClient.SqlCommand.<>c.<ExecuteDbDataReaderAsync>b__211_0(Task`1 result)
   at System.Threading.Tasks.ContinuationResultTaskFromResultTask`2.InnerInvoke()
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.InitializeReaderAsync(AsyncEnumerator enumerator, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.SqlServer.Storage.Internal.SqlServerExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
ClientConnectionId:6356fe64-911e-4f04-80c3-ca95dcdfa3b6
Error Number:207,State:1,Class:16
Microsoft.Data.SqlClient.SqlException (0x80131904): Invalid column name 'Currency'.
   at Microsoft.Data.SqlClient.SqlCommand.<>c.<ExecuteDbDataReaderAsync>b__211_0(Task`1 result)
   at System.Threading.Tasks.ContinuationResultTaskFromResultTask`2.InnerInvoke()
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.InitializeReaderAsync(AsyncEnumerator enumerator, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.SqlServer.Storage.Internal.SqlServerExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
ClientConnectionId:6356fe64-911e-4f04-80c3-ca95dcdfa3b6
Error Number:207,State:1,Class:16
2025-07-29 16:19:19.503 +03:00 [ERR] HTTP GET /GoodsReceipt responded 500 in 128.1876 ms
Microsoft.Data.SqlClient.SqlException (0x80131904): Invalid column name 'Currency'.
   at Microsoft.Data.SqlClient.SqlCommand.<>c.<ExecuteDbDataReaderAsync>b__211_0(Task`1 result)
   at System.Threading.Tasks.ContinuationResultTaskFromResultTask`2.InnerInvoke()
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.InitializeReaderAsync(AsyncEnumerator enumerator, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.SqlServer.Storage.Internal.SqlServerExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at nCarry.Web.Controllers.GoodsReceiptController.Index(String status, Nullable`1 fromDate, Nullable`1 toDate) in /Users/<USER>/Source/nCarry/nCarry/nCarry.Web/Controllers/GoodsReceiptController.cs:line 50
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.TaskOfIActionResultExecutor.Execute(ActionContext actionContext, IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeActionMethodAsync>g__Awaited|12_0(ControllerActionInvoker invoker, ValueTask`1 actionResultValueTask)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeInnerFilterAsync>g__Awaited|13_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextResourceFilter>g__Awaited|25_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.Rethrow(ResourceExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeFilterPipelineAsync>g__Awaited|20_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Session.SessionMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Session.SessionMiddleware.Invoke(HttpContext context)
   at Serilog.AspNetCore.RequestLoggingMiddleware.Invoke(HttpContext httpContext)
ClientConnectionId:6356fe64-911e-4f04-80c3-ca95dcdfa3b6
Error Number:207,State:1,Class:16
2025-07-29 16:19:19.512 +03:00 [ERR] An unhandled exception has occurred while executing the request.
Microsoft.Data.SqlClient.SqlException (0x80131904): Invalid column name 'Currency'.
   at Microsoft.Data.SqlClient.SqlCommand.<>c.<ExecuteDbDataReaderAsync>b__211_0(Task`1 result)
   at System.Threading.Tasks.ContinuationResultTaskFromResultTask`2.InnerInvoke()
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.InitializeReaderAsync(AsyncEnumerator enumerator, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.SqlServer.Storage.Internal.SqlServerExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at nCarry.Web.Controllers.GoodsReceiptController.Index(String status, Nullable`1 fromDate, Nullable`1 toDate) in /Users/<USER>/Source/nCarry/nCarry/nCarry.Web/Controllers/GoodsReceiptController.cs:line 50
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.TaskOfIActionResultExecutor.Execute(ActionContext actionContext, IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeActionMethodAsync>g__Awaited|12_0(ControllerActionInvoker invoker, ValueTask`1 actionResultValueTask)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeInnerFilterAsync>g__Awaited|13_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextResourceFilter>g__Awaited|25_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.Rethrow(ResourceExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeFilterPipelineAsync>g__Awaited|20_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Session.SessionMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Session.SessionMiddleware.Invoke(HttpContext context)
   at Serilog.AspNetCore.RequestLoggingMiddleware.Invoke(HttpContext httpContext)
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
ClientConnectionId:6356fe64-911e-4f04-80c3-ca95dcdfa3b6
Error Number:207,State:1,Class:16
2025-07-29 16:19:21.311 +03:00 [INF] HTTP GET /PurchaseOrder responded 200 in 118.8845 ms
2025-07-29 16:19:23.584 +03:00 [INF] HTTP GET /Warehouse responded 200 in 179.5303 ms
2025-07-29 16:19:26.267 +03:00 [INF] HTTP GET /CustomerPriceList responded 200 in 99.5129 ms
2025-07-29 16:22:30.799 +03:00 [INF] HTTP GET /Supplier responded 200 in 91.7923 ms
2025-07-29 16:22:32.647 +03:00 [INF] HTTP GET /Customer responded 200 in 91.3453 ms
2025-07-29 16:22:36.314 +03:00 [INF] HTTP GET /Dashboard responded 200 in 1426.3769 ms
2025-07-29 16:22:36.478 +03:00 [INF] HTTP GET /Dashboard/GetChartData responded 200 in 118.6641 ms
2025-07-29 16:22:43.224 +03:00 [INF] HTTP GET /CustomerPriceList responded 200 in 87.4595 ms
2025-07-29 16:25:39.748 +03:00 [INF] HTTP GET /Dashboard responded 200 in 1469.1133 ms
2025-07-29 16:25:39.894 +03:00 [INF] HTTP GET /Dashboard/GetChartData responded 200 in 115.4040 ms
2025-07-29 16:25:52.971 +03:00 [INF] HTTP GET /CustomerPriceList responded 200 in 100.3278 ms
2025-07-29 16:25:57.026 +03:00 [INF] HTTP GET /CustomerPriceList/Create responded 200 in 86.0413 ms
2025-07-29 16:26:36.074 +03:00 [INF] Creating price list - Code: PL-TC1-0022, CustomerID: 11, Priority: 100
2025-07-29 16:26:36.256 +03:00 [INF] HTTP POST /CustomerPriceList/Create responded 302 in 184.3920 ms
2025-07-29 16:26:36.372 +03:00 [INF] HTTP GET /CustomerPriceList/Details/2 responded 200 in 114.2184 ms
2025-07-29 16:26:41.748 +03:00 [INF] HTTP GET /CustomerPriceList/AddItem responded 200 in 243.0795 ms
2025-07-29 16:26:49.432 +03:00 [INF] HTTP POST /CustomerPriceList/AddItem responded 200 in 163.8663 ms
2025-07-29 16:26:52.701 +03:00 [INF] HTTP POST /CustomerPriceList/AddItem responded 200 in 195.1088 ms
2025-07-29 16:26:59.837 +03:00 [INF] HTTP POST /CustomerPriceList/AddItem responded 200 in 156.3356 ms
2025-07-29 16:27:00.897 +03:00 [INF] HTTP POST /CustomerPriceList/AddItem responded 200 in 181.9320 ms
2025-07-29 16:27:01.095 +03:00 [INF] HTTP POST /CustomerPriceList/AddItem responded 200 in 199.4850 ms
2025-07-29 16:27:01.564 +03:00 [INF] HTTP GET /CustomerPriceList/Details/2 responded 200 in 100.5858 ms
2025-07-29 16:27:05.532 +03:00 [INF] HTTP GET /Customer responded 200 in 97.9852 ms
2025-07-29 16:30:18.995 +03:00 [INF] HTTP GET /Dashboard responded 200 in 1384.8247 ms
2025-07-29 16:30:19.126 +03:00 [INF] HTTP GET /Dashboard/GetChartData responded 200 in 102.8852 ms
2025-07-29 16:33:16.233 +03:00 [INF] HTTP GET /CustomerPriceList responded 200 in 113.8823 ms
2025-07-29 16:33:24.258 +03:00 [INF] HTTP GET /CustomerPriceList/Create responded 200 in 69.5924 ms
2025-07-29 16:33:52.118 +03:00 [INF] HTTP GET /CustomerPriceList responded 200 in 80.8030 ms
2025-07-29 16:33:55.472 +03:00 [INF] HTTP GET /SalesOrder responded 200 in 103.9311 ms
2025-07-29 16:34:00.007 +03:00 [INF] HTTP GET /SalesOrder/Create responded 200 in 353.5828 ms
2025-07-29 16:34:34.833 +03:00 [INF] HTTP GET /Inventory responded 200 in 427.1875 ms
2025-07-29 16:34:42.491 +03:00 [INF] HTTP GET /Shipment responded 200 in 413.1887 ms
2025-07-29 16:35:02.024 +03:00 [INF] HTTP GET /Supplier responded 200 in 161.3581 ms
2025-07-29 16:35:06.966 +03:00 [INF] HTTP GET /Inventory responded 200 in 244.4697 ms
2025-07-29 16:35:15.788 +03:00 [ERR] Failed executing DbCommand (84ms) [Parameters=[@__p_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [i0].[TransactionID], [i0].[BatchNumber], [i0].[CreatedByUserID], [i0].[CreatedDate], [i0].[LocationID], [i0].[Notes], [i0].[ProductID], [i0].[Quantity], [i0].[ReferenceID], [i0].[ReferenceNumber], [i0].[ReferenceType], [i0].[SerialNumber], [i0].[TotalCost], [i0].[TransactionDate], [i0].[TransactionType], [i0].[UOMID], [i0].[UnitCost], [i0].[VariantID], [i0].[WarehouseID], [w].[WarehouseID], [w].[Address1], [w].[Address2], [w].[AllowNegativeStock], [w].[City], [w].[Country], [w].[CreatedByUserID], [w].[CreatedDate], [w].[CurrentUtilization], [w].[Description], [w].[Email], [w].[IsActive], [w].[IsDefault], [w].[ManagerName], [w].[OperatingHours], [w].[Phone], [w].[PostCode], [w].[State], [w].[StorageCapacity], [w].[TimeZone], [w].[TotalArea], [w].[UpdatedByUserID], [w].[UpdatedDate], [w].[WarehouseCode], [w].[WarehouseName], [w].[WarehouseType], [w0].[LocationID], [w0].[Aisle], [w0].[AllowMixedProducts], [w0].[Bay], [w0].[Bin], [w0].[Capacity], [w0].[CreatedDate], [w0].[CurrentOccupancy], [w0].[IsActive], [w0].[IsDefault], [w0].[Level], [w0].[LocationCode], [w0].[LocationName], [w0].[LocationType], [w0].[MaxWeight], [w0].[UpdatedDate], [w0].[WarehouseID], [w0].[Zone], [p].[ProductID], [p].[AllowBackorder], [p].[AllowPreorder], [p].[Brand], [p].[CategoryID], [p].[CommodityCode], [p].[CountryOfOrigin], [p].[CreatedByUserID], [p].[CreatedDate], [p].[Currency], [p].[DeletedByUserID], [p].[DeletedDate], [p].[Description], [p].[DimensionUOMID], [p].[HSCode], [p].[HasVariants], [p].[Height], [p].[InventoryUOMID], [p].[IsActive], [p].[IsDeleted], [p].[IsKit], [p].[IsTaxExempt], [p].[LeadTimeDays], [p].[Length], [p].[ListPrice], [p].[Manufacturer], [p].[ManufacturerPartNumber], [p].[MaxStockLevel], [p].[MinStockLevel], [p].[Notes], [p].[ProductCode], [p].[ProductName], [p].[ProductType], [p].[PurchaseUOMID], [p].[ReorderPoint], [p].[ReorderQuantity], [p].[RequiresBatch], [p].[RequiresSerial], [p].[SalesUOMID], [p].[SellPrice], [p].[ShelfLifeDays], [p].[ShortDescription], [p].[StandardCost], [p].[Status], [p].[TaxCodeID], [p].[TrackInventory], [p].[UpdatedByUserID], [p].[UpdatedDate], [p].[Volume], [p].[VolumeUOMID], [p].[WarrantyDays], [p].[Weight], [p].[WeightUOMID], [p].[Width], [u].[UOMID], [u].[BaseUnit], [u].[ConversionFactor], [u].[CreatedDate], [u].[DecimalPlaces], [u].[IsActive], [u].[Symbol], [u].[UOMCode], [u].[UOMName], [u].[UOMType], [u].[UpdatedDate]
FROM (
    SELECT TOP(@__p_0) [i].[TransactionID], [i].[BatchNumber], [i].[CreatedByUserID], [i].[CreatedDate], [i].[LocationID], [i].[Notes], [i].[ProductID], [i].[Quantity], [i].[ReferenceID], [i].[ReferenceNumber], [i].[ReferenceType], [i].[SerialNumber], [i].[TotalCost], [i].[TransactionDate], [i].[TransactionType], [i].[UOMID], [i].[UnitCost], [i].[VariantID], [i].[WarehouseID]
    FROM [InventoryTransaction] AS [i]
    ORDER BY [i].[TransactionDate] DESC
) AS [i0]
INNER JOIN [Warehouse] AS [w] ON [i0].[WarehouseID] = [w].[WarehouseID]
LEFT JOIN [WarehouseLocation] AS [w0] ON [i0].[LocationID] = [w0].[LocationID]
INNER JOIN [Product] AS [p] ON [i0].[ProductID] = [p].[ProductID]
INNER JOIN [UnitOfMeasure] AS [u] ON [i0].[UOMID] = [u].[UOMID]
ORDER BY [i0].[TransactionDate] DESC
2025-07-29 16:35:15.790 +03:00 [ERR] An exception occurred while iterating over the results of a query for context type 'nCarry.Web.Data.ApplicationDbContext'.
Microsoft.Data.SqlClient.SqlException (0x80131904): Invalid column name 'UOMID'.
   at System.Threading.Tasks.ContinuationResultTaskFromResultTask`2.InnerInvoke()
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.InitializeReaderAsync(AsyncEnumerator enumerator, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.SqlServer.Storage.Internal.SqlServerExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
ClientConnectionId:00aa7b1a-859d-4989-b558-f155d64ad793
Error Number:207,State:1,Class:16
Microsoft.Data.SqlClient.SqlException (0x80131904): Invalid column name 'UOMID'.
   at System.Threading.Tasks.ContinuationResultTaskFromResultTask`2.InnerInvoke()
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.InitializeReaderAsync(AsyncEnumerator enumerator, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.SqlServer.Storage.Internal.SqlServerExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
ClientConnectionId:00aa7b1a-859d-4989-b558-f155d64ad793
Error Number:207,State:1,Class:16
2025-07-29 16:35:15.791 +03:00 [ERR] HTTP GET /Inventory/Transactions responded 500 in 114.3818 ms
Microsoft.Data.SqlClient.SqlException (0x80131904): Invalid column name 'UOMID'.
   at System.Threading.Tasks.ContinuationResultTaskFromResultTask`2.InnerInvoke()
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.InitializeReaderAsync(AsyncEnumerator enumerator, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.SqlServer.Storage.Internal.SqlServerExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at nCarry.Web.Controllers.InventoryController.Transactions(Nullable`1 warehouseId, Nullable`1 productId, Nullable`1 fromDate, Nullable`1 toDate) in /Users/<USER>/Source/nCarry/nCarry/nCarry.Web/Controllers/InventoryController.cs:line 332
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.TaskOfIActionResultExecutor.Execute(ActionContext actionContext, IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeActionMethodAsync>g__Awaited|12_0(ControllerActionInvoker invoker, ValueTask`1 actionResultValueTask)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeInnerFilterAsync>g__Awaited|13_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextResourceFilter>g__Awaited|25_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.Rethrow(ResourceExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeFilterPipelineAsync>g__Awaited|20_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Session.SessionMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Session.SessionMiddleware.Invoke(HttpContext context)
   at Serilog.AspNetCore.RequestLoggingMiddleware.Invoke(HttpContext httpContext)
ClientConnectionId:00aa7b1a-859d-4989-b558-f155d64ad793
Error Number:207,State:1,Class:16
2025-07-29 16:35:15.794 +03:00 [ERR] An unhandled exception has occurred while executing the request.
Microsoft.Data.SqlClient.SqlException (0x80131904): Invalid column name 'UOMID'.
   at System.Threading.Tasks.ContinuationResultTaskFromResultTask`2.InnerInvoke()
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.InitializeReaderAsync(AsyncEnumerator enumerator, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.SqlServer.Storage.Internal.SqlServerExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at nCarry.Web.Controllers.InventoryController.Transactions(Nullable`1 warehouseId, Nullable`1 productId, Nullable`1 fromDate, Nullable`1 toDate) in /Users/<USER>/Source/nCarry/nCarry/nCarry.Web/Controllers/InventoryController.cs:line 332
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.TaskOfIActionResultExecutor.Execute(ActionContext actionContext, IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeActionMethodAsync>g__Awaited|12_0(ControllerActionInvoker invoker, ValueTask`1 actionResultValueTask)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeInnerFilterAsync>g__Awaited|13_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextResourceFilter>g__Awaited|25_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.Rethrow(ResourceExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeFilterPipelineAsync>g__Awaited|20_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Session.SessionMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Session.SessionMiddleware.Invoke(HttpContext context)
   at Serilog.AspNetCore.RequestLoggingMiddleware.Invoke(HttpContext httpContext)
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
ClientConnectionId:00aa7b1a-859d-4989-b558-f155d64ad793
Error Number:207,State:1,Class:16
2025-07-29 16:35:17.798 +03:00 [INF] HTTP GET /Inventory responded 200 in 247.5597 ms
2025-07-29 16:35:38.307 +03:00 [INF] HTTP GET /Inventory/StockAdjustment responded 200 in 250.6713 ms
2025-07-29 16:35:41.641 +03:00 [INF] HTTP GET /api/warehouse/1/locations responded 404 in 2.0553 ms
2025-07-29 16:35:54.128 +03:00 [INF] HTTP GET /Inventory responded 200 in 248.4434 ms
2025-07-29 16:36:46.416 +03:00 [INF] HTTP GET /Shipment responded 200 in 214.0311 ms
2025-07-29 16:37:04.915 +03:00 [INF] HTTP GET /Reports responded 200 in 9.9621 ms
2025-07-29 16:37:12.289 +03:00 [ERR] Failed executing DbCommand (119ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [g].[ReceiptID], [g].[CreatedByUserID], [g].[CreatedDate], [g].[Currency], [g].[Notes], [g].[PostedByUserID], [g].[PostedDate], [g].[PurchaseOrderID], [g].[QualityCheckByUserID], [g].[QualityCheckDate], [g].[QualityCheckStatus], [g].[ReceiptDate], [g].[ReceiptNumber], [g].[ReceiptType], [g].[ReceivedByUserID], [g].[ReceiptStatus], [g].[SupplierDeliveryNote], [g].[SupplierID], [g].[UpdatedByUserID], [g].[UpdatedDate], [g].[WarehouseID], [p].[PurchaseOrderID], [p].[ApprovalNotes], [p].[ApprovalStatus], [p].[ApprovedByUserID], [p].[ApprovedDate], [p].[BuyerID], [p].[CreatedByUserID], [p].[CreatedDate], [p].[Currency], [p].[DeletedByUserID], [p].[DeletedDate], [p].[DeliveryAddressID], [p].[DeliveryInstructions], [p].[DeliveryWarehouseID], [p].[DiscountAmount], [p].[ExchangeRate], [p].[ExpectedDate], [p].[FreightAmount], [p].[FreightTerms], [p].[InternalNotes], [p].[IsDeleted], [p].[Notes], [p].[OrderDate], [p].[OrderStatus], [p].[OrderType], [p].[PaymentMethod], [p].[PaymentTerms], [p].[PromisedDate], [p].[PurchaseOrderNumber], [p].[RequiredDate], [p].[RequisitionID], [p].[ShippingMethod], [p].[SubTotal], [p].[SupplierID], [p].[SupplierReference], [p].[TaxAmount], [p].[TermsAndConditions], [p].[TotalAmount], [p].[UpdatedByUserID], [p].[UpdatedDate], [s].[SupplierID], [s].[APIEnabled], [s].[BankAccountDetails], [s].[BlockedByUserID], [s].[BlockedDate], [s].[BlockedReason], [s].[CertificateExpiryDate], [s].[CertificateNumber], [s].[ComplianceStatus], [s].[ContactEmail], [s].[ContactLandline], [s].[ContactMobile], [s].[ContactPhone], [s].[CreatedByUserID], [s].[CreatedDate], [s].[DeletedByUserID], [s].[DeletedDate], [s].[DeliveryDays], [s].[DeliveryScore], [s].[EDIEnabled], [s].[EarlyPaymentDiscountDays], [s].[EarlyPaymentDiscountPercent], [s].[InsuranceExpiryDate], [s].[InsurancePolicyNumber], [s].[IntegrationKey], [s].[InternalNotes], [s].[IsActive], [s].[IsDeleted], [s].[IsPreferred], [s].[LastOrderDate], [s].[LeadTimeDays], [s].[MinimumOrderValue], [s].[Notes], [s].[OrderCutoffTime], [s].[OutstandingBalance], [s].[PaymentMethodID], [s].[PaymentTermDays], [s].[PaymentTerms], [s].[PreferredCurrency], [s].[PriceScore], [s].[QualityScore], [s].[Rating], [s].[ReferenceCode], [s].[ResponsibleContact], [s].[ReturnPolicy], [s].[SupplierAddress], [s].[SupplierAddress2], [s].[SupplierCity], [s].[SupplierCode], [s].[SupplierCountry], [s].[SupplierGroup], [s].[SupplierName], [s].[SupplierPostCode], [s].[SupplierState], [s].[SupplierStatus], [s].[SupplierType], [s].[TaxNumber], [s].[TaxRate], [s].[TotalPurchaseValue], [s].[TradingName], [s].[UpdatedByUserID], [s].[UpdatedDate], [s].[WarrantyTerms], [s].[Website], [s].[WithholdingTaxRate], [s].[YTDPurchases], [u].[UserID], [u].[AccessFailedCount], [u].[CreatedByUserID], [u].[CreatedDate], [u].[DateFormat], [u].[DeletedByUserID], [u].[DeletedDate], [u].[Department], [u].[DisplayName], [u].[Email], [u].[FirstName], [u].[IsActive], [u].[IsDeleted], [u].[IsLocked], [u].[JobTitle], [u].[Language], [u].[LastActivityDate], [u].[LastLoginDate], [u].[LastName], [u].[LockoutEnd], [u].[MobileNumber], [u].[MustChangePassword], [u].[PasswordChangedDate], [u].[PasswordHash], [u].[PasswordSalt], [u].[PhoneNumber], [u].[ProfilePicture], [u].[ThemePreference], [u].[TimeZone], [u].[TwoFactorEnabled], [u].[TwoFactorSecret], [u].[UpdatedByUserID], [u].[UpdatedDate], [u].[Username], [w].[WarehouseID], [w].[Address1], [w].[Address2], [w].[AllowNegativeStock], [w].[City], [w].[Country], [w].[CreatedByUserID], [w].[CreatedDate], [w].[CurrentUtilization], [w].[Description], [w].[Email], [w].[IsActive], [w].[IsDefault], [w].[ManagerName], [w].[OperatingHours], [w].[Phone], [w].[PostCode], [w].[State], [w].[StorageCapacity], [w].[TimeZone], [w].[TotalArea], [w].[UpdatedByUserID], [w].[UpdatedDate], [w].[WarehouseCode], [w].[WarehouseName], [w].[WarehouseType]
FROM [GoodsReceipt] AS [g]
LEFT JOIN [PurchaseOrder] AS [p] ON [g].[PurchaseOrderID] = [p].[PurchaseOrderID]
LEFT JOIN [Supplier] AS [s] ON [p].[SupplierID] = [s].[SupplierID]
INNER JOIN [User] AS [u] ON [g].[ReceivedByUserID] = [u].[UserID]
INNER JOIN [Warehouse] AS [w] ON [g].[WarehouseID] = [w].[WarehouseID]
ORDER BY [g].[ReceiptDate] DESC
2025-07-29 16:37:12.290 +03:00 [ERR] An exception occurred while iterating over the results of a query for context type 'nCarry.Web.Data.ApplicationDbContext'.
Microsoft.Data.SqlClient.SqlException (0x80131904): Invalid column name 'Currency'.
   at System.Threading.Tasks.ContinuationResultTaskFromResultTask`2.InnerInvoke()
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.InitializeReaderAsync(AsyncEnumerator enumerator, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.SqlServer.Storage.Internal.SqlServerExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
ClientConnectionId:00aa7b1a-859d-4989-b558-f155d64ad793
Error Number:207,State:1,Class:16
Microsoft.Data.SqlClient.SqlException (0x80131904): Invalid column name 'Currency'.
   at System.Threading.Tasks.ContinuationResultTaskFromResultTask`2.InnerInvoke()
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.InitializeReaderAsync(AsyncEnumerator enumerator, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.SqlServer.Storage.Internal.SqlServerExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
ClientConnectionId:00aa7b1a-859d-4989-b558-f155d64ad793
Error Number:207,State:1,Class:16
2025-07-29 16:37:12.291 +03:00 [ERR] HTTP GET /GoodsReceipt responded 500 in 123.3715 ms
Microsoft.Data.SqlClient.SqlException (0x80131904): Invalid column name 'Currency'.
   at System.Threading.Tasks.ContinuationResultTaskFromResultTask`2.InnerInvoke()
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.InitializeReaderAsync(AsyncEnumerator enumerator, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.SqlServer.Storage.Internal.SqlServerExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at nCarry.Web.Controllers.GoodsReceiptController.Index(String status, Nullable`1 fromDate, Nullable`1 toDate) in /Users/<USER>/Source/nCarry/nCarry/nCarry.Web/Controllers/GoodsReceiptController.cs:line 50
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.TaskOfIActionResultExecutor.Execute(ActionContext actionContext, IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeActionMethodAsync>g__Awaited|12_0(ControllerActionInvoker invoker, ValueTask`1 actionResultValueTask)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeInnerFilterAsync>g__Awaited|13_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextResourceFilter>g__Awaited|25_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.Rethrow(ResourceExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeFilterPipelineAsync>g__Awaited|20_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Session.SessionMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Session.SessionMiddleware.Invoke(HttpContext context)
   at Serilog.AspNetCore.RequestLoggingMiddleware.Invoke(HttpContext httpContext)
ClientConnectionId:00aa7b1a-859d-4989-b558-f155d64ad793
Error Number:207,State:1,Class:16
2025-07-29 16:37:12.292 +03:00 [ERR] An unhandled exception has occurred while executing the request.
Microsoft.Data.SqlClient.SqlException (0x80131904): Invalid column name 'Currency'.
   at System.Threading.Tasks.ContinuationResultTaskFromResultTask`2.InnerInvoke()
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.InitializeReaderAsync(AsyncEnumerator enumerator, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.SqlServer.Storage.Internal.SqlServerExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at nCarry.Web.Controllers.GoodsReceiptController.Index(String status, Nullable`1 fromDate, Nullable`1 toDate) in /Users/<USER>/Source/nCarry/nCarry/nCarry.Web/Controllers/GoodsReceiptController.cs:line 50
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.TaskOfIActionResultExecutor.Execute(ActionContext actionContext, IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeActionMethodAsync>g__Awaited|12_0(ControllerActionInvoker invoker, ValueTask`1 actionResultValueTask)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeInnerFilterAsync>g__Awaited|13_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextResourceFilter>g__Awaited|25_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.Rethrow(ResourceExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeFilterPipelineAsync>g__Awaited|20_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Session.SessionMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Session.SessionMiddleware.Invoke(HttpContext context)
   at Serilog.AspNetCore.RequestLoggingMiddleware.Invoke(HttpContext httpContext)
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
ClientConnectionId:00aa7b1a-859d-4989-b558-f155d64ad793
Error Number:207,State:1,Class:16
2025-07-29 16:37:13.845 +03:00 [INF] HTTP GET /Reports responded 200 in 1.0157 ms
2025-07-29 16:37:16.753 +03:00 [INF] HTTP GET /PurchaseOrder responded 200 in 119.1202 ms
2025-07-29 16:37:21.128 +03:00 [INF] HTTP GET /CustomerPriceList responded 200 in 224.1825 ms
2025-07-29 16:37:27.332 +03:00 [INF] HTTP GET /ProductCategory responded 200 in 83.7931 ms
2025-07-29 16:37:42.918 +03:00 [INF] HTTP GET /Product responded 200 in 135.3405 ms
2025-07-29 16:37:44.623 +03:00 [INF] HTTP GET /Product/Create responded 200 in 490.1918 ms
2025-07-29 16:38:19.136 +03:00 [INF] HTTP GET /Product responded 200 in 87.6103 ms
2025-07-29 16:38:20.847 +03:00 [INF] HTTP GET /ProductCategory responded 200 in 72.5053 ms
2025-07-29 16:38:22.178 +03:00 [INF] HTTP GET /ProductCategory/Create responded 200 in 19.1277 ms
2025-07-29 16:38:30.965 +03:00 [INF] HTTP GET /ProductCategory responded 200 in 368.9694 ms
2025-07-29 16:39:33.494 +03:00 [INF] HTTP GET /Product responded 200 in 81.5503 ms
2025-07-29 16:39:36.178 +03:00 [INF] HTTP GET /Product/Create responded 200 in 1090.9548 ms
2025-07-29 16:41:10.791 +03:00 [INF] HTTP GET /SalesOrder responded 200 in 120.6826 ms
2025-07-29 16:41:18.432 +03:00 [INF] HTTP GET /SalesOrder/Create responded 200 in 259.1539 ms
2025-07-29 16:42:04.969 +03:00 [INF] HTTP GET /Warehouse responded 200 in 86.3425 ms
2025-07-29 16:42:07.456 +03:00 [INF] HTTP GET /Warehouse/Create responded 200 in 52.5528 ms
2025-07-29 16:42:15.873 +03:00 [INF] HTTP GET /Warehouse responded 200 in 111.2088 ms
2025-07-29 16:42:22.788 +03:00 [INF] HTTP GET /Warehouse/Create responded 200 in 7.2199 ms
2025-07-29 16:42:35.272 +03:00 [INF] HTTP GET /Warehouse responded 200 in 65.7485 ms
2025-07-29 16:43:10.140 +03:00 [INF] HTTP GET /Supplier responded 200 in 89.9354 ms
2025-07-29 16:43:29.652 +03:00 [INF] HTTP GET /Product responded 200 in 80.7003 ms
2025-07-29 16:44:28.542 +03:00 [INF] HTTP GET /Product/Create responded 200 in 413.7310 ms
2025-07-29 16:44:45.909 +03:00 [INF] HTTP GET /CustomerPriceList responded 200 in 91.2220 ms
2025-07-29 16:44:59.245 +03:00 [INF] HTTP GET /Product responded 200 in 92.8519 ms
2025-07-29 16:45:08.719 +03:00 [INF] HTTP GET /SalesOrder responded 200 in 102.1969 ms
2025-07-29 16:45:12.076 +03:00 [INF] HTTP GET /SalesOrder/Create responded 200 in 272.8384 ms
2025-07-29 16:45:26.601 +03:00 [INF] HTTP POST /SalesOrder/Create responded 302 in 194.6408 ms
2025-07-29 16:45:26.711 +03:00 [INF] HTTP GET /SalesOrder responded 200 in 107.2627 ms
2025-07-29 16:45:29.867 +03:00 [ERR] Failed executing DbCommand (104ms) [Parameters=[@__id_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [s1].[OrderID], [s1].[BalanceAmount], [s1].[BillingAddressID], [s1].[CarrierID], [s1].[CreatedByUserID], [s1].[CreatedDate], [s1].[Currency], [s1].[CustomerID], [s1].[CustomerPO], [s1].[DeletedByUserID], [s1].[DeletedDate], [s1].[DeliveredDate], [s1].[DeliveryInstructions], [s1].[DiscountAmount], [s1].[ExchangeRate], [s1].[FreightTerms], [s1].[InternalNotes], [s1].[IsDeleted], [s1].[Notes], [s1].[OrderDate], [s1].[OrderNumber], [s1].[OrderStatus], [s1].[OrderType], [s1].[PaidAmount], [s1].[PaymentDueDate], [s1].[PaymentTerms], [s1].[PickingPriority], [s1].[PromisedDate], [s1].[QuoteID], [s1].[RequestedDate], [s1].[SalesRepID], [s1].[ShippedDate], [s1].[ShippingAddressID], [s1].[ShippingAmount], [s1].[ShippingMethod], [s1].[SubTotal], [s1].[TaxAmount], [s1].[TotalAmount], [s1].[TrackingNumber], [s1].[UpdatedByUserID], [s1].[UpdatedDate], [s1].[WarehouseID], [s1].[CustomerID0], [s1].[BlockedByUserID], [s1].[BlockedDate], [s1].[BlockedReason], [s1].[ContactEmail], [s1].[ContactLandline], [s1].[ContactMobile], [s1].[ContactPhone], [s1].[CreatedByUserID0], [s1].[CreatedDate0], [s1].[CreditLimit], [s1].[CreditReviewDate], [s1].[CreditStatus], [s1].[CurrencyCode], [s1].[CustomerAddress], [s1].[CustomerAddress2], [s1].[CustomerCity], [s1].[CustomerCode], [s1].[CustomerCountry], [s1].[CustomerGroup], [s1].[CustomerName], [s1].[CustomerPostCode], [s1].[CustomerSince], [s1].[CustomerState], [s1].[CustomerStatus], [s1].[CustomerType], [s1].[DeletedByUserID0], [s1].[DeletedDate0], [s1].[DeliveryInstructions0], [s1].[DeliveryTimePreference], [s1].[DiscountPercent], [s1].[InternalNotes0], [s1].[IsActive], [s1].[IsDeleted0], [s1].[LastOrderDate], [s1].[LeadSource], [s1].[LoyaltyPoints], [s1].[Notes0], [s1].[OutstandingBalance], [s1].[PaymentMethodID], [s1].[PaymentTerm], [s1].[PreferredCarrierID], [s1].[PriceListID], [s1].[Rating], [s1].[ReferenceCode], [s1].[ResponsibleContact], [s1].[SalesRepID0], [s1].[TaxExempt], [s1].[TaxNumber], [s1].[TerritoryID], [s1].[TotalOrderValue], [s1].[TradingName], [s1].[UpdatedByUserID0], [s1].[UpdatedDate0], [s1].[Website], [s1].[YTDSales], [s1].[AddressID], [s1].[Address1], [s1].[Address2], [s1].[AddressName], [s1].[AddressType], [s1].[City], [s1].[ContactName], [s1].[ContactPhone0], [s1].[Country], [s1].[CreatedDate1], [s1].[CustomerID1], [s1].[IsActive0], [s1].[IsDefault], [s1].[PostCode], [s1].[State], [s1].[UpdatedDate1], [s1].[AddressID0], [s1].[Address10], [s1].[Address20], [s1].[AddressName0], [s1].[AddressType0], [s1].[City0], [s1].[ContactName0], [s1].[ContactPhone1], [s1].[Country0], [s1].[CreatedDate2], [s1].[CustomerID2], [s1].[IsActive1], [s1].[IsDefault0], [s1].[PostCode0], [s1].[State0], [s1].[UpdatedDate2], [s2].[OrderItemID], [s2].[BackorderedQuantity], [s2].[BatchNumber], [s2].[CancelledQuantity], [s2].[Currency], [s2].[Description], [s2].[DiscountAmount], [s2].[DiscountPercent], [s2].[ItemStatus], [s2].[LineNumber], [s2].[LineTotal], [s2].[LocationID], [s2].[Notes], [s2].[OrderID], [s2].[OrderedQuantity], [s2].[ProductCode], [s2].[ProductID], [s2].[ProductName], [s2].[SerialNumber], [s2].[ShippedQuantity], [s2].[SortOrder], [s2].[TaxAmount], [s2].[TaxRate], [s2].[UOMID], [s2].[UnitCost], [s2].[UnitPrice], [s2].[VariantID], [s2].[WarehouseID], [s2].[ProductID0], [s2].[AllowBackorder], [s2].[AllowPreorder], [s2].[Brand], [s2].[CategoryID], [s2].[CommodityCode], [s2].[CountryOfOrigin], [s2].[CreatedByUserID], [s2].[CreatedDate], [s2].[Currency0], [s2].[DeletedByUserID], [s2].[DeletedDate], [s2].[Description0], [s2].[DimensionUOMID], [s2].[HSCode], [s2].[HasVariants], [s2].[Height], [s2].[InventoryUOMID], [s2].[IsActive], [s2].[IsDeleted], [s2].[IsKit], [s2].[IsTaxExempt], [s2].[LeadTimeDays], [s2].[Length], [s2].[ListPrice], [s2].[Manufacturer], [s2].[ManufacturerPartNumber], [s2].[MaxStockLevel], [s2].[MinStockLevel], [s2].[Notes0], [s2].[ProductCode0], [s2].[ProductName0], [s2].[ProductType], [s2].[PurchaseUOMID], [s2].[ReorderPoint], [s2].[ReorderQuantity], [s2].[RequiresBatch], [s2].[RequiresSerial], [s2].[SalesUOMID], [s2].[SellPrice], [s2].[ShelfLifeDays], [s2].[ShortDescription], [s2].[StandardCost], [s2].[Status], [s2].[TaxCodeID], [s2].[TrackInventory], [s2].[UpdatedByUserID], [s2].[UpdatedDate], [s2].[Volume], [s2].[VolumeUOMID], [s2].[WarrantyDays], [s2].[Weight], [s2].[WeightUOMID], [s2].[Width], [s2].[UOMID0], [s2].[BaseUnit], [s2].[ConversionFactor], [s2].[CreatedDate0], [s2].[DecimalPlaces], [s2].[IsActive0], [s2].[Symbol], [s2].[UOMCode], [s2].[UOMName], [s2].[UOMType], [s2].[UpdatedDate0], [s2].[VariantID0], [s2].[AdditionalCost], [s2].[AdditionalPrice], [s2].[Attribute1Name], [s2].[Attribute1Value], [s2].[Attribute2Name], [s2].[Attribute2Value], [s2].[Attribute3Name], [s2].[Attribute3Value], [s2].[CreatedByUserID0], [s2].[CreatedDate1], [s2].[ImageURL], [s2].[IsActive1], [s2].[ProductID1], [s2].[SortOrder0], [s2].[UpdatedByUserID0], [s2].[UpdatedDate1], [s2].[VariantCode], [s2].[VariantName], [s2].[VariantType], [s2].[VariantValue], [s2].[Weight0]
FROM (
    SELECT TOP(1) [s].[OrderID], [s].[BalanceAmount], [s].[BillingAddressID], [s].[CarrierID], [s].[CreatedByUserID], [s].[CreatedDate], [s].[Currency], [s].[CustomerID], [s].[CustomerPO], [s].[DeletedByUserID], [s].[DeletedDate], [s].[DeliveredDate], [s].[DeliveryInstructions], [s].[DiscountAmount], [s].[ExchangeRate], [s].[FreightTerms], [s].[InternalNotes], [s].[IsDeleted], [s].[Notes], [s].[OrderDate], [s].[OrderNumber], [s].[OrderStatus], [s].[OrderType], [s].[PaidAmount], [s].[PaymentDueDate], [s].[PaymentTerms], [s].[PickingPriority], [s].[PromisedDate], [s].[QuoteID], [s].[RequestedDate], [s].[SalesRepID], [s].[ShippedDate], [s].[ShippingAddressID], [s].[ShippingAmount], [s].[ShippingMethod], [s].[SubTotal], [s].[TaxAmount], [s].[TotalAmount], [s].[TrackingNumber], [s].[UpdatedByUserID], [s].[UpdatedDate], [s].[WarehouseID], [c].[CustomerID] AS [CustomerID0], [c].[BlockedByUserID], [c].[BlockedDate], [c].[BlockedReason], [c].[ContactEmail], [c].[ContactLandline], [c].[ContactMobile], [c].[ContactPhone], [c].[CreatedByUserID] AS [CreatedByUserID0], [c].[CreatedDate] AS [CreatedDate0], [c].[CreditLimit], [c].[CreditReviewDate], [c].[CreditStatus], [c].[CurrencyCode], [c].[CustomerAddress], [c].[CustomerAddress2], [c].[CustomerCity], [c].[CustomerCode], [c].[CustomerCountry], [c].[CustomerGroup], [c].[CustomerName], [c].[CustomerPostCode], [c].[CustomerSince], [c].[CustomerState], [c].[CustomerStatus], [c].[CustomerType], [c].[DeletedByUserID] AS [DeletedByUserID0], [c].[DeletedDate] AS [DeletedDate0], [c].[DeliveryInstructions] AS [DeliveryInstructions0], [c].[DeliveryTimePreference], [c].[DiscountPercent], [c].[InternalNotes] AS [InternalNotes0], [c].[IsActive], [c].[IsDeleted] AS [IsDeleted0], [c].[LastOrderDate], [c].[LeadSource], [c].[LoyaltyPoints], [c].[Notes] AS [Notes0], [c].[OutstandingBalance], [c].[PaymentMethodID], [c].[PaymentTerm], [c].[PreferredCarrierID], [c].[PriceListID], [c].[Rating], [c].[ReferenceCode], [c].[ResponsibleContact], [c].[SalesRepID] AS [SalesRepID0], [c].[TaxExempt], [c].[TaxNumber], [c].[TerritoryID], [c].[TotalOrderValue], [c].[TradingName], [c].[UpdatedByUserID] AS [UpdatedByUserID0], [c].[UpdatedDate] AS [UpdatedDate0], [c].[Website], [c].[YTDSales], [c0].[AddressID], [c0].[Address1], [c0].[Address2], [c0].[AddressName], [c0].[AddressType], [c0].[City], [c0].[ContactName], [c0].[ContactPhone] AS [ContactPhone0], [c0].[Country], [c0].[CreatedDate] AS [CreatedDate1], [c0].[CustomerID] AS [CustomerID1], [c0].[IsActive] AS [IsActive0], [c0].[IsDefault], [c0].[PostCode], [c0].[State], [c0].[UpdatedDate] AS [UpdatedDate1], [c1].[AddressID] AS [AddressID0], [c1].[Address1] AS [Address10], [c1].[Address2] AS [Address20], [c1].[AddressName] AS [AddressName0], [c1].[AddressType] AS [AddressType0], [c1].[City] AS [City0], [c1].[ContactName] AS [ContactName0], [c1].[ContactPhone] AS [ContactPhone1], [c1].[Country] AS [Country0], [c1].[CreatedDate] AS [CreatedDate2], [c1].[CustomerID] AS [CustomerID2], [c1].[IsActive] AS [IsActive1], [c1].[IsDefault] AS [IsDefault0], [c1].[PostCode] AS [PostCode0], [c1].[State] AS [State0], [c1].[UpdatedDate] AS [UpdatedDate2]
    FROM [SalesOrder] AS [s]
    INNER JOIN [Customer] AS [c] ON [s].[CustomerID] = [c].[CustomerID]
    LEFT JOIN [CustomerAddress] AS [c0] ON [s].[BillingAddressID] = [c0].[AddressID]
    LEFT JOIN [CustomerAddress] AS [c1] ON [s].[ShippingAddressID] = [c1].[AddressID]
    WHERE [s].[OrderID] = @__id_0 AND [s].[IsDeleted] = CAST(0 AS bit)
) AS [s1]
LEFT JOIN (
    SELECT [s0].[OrderItemID], [s0].[BackorderedQuantity], [s0].[BatchNumber], [s0].[CancelledQuantity], [s0].[Currency], [s0].[Description], [s0].[DiscountAmount], [s0].[DiscountPercent], [s0].[ItemStatus], [s0].[LineNumber], [s0].[LineTotal], [s0].[LocationID], [s0].[Notes], [s0].[OrderID], [s0].[OrderedQuantity], [s0].[ProductCode], [s0].[ProductID], [s0].[ProductName], [s0].[SerialNumber], [s0].[ShippedQuantity], [s0].[SortOrder], [s0].[TaxAmount], [s0].[TaxRate], [s0].[UOMID], [s0].[UnitCost], [s0].[UnitPrice], [s0].[VariantID], [s0].[WarehouseID], [p].[ProductID] AS [ProductID0], [p].[AllowBackorder], [p].[AllowPreorder], [p].[Brand], [p].[CategoryID], [p].[CommodityCode], [p].[CountryOfOrigin], [p].[CreatedByUserID], [p].[CreatedDate], [p].[Currency] AS [Currency0], [p].[DeletedByUserID], [p].[DeletedDate], [p].[Description] AS [Description0], [p].[DimensionUOMID], [p].[HSCode], [p].[HasVariants], [p].[Height], [p].[InventoryUOMID], [p].[IsActive], [p].[IsDeleted], [p].[IsKit], [p].[IsTaxExempt], [p].[LeadTimeDays], [p].[Length], [p].[ListPrice], [p].[Manufacturer], [p].[ManufacturerPartNumber], [p].[MaxStockLevel], [p].[MinStockLevel], [p].[Notes] AS [Notes0], [p].[ProductCode] AS [ProductCode0], [p].[ProductName] AS [ProductName0], [p].[ProductType], [p].[PurchaseUOMID], [p].[ReorderPoint], [p].[ReorderQuantity], [p].[RequiresBatch], [p].[RequiresSerial], [p].[SalesUOMID], [p].[SellPrice], [p].[ShelfLifeDays], [p].[ShortDescription], [p].[StandardCost], [p].[Status], [p].[TaxCodeID], [p].[TrackInventory], [p].[UpdatedByUserID], [p].[UpdatedDate], [p].[Volume], [p].[VolumeUOMID], [p].[WarrantyDays], [p].[Weight], [p].[WeightUOMID], [p].[Width], [u].[UOMID] AS [UOMID0], [u].[BaseUnit], [u].[ConversionFactor], [u].[CreatedDate] AS [CreatedDate0], [u].[DecimalPlaces], [u].[IsActive] AS [IsActive0], [u].[Symbol], [u].[UOMCode], [u].[UOMName], [u].[UOMType], [u].[UpdatedDate] AS [UpdatedDate0], [p0].[VariantID] AS [VariantID0], [p0].[AdditionalCost], [p0].[AdditionalPrice], [p0].[Attribute1Name], [p0].[Attribute1Value], [p0].[Attribute2Name], [p0].[Attribute2Value], [p0].[Attribute3Name], [p0].[Attribute3Value], [p0].[CreatedByUserID] AS [CreatedByUserID0], [p0].[CreatedDate] AS [CreatedDate1], [p0].[ImageURL], [p0].[IsActive] AS [IsActive1], [p0].[ProductID] AS [ProductID1], [p0].[SortOrder] AS [SortOrder0], [p0].[UpdatedByUserID] AS [UpdatedByUserID0], [p0].[UpdatedDate] AS [UpdatedDate1], [p0].[VariantCode], [p0].[VariantName], [p0].[VariantType], [p0].[VariantValue], [p0].[Weight] AS [Weight0]
    FROM [SalesOrderItem] AS [s0]
    INNER JOIN [Product] AS [p] ON [s0].[ProductID] = [p].[ProductID]
    INNER JOIN [UnitOfMeasure] AS [u] ON [s0].[UOMID] = [u].[UOMID]
    LEFT JOIN [ProductVariant] AS [p0] ON [s0].[VariantID] = [p0].[VariantID]
) AS [s2] ON [s1].[OrderID] = [s2].[OrderID]
ORDER BY [s1].[OrderID], [s1].[CustomerID0], [s1].[AddressID], [s1].[AddressID0], [s2].[OrderItemID], [s2].[ProductID0], [s2].[UOMID0]
2025-07-29 16:45:29.867 +03:00 [ERR] An exception occurred while iterating over the results of a query for context type 'nCarry.Web.Data.ApplicationDbContext'.
Microsoft.Data.SqlClient.SqlException (0x80131904): Invalid column name 'Currency'.
Invalid column name 'Attribute1Name'.
Invalid column name 'Attribute1Value'.
Invalid column name 'Attribute2Name'.
Invalid column name 'Attribute2Value'.
Invalid column name 'Attribute3Name'.
Invalid column name 'Attribute3Value'.
Invalid column name 'CreatedByUserID'.
Invalid column name 'ImageURL'.
Invalid column name 'UpdatedByUserID'.
   at System.Threading.Tasks.ContinuationResultTaskFromResultTask`2.InnerInvoke()
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.InitializeReaderAsync(AsyncEnumerator enumerator, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.SqlServer.Storage.Internal.SqlServerExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
ClientConnectionId:00aa7b1a-859d-4989-b558-f155d64ad793
Error Number:207,State:1,Class:16
Microsoft.Data.SqlClient.SqlException (0x80131904): Invalid column name 'Currency'.
Invalid column name 'Attribute1Name'.
Invalid column name 'Attribute1Value'.
Invalid column name 'Attribute2Name'.
Invalid column name 'Attribute2Value'.
Invalid column name 'Attribute3Name'.
Invalid column name 'Attribute3Value'.
Invalid column name 'CreatedByUserID'.
Invalid column name 'ImageURL'.
Invalid column name 'UpdatedByUserID'.
   at System.Threading.Tasks.ContinuationResultTaskFromResultTask`2.InnerInvoke()
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.InitializeReaderAsync(AsyncEnumerator enumerator, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.SqlServer.Storage.Internal.SqlServerExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
ClientConnectionId:00aa7b1a-859d-4989-b558-f155d64ad793
Error Number:207,State:1,Class:16
2025-07-29 16:45:29.868 +03:00 [ERR] HTTP GET /SalesOrder/Details/28 responded 500 in 141.6045 ms
Microsoft.Data.SqlClient.SqlException (0x80131904): Invalid column name 'Currency'.
Invalid column name 'Attribute1Name'.
Invalid column name 'Attribute1Value'.
Invalid column name 'Attribute2Name'.
Invalid column name 'Attribute2Value'.
Invalid column name 'Attribute3Name'.
Invalid column name 'Attribute3Value'.
Invalid column name 'CreatedByUserID'.
Invalid column name 'ImageURL'.
Invalid column name 'UpdatedByUserID'.
   at System.Threading.Tasks.ContinuationResultTaskFromResultTask`2.InnerInvoke()
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.InitializeReaderAsync(AsyncEnumerator enumerator, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.SqlServer.Storage.Internal.SqlServerExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
   at Microsoft.EntityFrameworkCore.Query.ShapedQueryCompilingExpressionVisitor.SingleOrDefaultAsync[TSource](IAsyncEnumerable`1 asyncEnumerable, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.ShapedQueryCompilingExpressionVisitor.SingleOrDefaultAsync[TSource](IAsyncEnumerable`1 asyncEnumerable, CancellationToken cancellationToken)
   at nCarry.Web.Controllers.SalesOrderController.Details(Nullable`1 id) in /Users/<USER>/Source/nCarry/nCarry/nCarry.Web/Controllers/SalesOrderController.cs:line 43
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.TaskOfIActionResultExecutor.Execute(ActionContext actionContext, IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeActionMethodAsync>g__Awaited|12_0(ControllerActionInvoker invoker, ValueTask`1 actionResultValueTask)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeInnerFilterAsync>g__Awaited|13_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextResourceFilter>g__Awaited|25_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.Rethrow(ResourceExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeFilterPipelineAsync>g__Awaited|20_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Session.SessionMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Session.SessionMiddleware.Invoke(HttpContext context)
   at Serilog.AspNetCore.RequestLoggingMiddleware.Invoke(HttpContext httpContext)
ClientConnectionId:00aa7b1a-859d-4989-b558-f155d64ad793
Error Number:207,State:1,Class:16
2025-07-29 16:45:29.873 +03:00 [ERR] An unhandled exception has occurred while executing the request.
Microsoft.Data.SqlClient.SqlException (0x80131904): Invalid column name 'Currency'.
Invalid column name 'Attribute1Name'.
Invalid column name 'Attribute1Value'.
Invalid column name 'Attribute2Name'.
Invalid column name 'Attribute2Value'.
Invalid column name 'Attribute3Name'.
Invalid column name 'Attribute3Value'.
Invalid column name 'CreatedByUserID'.
Invalid column name 'ImageURL'.
Invalid column name 'UpdatedByUserID'.
   at System.Threading.Tasks.ContinuationResultTaskFromResultTask`2.InnerInvoke()
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.InitializeReaderAsync(AsyncEnumerator enumerator, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.SqlServer.Storage.Internal.SqlServerExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
   at Microsoft.EntityFrameworkCore.Query.ShapedQueryCompilingExpressionVisitor.SingleOrDefaultAsync[TSource](IAsyncEnumerable`1 asyncEnumerable, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.ShapedQueryCompilingExpressionVisitor.SingleOrDefaultAsync[TSource](IAsyncEnumerable`1 asyncEnumerable, CancellationToken cancellationToken)
   at nCarry.Web.Controllers.SalesOrderController.Details(Nullable`1 id) in /Users/<USER>/Source/nCarry/nCarry/nCarry.Web/Controllers/SalesOrderController.cs:line 43
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.TaskOfIActionResultExecutor.Execute(ActionContext actionContext, IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeActionMethodAsync>g__Awaited|12_0(ControllerActionInvoker invoker, ValueTask`1 actionResultValueTask)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeInnerFilterAsync>g__Awaited|13_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextResourceFilter>g__Awaited|25_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.Rethrow(ResourceExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeFilterPipelineAsync>g__Awaited|20_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Session.SessionMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Session.SessionMiddleware.Invoke(HttpContext context)
   at Serilog.AspNetCore.RequestLoggingMiddleware.Invoke(HttpContext httpContext)
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
ClientConnectionId:00aa7b1a-859d-4989-b558-f155d64ad793
Error Number:207,State:1,Class:16
2025-07-29 16:45:31.375 +03:00 [INF] HTTP GET /SalesOrder responded 200 in 84.2226 ms
2025-07-29 16:45:36.488 +03:00 [INF] HTTP GET /CustomerPriceList responded 200 in 83.1908 ms
2025-07-29 16:45:52.378 +03:00 [INF] HTTP GET /Dashboard responded 200 in 1338.3966 ms
2025-07-29 16:45:52.531 +03:00 [INF] HTTP GET /Dashboard/GetChartData responded 200 in 113.1395 ms
2025-07-29 16:46:16.548 +03:00 [INF] HTTP GET /SalesOrder responded 200 in 99.0912 ms
2025-07-29 16:47:20.347 +03:00 [INF] HTTP GET /User responded 200 in 116.2405 ms
2025-07-29 16:47:25.287 +03:00 [WRN] Compiling a query which loads related collections for more than one collection navigation, either via 'Include' or through projection, but no 'QuerySplittingBehavior' has been configured. By default, Entity Framework will use 'QuerySplittingBehavior.SingleQuery', which can potentially result in slow query performance. See https://go.microsoft.com/fwlink/?linkid=2134277 for more information. To identify the query that's triggering this warning call 'ConfigureWarnings(w => w.Throw(RelationalEventId.MultipleCollectionIncludeWarning))'.
2025-07-29 16:47:25.428 +03:00 [INF] HTTP GET /Role responded 200 in 155.7718 ms
2025-07-29 16:47:32.629 +03:00 [INF] HTTP GET /User responded 200 in 87.3330 ms
2025-07-29 16:48:55.122 +03:00 [INF] HTTP GET /Dashboard responded 200 in 1222.9148 ms
2025-07-29 16:48:55.231 +03:00 [INF] HTTP GET /Dashboard/GetChartData responded 200 in 83.7813 ms
2025-07-29 16:51:38.695 +03:00 [INF] HTTP GET /Shipment responded 200 in 250.9325 ms
2025-07-29 16:51:45.572 +03:00 [INF] HTTP GET /Shipment/Create responded 200 in 769.5463 ms
2025-07-29 16:52:28.779 +03:00 [INF] HTTP GET /Shipment responded 200 in 296.5001 ms
2025-07-29 16:52:35.890 +03:00 [INF] HTTP GET /Shipment/Create responded 200 in 588.7191 ms
2025-07-29 16:52:38.390 +03:00 [INF] HTTP GET /Customer responded 200 in 74.6380 ms
2025-07-29 16:52:44.242 +03:00 [INF] HTTP GET /Customer/Create responded 200 in 221.5511 ms
2025-07-29 16:52:55.336 +03:00 [INF] HTTP GET /Customer responded 200 in 86.5621 ms
2025-07-29 16:53:07.745 +03:00 [INF] HTTP GET /SalesOrder responded 200 in 92.2215 ms
2025-07-29 16:53:09.642 +03:00 [INF] HTTP GET /SalesOrder/Create responded 200 in 222.1556 ms
2025-07-29 16:54:35.182 +03:00 [INF] HTTP GET /SalesOrder responded 200 in 83.9965 ms
2025-07-29 16:55:08.306 +03:00 [INF] HTTP GET /SalesOrder/Create responded 200 in 541.9433 ms
2025-07-29 16:55:20.675 +03:00 [INF] HTTP GET /PurchaseOrder responded 200 in 2020.4130 ms
2025-07-29 16:55:52.019 +03:00 [INF] HTTP GET /Shipment responded 200 in 214.2180 ms
2025-07-29 18:37:09.091 +03:00 [INF] HTTP GET / responded 200 in 10.6985 ms
2025-07-29 18:37:16.634 +03:00 [INF] HTTP POST /Account/Logout responded 302 in 34.9461 ms
2025-07-29 18:37:16.666 +03:00 [INF] HTTP GET /Account/Login responded 200 in 27.0311 ms
2025-07-29 18:37:27.586 +03:00 [INF] HTTP GET / responded 200 in 5.2683 ms
2025-07-29 18:42:52.579 +03:00 [INF] Starting database seeding.
2025-07-29 18:42:55.989 +03:00 [INF] Admin user already exists. Seeding is not required.
2025-07-29 18:43:18.642 +03:00 [WRN] Failed to determine the https port for redirect.
2025-07-29 18:43:18.686 +03:00 [INF] HTTP GET / responded 302 in 47.3725 ms
2025-07-29 18:43:18.823 +03:00 [INF] HTTP GET /Account/Login responded 200 in 128.5061 ms
2025-07-29 18:43:57.574 +03:00 [INF] HTTP POST /Account/Login responded 302 in 814.2590 ms
2025-07-29 18:44:00.532 +03:00 [INF] HTTP GET /Dashboard responded 200 in 2952.9522 ms
2025-07-29 18:44:00.721 +03:00 [INF] HTTP GET /Dashboard/GetChartData responded 200 in 142.5970 ms
2025-07-29 18:44:11.416 +03:00 [INF] HTTP GET / responded 302 in 1.3763 ms
2025-07-29 18:44:13.352 +03:00 [INF] HTTP GET /Dashboard responded 200 in 1934.6302 ms
2025-07-29 18:44:13.507 +03:00 [INF] HTTP GET /Dashboard/GetChartData responded 200 in 115.2617 ms
2025-07-29 18:46:45.299 +03:00 [INF] HTTP GET /Customer responded 200 in 151.4610 ms
2025-07-29 18:46:53.553 +03:00 [INF] HTTP GET /Supplier responded 200 in 193.0873 ms
2025-07-29 18:47:00.832 +03:00 [INF] HTTP GET /Product responded 200 in 310.4738 ms
2025-07-29 18:47:09.111 +03:00 [INF] HTTP GET /SalesOrder responded 200 in 134.9129 ms
2025-07-29 18:47:17.924 +03:00 [INF] HTTP GET /PurchaseOrder responded 200 in 205.1649 ms
2025-07-29 18:47:26.223 +03:00 [INF] HTTP GET /Warehouse responded 200 in 121.6859 ms
2025-07-29 18:47:35.669 +03:00 [INF] HTTP GET /Inventory responded 200 in 638.6058 ms
2025-07-29 18:47:43.867 +03:00 [INF] HTTP GET /User responded 200 in 119.9151 ms
2025-07-29 18:47:54.401 +03:00 [ERR] Failed executing DbCommand (208ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [g].[ReceiptID], [g].[CreatedByUserID], [g].[CreatedDate], [g].[Currency], [g].[Notes], [g].[PostedByUserID], [g].[PostedDate], [g].[PurchaseOrderID], [g].[QualityCheckByUserID], [g].[QualityCheckDate], [g].[QualityCheckStatus], [g].[ReceiptDate], [g].[ReceiptNumber], [g].[ReceiptType], [g].[ReceivedByUserID], [g].[ReceiptStatus], [g].[SupplierDeliveryNote], [g].[SupplierID], [g].[UpdatedByUserID], [g].[UpdatedDate], [g].[WarehouseID], [p].[PurchaseOrderID], [p].[ApprovalNotes], [p].[ApprovalStatus], [p].[ApprovedByUserID], [p].[ApprovedDate], [p].[BuyerID], [p].[CreatedByUserID], [p].[CreatedDate], [p].[Currency], [p].[DeletedByUserID], [p].[DeletedDate], [p].[DeliveryAddressID], [p].[DeliveryInstructions], [p].[DeliveryWarehouseID], [p].[DiscountAmount], [p].[ExchangeRate], [p].[ExpectedDate], [p].[FreightAmount], [p].[FreightTerms], [p].[InternalNotes], [p].[IsDeleted], [p].[Notes], [p].[OrderDate], [p].[OrderStatus], [p].[OrderType], [p].[PaymentMethod], [p].[PaymentTerms], [p].[PromisedDate], [p].[PurchaseOrderNumber], [p].[RequiredDate], [p].[RequisitionID], [p].[ShippingMethod], [p].[SubTotal], [p].[SupplierID], [p].[SupplierReference], [p].[TaxAmount], [p].[TermsAndConditions], [p].[TotalAmount], [p].[UpdatedByUserID], [p].[UpdatedDate], [s].[SupplierID], [s].[APIEnabled], [s].[BankAccountDetails], [s].[BlockedByUserID], [s].[BlockedDate], [s].[BlockedReason], [s].[CertificateExpiryDate], [s].[CertificateNumber], [s].[ComplianceStatus], [s].[ContactEmail], [s].[ContactLandline], [s].[ContactMobile], [s].[ContactPhone], [s].[CreatedByUserID], [s].[CreatedDate], [s].[DeletedByUserID], [s].[DeletedDate], [s].[DeliveryDays], [s].[DeliveryScore], [s].[EDIEnabled], [s].[EarlyPaymentDiscountDays], [s].[EarlyPaymentDiscountPercent], [s].[InsuranceExpiryDate], [s].[InsurancePolicyNumber], [s].[IntegrationKey], [s].[InternalNotes], [s].[IsActive], [s].[IsDeleted], [s].[IsPreferred], [s].[LastOrderDate], [s].[LeadTimeDays], [s].[MinimumOrderValue], [s].[Notes], [s].[OrderCutoffTime], [s].[OutstandingBalance], [s].[PaymentMethodID], [s].[PaymentTermDays], [s].[PaymentTerms], [s].[PreferredCurrency], [s].[PriceScore], [s].[QualityScore], [s].[Rating], [s].[ReferenceCode], [s].[ResponsibleContact], [s].[ReturnPolicy], [s].[SupplierAddress], [s].[SupplierAddress2], [s].[SupplierCity], [s].[SupplierCode], [s].[SupplierCountry], [s].[SupplierGroup], [s].[SupplierName], [s].[SupplierPostCode], [s].[SupplierState], [s].[SupplierStatus], [s].[SupplierType], [s].[TaxNumber], [s].[TaxRate], [s].[TotalPurchaseValue], [s].[TradingName], [s].[UpdatedByUserID], [s].[UpdatedDate], [s].[WarrantyTerms], [s].[Website], [s].[WithholdingTaxRate], [s].[YTDPurchases], [u].[UserID], [u].[AccessFailedCount], [u].[CreatedByUserID], [u].[CreatedDate], [u].[DateFormat], [u].[DeletedByUserID], [u].[DeletedDate], [u].[Department], [u].[DisplayName], [u].[Email], [u].[FirstName], [u].[IsActive], [u].[IsDeleted], [u].[IsLocked], [u].[JobTitle], [u].[Language], [u].[LastActivityDate], [u].[LastLoginDate], [u].[LastName], [u].[LockoutEnd], [u].[MobileNumber], [u].[MustChangePassword], [u].[PasswordChangedDate], [u].[PasswordHash], [u].[PasswordSalt], [u].[PhoneNumber], [u].[ProfilePicture], [u].[ThemePreference], [u].[TimeZone], [u].[TwoFactorEnabled], [u].[TwoFactorSecret], [u].[UpdatedByUserID], [u].[UpdatedDate], [u].[Username], [w].[WarehouseID], [w].[Address1], [w].[Address2], [w].[AllowNegativeStock], [w].[City], [w].[Country], [w].[CreatedByUserID], [w].[CreatedDate], [w].[CurrentUtilization], [w].[Description], [w].[Email], [w].[IsActive], [w].[IsDefault], [w].[ManagerName], [w].[OperatingHours], [w].[Phone], [w].[PostCode], [w].[State], [w].[StorageCapacity], [w].[TimeZone], [w].[TotalArea], [w].[UpdatedByUserID], [w].[UpdatedDate], [w].[WarehouseCode], [w].[WarehouseName], [w].[WarehouseType]
FROM [GoodsReceipt] AS [g]
LEFT JOIN [PurchaseOrder] AS [p] ON [g].[PurchaseOrderID] = [p].[PurchaseOrderID]
LEFT JOIN [Supplier] AS [s] ON [p].[SupplierID] = [s].[SupplierID]
INNER JOIN [User] AS [u] ON [g].[ReceivedByUserID] = [u].[UserID]
INNER JOIN [Warehouse] AS [w] ON [g].[WarehouseID] = [w].[WarehouseID]
ORDER BY [g].[ReceiptDate] DESC
2025-07-29 18:47:54.417 +03:00 [ERR] An exception occurred while iterating over the results of a query for context type 'nCarry.Web.Data.ApplicationDbContext'.
Microsoft.Data.SqlClient.SqlException (0x80131904): Invalid column name 'Currency'.
   at System.Threading.Tasks.ContinuationResultTaskFromResultTask`2.InnerInvoke()
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.InitializeReaderAsync(AsyncEnumerator enumerator, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.SqlServer.Storage.Internal.SqlServerExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
ClientConnectionId:200db107-0369-4ac5-912d-39e5f76b3f7d
Error Number:207,State:1,Class:16
Microsoft.Data.SqlClient.SqlException (0x80131904): Invalid column name 'Currency'.
   at System.Threading.Tasks.ContinuationResultTaskFromResultTask`2.InnerInvoke()
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.InitializeReaderAsync(AsyncEnumerator enumerator, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.SqlServer.Storage.Internal.SqlServerExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
ClientConnectionId:200db107-0369-4ac5-912d-39e5f76b3f7d
Error Number:207,State:1,Class:16
2025-07-29 18:47:54.419 +03:00 [ERR] HTTP GET /GoodsReceipt responded 500 in 258.9324 ms
Microsoft.Data.SqlClient.SqlException (0x80131904): Invalid column name 'Currency'.
   at System.Threading.Tasks.ContinuationResultTaskFromResultTask`2.InnerInvoke()
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.InitializeReaderAsync(AsyncEnumerator enumerator, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.SqlServer.Storage.Internal.SqlServerExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at nCarry.Web.Controllers.GoodsReceiptController.Index(String status, Nullable`1 fromDate, Nullable`1 toDate) in /Users/<USER>/Source/nCarry/nCarry/nCarry.Web/Controllers/GoodsReceiptController.cs:line 50
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.TaskOfIActionResultExecutor.Execute(ActionContext actionContext, IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeActionMethodAsync>g__Awaited|12_0(ControllerActionInvoker invoker, ValueTask`1 actionResultValueTask)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeInnerFilterAsync>g__Awaited|13_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextResourceFilter>g__Awaited|25_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.Rethrow(ResourceExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeFilterPipelineAsync>g__Awaited|20_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Session.SessionMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Session.SessionMiddleware.Invoke(HttpContext context)
   at Serilog.AspNetCore.RequestLoggingMiddleware.Invoke(HttpContext httpContext)
ClientConnectionId:200db107-0369-4ac5-912d-39e5f76b3f7d
Error Number:207,State:1,Class:16
2025-07-29 18:47:54.425 +03:00 [ERR] An unhandled exception has occurred while executing the request.
Microsoft.Data.SqlClient.SqlException (0x80131904): Invalid column name 'Currency'.
   at System.Threading.Tasks.ContinuationResultTaskFromResultTask`2.InnerInvoke()
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.InitializeReaderAsync(AsyncEnumerator enumerator, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.SqlServer.Storage.Internal.SqlServerExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at nCarry.Web.Controllers.GoodsReceiptController.Index(String status, Nullable`1 fromDate, Nullable`1 toDate) in /Users/<USER>/Source/nCarry/nCarry/nCarry.Web/Controllers/GoodsReceiptController.cs:line 50
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.TaskOfIActionResultExecutor.Execute(ActionContext actionContext, IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeActionMethodAsync>g__Awaited|12_0(ControllerActionInvoker invoker, ValueTask`1 actionResultValueTask)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeInnerFilterAsync>g__Awaited|13_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextResourceFilter>g__Awaited|25_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.Rethrow(ResourceExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeFilterPipelineAsync>g__Awaited|20_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Session.SessionMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Session.SessionMiddleware.Invoke(HttpContext context)
   at Serilog.AspNetCore.RequestLoggingMiddleware.Invoke(HttpContext httpContext)
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
ClientConnectionId:200db107-0369-4ac5-912d-39e5f76b3f7d
Error Number:207,State:1,Class:16
2025-07-29 18:48:02.896 +03:00 [INF] HTTP GET /Reports responded 200 in 7.3769 ms
2025-07-29 18:48:12.357 +03:00 [INF] HTTP GET /ProductCategory responded 200 in 117.4794 ms
2025-07-29 18:55:14.253 +03:00 [INF] Starting database seeding.
2025-07-29 18:55:17.556 +03:00 [INF] Admin user already exists. Seeding is not required.
2025-07-29 18:55:40.455 +03:00 [WRN] Failed to determine the https port for redirect.
2025-07-29 18:55:40.919 +03:00 [INF] HTTP GET /GoodsReceipt responded 200 in 465.7110 ms
2025-07-29 18:57:57.392 +03:00 [INF] HTTP GET / responded 302 in 4.0237 ms
2025-07-29 18:57:59.164 +03:00 [INF] HTTP GET /Dashboard responded 200 in 1770.2017 ms
2025-07-29 18:57:59.302 +03:00 [INF] HTTP GET /Dashboard/GetChartData responded 200 in 107.4077 ms
2025-07-29 18:58:13.986 +03:00 [INF] HTTP GET /GoodsReceipt responded 200 in 81.0696 ms
2025-07-29 18:58:23.225 +03:00 [INF] HTTP GET /CustomerPriceList responded 200 in 131.7615 ms
2025-07-29 19:15:37.301 +03:00 [INF] HTTP GET /CustomerPriceList responded 200 in 768.3990 ms
2025-07-29 19:15:53.210 +03:00 [INF] HTTP GET /CustomerPriceList/Create responded 200 in 204.8392 ms
2025-07-29 19:16:28.905 +03:00 [INF] HTTP GET /CustomerPriceList/Create responded 200 in 476.1341 ms
2025-07-29 19:17:00.622 +03:00 [INF] HTTP GET /CustomerPriceList/Create responded 200 in 150.5269 ms
2025-07-29 19:17:12.479 +03:00 [INF] HTTP GET /CustomerPriceList/Create responded 200 in 101.0638 ms
2025-07-29 19:17:39.241 +03:00 [INF] HTTP GET /CustomerPriceList/Details/1 responded 200 in 321.9511 ms
2025-07-29 19:17:48.077 +03:00 [INF] HTTP GET /CustomerPriceList/AddItem responded 200 in 328.2680 ms
2025-07-29 19:18:53.909 +03:00 [INF] HTTP POST /CustomerPriceList/AddItem responded 200 in 252.7352 ms
2025-07-29 19:20:01.917 +03:00 [INF] HTTP POST /Account/Logout responded 302 in 6.6062 ms
2025-07-29 19:20:01.939 +03:00 [INF] HTTP GET /Account/Login responded 200 in 18.9386 ms
2025-07-29 19:20:47.925 +03:00 [INF] HTTP POST /Account/Login responded 302 in 1174.8029 ms
2025-07-29 19:20:51.005 +03:00 [INF] HTTP GET /Dashboard responded 200 in 3076.8555 ms
2025-07-29 19:20:51.143 +03:00 [INF] HTTP GET /Dashboard/GetChartData responded 200 in 101.4861 ms
2025-07-29 19:21:01.675 +03:00 [INF] HTTP GET /CustomerPriceList/Details/1 responded 200 in 305.2588 ms
2025-07-29 19:21:26.215 +03:00 [INF] HTTP GET /CustomerPriceList/Details/1 responded 200 in 390.3259 ms
2025-07-29 19:22:01.912 +03:00 [INF] HTTP GET /SalesOrder responded 200 in 370.9007 ms
2025-07-29 19:22:17.119 +03:00 [INF] HTTP GET /SalesOrder/Edit/18 responded 200 in 1156.5138 ms
2025-07-29 19:22:31.500 +03:00 [ERR] Failed executing DbCommand (884ms) [Parameters=[@__id_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [s1].[OrderID], [s1].[BalanceAmount], [s1].[BillingAddressID], [s1].[CarrierID], [s1].[CreatedByUserID], [s1].[CreatedDate], [s1].[Currency], [s1].[CustomerID], [s1].[CustomerPO], [s1].[DeletedByUserID], [s1].[DeletedDate], [s1].[DeliveredDate], [s1].[DeliveryInstructions], [s1].[DiscountAmount], [s1].[ExchangeRate], [s1].[FreightTerms], [s1].[InternalNotes], [s1].[IsDeleted], [s1].[Notes], [s1].[OrderDate], [s1].[OrderNumber], [s1].[OrderStatus], [s1].[OrderType], [s1].[PaidAmount], [s1].[PaymentDueDate], [s1].[PaymentTerms], [s1].[PickingPriority], [s1].[PromisedDate], [s1].[QuoteID], [s1].[RequestedDate], [s1].[SalesRepID], [s1].[ShippedDate], [s1].[ShippingAddressID], [s1].[ShippingAmount], [s1].[ShippingMethod], [s1].[SubTotal], [s1].[TaxAmount], [s1].[TotalAmount], [s1].[TrackingNumber], [s1].[UpdatedByUserID], [s1].[UpdatedDate], [s1].[WarehouseID], [s1].[CustomerID0], [s1].[BlockedByUserID], [s1].[BlockedDate], [s1].[BlockedReason], [s1].[ContactEmail], [s1].[ContactLandline], [s1].[ContactMobile], [s1].[ContactPhone], [s1].[CreatedByUserID0], [s1].[CreatedDate0], [s1].[CreditLimit], [s1].[CreditReviewDate], [s1].[CreditStatus], [s1].[CurrencyCode], [s1].[CustomerAddress], [s1].[CustomerAddress2], [s1].[CustomerCity], [s1].[CustomerCode], [s1].[CustomerCountry], [s1].[CustomerGroup], [s1].[CustomerName], [s1].[CustomerPostCode], [s1].[CustomerSince], [s1].[CustomerState], [s1].[CustomerStatus], [s1].[CustomerType], [s1].[DeletedByUserID0], [s1].[DeletedDate0], [s1].[DeliveryInstructions0], [s1].[DeliveryTimePreference], [s1].[DiscountPercent], [s1].[InternalNotes0], [s1].[IsActive], [s1].[IsDeleted0], [s1].[LastOrderDate], [s1].[LeadSource], [s1].[LoyaltyPoints], [s1].[Notes0], [s1].[OutstandingBalance], [s1].[PaymentMethodID], [s1].[PaymentTerm], [s1].[PreferredCarrierID], [s1].[PriceListID], [s1].[Rating], [s1].[ReferenceCode], [s1].[ResponsibleContact], [s1].[SalesRepID0], [s1].[TaxExempt], [s1].[TaxNumber], [s1].[TerritoryID], [s1].[TotalOrderValue], [s1].[TradingName], [s1].[UpdatedByUserID0], [s1].[UpdatedDate0], [s1].[Website], [s1].[YTDSales], [s1].[AddressID], [s1].[Address1], [s1].[Address2], [s1].[AddressName], [s1].[AddressType], [s1].[City], [s1].[ContactName], [s1].[ContactPhone0], [s1].[Country], [s1].[CreatedDate1], [s1].[CustomerID1], [s1].[IsActive0], [s1].[IsDefault], [s1].[PostCode], [s1].[State], [s1].[UpdatedDate1], [s1].[AddressID0], [s1].[Address10], [s1].[Address20], [s1].[AddressName0], [s1].[AddressType0], [s1].[City0], [s1].[ContactName0], [s1].[ContactPhone1], [s1].[Country0], [s1].[CreatedDate2], [s1].[CustomerID2], [s1].[IsActive1], [s1].[IsDefault0], [s1].[PostCode0], [s1].[State0], [s1].[UpdatedDate2], [s2].[OrderItemID], [s2].[BackorderedQuantity], [s2].[BatchNumber], [s2].[CancelledQuantity], [s2].[Currency], [s2].[Description], [s2].[DiscountAmount], [s2].[DiscountPercent], [s2].[ItemStatus], [s2].[LineNumber], [s2].[LineTotal], [s2].[LocationID], [s2].[Notes], [s2].[OrderID], [s2].[OrderedQuantity], [s2].[ProductCode], [s2].[ProductID], [s2].[ProductName], [s2].[SerialNumber], [s2].[ShippedQuantity], [s2].[SortOrder], [s2].[TaxAmount], [s2].[TaxRate], [s2].[UOMID], [s2].[UnitCost], [s2].[UnitPrice], [s2].[VariantID], [s2].[WarehouseID], [s2].[ProductID0], [s2].[AllowBackorder], [s2].[AllowPreorder], [s2].[Brand], [s2].[CategoryID], [s2].[CommodityCode], [s2].[CountryOfOrigin], [s2].[CreatedByUserID], [s2].[CreatedDate], [s2].[Currency0], [s2].[DeletedByUserID], [s2].[DeletedDate], [s2].[Description0], [s2].[DimensionUOMID], [s2].[HSCode], [s2].[HasVariants], [s2].[Height], [s2].[InventoryUOMID], [s2].[IsActive], [s2].[IsDeleted], [s2].[IsKit], [s2].[IsTaxExempt], [s2].[LeadTimeDays], [s2].[Length], [s2].[ListPrice], [s2].[Manufacturer], [s2].[ManufacturerPartNumber], [s2].[MaxStockLevel], [s2].[MinStockLevel], [s2].[Notes0], [s2].[ProductCode0], [s2].[ProductName0], [s2].[ProductType], [s2].[PurchaseUOMID], [s2].[ReorderPoint], [s2].[ReorderQuantity], [s2].[RequiresBatch], [s2].[RequiresSerial], [s2].[SalesUOMID], [s2].[SellPrice], [s2].[ShelfLifeDays], [s2].[ShortDescription], [s2].[StandardCost], [s2].[Status], [s2].[TaxCodeID], [s2].[TrackInventory], [s2].[UpdatedByUserID], [s2].[UpdatedDate], [s2].[Volume], [s2].[VolumeUOMID], [s2].[WarrantyDays], [s2].[Weight], [s2].[WeightUOMID], [s2].[Width], [s2].[UOMID0], [s2].[BaseUnit], [s2].[ConversionFactor], [s2].[CreatedDate0], [s2].[DecimalPlaces], [s2].[IsActive0], [s2].[Symbol], [s2].[UOMCode], [s2].[UOMName], [s2].[UOMType], [s2].[UpdatedDate0], [s2].[VariantID0], [s2].[AdditionalCost], [s2].[AdditionalPrice], [s2].[Attribute1Name], [s2].[Attribute1Value], [s2].[Attribute2Name], [s2].[Attribute2Value], [s2].[Attribute3Name], [s2].[Attribute3Value], [s2].[CreatedByUserID0], [s2].[CreatedDate1], [s2].[ImageURL], [s2].[IsActive1], [s2].[ProductID1], [s2].[SortOrder0], [s2].[UpdatedByUserID0], [s2].[UpdatedDate1], [s2].[VariantCode], [s2].[VariantName], [s2].[VariantType], [s2].[VariantValue], [s2].[Weight0]
FROM (
    SELECT TOP(1) [s].[OrderID], [s].[BalanceAmount], [s].[BillingAddressID], [s].[CarrierID], [s].[CreatedByUserID], [s].[CreatedDate], [s].[Currency], [s].[CustomerID], [s].[CustomerPO], [s].[DeletedByUserID], [s].[DeletedDate], [s].[DeliveredDate], [s].[DeliveryInstructions], [s].[DiscountAmount], [s].[ExchangeRate], [s].[FreightTerms], [s].[InternalNotes], [s].[IsDeleted], [s].[Notes], [s].[OrderDate], [s].[OrderNumber], [s].[OrderStatus], [s].[OrderType], [s].[PaidAmount], [s].[PaymentDueDate], [s].[PaymentTerms], [s].[PickingPriority], [s].[PromisedDate], [s].[QuoteID], [s].[RequestedDate], [s].[SalesRepID], [s].[ShippedDate], [s].[ShippingAddressID], [s].[ShippingAmount], [s].[ShippingMethod], [s].[SubTotal], [s].[TaxAmount], [s].[TotalAmount], [s].[TrackingNumber], [s].[UpdatedByUserID], [s].[UpdatedDate], [s].[WarehouseID], [c].[CustomerID] AS [CustomerID0], [c].[BlockedByUserID], [c].[BlockedDate], [c].[BlockedReason], [c].[ContactEmail], [c].[ContactLandline], [c].[ContactMobile], [c].[ContactPhone], [c].[CreatedByUserID] AS [CreatedByUserID0], [c].[CreatedDate] AS [CreatedDate0], [c].[CreditLimit], [c].[CreditReviewDate], [c].[CreditStatus], [c].[CurrencyCode], [c].[CustomerAddress], [c].[CustomerAddress2], [c].[CustomerCity], [c].[CustomerCode], [c].[CustomerCountry], [c].[CustomerGroup], [c].[CustomerName], [c].[CustomerPostCode], [c].[CustomerSince], [c].[CustomerState], [c].[CustomerStatus], [c].[CustomerType], [c].[DeletedByUserID] AS [DeletedByUserID0], [c].[DeletedDate] AS [DeletedDate0], [c].[DeliveryInstructions] AS [DeliveryInstructions0], [c].[DeliveryTimePreference], [c].[DiscountPercent], [c].[InternalNotes] AS [InternalNotes0], [c].[IsActive], [c].[IsDeleted] AS [IsDeleted0], [c].[LastOrderDate], [c].[LeadSource], [c].[LoyaltyPoints], [c].[Notes] AS [Notes0], [c].[OutstandingBalance], [c].[PaymentMethodID], [c].[PaymentTerm], [c].[PreferredCarrierID], [c].[PriceListID], [c].[Rating], [c].[ReferenceCode], [c].[ResponsibleContact], [c].[SalesRepID] AS [SalesRepID0], [c].[TaxExempt], [c].[TaxNumber], [c].[TerritoryID], [c].[TotalOrderValue], [c].[TradingName], [c].[UpdatedByUserID] AS [UpdatedByUserID0], [c].[UpdatedDate] AS [UpdatedDate0], [c].[Website], [c].[YTDSales], [c0].[AddressID], [c0].[Address1], [c0].[Address2], [c0].[AddressName], [c0].[AddressType], [c0].[City], [c0].[ContactName], [c0].[ContactPhone] AS [ContactPhone0], [c0].[Country], [c0].[CreatedDate] AS [CreatedDate1], [c0].[CustomerID] AS [CustomerID1], [c0].[IsActive] AS [IsActive0], [c0].[IsDefault], [c0].[PostCode], [c0].[State], [c0].[UpdatedDate] AS [UpdatedDate1], [c1].[AddressID] AS [AddressID0], [c1].[Address1] AS [Address10], [c1].[Address2] AS [Address20], [c1].[AddressName] AS [AddressName0], [c1].[AddressType] AS [AddressType0], [c1].[City] AS [City0], [c1].[ContactName] AS [ContactName0], [c1].[ContactPhone] AS [ContactPhone1], [c1].[Country] AS [Country0], [c1].[CreatedDate] AS [CreatedDate2], [c1].[CustomerID] AS [CustomerID2], [c1].[IsActive] AS [IsActive1], [c1].[IsDefault] AS [IsDefault0], [c1].[PostCode] AS [PostCode0], [c1].[State] AS [State0], [c1].[UpdatedDate] AS [UpdatedDate2]
    FROM [SalesOrder] AS [s]
    INNER JOIN [Customer] AS [c] ON [s].[CustomerID] = [c].[CustomerID]
    LEFT JOIN [CustomerAddress] AS [c0] ON [s].[BillingAddressID] = [c0].[AddressID]
    LEFT JOIN [CustomerAddress] AS [c1] ON [s].[ShippingAddressID] = [c1].[AddressID]
    WHERE [s].[OrderID] = @__id_0 AND [s].[IsDeleted] = CAST(0 AS bit)
) AS [s1]
LEFT JOIN (
    SELECT [s0].[OrderItemID], [s0].[BackorderedQuantity], [s0].[BatchNumber], [s0].[CancelledQuantity], [s0].[Currency], [s0].[Description], [s0].[DiscountAmount], [s0].[DiscountPercent], [s0].[ItemStatus], [s0].[LineNumber], [s0].[LineTotal], [s0].[LocationID], [s0].[Notes], [s0].[OrderID], [s0].[OrderedQuantity], [s0].[ProductCode], [s0].[ProductID], [s0].[ProductName], [s0].[SerialNumber], [s0].[ShippedQuantity], [s0].[SortOrder], [s0].[TaxAmount], [s0].[TaxRate], [s0].[UOMID], [s0].[UnitCost], [s0].[UnitPrice], [s0].[VariantID], [s0].[WarehouseID], [p].[ProductID] AS [ProductID0], [p].[AllowBackorder], [p].[AllowPreorder], [p].[Brand], [p].[CategoryID], [p].[CommodityCode], [p].[CountryOfOrigin], [p].[CreatedByUserID], [p].[CreatedDate], [p].[Currency] AS [Currency0], [p].[DeletedByUserID], [p].[DeletedDate], [p].[Description] AS [Description0], [p].[DimensionUOMID], [p].[HSCode], [p].[HasVariants], [p].[Height], [p].[InventoryUOMID], [p].[IsActive], [p].[IsDeleted], [p].[IsKit], [p].[IsTaxExempt], [p].[LeadTimeDays], [p].[Length], [p].[ListPrice], [p].[Manufacturer], [p].[ManufacturerPartNumber], [p].[MaxStockLevel], [p].[MinStockLevel], [p].[Notes] AS [Notes0], [p].[ProductCode] AS [ProductCode0], [p].[ProductName] AS [ProductName0], [p].[ProductType], [p].[PurchaseUOMID], [p].[ReorderPoint], [p].[ReorderQuantity], [p].[RequiresBatch], [p].[RequiresSerial], [p].[SalesUOMID], [p].[SellPrice], [p].[ShelfLifeDays], [p].[ShortDescription], [p].[StandardCost], [p].[Status], [p].[TaxCodeID], [p].[TrackInventory], [p].[UpdatedByUserID], [p].[UpdatedDate], [p].[Volume], [p].[VolumeUOMID], [p].[WarrantyDays], [p].[Weight], [p].[WeightUOMID], [p].[Width], [u].[UOMID] AS [UOMID0], [u].[BaseUnit], [u].[ConversionFactor], [u].[CreatedDate] AS [CreatedDate0], [u].[DecimalPlaces], [u].[IsActive] AS [IsActive0], [u].[Symbol], [u].[UOMCode], [u].[UOMName], [u].[UOMType], [u].[UpdatedDate] AS [UpdatedDate0], [p0].[VariantID] AS [VariantID0], [p0].[AdditionalCost], [p0].[AdditionalPrice], [p0].[Attribute1Name], [p0].[Attribute1Value], [p0].[Attribute2Name], [p0].[Attribute2Value], [p0].[Attribute3Name], [p0].[Attribute3Value], [p0].[CreatedByUserID] AS [CreatedByUserID0], [p0].[CreatedDate] AS [CreatedDate1], [p0].[ImageURL], [p0].[IsActive] AS [IsActive1], [p0].[ProductID] AS [ProductID1], [p0].[SortOrder] AS [SortOrder0], [p0].[UpdatedByUserID] AS [UpdatedByUserID0], [p0].[UpdatedDate] AS [UpdatedDate1], [p0].[VariantCode], [p0].[VariantName], [p0].[VariantType], [p0].[VariantValue], [p0].[Weight] AS [Weight0]
    FROM [SalesOrderItem] AS [s0]
    INNER JOIN [Product] AS [p] ON [s0].[ProductID] = [p].[ProductID]
    INNER JOIN [UnitOfMeasure] AS [u] ON [s0].[UOMID] = [u].[UOMID]
    LEFT JOIN [ProductVariant] AS [p0] ON [s0].[VariantID] = [p0].[VariantID]
) AS [s2] ON [s1].[OrderID] = [s2].[OrderID]
ORDER BY [s1].[OrderID], [s1].[CustomerID0], [s1].[AddressID], [s1].[AddressID0], [s2].[OrderItemID], [s2].[ProductID0], [s2].[UOMID0]
2025-07-29 19:22:31.520 +03:00 [ERR] An exception occurred while iterating over the results of a query for context type 'nCarry.Web.Data.ApplicationDbContext'.
Microsoft.Data.SqlClient.SqlException (0x80131904): Invalid column name 'Currency'.
Invalid column name 'Attribute1Name'.
Invalid column name 'Attribute1Value'.
Invalid column name 'Attribute2Name'.
Invalid column name 'Attribute2Value'.
Invalid column name 'Attribute3Name'.
Invalid column name 'Attribute3Value'.
Invalid column name 'CreatedByUserID'.
Invalid column name 'ImageURL'.
Invalid column name 'UpdatedByUserID'.
   at System.Threading.Tasks.ContinuationResultTaskFromResultTask`2.InnerInvoke()
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.InitializeReaderAsync(AsyncEnumerator enumerator, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.SqlServer.Storage.Internal.SqlServerExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
ClientConnectionId:99c2f097-13df-4532-9f7d-2abad3aa5714
Error Number:207,State:1,Class:16
Microsoft.Data.SqlClient.SqlException (0x80131904): Invalid column name 'Currency'.
Invalid column name 'Attribute1Name'.
Invalid column name 'Attribute1Value'.
Invalid column name 'Attribute2Name'.
Invalid column name 'Attribute2Value'.
Invalid column name 'Attribute3Name'.
Invalid column name 'Attribute3Value'.
Invalid column name 'CreatedByUserID'.
Invalid column name 'ImageURL'.
Invalid column name 'UpdatedByUserID'.
   at System.Threading.Tasks.ContinuationResultTaskFromResultTask`2.InnerInvoke()
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.InitializeReaderAsync(AsyncEnumerator enumerator, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.SqlServer.Storage.Internal.SqlServerExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
ClientConnectionId:99c2f097-13df-4532-9f7d-2abad3aa5714
Error Number:207,State:1,Class:16
2025-07-29 19:22:31.522 +03:00 [ERR] HTTP GET /SalesOrder/Details/18 responded 500 in 959.7493 ms
Microsoft.Data.SqlClient.SqlException (0x80131904): Invalid column name 'Currency'.
Invalid column name 'Attribute1Name'.
Invalid column name 'Attribute1Value'.
Invalid column name 'Attribute2Name'.
Invalid column name 'Attribute2Value'.
Invalid column name 'Attribute3Name'.
Invalid column name 'Attribute3Value'.
Invalid column name 'CreatedByUserID'.
Invalid column name 'ImageURL'.
Invalid column name 'UpdatedByUserID'.
   at System.Threading.Tasks.ContinuationResultTaskFromResultTask`2.InnerInvoke()
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.InitializeReaderAsync(AsyncEnumerator enumerator, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.SqlServer.Storage.Internal.SqlServerExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
   at Microsoft.EntityFrameworkCore.Query.ShapedQueryCompilingExpressionVisitor.SingleOrDefaultAsync[TSource](IAsyncEnumerable`1 asyncEnumerable, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.ShapedQueryCompilingExpressionVisitor.SingleOrDefaultAsync[TSource](IAsyncEnumerable`1 asyncEnumerable, CancellationToken cancellationToken)
   at nCarry.Web.Controllers.SalesOrderController.Details(Nullable`1 id)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.TaskOfIActionResultExecutor.Execute(ActionContext actionContext, IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeActionMethodAsync>g__Awaited|12_0(ControllerActionInvoker invoker, ValueTask`1 actionResultValueTask)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeInnerFilterAsync>g__Awaited|13_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextResourceFilter>g__Awaited|25_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.Rethrow(ResourceExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeFilterPipelineAsync>g__Awaited|20_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Session.SessionMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Session.SessionMiddleware.Invoke(HttpContext context)
   at Serilog.AspNetCore.RequestLoggingMiddleware.Invoke(HttpContext httpContext)
ClientConnectionId:99c2f097-13df-4532-9f7d-2abad3aa5714
Error Number:207,State:1,Class:16
2025-07-29 19:22:31.527 +03:00 [ERR] An unhandled exception has occurred while executing the request.
Microsoft.Data.SqlClient.SqlException (0x80131904): Invalid column name 'Currency'.
Invalid column name 'Attribute1Name'.
Invalid column name 'Attribute1Value'.
Invalid column name 'Attribute2Name'.
Invalid column name 'Attribute2Value'.
Invalid column name 'Attribute3Name'.
Invalid column name 'Attribute3Value'.
Invalid column name 'CreatedByUserID'.
Invalid column name 'ImageURL'.
Invalid column name 'UpdatedByUserID'.
   at System.Threading.Tasks.ContinuationResultTaskFromResultTask`2.InnerInvoke()
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.InitializeReaderAsync(AsyncEnumerator enumerator, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.SqlServer.Storage.Internal.SqlServerExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
   at Microsoft.EntityFrameworkCore.Query.ShapedQueryCompilingExpressionVisitor.SingleOrDefaultAsync[TSource](IAsyncEnumerable`1 asyncEnumerable, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.ShapedQueryCompilingExpressionVisitor.SingleOrDefaultAsync[TSource](IAsyncEnumerable`1 asyncEnumerable, CancellationToken cancellationToken)
   at nCarry.Web.Controllers.SalesOrderController.Details(Nullable`1 id)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.TaskOfIActionResultExecutor.Execute(ActionContext actionContext, IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeActionMethodAsync>g__Awaited|12_0(ControllerActionInvoker invoker, ValueTask`1 actionResultValueTask)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeInnerFilterAsync>g__Awaited|13_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextResourceFilter>g__Awaited|25_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.Rethrow(ResourceExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeFilterPipelineAsync>g__Awaited|20_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Session.SessionMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Session.SessionMiddleware.Invoke(HttpContext context)
   at Serilog.AspNetCore.RequestLoggingMiddleware.Invoke(HttpContext httpContext)
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
ClientConnectionId:99c2f097-13df-4532-9f7d-2abad3aa5714
Error Number:207,State:1,Class:16
2025-07-29 19:31:44.774 +03:00 [INF] Starting database seeding.
2025-07-29 19:31:46.860 +03:00 [INF] Admin user already exists. Seeding is not required.
2025-07-29 19:32:47.795 +03:00 [INF] Starting database seeding.
2025-07-29 19:32:50.012 +03:00 [INF] Admin user already exists. Seeding is not required.
2025-07-29 19:37:08.394 +03:00 [WRN] Failed to determine the https port for redirect.
2025-07-29 19:37:08.451 +03:00 [INF] HTTP GET / responded 302 in 60.3503 ms
2025-07-29 19:37:11.325 +03:00 [INF] HTTP GET /Dashboard responded 200 in 2864.9145 ms
2025-07-29 19:37:11.346 +03:00 [INF] HTTP GET /nCarry.Web.styles.css responded 304 in 5.5729 ms
2025-07-29 19:37:11.594 +03:00 [INF] HTTP GET /Dashboard/GetChartData responded 200 in 200.3758 ms
2025-07-29 19:37:18.892 +03:00 [INF] HTTP GET /SalesOrder/Details/24 responded 200 in 267.9768 ms
2025-07-29 19:37:27.579 +03:00 [INF] HTTP GET /SalesOrder responded 200 in 132.3985 ms
2025-07-29 19:37:37.161 +03:00 [INF] HTTP GET /SalesOrder/Edit/18 responded 200 in 670.3987 ms
2025-07-29 19:37:46.248 +03:00 [INF] HTTP GET /SalesOrder/Details/18 responded 200 in 111.0018 ms
2025-07-29 19:38:26.966 +03:00 [INF] HTTP POST /SalesOrder/AddItem responded 200 in 274.0281 ms
2025-07-29 19:39:52.408 +03:00 [INF] HTTP GET /SalesOrder/Details/18 responded 200 in 269.8185 ms
2025-07-29 19:52:31.430 +03:00 [INF] HTTP GET /CustomerPriceList responded 200 in 689.2805 ms
2025-07-29 19:52:40.140 +03:00 [INF] HTTP GET /CustomerPriceList/Details/1 responded 200 in 198.4113 ms
2025-07-29 19:56:20.661 +03:00 [INF] HTTP GET /Product responded 200 in 132.3561 ms
2025-07-29 19:56:29.134 +03:00 [INF] HTTP GET /Product/Create responded 200 in 475.7140 ms
2025-07-29 19:57:50.368 +03:00 [INF] HTTP POST /Product/Create responded 302 in 212.9157 ms
2025-07-29 19:57:50.476 +03:00 [INF] HTTP GET /Product responded 200 in 105.6740 ms
2025-07-29 19:58:16.694 +03:00 [INF] HTTP GET /Product/Create responded 200 in 549.8796 ms
2025-07-29 19:59:26.277 +03:00 [INF] HTTP POST /Product/Create responded 302 in 92.7645 ms
2025-07-29 19:59:26.391 +03:00 [INF] HTTP GET /Product responded 200 in 111.6818 ms
2025-07-29 19:59:53.302 +03:00 [INF] HTTP GET /Supplier responded 200 in 160.6252 ms
2025-07-29 20:00:03.012 +03:00 [INF] HTTP GET /Supplier/Create responded 200 in 135.4160 ms
2025-07-29 20:01:36.285 +03:00 [ERR] An exception occurred in the database while saving changes for context type 'nCarry.Web.Data.ApplicationDbContext'.
Microsoft.EntityFrameworkCore.DbUpdateException: An error occurred while saving the entity changes. See the inner exception for details.
 ---> Microsoft.Data.SqlClient.SqlException (0x80131904): Violation of UNIQUE KEY constraint 'UQ__Supplier__E16A0484BDC09C41'. Cannot insert duplicate key in object 'dbo.Supplier'. The duplicate key value is (<NULL>).
   at Microsoft.Data.SqlClient.SqlConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.SqlInternalConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.TdsParser.ThrowExceptionAndWarning(TdsParserStateObject stateObj, Boolean callerHasConnectionLock, Boolean asyncClose)
   at Microsoft.Data.SqlClient.TdsParser.TryRun(RunBehavior runBehavior, SqlCommand cmdHandler, SqlDataReader dataStream, BulkCopySimpleResultSet bulkCopyHandler, TdsParserStateObject stateObj, Boolean& dataReady)
   at Microsoft.Data.SqlClient.SqlDataReader.TryHasMoreRows(Boolean& moreRows)
   at Microsoft.Data.SqlClient.SqlDataReader.TryReadInternal(Boolean setTimeout, Boolean& more)
   at Microsoft.Data.SqlClient.SqlDataReader.ReadAsyncExecute(Task task, Object state)
   at Microsoft.Data.SqlClient.SqlDataReader.InvokeAsyncCall[T](SqlDataReaderBaseAsyncCallContext`1 context)
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Update.AffectedCountModificationCommandBatch.ConsumeResultSetAsync(Int32 startCommandIndex, RelationalDataReader reader, CancellationToken cancellationToken)
ClientConnectionId:97ba6c7f-fbe5-4a4f-a98f-cf3c9f380dbd
Error Number:2627,State:1,Class:14
   --- End of inner exception stack trace ---
   at Microsoft.EntityFrameworkCore.Update.AffectedCountModificationCommandBatch.ConsumeResultSetAsync(Int32 startCommandIndex, RelationalDataReader reader, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.AffectedCountModificationCommandBatch.ConsumeAsync(RelationalDataReader reader, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.SqlServer.Update.Internal.SqlServerModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalDatabase.SaveChangesAsync(IList`1 entries, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(IList`1 entriesToSave, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(StateManager stateManager, Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.SqlServer.Storage.Internal.SqlServerExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.DbContext.SaveChangesAsync(Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
Microsoft.EntityFrameworkCore.DbUpdateException: An error occurred while saving the entity changes. See the inner exception for details.
 ---> Microsoft.Data.SqlClient.SqlException (0x80131904): Violation of UNIQUE KEY constraint 'UQ__Supplier__E16A0484BDC09C41'. Cannot insert duplicate key in object 'dbo.Supplier'. The duplicate key value is (<NULL>).
   at Microsoft.Data.SqlClient.SqlConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.SqlInternalConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.TdsParser.ThrowExceptionAndWarning(TdsParserStateObject stateObj, Boolean callerHasConnectionLock, Boolean asyncClose)
   at Microsoft.Data.SqlClient.TdsParser.TryRun(RunBehavior runBehavior, SqlCommand cmdHandler, SqlDataReader dataStream, BulkCopySimpleResultSet bulkCopyHandler, TdsParserStateObject stateObj, Boolean& dataReady)
   at Microsoft.Data.SqlClient.SqlDataReader.TryHasMoreRows(Boolean& moreRows)
   at Microsoft.Data.SqlClient.SqlDataReader.TryReadInternal(Boolean setTimeout, Boolean& more)
   at Microsoft.Data.SqlClient.SqlDataReader.ReadAsyncExecute(Task task, Object state)
   at Microsoft.Data.SqlClient.SqlDataReader.InvokeAsyncCall[T](SqlDataReaderBaseAsyncCallContext`1 context)
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Update.AffectedCountModificationCommandBatch.ConsumeResultSetAsync(Int32 startCommandIndex, RelationalDataReader reader, CancellationToken cancellationToken)
ClientConnectionId:97ba6c7f-fbe5-4a4f-a98f-cf3c9f380dbd
Error Number:2627,State:1,Class:14
   --- End of inner exception stack trace ---
   at Microsoft.EntityFrameworkCore.Update.AffectedCountModificationCommandBatch.ConsumeResultSetAsync(Int32 startCommandIndex, RelationalDataReader reader, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.AffectedCountModificationCommandBatch.ConsumeAsync(RelationalDataReader reader, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.SqlServer.Update.Internal.SqlServerModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalDatabase.SaveChangesAsync(IList`1 entries, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(IList`1 entriesToSave, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(StateManager stateManager, Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.SqlServer.Storage.Internal.SqlServerExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.DbContext.SaveChangesAsync(Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
2025-07-29 20:01:36.385 +03:00 [INF] HTTP POST /Supplier/Create responded 200 in 386.5429 ms
2025-07-29 20:03:30.520 +03:00 [INF] HTTP GET /PurchaseOrder responded 200 in 167.2348 ms
2025-07-29 20:03:42.738 +03:00 [INF] HTTP GET /PurchaseOrder/Create responded 200 in 624.1973 ms
2025-07-29 20:06:26.983 +03:00 [INF] HTTP GET /PurchaseOrder responded 302 in 2.6950 ms
2025-07-29 20:06:40.392 +03:00 [INF] HTTP GET /PurchaseOrder responded 200 in 88.1420 ms
2025-07-29 20:06:47.367 +03:00 [INF] HTTP GET /PurchaseOrder/Create responded 200 in 618.9293 ms
2025-07-29 20:08:52.605 +03:00 [ERR] Failed executing DbCommand (103ms) [Parameters=[@p0='?' (Precision = 18) (Scale = 4) (DbType = Decimal), @p1='?' (Size = 3), @p2='?' (Size = 4000), @p3='?' (Precision = 18) (Scale = 2) (DbType = Decimal), @p4='?' (Precision = 5) (Scale = 2) (DbType = Decimal), @p5='?' (Precision = 18) (Scale = 4) (DbType = Decimal), @p6='?' (Size = 20), @p7='?' (DbType = DateTime2), @p8='?' (DbType = Int32), @p9='?' (Precision = 18) (Scale = 2) (DbType = Decimal), @p10='?' (Size = 500), @p11='?' (Precision = 18) (Scale = 4) (DbType = Decimal), @p12='?' (DbType = Int32), @p13='?' (Size = 255), @p14='?' (DbType = DateTime2), @p15='?' (DbType = Int32), @p16='?' (Precision = 18) (Scale = 4) (DbType = Decimal), @p17='?' (DbType = DateTime2), @p18='?' (DbType = Int32), @p19='?' (Size = 100), @p20='?' (Precision = 18) (Scale = 2) (DbType = Decimal), @p21='?' (Precision = 5) (Scale = 2) (DbType = Decimal), @p22='?' (DbType = Int32), @p23='?' (Precision = 18) (Scale = 2) (DbType = Decimal), @p24='?' (Precision = 18) (Scale = 4) (DbType = Decimal), @p25='?' (Size = 3), @p26='?' (Size = 4000), @p27='?' (Precision = 18) (Scale = 2) (DbType = Decimal), @p28='?' (Precision = 5) (Scale = 2) (DbType = Decimal), @p29='?' (Precision = 18) (Scale = 4) (DbType = Decimal), @p30='?' (Size = 20), @p31='?' (DbType = DateTime2), @p32='?' (DbType = Int32), @p33='?' (Precision = 18) (Scale = 2) (DbType = Decimal), @p34='?' (Size = 500), @p35='?' (Precision = 18) (Scale = 4) (DbType = Decimal), @p36='?' (DbType = Int32), @p37='?' (Size = 255), @p38='?' (DbType = DateTime2), @p39='?' (DbType = Int32), @p40='?' (Precision = 18) (Scale = 4) (DbType = Decimal), @p41='?' (DbType = DateTime2), @p42='?' (DbType = Int32), @p43='?' (Size = 100), @p44='?' (Precision = 18) (Scale = 2) (DbType = Decimal), @p45='?' (Precision = 5) (Scale = 2) (DbType = Decimal), @p46='?' (DbType = Int32), @p47='?' (Precision = 18) (Scale = 2) (DbType = Decimal)], CommandType='"Text"', CommandTimeout='30']
SET IMPLICIT_TRANSACTIONS OFF;
SET NOCOUNT ON;
MERGE [PurchaseOrderItem] USING (
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10, @p11, @p12, @p13, @p14, @p15, @p16, @p17, @p18, @p19, @p20, @p21, @p22, @p23, 0),
(@p24, @p25, @p26, @p27, @p28, @p29, @p30, @p31, @p32, @p33, @p34, @p35, @p36, @p37, @p38, @p39, @p40, @p41, @p42, @p43, @p44, @p45, @p46, @p47, 1)) AS i ([CancelledQuantity], [Currency], [Description], [DiscountAmount], [DiscountPercent], [InvoicedQuantity], [ItemStatus], [LastReceiptDate], [LineNumber], [LineTotal], [Notes], [OrderedQuantity], [ProductID], [ProductName], [PromisedDate], [PurchaseOrderID], [ReceivedQuantity], [RequiredDate], [SortOrder], [SupplierProductCode], [TaxAmount], [TaxRate], [UOMID], [UnitPrice], _Position) ON 1=0
WHEN NOT MATCHED THEN
INSERT ([CancelledQuantity], [Currency], [Description], [DiscountAmount], [DiscountPercent], [InvoicedQuantity], [ItemStatus], [LastReceiptDate], [LineNumber], [LineTotal], [Notes], [OrderedQuantity], [ProductID], [ProductName], [PromisedDate], [PurchaseOrderID], [ReceivedQuantity], [RequiredDate], [SortOrder], [SupplierProductCode], [TaxAmount], [TaxRate], [UOMID], [UnitPrice])
VALUES (i.[CancelledQuantity], i.[Currency], i.[Description], i.[DiscountAmount], i.[DiscountPercent], i.[InvoicedQuantity], i.[ItemStatus], i.[LastReceiptDate], i.[LineNumber], i.[LineTotal], i.[Notes], i.[OrderedQuantity], i.[ProductID], i.[ProductName], i.[PromisedDate], i.[PurchaseOrderID], i.[ReceivedQuantity], i.[RequiredDate], i.[SortOrder], i.[SupplierProductCode], i.[TaxAmount], i.[TaxRate], i.[UOMID], i.[UnitPrice])
OUTPUT INSERTED.[PurchaseOrderItemID], i._Position;
2025-07-29 20:08:52.683 +03:00 [ERR] An exception occurred in the database while saving changes for context type 'nCarry.Web.Data.ApplicationDbContext'.
Microsoft.EntityFrameworkCore.DbUpdateException: An error occurred while saving the entity changes. See the inner exception for details.
 ---> Microsoft.Data.SqlClient.SqlException (0x80131904): Invalid column name 'Currency'.
   at System.Threading.Tasks.ContinuationResultTaskFromResultTask`2.InnerInvoke()
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
ClientConnectionId:97ba6c7f-fbe5-4a4f-a98f-cf3c9f380dbd
Error Number:207,State:1,Class:16
   --- End of inner exception stack trace ---
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.SqlServer.Update.Internal.SqlServerModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalDatabase.SaveChangesAsync(IList`1 entries, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(IList`1 entriesToSave, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(StateManager stateManager, Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.SqlServer.Storage.Internal.SqlServerExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.DbContext.SaveChangesAsync(Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
Microsoft.EntityFrameworkCore.DbUpdateException: An error occurred while saving the entity changes. See the inner exception for details.
 ---> Microsoft.Data.SqlClient.SqlException (0x80131904): Invalid column name 'Currency'.
   at System.Threading.Tasks.ContinuationResultTaskFromResultTask`2.InnerInvoke()
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
ClientConnectionId:97ba6c7f-fbe5-4a4f-a98f-cf3c9f380dbd
Error Number:207,State:1,Class:16
   --- End of inner exception stack trace ---
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.SqlServer.Update.Internal.SqlServerModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalDatabase.SaveChangesAsync(IList`1 entries, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(IList`1 entriesToSave, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(StateManager stateManager, Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.SqlServer.Storage.Internal.SqlServerExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.DbContext.SaveChangesAsync(Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
2025-07-29 20:08:53.467 +03:00 [INF] HTTP POST /PurchaseOrder/Create responded 200 in 1808.6257 ms
2025-07-29 20:11:20.799 +03:00 [INF] HTTP GET /PurchaseOrder responded 200 in 96.8170 ms
2025-07-29 20:12:36.949 +03:00 [ERR] Failed executing DbCommand (95ms) [Parameters=[@__id_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [s0].[PurchaseOrderID], [s0].[ApprovalNotes], [s0].[ApprovalStatus], [s0].[ApprovedByUserID], [s0].[ApprovedDate], [s0].[BuyerID], [s0].[CreatedByUserID], [s0].[CreatedDate], [s0].[Currency], [s0].[DeletedByUserID], [s0].[DeletedDate], [s0].[DeliveryAddressID], [s0].[DeliveryInstructions], [s0].[DeliveryWarehouseID], [s0].[DiscountAmount], [s0].[ExchangeRate], [s0].[ExpectedDate], [s0].[FreightAmount], [s0].[FreightTerms], [s0].[InternalNotes], [s0].[IsDeleted], [s0].[Notes], [s0].[OrderDate], [s0].[OrderStatus], [s0].[OrderType], [s0].[PaymentMethod], [s0].[PaymentTerms], [s0].[PromisedDate], [s0].[PurchaseOrderNumber], [s0].[RequiredDate], [s0].[RequisitionID], [s0].[ShippingMethod], [s0].[SubTotal], [s0].[SupplierID], [s0].[SupplierReference], [s0].[TaxAmount], [s0].[TermsAndConditions], [s0].[TotalAmount], [s0].[UpdatedByUserID], [s0].[UpdatedDate], [s0].[SupplierID0], [s0].[APIEnabled], [s0].[BankAccountDetails], [s0].[BlockedByUserID], [s0].[BlockedDate], [s0].[BlockedReason], [s0].[CertificateExpiryDate], [s0].[CertificateNumber], [s0].[ComplianceStatus], [s0].[ContactEmail], [s0].[ContactLandline], [s0].[ContactMobile], [s0].[ContactPhone], [s0].[CreatedByUserID0], [s0].[CreatedDate0], [s0].[DeletedByUserID0], [s0].[DeletedDate0], [s0].[DeliveryDays], [s0].[DeliveryScore], [s0].[EDIEnabled], [s0].[EarlyPaymentDiscountDays], [s0].[EarlyPaymentDiscountPercent], [s0].[InsuranceExpiryDate], [s0].[InsurancePolicyNumber], [s0].[IntegrationKey], [s0].[InternalNotes0], [s0].[IsActive], [s0].[IsDeleted0], [s0].[IsPreferred], [s0].[LastOrderDate], [s0].[LeadTimeDays], [s0].[MinimumOrderValue], [s0].[Notes0], [s0].[OrderCutoffTime], [s0].[OutstandingBalance], [s0].[PaymentMethodID], [s0].[PaymentTermDays], [s0].[PaymentTerms0], [s0].[PreferredCurrency], [s0].[PriceScore], [s0].[QualityScore], [s0].[Rating], [s0].[ReferenceCode], [s0].[ResponsibleContact], [s0].[ReturnPolicy], [s0].[SupplierAddress], [s0].[SupplierAddress2], [s0].[SupplierCity], [s0].[SupplierCode], [s0].[SupplierCountry], [s0].[SupplierGroup], [s0].[SupplierName], [s0].[SupplierPostCode], [s0].[SupplierState], [s0].[SupplierStatus], [s0].[SupplierType], [s0].[TaxNumber], [s0].[TaxRate], [s0].[TotalPurchaseValue], [s0].[TradingName], [s0].[UpdatedByUserID0], [s0].[UpdatedDate0], [s0].[WarrantyTerms], [s0].[Website], [s0].[WithholdingTaxRate], [s0].[YTDPurchases], [s0].[WarehouseID], [s0].[Address1], [s0].[Address2], [s0].[AllowNegativeStock], [s0].[City], [s0].[Country], [s0].[CreatedByUserID1], [s0].[CreatedDate1], [s0].[CurrentUtilization], [s0].[Description], [s0].[Email], [s0].[IsActive0], [s0].[IsDefault], [s0].[ManagerName], [s0].[OperatingHours], [s0].[Phone], [s0].[PostCode], [s0].[State], [s0].[StorageCapacity], [s0].[TimeZone], [s0].[TotalArea], [s0].[UpdatedByUserID1], [s0].[UpdatedDate1], [s0].[WarehouseCode], [s0].[WarehouseName], [s0].[WarehouseType], [s0].[UserID], [s0].[AccessFailedCount], [s0].[CreatedByUserID2], [s0].[CreatedDate2], [s0].[DateFormat], [s0].[DeletedByUserID1], [s0].[DeletedDate1], [s0].[Department], [s0].[DisplayName], [s0].[Email0], [s0].[FirstName], [s0].[IsActive1], [s0].[IsDeleted1], [s0].[IsLocked], [s0].[JobTitle], [s0].[Language], [s0].[LastActivityDate], [s0].[LastLoginDate], [s0].[LastName], [s0].[LockoutEnd], [s0].[MobileNumber], [s0].[MustChangePassword], [s0].[PasswordChangedDate], [s0].[PasswordHash], [s0].[PasswordSalt], [s0].[PhoneNumber], [s0].[ProfilePicture], [s0].[ThemePreference], [s0].[TimeZone0], [s0].[TwoFactorEnabled], [s0].[TwoFactorSecret], [s0].[UpdatedByUserID2], [s0].[UpdatedDate2], [s0].[Username], [s0].[UserID0], [s0].[AccessFailedCount0], [s0].[CreatedByUserID3], [s0].[CreatedDate3], [s0].[DateFormat0], [s0].[DeletedByUserID2], [s0].[DeletedDate2], [s0].[Department0], [s0].[DisplayName0], [s0].[Email1], [s0].[FirstName0], [s0].[IsActive2], [s0].[IsDeleted2], [s0].[IsLocked0], [s0].[JobTitle0], [s0].[Language0], [s0].[LastActivityDate0], [s0].[LastLoginDate0], [s0].[LastName0], [s0].[LockoutEnd0], [s0].[MobileNumber0], [s0].[MustChangePassword0], [s0].[PasswordChangedDate0], [s0].[PasswordHash0], [s0].[PasswordSalt0], [s0].[PhoneNumber0], [s0].[ProfilePicture0], [s0].[ThemePreference0], [s0].[TimeZone1], [s0].[TwoFactorEnabled0], [s0].[TwoFactorSecret0], [s0].[UpdatedByUserID3], [s0].[UpdatedDate3], [s0].[Username0], [s1].[PurchaseOrderItemID], [s1].[CancelledQuantity], [s1].[Currency], [s1].[Description], [s1].[DiscountAmount], [s1].[DiscountPercent], [s1].[InvoicedQuantity], [s1].[ItemStatus], [s1].[LastReceiptDate], [s1].[LineNumber], [s1].[LineTotal], [s1].[Notes], [s1].[OrderedQuantity], [s1].[ProductID], [s1].[ProductName], [s1].[PromisedDate], [s1].[PurchaseOrderID], [s1].[ReceivedQuantity], [s1].[RequiredDate], [s1].[SortOrder], [s1].[SupplierProductCode], [s1].[TaxAmount], [s1].[TaxRate], [s1].[UOMID], [s1].[UnitPrice], [s1].[ProductID0], [s1].[AllowBackorder], [s1].[AllowPreorder], [s1].[Brand], [s1].[CategoryID], [s1].[CommodityCode], [s1].[CountryOfOrigin], [s1].[CreatedByUserID], [s1].[CreatedDate], [s1].[Currency0], [s1].[DeletedByUserID], [s1].[DeletedDate], [s1].[Description0], [s1].[DimensionUOMID], [s1].[HSCode], [s1].[HasVariants], [s1].[Height], [s1].[InventoryUOMID], [s1].[IsActive], [s1].[IsDeleted], [s1].[IsKit], [s1].[IsTaxExempt], [s1].[LeadTimeDays], [s1].[Length], [s1].[ListPrice], [s1].[Manufacturer], [s1].[ManufacturerPartNumber], [s1].[MaxStockLevel], [s1].[MinStockLevel], [s1].[Notes0], [s1].[ProductCode], [s1].[ProductName0], [s1].[ProductType], [s1].[PurchaseUOMID], [s1].[ReorderPoint], [s1].[ReorderQuantity], [s1].[RequiresBatch], [s1].[RequiresSerial], [s1].[SalesUOMID], [s1].[SellPrice], [s1].[ShelfLifeDays], [s1].[ShortDescription], [s1].[StandardCost], [s1].[Status], [s1].[TaxCodeID], [s1].[TrackInventory], [s1].[UpdatedByUserID], [s1].[UpdatedDate], [s1].[Volume], [s1].[VolumeUOMID], [s1].[WarrantyDays], [s1].[Weight], [s1].[WeightUOMID], [s1].[Width], [s1].[UOMID0], [s1].[BaseUnit], [s1].[ConversionFactor], [s1].[CreatedDate0], [s1].[DecimalPlaces], [s1].[IsActive0], [s1].[Symbol], [s1].[UOMCode], [s1].[UOMName], [s1].[UOMType], [s1].[UpdatedDate0]
FROM (
    SELECT TOP(1) [p].[PurchaseOrderID], [p].[ApprovalNotes], [p].[ApprovalStatus], [p].[ApprovedByUserID], [p].[ApprovedDate], [p].[BuyerID], [p].[CreatedByUserID], [p].[CreatedDate], [p].[Currency], [p].[DeletedByUserID], [p].[DeletedDate], [p].[DeliveryAddressID], [p].[DeliveryInstructions], [p].[DeliveryWarehouseID], [p].[DiscountAmount], [p].[ExchangeRate], [p].[ExpectedDate], [p].[FreightAmount], [p].[FreightTerms], [p].[InternalNotes], [p].[IsDeleted], [p].[Notes], [p].[OrderDate], [p].[OrderStatus], [p].[OrderType], [p].[PaymentMethod], [p].[PaymentTerms], [p].[PromisedDate], [p].[PurchaseOrderNumber], [p].[RequiredDate], [p].[RequisitionID], [p].[ShippingMethod], [p].[SubTotal], [p].[SupplierID], [p].[SupplierReference], [p].[TaxAmount], [p].[TermsAndConditions], [p].[TotalAmount], [p].[UpdatedByUserID], [p].[UpdatedDate], [s].[SupplierID] AS [SupplierID0], [s].[APIEnabled], [s].[BankAccountDetails], [s].[BlockedByUserID], [s].[BlockedDate], [s].[BlockedReason], [s].[CertificateExpiryDate], [s].[CertificateNumber], [s].[ComplianceStatus], [s].[ContactEmail], [s].[ContactLandline], [s].[ContactMobile], [s].[ContactPhone], [s].[CreatedByUserID] AS [CreatedByUserID0], [s].[CreatedDate] AS [CreatedDate0], [s].[DeletedByUserID] AS [DeletedByUserID0], [s].[DeletedDate] AS [DeletedDate0], [s].[DeliveryDays], [s].[DeliveryScore], [s].[EDIEnabled], [s].[EarlyPaymentDiscountDays], [s].[EarlyPaymentDiscountPercent], [s].[InsuranceExpiryDate], [s].[InsurancePolicyNumber], [s].[IntegrationKey], [s].[InternalNotes] AS [InternalNotes0], [s].[IsActive], [s].[IsDeleted] AS [IsDeleted0], [s].[IsPreferred], [s].[LastOrderDate], [s].[LeadTimeDays], [s].[MinimumOrderValue], [s].[Notes] AS [Notes0], [s].[OrderCutoffTime], [s].[OutstandingBalance], [s].[PaymentMethodID], [s].[PaymentTermDays], [s].[PaymentTerms] AS [PaymentTerms0], [s].[PreferredCurrency], [s].[PriceScore], [s].[QualityScore], [s].[Rating], [s].[ReferenceCode], [s].[ResponsibleContact], [s].[ReturnPolicy], [s].[SupplierAddress], [s].[SupplierAddress2], [s].[SupplierCity], [s].[SupplierCode], [s].[SupplierCountry], [s].[SupplierGroup], [s].[SupplierName], [s].[SupplierPostCode], [s].[SupplierState], [s].[SupplierStatus], [s].[SupplierType], [s].[TaxNumber], [s].[TaxRate], [s].[TotalPurchaseValue], [s].[TradingName], [s].[UpdatedByUserID] AS [UpdatedByUserID0], [s].[UpdatedDate] AS [UpdatedDate0], [s].[WarrantyTerms], [s].[Website], [s].[WithholdingTaxRate], [s].[YTDPurchases], [w].[WarehouseID], [w].[Address1], [w].[Address2], [w].[AllowNegativeStock], [w].[City], [w].[Country], [w].[CreatedByUserID] AS [CreatedByUserID1], [w].[CreatedDate] AS [CreatedDate1], [w].[CurrentUtilization], [w].[Description], [w].[Email], [w].[IsActive] AS [IsActive0], [w].[IsDefault], [w].[ManagerName], [w].[OperatingHours], [w].[Phone], [w].[PostCode], [w].[State], [w].[StorageCapacity], [w].[TimeZone], [w].[TotalArea], [w].[UpdatedByUserID] AS [UpdatedByUserID1], [w].[UpdatedDate] AS [UpdatedDate1], [w].[WarehouseCode], [w].[WarehouseName], [w].[WarehouseType], [u].[UserID], [u].[AccessFailedCount], [u].[CreatedByUserID] AS [CreatedByUserID2], [u].[CreatedDate] AS [CreatedDate2], [u].[DateFormat], [u].[DeletedByUserID] AS [DeletedByUserID1], [u].[DeletedDate] AS [DeletedDate1], [u].[Department], [u].[DisplayName], [u].[Email] AS [Email0], [u].[FirstName], [u].[IsActive] AS [IsActive1], [u].[IsDeleted] AS [IsDeleted1], [u].[IsLocked], [u].[JobTitle], [u].[Language], [u].[LastActivityDate], [u].[LastLoginDate], [u].[LastName], [u].[LockoutEnd], [u].[MobileNumber], [u].[MustChangePassword], [u].[PasswordChangedDate], [u].[PasswordHash], [u].[PasswordSalt], [u].[PhoneNumber], [u].[ProfilePicture], [u].[ThemePreference], [u].[TimeZone] AS [TimeZone0], [u].[TwoFactorEnabled], [u].[TwoFactorSecret], [u].[UpdatedByUserID] AS [UpdatedByUserID2], [u].[UpdatedDate] AS [UpdatedDate2], [u].[Username], [u0].[UserID] AS [UserID0], [u0].[AccessFailedCount] AS [AccessFailedCount0], [u0].[CreatedByUserID] AS [CreatedByUserID3], [u0].[CreatedDate] AS [CreatedDate3], [u0].[DateFormat] AS [DateFormat0], [u0].[DeletedByUserID] AS [DeletedByUserID2], [u0].[DeletedDate] AS [DeletedDate2], [u0].[Department] AS [Department0], [u0].[DisplayName] AS [DisplayName0], [u0].[Email] AS [Email1], [u0].[FirstName] AS [FirstName0], [u0].[IsActive] AS [IsActive2], [u0].[IsDeleted] AS [IsDeleted2], [u0].[IsLocked] AS [IsLocked0], [u0].[JobTitle] AS [JobTitle0], [u0].[Language] AS [Language0], [u0].[LastActivityDate] AS [LastActivityDate0], [u0].[LastLoginDate] AS [LastLoginDate0], [u0].[LastName] AS [LastName0], [u0].[LockoutEnd] AS [LockoutEnd0], [u0].[MobileNumber] AS [MobileNumber0], [u0].[MustChangePassword] AS [MustChangePassword0], [u0].[PasswordChangedDate] AS [PasswordChangedDate0], [u0].[PasswordHash] AS [PasswordHash0], [u0].[PasswordSalt] AS [PasswordSalt0], [u0].[PhoneNumber] AS [PhoneNumber0], [u0].[ProfilePicture] AS [ProfilePicture0], [u0].[ThemePreference] AS [ThemePreference0], [u0].[TimeZone] AS [TimeZone1], [u0].[TwoFactorEnabled] AS [TwoFactorEnabled0], [u0].[TwoFactorSecret] AS [TwoFactorSecret0], [u0].[UpdatedByUserID] AS [UpdatedByUserID3], [u0].[UpdatedDate] AS [UpdatedDate3], [u0].[Username] AS [Username0]
    FROM [PurchaseOrder] AS [p]
    INNER JOIN [Supplier] AS [s] ON [p].[SupplierID] = [s].[SupplierID]
    INNER JOIN [Warehouse] AS [w] ON [p].[DeliveryWarehouseID] = [w].[WarehouseID]
    LEFT JOIN [User] AS [u] ON [p].[BuyerID] = [u].[UserID]
    LEFT JOIN [User] AS [u0] ON [p].[ApprovedByUserID] = [u0].[UserID]
    WHERE [p].[PurchaseOrderID] = @__id_0 AND [p].[IsDeleted] = CAST(0 AS bit)
) AS [s0]
LEFT JOIN (
    SELECT [p0].[PurchaseOrderItemID], [p0].[CancelledQuantity], [p0].[Currency], [p0].[Description], [p0].[DiscountAmount], [p0].[DiscountPercent], [p0].[InvoicedQuantity], [p0].[ItemStatus], [p0].[LastReceiptDate], [p0].[LineNumber], [p0].[LineTotal], [p0].[Notes], [p0].[OrderedQuantity], [p0].[ProductID], [p0].[ProductName], [p0].[PromisedDate], [p0].[PurchaseOrderID], [p0].[ReceivedQuantity], [p0].[RequiredDate], [p0].[SortOrder], [p0].[SupplierProductCode], [p0].[TaxAmount], [p0].[TaxRate], [p0].[UOMID], [p0].[UnitPrice], [p1].[ProductID] AS [ProductID0], [p1].[AllowBackorder], [p1].[AllowPreorder], [p1].[Brand], [p1].[CategoryID], [p1].[CommodityCode], [p1].[CountryOfOrigin], [p1].[CreatedByUserID], [p1].[CreatedDate], [p1].[Currency] AS [Currency0], [p1].[DeletedByUserID], [p1].[DeletedDate], [p1].[Description] AS [Description0], [p1].[DimensionUOMID], [p1].[HSCode], [p1].[HasVariants], [p1].[Height], [p1].[InventoryUOMID], [p1].[IsActive], [p1].[IsDeleted], [p1].[IsKit], [p1].[IsTaxExempt], [p1].[LeadTimeDays], [p1].[Length], [p1].[ListPrice], [p1].[Manufacturer], [p1].[ManufacturerPartNumber], [p1].[MaxStockLevel], [p1].[MinStockLevel], [p1].[Notes] AS [Notes0], [p1].[ProductCode], [p1].[ProductName] AS [ProductName0], [p1].[ProductType], [p1].[PurchaseUOMID], [p1].[ReorderPoint], [p1].[ReorderQuantity], [p1].[RequiresBatch], [p1].[RequiresSerial], [p1].[SalesUOMID], [p1].[SellPrice], [p1].[ShelfLifeDays], [p1].[ShortDescription], [p1].[StandardCost], [p1].[Status], [p1].[TaxCodeID], [p1].[TrackInventory], [p1].[UpdatedByUserID], [p1].[UpdatedDate], [p1].[Volume], [p1].[VolumeUOMID], [p1].[WarrantyDays], [p1].[Weight], [p1].[WeightUOMID], [p1].[Width], [u1].[UOMID] AS [UOMID0], [u1].[BaseUnit], [u1].[ConversionFactor], [u1].[CreatedDate] AS [CreatedDate0], [u1].[DecimalPlaces], [u1].[IsActive] AS [IsActive0], [u1].[Symbol], [u1].[UOMCode], [u1].[UOMName], [u1].[UOMType], [u1].[UpdatedDate] AS [UpdatedDate0]
    FROM [PurchaseOrderItem] AS [p0]
    INNER JOIN [Product] AS [p1] ON [p0].[ProductID] = [p1].[ProductID]
    INNER JOIN [UnitOfMeasure] AS [u1] ON [p0].[UOMID] = [u1].[UOMID]
) AS [s1] ON [s0].[PurchaseOrderID] = [s1].[PurchaseOrderID]
ORDER BY [s0].[PurchaseOrderID], [s0].[SupplierID0], [s0].[WarehouseID], [s0].[UserID], [s0].[UserID0], [s1].[PurchaseOrderItemID], [s1].[ProductID0]
2025-07-29 20:12:36.952 +03:00 [ERR] An exception occurred while iterating over the results of a query for context type 'nCarry.Web.Data.ApplicationDbContext'.
Microsoft.Data.SqlClient.SqlException (0x80131904): Invalid column name 'Currency'.
   at System.Threading.Tasks.ContinuationResultTaskFromResultTask`2.InnerInvoke()
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.InitializeReaderAsync(AsyncEnumerator enumerator, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.SqlServer.Storage.Internal.SqlServerExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
ClientConnectionId:97ba6c7f-fbe5-4a4f-a98f-cf3c9f380dbd
Error Number:207,State:1,Class:16
Microsoft.Data.SqlClient.SqlException (0x80131904): Invalid column name 'Currency'.
   at System.Threading.Tasks.ContinuationResultTaskFromResultTask`2.InnerInvoke()
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.InitializeReaderAsync(AsyncEnumerator enumerator, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.SqlServer.Storage.Internal.SqlServerExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
ClientConnectionId:97ba6c7f-fbe5-4a4f-a98f-cf3c9f380dbd
Error Number:207,State:1,Class:16
2025-07-29 20:12:36.953 +03:00 [ERR] HTTP GET /PurchaseOrder/Details/5 responded 500 in 158.5928 ms
Microsoft.Data.SqlClient.SqlException (0x80131904): Invalid column name 'Currency'.
   at System.Threading.Tasks.ContinuationResultTaskFromResultTask`2.InnerInvoke()
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.InitializeReaderAsync(AsyncEnumerator enumerator, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.SqlServer.Storage.Internal.SqlServerExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
   at Microsoft.EntityFrameworkCore.Query.ShapedQueryCompilingExpressionVisitor.SingleOrDefaultAsync[TSource](IAsyncEnumerable`1 asyncEnumerable, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.ShapedQueryCompilingExpressionVisitor.SingleOrDefaultAsync[TSource](IAsyncEnumerable`1 asyncEnumerable, CancellationToken cancellationToken)
   at nCarry.Web.Controllers.PurchaseOrderController.Details(Nullable`1 id) in /Users/<USER>/Source/nCarry/nCarry/nCarry.Web/Controllers/PurchaseOrderController.cs:line 42
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.TaskOfIActionResultExecutor.Execute(ActionContext actionContext, IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeActionMethodAsync>g__Awaited|12_0(ControllerActionInvoker invoker, ValueTask`1 actionResultValueTask)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeInnerFilterAsync>g__Awaited|13_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextResourceFilter>g__Awaited|25_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.Rethrow(ResourceExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeFilterPipelineAsync>g__Awaited|20_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Session.SessionMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Session.SessionMiddleware.Invoke(HttpContext context)
   at Serilog.AspNetCore.RequestLoggingMiddleware.Invoke(HttpContext httpContext)
ClientConnectionId:97ba6c7f-fbe5-4a4f-a98f-cf3c9f380dbd
Error Number:207,State:1,Class:16
2025-07-29 20:12:36.959 +03:00 [ERR] An unhandled exception has occurred while executing the request.
Microsoft.Data.SqlClient.SqlException (0x80131904): Invalid column name 'Currency'.
   at System.Threading.Tasks.ContinuationResultTaskFromResultTask`2.InnerInvoke()
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.InitializeReaderAsync(AsyncEnumerator enumerator, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.SqlServer.Storage.Internal.SqlServerExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
   at Microsoft.EntityFrameworkCore.Query.ShapedQueryCompilingExpressionVisitor.SingleOrDefaultAsync[TSource](IAsyncEnumerable`1 asyncEnumerable, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.ShapedQueryCompilingExpressionVisitor.SingleOrDefaultAsync[TSource](IAsyncEnumerable`1 asyncEnumerable, CancellationToken cancellationToken)
   at nCarry.Web.Controllers.PurchaseOrderController.Details(Nullable`1 id) in /Users/<USER>/Source/nCarry/nCarry/nCarry.Web/Controllers/PurchaseOrderController.cs:line 42
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.TaskOfIActionResultExecutor.Execute(ActionContext actionContext, IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeActionMethodAsync>g__Awaited|12_0(ControllerActionInvoker invoker, ValueTask`1 actionResultValueTask)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeInnerFilterAsync>g__Awaited|13_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextResourceFilter>g__Awaited|25_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.Rethrow(ResourceExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeFilterPipelineAsync>g__Awaited|20_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Session.SessionMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Session.SessionMiddleware.Invoke(HttpContext context)
   at Serilog.AspNetCore.RequestLoggingMiddleware.Invoke(HttpContext httpContext)
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
ClientConnectionId:97ba6c7f-fbe5-4a4f-a98f-cf3c9f380dbd
Error Number:207,State:1,Class:16
2025-07-29 20:12:44.248 +03:00 [INF] HTTP GET /GoodsReceipt responded 200 in 121.2441 ms
2025-07-29 20:12:51.275 +03:00 [INF] HTTP GET /GoodsReceipt/Create responded 200 in 367.4090 ms
2025-07-29 20:13:00.146 +03:00 [ERR] Failed executing DbCommand (92ms) [Parameters=[@__purchaseOrderId_Value_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [s0].[PurchaseOrderID], [s0].[ApprovalNotes], [s0].[ApprovalStatus], [s0].[ApprovedByUserID], [s0].[ApprovedDate], [s0].[BuyerID], [s0].[CreatedByUserID], [s0].[CreatedDate], [s0].[Currency], [s0].[DeletedByUserID], [s0].[DeletedDate], [s0].[DeliveryAddressID], [s0].[DeliveryInstructions], [s0].[DeliveryWarehouseID], [s0].[DiscountAmount], [s0].[ExchangeRate], [s0].[ExpectedDate], [s0].[FreightAmount], [s0].[FreightTerms], [s0].[InternalNotes], [s0].[IsDeleted], [s0].[Notes], [s0].[OrderDate], [s0].[OrderStatus], [s0].[OrderType], [s0].[PaymentMethod], [s0].[PaymentTerms], [s0].[PromisedDate], [s0].[PurchaseOrderNumber], [s0].[RequiredDate], [s0].[RequisitionID], [s0].[ShippingMethod], [s0].[SubTotal], [s0].[SupplierID], [s0].[SupplierReference], [s0].[TaxAmount], [s0].[TermsAndConditions], [s0].[TotalAmount], [s0].[UpdatedByUserID], [s0].[UpdatedDate], [s0].[SupplierID0], [s0].[APIEnabled], [s0].[BankAccountDetails], [s0].[BlockedByUserID], [s0].[BlockedDate], [s0].[BlockedReason], [s0].[CertificateExpiryDate], [s0].[CertificateNumber], [s0].[ComplianceStatus], [s0].[ContactEmail], [s0].[ContactLandline], [s0].[ContactMobile], [s0].[ContactPhone], [s0].[CreatedByUserID0], [s0].[CreatedDate0], [s0].[DeletedByUserID0], [s0].[DeletedDate0], [s0].[DeliveryDays], [s0].[DeliveryScore], [s0].[EDIEnabled], [s0].[EarlyPaymentDiscountDays], [s0].[EarlyPaymentDiscountPercent], [s0].[InsuranceExpiryDate], [s0].[InsurancePolicyNumber], [s0].[IntegrationKey], [s0].[InternalNotes0], [s0].[IsActive], [s0].[IsDeleted0], [s0].[IsPreferred], [s0].[LastOrderDate], [s0].[LeadTimeDays], [s0].[MinimumOrderValue], [s0].[Notes0], [s0].[OrderCutoffTime], [s0].[OutstandingBalance], [s0].[PaymentMethodID], [s0].[PaymentTermDays], [s0].[PaymentTerms0], [s0].[PreferredCurrency], [s0].[PriceScore], [s0].[QualityScore], [s0].[Rating], [s0].[ReferenceCode], [s0].[ResponsibleContact], [s0].[ReturnPolicy], [s0].[SupplierAddress], [s0].[SupplierAddress2], [s0].[SupplierCity], [s0].[SupplierCode], [s0].[SupplierCountry], [s0].[SupplierGroup], [s0].[SupplierName], [s0].[SupplierPostCode], [s0].[SupplierState], [s0].[SupplierStatus], [s0].[SupplierType], [s0].[TaxNumber], [s0].[TaxRate], [s0].[TotalPurchaseValue], [s0].[TradingName], [s0].[UpdatedByUserID0], [s0].[UpdatedDate0], [s0].[WarrantyTerms], [s0].[Website], [s0].[WithholdingTaxRate], [s0].[YTDPurchases], [s1].[PurchaseOrderItemID], [s1].[CancelledQuantity], [s1].[Currency], [s1].[Description], [s1].[DiscountAmount], [s1].[DiscountPercent], [s1].[InvoicedQuantity], [s1].[ItemStatus], [s1].[LastReceiptDate], [s1].[LineNumber], [s1].[LineTotal], [s1].[Notes], [s1].[OrderedQuantity], [s1].[ProductID], [s1].[ProductName], [s1].[PromisedDate], [s1].[PurchaseOrderID], [s1].[ReceivedQuantity], [s1].[RequiredDate], [s1].[SortOrder], [s1].[SupplierProductCode], [s1].[TaxAmount], [s1].[TaxRate], [s1].[UOMID], [s1].[UnitPrice], [s1].[ProductID0], [s1].[AllowBackorder], [s1].[AllowPreorder], [s1].[Brand], [s1].[CategoryID], [s1].[CommodityCode], [s1].[CountryOfOrigin], [s1].[CreatedByUserID], [s1].[CreatedDate], [s1].[Currency0], [s1].[DeletedByUserID], [s1].[DeletedDate], [s1].[Description0], [s1].[DimensionUOMID], [s1].[HSCode], [s1].[HasVariants], [s1].[Height], [s1].[InventoryUOMID], [s1].[IsActive], [s1].[IsDeleted], [s1].[IsKit], [s1].[IsTaxExempt], [s1].[LeadTimeDays], [s1].[Length], [s1].[ListPrice], [s1].[Manufacturer], [s1].[ManufacturerPartNumber], [s1].[MaxStockLevel], [s1].[MinStockLevel], [s1].[Notes0], [s1].[ProductCode], [s1].[ProductName0], [s1].[ProductType], [s1].[PurchaseUOMID], [s1].[ReorderPoint], [s1].[ReorderQuantity], [s1].[RequiresBatch], [s1].[RequiresSerial], [s1].[SalesUOMID], [s1].[SellPrice], [s1].[ShelfLifeDays], [s1].[ShortDescription], [s1].[StandardCost], [s1].[Status], [s1].[TaxCodeID], [s1].[TrackInventory], [s1].[UpdatedByUserID], [s1].[UpdatedDate], [s1].[Volume], [s1].[VolumeUOMID], [s1].[WarrantyDays], [s1].[Weight], [s1].[WeightUOMID], [s1].[Width]
FROM (
    SELECT TOP(1) [p].[PurchaseOrderID], [p].[ApprovalNotes], [p].[ApprovalStatus], [p].[ApprovedByUserID], [p].[ApprovedDate], [p].[BuyerID], [p].[CreatedByUserID], [p].[CreatedDate], [p].[Currency], [p].[DeletedByUserID], [p].[DeletedDate], [p].[DeliveryAddressID], [p].[DeliveryInstructions], [p].[DeliveryWarehouseID], [p].[DiscountAmount], [p].[ExchangeRate], [p].[ExpectedDate], [p].[FreightAmount], [p].[FreightTerms], [p].[InternalNotes], [p].[IsDeleted], [p].[Notes], [p].[OrderDate], [p].[OrderStatus], [p].[OrderType], [p].[PaymentMethod], [p].[PaymentTerms], [p].[PromisedDate], [p].[PurchaseOrderNumber], [p].[RequiredDate], [p].[RequisitionID], [p].[ShippingMethod], [p].[SubTotal], [p].[SupplierID], [p].[SupplierReference], [p].[TaxAmount], [p].[TermsAndConditions], [p].[TotalAmount], [p].[UpdatedByUserID], [p].[UpdatedDate], [s].[SupplierID] AS [SupplierID0], [s].[APIEnabled], [s].[BankAccountDetails], [s].[BlockedByUserID], [s].[BlockedDate], [s].[BlockedReason], [s].[CertificateExpiryDate], [s].[CertificateNumber], [s].[ComplianceStatus], [s].[ContactEmail], [s].[ContactLandline], [s].[ContactMobile], [s].[ContactPhone], [s].[CreatedByUserID] AS [CreatedByUserID0], [s].[CreatedDate] AS [CreatedDate0], [s].[DeletedByUserID] AS [DeletedByUserID0], [s].[DeletedDate] AS [DeletedDate0], [s].[DeliveryDays], [s].[DeliveryScore], [s].[EDIEnabled], [s].[EarlyPaymentDiscountDays], [s].[EarlyPaymentDiscountPercent], [s].[InsuranceExpiryDate], [s].[InsurancePolicyNumber], [s].[IntegrationKey], [s].[InternalNotes] AS [InternalNotes0], [s].[IsActive], [s].[IsDeleted] AS [IsDeleted0], [s].[IsPreferred], [s].[LastOrderDate], [s].[LeadTimeDays], [s].[MinimumOrderValue], [s].[Notes] AS [Notes0], [s].[OrderCutoffTime], [s].[OutstandingBalance], [s].[PaymentMethodID], [s].[PaymentTermDays], [s].[PaymentTerms] AS [PaymentTerms0], [s].[PreferredCurrency], [s].[PriceScore], [s].[QualityScore], [s].[Rating], [s].[ReferenceCode], [s].[ResponsibleContact], [s].[ReturnPolicy], [s].[SupplierAddress], [s].[SupplierAddress2], [s].[SupplierCity], [s].[SupplierCode], [s].[SupplierCountry], [s].[SupplierGroup], [s].[SupplierName], [s].[SupplierPostCode], [s].[SupplierState], [s].[SupplierStatus], [s].[SupplierType], [s].[TaxNumber], [s].[TaxRate], [s].[TotalPurchaseValue], [s].[TradingName], [s].[UpdatedByUserID] AS [UpdatedByUserID0], [s].[UpdatedDate] AS [UpdatedDate0], [s].[WarrantyTerms], [s].[Website], [s].[WithholdingTaxRate], [s].[YTDPurchases]
    FROM [PurchaseOrder] AS [p]
    INNER JOIN [Supplier] AS [s] ON [p].[SupplierID] = [s].[SupplierID]
    WHERE [p].[PurchaseOrderID] = @__purchaseOrderId_Value_0
) AS [s0]
LEFT JOIN (
    SELECT [p0].[PurchaseOrderItemID], [p0].[CancelledQuantity], [p0].[Currency], [p0].[Description], [p0].[DiscountAmount], [p0].[DiscountPercent], [p0].[InvoicedQuantity], [p0].[ItemStatus], [p0].[LastReceiptDate], [p0].[LineNumber], [p0].[LineTotal], [p0].[Notes], [p0].[OrderedQuantity], [p0].[ProductID], [p0].[ProductName], [p0].[PromisedDate], [p0].[PurchaseOrderID], [p0].[ReceivedQuantity], [p0].[RequiredDate], [p0].[SortOrder], [p0].[SupplierProductCode], [p0].[TaxAmount], [p0].[TaxRate], [p0].[UOMID], [p0].[UnitPrice], [p1].[ProductID] AS [ProductID0], [p1].[AllowBackorder], [p1].[AllowPreorder], [p1].[Brand], [p1].[CategoryID], [p1].[CommodityCode], [p1].[CountryOfOrigin], [p1].[CreatedByUserID], [p1].[CreatedDate], [p1].[Currency] AS [Currency0], [p1].[DeletedByUserID], [p1].[DeletedDate], [p1].[Description] AS [Description0], [p1].[DimensionUOMID], [p1].[HSCode], [p1].[HasVariants], [p1].[Height], [p1].[InventoryUOMID], [p1].[IsActive], [p1].[IsDeleted], [p1].[IsKit], [p1].[IsTaxExempt], [p1].[LeadTimeDays], [p1].[Length], [p1].[ListPrice], [p1].[Manufacturer], [p1].[ManufacturerPartNumber], [p1].[MaxStockLevel], [p1].[MinStockLevel], [p1].[Notes] AS [Notes0], [p1].[ProductCode], [p1].[ProductName] AS [ProductName0], [p1].[ProductType], [p1].[PurchaseUOMID], [p1].[ReorderPoint], [p1].[ReorderQuantity], [p1].[RequiresBatch], [p1].[RequiresSerial], [p1].[SalesUOMID], [p1].[SellPrice], [p1].[ShelfLifeDays], [p1].[ShortDescription], [p1].[StandardCost], [p1].[Status], [p1].[TaxCodeID], [p1].[TrackInventory], [p1].[UpdatedByUserID], [p1].[UpdatedDate], [p1].[Volume], [p1].[VolumeUOMID], [p1].[WarrantyDays], [p1].[Weight], [p1].[WeightUOMID], [p1].[Width]
    FROM [PurchaseOrderItem] AS [p0]
    INNER JOIN [Product] AS [p1] ON [p0].[ProductID] = [p1].[ProductID]
) AS [s1] ON [s0].[PurchaseOrderID] = [s1].[PurchaseOrderID]
ORDER BY [s0].[PurchaseOrderID], [s0].[SupplierID0], [s1].[PurchaseOrderItemID]
2025-07-29 20:13:00.147 +03:00 [ERR] An exception occurred while iterating over the results of a query for context type 'nCarry.Web.Data.ApplicationDbContext'.
Microsoft.Data.SqlClient.SqlException (0x80131904): Invalid column name 'Currency'.
   at System.Threading.Tasks.ContinuationResultTaskFromResultTask`2.InnerInvoke()
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.InitializeReaderAsync(AsyncEnumerator enumerator, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.SqlServer.Storage.Internal.SqlServerExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
ClientConnectionId:97ba6c7f-fbe5-4a4f-a98f-cf3c9f380dbd
Error Number:207,State:1,Class:16
Microsoft.Data.SqlClient.SqlException (0x80131904): Invalid column name 'Currency'.
   at System.Threading.Tasks.ContinuationResultTaskFromResultTask`2.InnerInvoke()
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.InitializeReaderAsync(AsyncEnumerator enumerator, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.SqlServer.Storage.Internal.SqlServerExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
ClientConnectionId:97ba6c7f-fbe5-4a4f-a98f-cf3c9f380dbd
Error Number:207,State:1,Class:16
2025-07-29 20:13:00.148 +03:00 [ERR] HTTP GET /GoodsReceipt/Create responded 500 in 188.7960 ms
Microsoft.Data.SqlClient.SqlException (0x80131904): Invalid column name 'Currency'.
   at System.Threading.Tasks.ContinuationResultTaskFromResultTask`2.InnerInvoke()
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.InitializeReaderAsync(AsyncEnumerator enumerator, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.SqlServer.Storage.Internal.SqlServerExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
   at Microsoft.EntityFrameworkCore.Query.ShapedQueryCompilingExpressionVisitor.SingleOrDefaultAsync[TSource](IAsyncEnumerable`1 asyncEnumerable, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.ShapedQueryCompilingExpressionVisitor.SingleOrDefaultAsync[TSource](IAsyncEnumerable`1 asyncEnumerable, CancellationToken cancellationToken)
   at nCarry.Web.Controllers.GoodsReceiptController.Create(Nullable`1 purchaseOrderId) in /Users/<USER>/Source/nCarry/nCarry/nCarry.Web/Controllers/GoodsReceiptController.cs:line 97
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.TaskOfIActionResultExecutor.Execute(ActionContext actionContext, IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeActionMethodAsync>g__Awaited|12_0(ControllerActionInvoker invoker, ValueTask`1 actionResultValueTask)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeInnerFilterAsync>g__Awaited|13_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextResourceFilter>g__Awaited|25_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.Rethrow(ResourceExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeFilterPipelineAsync>g__Awaited|20_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Session.SessionMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Session.SessionMiddleware.Invoke(HttpContext context)
   at Serilog.AspNetCore.RequestLoggingMiddleware.Invoke(HttpContext httpContext)
ClientConnectionId:97ba6c7f-fbe5-4a4f-a98f-cf3c9f380dbd
Error Number:207,State:1,Class:16
2025-07-29 20:13:00.151 +03:00 [ERR] An unhandled exception has occurred while executing the request.
Microsoft.Data.SqlClient.SqlException (0x80131904): Invalid column name 'Currency'.
   at System.Threading.Tasks.ContinuationResultTaskFromResultTask`2.InnerInvoke()
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.InitializeReaderAsync(AsyncEnumerator enumerator, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.SqlServer.Storage.Internal.SqlServerExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
   at Microsoft.EntityFrameworkCore.Query.ShapedQueryCompilingExpressionVisitor.SingleOrDefaultAsync[TSource](IAsyncEnumerable`1 asyncEnumerable, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.ShapedQueryCompilingExpressionVisitor.SingleOrDefaultAsync[TSource](IAsyncEnumerable`1 asyncEnumerable, CancellationToken cancellationToken)
   at nCarry.Web.Controllers.GoodsReceiptController.Create(Nullable`1 purchaseOrderId) in /Users/<USER>/Source/nCarry/nCarry/nCarry.Web/Controllers/GoodsReceiptController.cs:line 97
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.TaskOfIActionResultExecutor.Execute(ActionContext actionContext, IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeActionMethodAsync>g__Awaited|12_0(ControllerActionInvoker invoker, ValueTask`1 actionResultValueTask)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeInnerFilterAsync>g__Awaited|13_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextResourceFilter>g__Awaited|25_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.Rethrow(ResourceExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeFilterPipelineAsync>g__Awaited|20_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Session.SessionMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Session.SessionMiddleware.Invoke(HttpContext context)
   at Serilog.AspNetCore.RequestLoggingMiddleware.Invoke(HttpContext httpContext)
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
ClientConnectionId:97ba6c7f-fbe5-4a4f-a98f-cf3c9f380dbd
Error Number:207,State:1,Class:16
2025-07-29 20:38:06.238 +03:00 [INF] HTTP GET /Customer/Create responded 200 in 621.4321 ms
2025-07-29 20:39:08.051 +03:00 [INF] HTTP POST /Customer/Create responded 302 in 297.8237 ms
2025-07-29 20:39:08.170 +03:00 [INF] HTTP GET /Customer responded 200 in 115.9967 ms
2025-07-29 20:39:32.514 +03:00 [INF] HTTP GET /SalesOrder/Create responded 200 in 431.0129 ms
2025-07-29 20:39:56.639 +03:00 [INF] HTTP POST /SalesOrder/Create responded 302 in 259.9708 ms
2025-07-29 20:39:56.724 +03:00 [INF] HTTP GET /SalesOrder responded 200 in 83.4681 ms
2025-07-29 20:40:07.410 +03:00 [INF] HTTP GET /SalesOrder/Edit/29 responded 200 in 425.8030 ms
2025-07-29 20:40:17.510 +03:00 [INF] HTTP GET /SalesOrder/Details/29 responded 200 in 212.1419 ms
2025-07-29 20:51:44.925 +03:00 [INF] HTTP GET /SalesOrder/Edit/29 responded 200 in 2228.8846 ms
2025-07-29 20:51:54.350 +03:00 [INF] HTTP GET /Account/Login responded 200 in 19.9653 ms
2025-07-29 20:52:01.812 +03:00 [INF] HTTP POST /Account/Login responded 302 in 408.9466 ms
2025-07-29 20:52:04.082 +03:00 [INF] HTTP GET /Dashboard responded 200 in 2268.1119 ms
2025-07-29 20:52:04.271 +03:00 [INF] HTTP GET /Dashboard/GetChartData responded 200 in 138.1164 ms
2025-07-29 20:52:32.781 +03:00 [INF] HTTP GET /SalesInvoice responded 404 in 0.7814 ms
2025-07-29 20:52:38.284 +03:00 [INF] HTTP GET /Invoice responded 404 in 0.4245 ms
2025-07-29 20:59:35.208 +03:00 [INF] HTTP GET /SalesInvoice responded 404 in 0.8560 ms
2025-07-29 21:18:40.301 +03:00 [INF] HTTP GET /Account/Login responded 200 in 10.0592 ms
2025-07-29 21:18:46.074 +03:00 [INF] HTTP POST /Account/Login responded 302 in 813.1206 ms
2025-07-29 21:18:47.444 +03:00 [INF] HTTP GET /Dashboard responded 200 in 1366.2293 ms
2025-07-29 21:18:47.608 +03:00 [INF] HTTP GET /Dashboard/GetChartData responded 200 in 119.8286 ms
2025-07-29 21:19:26.654 +03:00 [INF] HTTP GET /Dashboard responded 200 in 1426.0313 ms
2025-07-29 21:19:26.773 +03:00 [INF] HTTP GET /Dashboard/GetChartData responded 200 in 91.0989 ms
2025-07-29 21:20:03.527 +03:00 [INF] HTTP GET /SalesInvoice responded 404 in 0.6891 ms
2025-07-29 21:20:16.359 +03:00 [INF] HTTP GET /SalesOrder responded 200 in 93.6465 ms
2025-07-29 21:20:27.058 +03:00 [INF] HTTP GET /SalesOrder/Details/29 responded 200 in 94.8182 ms
2025-07-29 21:20:51.130 +03:00 [INF] HTTP GET /SalesOrder/Details/29 responded 200 in 104.3604 ms
2025-07-29 21:21:40.119 +03:00 [INF] Starting database seeding.
2025-07-29 21:21:44.189 +03:00 [INF] Admin user already exists. Seeding is not required.
2025-07-29 21:26:36.952 +03:00 [INF] Starting database seeding.
2025-07-29 21:26:39.803 +03:00 [INF] Admin user already exists. Seeding is not required.
2025-07-29 21:26:53.718 +03:00 [WRN] Failed to determine the https port for redirect.
2025-07-29 21:26:53.772 +03:00 [INF] HTTP GET / responded 302 in 56.2830 ms
2025-07-29 21:26:56.285 +03:00 [INF] HTTP GET /Dashboard responded 200 in 2504.3628 ms
2025-07-29 21:26:56.520 +03:00 [INF] HTTP GET /Dashboard/GetChartData responded 200 in 180.1492 ms
2025-07-29 21:27:03.677 +03:00 [INF] HTTP GET /SalesOrder/Details/29 responded 200 in 453.7049 ms
2025-07-29 21:27:11.646 +03:00 [ERR] HTTP GET /SalesInvoice/CreateFromOrder responded 500 in 9.1823 ms
System.InvalidOperationException: The expression 'o.Items' is invalid inside an 'Include' operation, since it does not represent a property access: 't => t.MyProperty'. To target navigations declared on derived types, use casting ('t => ((Derived)t).MyProperty') or the 'as' operator ('t => (t as Derived).MyProperty'). Collection navigation access can be filtered by composing Where, OrderBy(Descending), ThenBy(Descending), Skip or Take operations. For more information on including related data, see https://go.microsoft.com/fwlink/?LinkID=746393.
   at Microsoft.EntityFrameworkCore.Query.Internal.NavigationExpandingExpressionVisitor.PopulateIncludeTree(IncludeTreeNode includeTreeNode, Expression expression, Boolean setLoaded)
   at Microsoft.EntityFrameworkCore.Query.Internal.NavigationExpandingExpressionVisitor.ProcessInclude(NavigationExpansionExpression source, Expression expression, Boolean thenInclude, Boolean setLoaded)
   at Microsoft.EntityFrameworkCore.Query.Internal.NavigationExpandingExpressionVisitor.VisitMethodCall(MethodCallExpression methodCallExpression)
   at Microsoft.EntityFrameworkCore.Query.Internal.NavigationExpandingExpressionVisitor.VisitMethodCall(MethodCallExpression methodCallExpression)
   at Microsoft.EntityFrameworkCore.Query.Internal.NavigationExpandingExpressionVisitor.VisitMethodCall(MethodCallExpression methodCallExpression)
   at Microsoft.EntityFrameworkCore.Query.Internal.NavigationExpandingExpressionVisitor.VisitMethodCall(MethodCallExpression methodCallExpression)
   at Microsoft.EntityFrameworkCore.Query.Internal.NavigationExpandingExpressionVisitor.VisitMethodCall(MethodCallExpression methodCallExpression)
   at Microsoft.EntityFrameworkCore.Query.Internal.NavigationExpandingExpressionVisitor.Expand(Expression query)
   at Microsoft.EntityFrameworkCore.Query.QueryTranslationPreprocessor.Process(Expression query)
   at Microsoft.EntityFrameworkCore.Query.QueryCompilationContext.CreateQueryExecutorExpression[TResult](Expression query)
   at Microsoft.EntityFrameworkCore.Query.QueryCompilationContext.CreateQueryExecutor[TResult](Expression query)
   at Microsoft.EntityFrameworkCore.Storage.Database.CompileQuery[TResult](Expression query, Boolean async)
   at Microsoft.EntityFrameworkCore.Query.Internal.QueryCompiler.CompileQueryCore[TResult](IDatabase database, Expression query, IModel model, Boolean async)
   at Microsoft.EntityFrameworkCore.Query.Internal.QueryCompiler.<>c__DisplayClass11_0`1.<ExecuteCore>b__0()
   at Microsoft.EntityFrameworkCore.Query.Internal.CompiledQueryCache.GetOrAddQuery[TResult](Object cacheKey, Func`1 compiler)
   at Microsoft.EntityFrameworkCore.Query.Internal.QueryCompiler.ExecuteCore[TResult](Expression query, Boolean async, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.QueryCompiler.ExecuteAsync[TResult](Expression query, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.EntityQueryProvider.ExecuteAsync[TResult](Expression expression, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ExecuteAsync[TSource,TResult](MethodInfo operatorMethodInfo, IQueryable`1 source, Expression expression, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ExecuteAsync[TSource,TResult](MethodInfo operatorMethodInfo, IQueryable`1 source, LambdaExpression expression, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.FirstOrDefaultAsync[TSource](IQueryable`1 source, Expression`1 predicate, CancellationToken cancellationToken)
   at nCarry.Web.Controllers.SalesInvoiceController.CreateFromOrder(Nullable`1 orderId) in /Users/<USER>/Source/nCarry/nCarry/nCarry.Web/Controllers/SalesInvoiceController.cs:line 67
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.TaskOfIActionResultExecutor.Execute(ActionContext actionContext, IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeActionMethodAsync>g__Awaited|12_0(ControllerActionInvoker invoker, ValueTask`1 actionResultValueTask)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeInnerFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextResourceFilter>g__Awaited|25_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.Rethrow(ResourceExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.InvokeFilterPipelineAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Session.SessionMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Session.SessionMiddleware.Invoke(HttpContext context)
   at Serilog.AspNetCore.RequestLoggingMiddleware.Invoke(HttpContext httpContext)
2025-07-29 21:27:11.653 +03:00 [ERR] An unhandled exception has occurred while executing the request.
System.InvalidOperationException: The expression 'o.Items' is invalid inside an 'Include' operation, since it does not represent a property access: 't => t.MyProperty'. To target navigations declared on derived types, use casting ('t => ((Derived)t).MyProperty') or the 'as' operator ('t => (t as Derived).MyProperty'). Collection navigation access can be filtered by composing Where, OrderBy(Descending), ThenBy(Descending), Skip or Take operations. For more information on including related data, see https://go.microsoft.com/fwlink/?LinkID=746393.
   at Microsoft.EntityFrameworkCore.Query.Internal.NavigationExpandingExpressionVisitor.PopulateIncludeTree(IncludeTreeNode includeTreeNode, Expression expression, Boolean setLoaded)
   at Microsoft.EntityFrameworkCore.Query.Internal.NavigationExpandingExpressionVisitor.ProcessInclude(NavigationExpansionExpression source, Expression expression, Boolean thenInclude, Boolean setLoaded)
   at Microsoft.EntityFrameworkCore.Query.Internal.NavigationExpandingExpressionVisitor.VisitMethodCall(MethodCallExpression methodCallExpression)
   at Microsoft.EntityFrameworkCore.Query.Internal.NavigationExpandingExpressionVisitor.VisitMethodCall(MethodCallExpression methodCallExpression)
   at Microsoft.EntityFrameworkCore.Query.Internal.NavigationExpandingExpressionVisitor.VisitMethodCall(MethodCallExpression methodCallExpression)
   at Microsoft.EntityFrameworkCore.Query.Internal.NavigationExpandingExpressionVisitor.VisitMethodCall(MethodCallExpression methodCallExpression)
   at Microsoft.EntityFrameworkCore.Query.Internal.NavigationExpandingExpressionVisitor.VisitMethodCall(MethodCallExpression methodCallExpression)
   at Microsoft.EntityFrameworkCore.Query.Internal.NavigationExpandingExpressionVisitor.Expand(Expression query)
   at Microsoft.EntityFrameworkCore.Query.QueryTranslationPreprocessor.Process(Expression query)
   at Microsoft.EntityFrameworkCore.Query.QueryCompilationContext.CreateQueryExecutorExpression[TResult](Expression query)
   at Microsoft.EntityFrameworkCore.Query.QueryCompilationContext.CreateQueryExecutor[TResult](Expression query)
   at Microsoft.EntityFrameworkCore.Storage.Database.CompileQuery[TResult](Expression query, Boolean async)
   at Microsoft.EntityFrameworkCore.Query.Internal.QueryCompiler.CompileQueryCore[TResult](IDatabase database, Expression query, IModel model, Boolean async)
   at Microsoft.EntityFrameworkCore.Query.Internal.QueryCompiler.<>c__DisplayClass11_0`1.<ExecuteCore>b__0()
   at Microsoft.EntityFrameworkCore.Query.Internal.CompiledQueryCache.GetOrAddQuery[TResult](Object cacheKey, Func`1 compiler)
   at Microsoft.EntityFrameworkCore.Query.Internal.QueryCompiler.ExecuteCore[TResult](Expression query, Boolean async, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.QueryCompiler.ExecuteAsync[TResult](Expression query, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.EntityQueryProvider.ExecuteAsync[TResult](Expression expression, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ExecuteAsync[TSource,TResult](MethodInfo operatorMethodInfo, IQueryable`1 source, Expression expression, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ExecuteAsync[TSource,TResult](MethodInfo operatorMethodInfo, IQueryable`1 source, LambdaExpression expression, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.FirstOrDefaultAsync[TSource](IQueryable`1 source, Expression`1 predicate, CancellationToken cancellationToken)
   at nCarry.Web.Controllers.SalesInvoiceController.CreateFromOrder(Nullable`1 orderId) in /Users/<USER>/Source/nCarry/nCarry/nCarry.Web/Controllers/SalesInvoiceController.cs:line 67
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.TaskOfIActionResultExecutor.Execute(ActionContext actionContext, IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeActionMethodAsync>g__Awaited|12_0(ControllerActionInvoker invoker, ValueTask`1 actionResultValueTask)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeInnerFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextResourceFilter>g__Awaited|25_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.Rethrow(ResourceExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.InvokeFilterPipelineAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Session.SessionMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Session.SessionMiddleware.Invoke(HttpContext context)
   at Serilog.AspNetCore.RequestLoggingMiddleware.Invoke(HttpContext httpContext)
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
2025-07-29 21:34:32.831 +03:00 [INF] HTTP GET /SalesOrder/Details/29 responded 200 in 948.1280 ms
2025-07-29 21:42:00.829 +03:00 [INF] Starting database seeding.
2025-07-29 21:42:04.310 +03:00 [INF] Admin user already exists. Seeding is not required.
2025-07-29 21:42:13.004 +03:00 [WRN] Failed to determine the https port for redirect.
2025-07-29 21:42:13.846 +03:00 [INF] HTTP GET /SalesOrder/Details/29 responded 200 in 841.5444 ms
2025-07-29 21:42:15.253 +03:00 [ERR] The view 'Create' was not found. Searched locations: ["/Views/SalesInvoice/Create.cshtml","/Views/Shared/Create.cshtml"]
2025-07-29 21:42:15.256 +03:00 [ERR] HTTP GET /SalesInvoice/CreateFromOrder responded 500 in 277.9871 ms
System.InvalidOperationException: The view 'Create' was not found. The following locations were searched:
/Views/SalesInvoice/Create.cshtml
/Views/Shared/Create.cshtml
   at Microsoft.AspNetCore.Mvc.ViewEngines.ViewEngineResult.EnsureSuccessful(IEnumerable`1 originalLocations)
   at Microsoft.AspNetCore.Mvc.ViewFeatures.ViewResultExecutor.ExecuteAsync(ActionContext context, ViewResult result)
   at Microsoft.AspNetCore.Mvc.ViewResult.ExecuteResultAsync(ActionContext context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextResultFilterAsync>g__Awaited|30_0[TFilter,TFilterAsync](ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.Rethrow(ResultExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.ResultNext[TFilter,TFilterAsync](State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.InvokeResultFilters()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextResourceFilter>g__Awaited|25_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.Rethrow(ResourceExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeFilterPipelineAsync>g__Awaited|20_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Session.SessionMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Session.SessionMiddleware.Invoke(HttpContext context)
   at Serilog.AspNetCore.RequestLoggingMiddleware.Invoke(HttpContext httpContext)
2025-07-29 21:42:15.262 +03:00 [ERR] An unhandled exception has occurred while executing the request.
System.InvalidOperationException: The view 'Create' was not found. The following locations were searched:
/Views/SalesInvoice/Create.cshtml
/Views/Shared/Create.cshtml
   at Microsoft.AspNetCore.Mvc.ViewEngines.ViewEngineResult.EnsureSuccessful(IEnumerable`1 originalLocations)
   at Microsoft.AspNetCore.Mvc.ViewFeatures.ViewResultExecutor.ExecuteAsync(ActionContext context, ViewResult result)
   at Microsoft.AspNetCore.Mvc.ViewResult.ExecuteResultAsync(ActionContext context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextResultFilterAsync>g__Awaited|30_0[TFilter,TFilterAsync](ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.Rethrow(ResultExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.ResultNext[TFilter,TFilterAsync](State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.InvokeResultFilters()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextResourceFilter>g__Awaited|25_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.Rethrow(ResourceExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeFilterPipelineAsync>g__Awaited|20_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Session.SessionMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Session.SessionMiddleware.Invoke(HttpContext context)
   at Serilog.AspNetCore.RequestLoggingMiddleware.Invoke(HttpContext httpContext)
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
2025-07-29 21:43:24.452 +03:00 [INF] HTTP GET /SalesOrder/Details/29 responded 200 in 128.0396 ms
2025-07-29 21:43:54.723 +03:00 [ERR] The view 'Create' was not found. Searched locations: ["/Views/SalesInvoice/Create.cshtml","/Views/Shared/Create.cshtml"]
2025-07-29 21:43:54.724 +03:00 [ERR] HTTP GET /SalesInvoice/CreateFromOrder responded 500 in 146.9475 ms
System.InvalidOperationException: The view 'Create' was not found. The following locations were searched:
/Views/SalesInvoice/Create.cshtml
/Views/Shared/Create.cshtml
   at Microsoft.AspNetCore.Mvc.ViewEngines.ViewEngineResult.EnsureSuccessful(IEnumerable`1 originalLocations)
   at Microsoft.AspNetCore.Mvc.ViewFeatures.ViewResultExecutor.ExecuteAsync(ActionContext context, ViewResult result)
   at Microsoft.AspNetCore.Mvc.ViewResult.ExecuteResultAsync(ActionContext context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextResultFilterAsync>g__Awaited|30_0[TFilter,TFilterAsync](ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.Rethrow(ResultExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.ResultNext[TFilter,TFilterAsync](State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.InvokeResultFilters()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextResourceFilter>g__Awaited|25_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.Rethrow(ResourceExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeFilterPipelineAsync>g__Awaited|20_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Session.SessionMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Session.SessionMiddleware.Invoke(HttpContext context)
   at Serilog.AspNetCore.RequestLoggingMiddleware.Invoke(HttpContext httpContext)
2025-07-29 21:43:54.725 +03:00 [ERR] An unhandled exception has occurred while executing the request.
System.InvalidOperationException: The view 'Create' was not found. The following locations were searched:
/Views/SalesInvoice/Create.cshtml
/Views/Shared/Create.cshtml
   at Microsoft.AspNetCore.Mvc.ViewEngines.ViewEngineResult.EnsureSuccessful(IEnumerable`1 originalLocations)
   at Microsoft.AspNetCore.Mvc.ViewFeatures.ViewResultExecutor.ExecuteAsync(ActionContext context, ViewResult result)
   at Microsoft.AspNetCore.Mvc.ViewResult.ExecuteResultAsync(ActionContext context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextResultFilterAsync>g__Awaited|30_0[TFilter,TFilterAsync](ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.Rethrow(ResultExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.ResultNext[TFilter,TFilterAsync](State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.InvokeResultFilters()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextResourceFilter>g__Awaited|25_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.Rethrow(ResourceExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeFilterPipelineAsync>g__Awaited|20_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Session.SessionMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Session.SessionMiddleware.Invoke(HttpContext context)
   at Serilog.AspNetCore.RequestLoggingMiddleware.Invoke(HttpContext httpContext)
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
2025-07-29 21:55:43.455 +03:00 [INF] Starting database seeding.
2025-07-29 21:55:46.169 +03:00 [INF] Admin user already exists. Seeding is not required.
2025-07-29 22:00:09.479 +03:00 [INF] Starting database seeding.
2025-07-29 22:00:11.687 +03:00 [INF] Admin user already exists. Seeding is not required.
2025-07-29 22:00:41.576 +03:00 [INF] Starting database seeding.
2025-07-29 22:00:44.804 +03:00 [INF] Admin user already exists. Seeding is not required.
2025-07-29 22:01:46.331 +03:00 [WRN] Failed to determine the https port for redirect.
2025-07-29 22:01:46.380 +03:00 [INF] HTTP GET / responded 302 in 51.1067 ms
2025-07-29 22:01:48.583 +03:00 [INF] HTTP GET /Dashboard responded 200 in 2194.7196 ms
2025-07-29 22:01:48.598 +03:00 [INF] HTTP GET /nCarry.Web.styles.css responded 304 in 7.8933 ms
2025-07-29 22:01:48.815 +03:00 [INF] HTTP GET /Dashboard/GetChartData responded 200 in 180.6011 ms
2025-07-29 22:01:55.960 +03:00 [INF] HTTP GET /SalesOrder/Details/29 responded 200 in 357.1405 ms
2025-07-29 22:02:03.601 +03:00 [ERR] Failed executing DbCommand (88ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [s].[InvoiceID], [s].[BillingAddressID], [s].[ContactID], [s].[CreatedByUserID], [s].[CreatedDate], [s].[Currency], [s].[CustomerID], [s].[CustomerPO], [s].[DeletedByUserID], [s].[DeletedDate], [s].[DeliveryNoteNumber], [s].[DiscountAmount], [s].[DueDate], [s].[ExchangeRate], [s].[FooterText], [s].[InternalNotes], [s].[InvoiceDate], [s].[InvoiceNumber], [s].[InvoiceStatus], [s].[InvoiceType], [s].[IsDeleted], [s].[LastPaymentDate], [s].[Notes], [s].[OrderID], [s].[PaidAmount], [s].[PaymentStatus], [s].[PaymentTerms], [s].[PostedByUserID], [s].[PostedDate], [s].[ShippingAddressID], [s].[ShippingAmount], [s].[SubTotal], [s].[TaxAmount], [s].[TotalAmount], [s].[UpdatedByUserID], [s].[UpdatedDate]
FROM [SalesInvoice] AS [s]
ORDER BY [s].[InvoiceID] DESC
2025-07-29 22:02:03.613 +03:00 [ERR] An exception occurred while iterating over the results of a query for context type 'nCarry.Web.Data.ApplicationDbContext'.
Microsoft.Data.SqlClient.SqlException (0x80131904): Invalid column name 'ContactID'.
   at Microsoft.Data.SqlClient.SqlCommand.<>c.<ExecuteDbDataReaderAsync>b__211_0(Task`1 result)
   at System.Threading.Tasks.ContinuationResultTaskFromResultTask`2.InnerInvoke()
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.InitializeReaderAsync(AsyncEnumerator enumerator, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.SqlServer.Storage.Internal.SqlServerExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
ClientConnectionId:b43b51a6-d95e-42c3-8d17-0aa06bf4c2c2
Error Number:207,State:1,Class:16
Microsoft.Data.SqlClient.SqlException (0x80131904): Invalid column name 'ContactID'.
   at Microsoft.Data.SqlClient.SqlCommand.<>c.<ExecuteDbDataReaderAsync>b__211_0(Task`1 result)
   at System.Threading.Tasks.ContinuationResultTaskFromResultTask`2.InnerInvoke()
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.InitializeReaderAsync(AsyncEnumerator enumerator, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.SqlServer.Storage.Internal.SqlServerExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
ClientConnectionId:b43b51a6-d95e-42c3-8d17-0aa06bf4c2c2
Error Number:207,State:1,Class:16
2025-07-29 22:02:03.615 +03:00 [ERR] HTTP GET /SalesInvoice/CreateFromOrder responded 500 in 265.6529 ms
Microsoft.Data.SqlClient.SqlException (0x80131904): Invalid column name 'ContactID'.
   at Microsoft.Data.SqlClient.SqlCommand.<>c.<ExecuteDbDataReaderAsync>b__211_0(Task`1 result)
   at System.Threading.Tasks.ContinuationResultTaskFromResultTask`2.InnerInvoke()
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.InitializeReaderAsync(AsyncEnumerator enumerator, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.SqlServer.Storage.Internal.SqlServerExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
   at Microsoft.EntityFrameworkCore.Query.ShapedQueryCompilingExpressionVisitor.SingleOrDefaultAsync[TSource](IAsyncEnumerable`1 asyncEnumerable, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.ShapedQueryCompilingExpressionVisitor.SingleOrDefaultAsync[TSource](IAsyncEnumerable`1 asyncEnumerable, CancellationToken cancellationToken)
   at nCarry.Web.Controllers.SalesInvoiceController.GenerateInvoiceNumber() in /Users/<USER>/Source/nCarry/nCarry/nCarry.Web/Controllers/SalesInvoiceController.cs:line 294
   at nCarry.Web.Controllers.SalesInvoiceController.CreateFromOrder(Nullable`1 orderId) in /Users/<USER>/Source/nCarry/nCarry/nCarry.Web/Controllers/SalesInvoiceController.cs:line 80
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.TaskOfIActionResultExecutor.Execute(ActionContext actionContext, IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeActionMethodAsync>g__Awaited|12_0(ControllerActionInvoker invoker, ValueTask`1 actionResultValueTask)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeInnerFilterAsync>g__Awaited|13_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextResourceFilter>g__Awaited|25_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.Rethrow(ResourceExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeFilterPipelineAsync>g__Awaited|20_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Session.SessionMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Session.SessionMiddleware.Invoke(HttpContext context)
   at Serilog.AspNetCore.RequestLoggingMiddleware.Invoke(HttpContext httpContext)
ClientConnectionId:b43b51a6-d95e-42c3-8d17-0aa06bf4c2c2
Error Number:207,State:1,Class:16
2025-07-29 22:02:03.620 +03:00 [ERR] An unhandled exception has occurred while executing the request.
Microsoft.Data.SqlClient.SqlException (0x80131904): Invalid column name 'ContactID'.
   at Microsoft.Data.SqlClient.SqlCommand.<>c.<ExecuteDbDataReaderAsync>b__211_0(Task`1 result)
   at System.Threading.Tasks.ContinuationResultTaskFromResultTask`2.InnerInvoke()
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.InitializeReaderAsync(AsyncEnumerator enumerator, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.SqlServer.Storage.Internal.SqlServerExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
   at Microsoft.EntityFrameworkCore.Query.ShapedQueryCompilingExpressionVisitor.SingleOrDefaultAsync[TSource](IAsyncEnumerable`1 asyncEnumerable, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.ShapedQueryCompilingExpressionVisitor.SingleOrDefaultAsync[TSource](IAsyncEnumerable`1 asyncEnumerable, CancellationToken cancellationToken)
   at nCarry.Web.Controllers.SalesInvoiceController.GenerateInvoiceNumber() in /Users/<USER>/Source/nCarry/nCarry/nCarry.Web/Controllers/SalesInvoiceController.cs:line 294
   at nCarry.Web.Controllers.SalesInvoiceController.CreateFromOrder(Nullable`1 orderId) in /Users/<USER>/Source/nCarry/nCarry/nCarry.Web/Controllers/SalesInvoiceController.cs:line 80
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.TaskOfIActionResultExecutor.Execute(ActionContext actionContext, IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeActionMethodAsync>g__Awaited|12_0(ControllerActionInvoker invoker, ValueTask`1 actionResultValueTask)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeInnerFilterAsync>g__Awaited|13_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextResourceFilter>g__Awaited|25_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.Rethrow(ResourceExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeFilterPipelineAsync>g__Awaited|20_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Session.SessionMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Session.SessionMiddleware.Invoke(HttpContext context)
   at Serilog.AspNetCore.RequestLoggingMiddleware.Invoke(HttpContext httpContext)
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
ClientConnectionId:b43b51a6-d95e-42c3-8d17-0aa06bf4c2c2
Error Number:207,State:1,Class:16
2025-07-29 22:10:43.956 +03:00 [ERR] Failed executing DbCommand (196ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [s].[InvoiceID], [s].[BillingAddressID], [s].[ContactID], [s].[CreatedByUserID], [s].[CreatedDate], [s].[Currency], [s].[CustomerID], [s].[CustomerPO], [s].[DeletedByUserID], [s].[DeletedDate], [s].[DeliveryNoteNumber], [s].[DiscountAmount], [s].[DueDate], [s].[ExchangeRate], [s].[FooterText], [s].[InternalNotes], [s].[InvoiceDate], [s].[InvoiceNumber], [s].[InvoiceStatus], [s].[InvoiceType], [s].[IsDeleted], [s].[LastPaymentDate], [s].[Notes], [s].[OrderID], [s].[PaidAmount], [s].[PaymentStatus], [s].[PaymentTerms], [s].[PostedByUserID], [s].[PostedDate], [s].[ShippingAddressID], [s].[ShippingAmount], [s].[SubTotal], [s].[TaxAmount], [s].[TotalAmount], [s].[UpdatedByUserID], [s].[UpdatedDate]
FROM [SalesInvoice] AS [s]
ORDER BY [s].[InvoiceID] DESC
2025-07-29 22:10:43.957 +03:00 [ERR] An exception occurred while iterating over the results of a query for context type 'nCarry.Web.Data.ApplicationDbContext'.
Microsoft.Data.SqlClient.SqlException (0x80131904): Invalid column name 'ContactID'.
   at Microsoft.Data.SqlClient.SqlCommand.<>c.<ExecuteDbDataReaderAsync>b__211_0(Task`1 result)
   at System.Threading.Tasks.ContinuationResultTaskFromResultTask`2.InnerInvoke()
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.InitializeReaderAsync(AsyncEnumerator enumerator, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.SqlServer.Storage.Internal.SqlServerExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
ClientConnectionId:8ca2e480-36e9-4eec-87ff-46a58db222e9
Error Number:207,State:1,Class:16
Microsoft.Data.SqlClient.SqlException (0x80131904): Invalid column name 'ContactID'.
   at Microsoft.Data.SqlClient.SqlCommand.<>c.<ExecuteDbDataReaderAsync>b__211_0(Task`1 result)
   at System.Threading.Tasks.ContinuationResultTaskFromResultTask`2.InnerInvoke()
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.InitializeReaderAsync(AsyncEnumerator enumerator, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.SqlServer.Storage.Internal.SqlServerExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
ClientConnectionId:8ca2e480-36e9-4eec-87ff-46a58db222e9
Error Number:207,State:1,Class:16
2025-07-29 22:10:43.958 +03:00 [ERR] HTTP GET /SalesInvoice/CreateFromOrder responded 500 in 968.0699 ms
Microsoft.Data.SqlClient.SqlException (0x80131904): Invalid column name 'ContactID'.
   at Microsoft.Data.SqlClient.SqlCommand.<>c.<ExecuteDbDataReaderAsync>b__211_0(Task`1 result)
   at System.Threading.Tasks.ContinuationResultTaskFromResultTask`2.InnerInvoke()
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.InitializeReaderAsync(AsyncEnumerator enumerator, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.SqlServer.Storage.Internal.SqlServerExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
   at Microsoft.EntityFrameworkCore.Query.ShapedQueryCompilingExpressionVisitor.SingleOrDefaultAsync[TSource](IAsyncEnumerable`1 asyncEnumerable, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.ShapedQueryCompilingExpressionVisitor.SingleOrDefaultAsync[TSource](IAsyncEnumerable`1 asyncEnumerable, CancellationToken cancellationToken)
   at nCarry.Web.Controllers.SalesInvoiceController.GenerateInvoiceNumber() in /Users/<USER>/Source/nCarry/nCarry/nCarry.Web/Controllers/SalesInvoiceController.cs:line 294
   at nCarry.Web.Controllers.SalesInvoiceController.CreateFromOrder(Nullable`1 orderId) in /Users/<USER>/Source/nCarry/nCarry/nCarry.Web/Controllers/SalesInvoiceController.cs:line 80
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.TaskOfIActionResultExecutor.Execute(ActionContext actionContext, IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeActionMethodAsync>g__Awaited|12_0(ControllerActionInvoker invoker, ValueTask`1 actionResultValueTask)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeInnerFilterAsync>g__Awaited|13_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextResourceFilter>g__Awaited|25_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.Rethrow(ResourceExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeFilterPipelineAsync>g__Awaited|20_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Session.SessionMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Session.SessionMiddleware.Invoke(HttpContext context)
   at Serilog.AspNetCore.RequestLoggingMiddleware.Invoke(HttpContext httpContext)
ClientConnectionId:8ca2e480-36e9-4eec-87ff-46a58db222e9
Error Number:207,State:1,Class:16
2025-07-29 22:10:43.961 +03:00 [ERR] An unhandled exception has occurred while executing the request.
Microsoft.Data.SqlClient.SqlException (0x80131904): Invalid column name 'ContactID'.
   at Microsoft.Data.SqlClient.SqlCommand.<>c.<ExecuteDbDataReaderAsync>b__211_0(Task`1 result)
   at System.Threading.Tasks.ContinuationResultTaskFromResultTask`2.InnerInvoke()
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.InitializeReaderAsync(AsyncEnumerator enumerator, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.SqlServer.Storage.Internal.SqlServerExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
   at Microsoft.EntityFrameworkCore.Query.ShapedQueryCompilingExpressionVisitor.SingleOrDefaultAsync[TSource](IAsyncEnumerable`1 asyncEnumerable, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.ShapedQueryCompilingExpressionVisitor.SingleOrDefaultAsync[TSource](IAsyncEnumerable`1 asyncEnumerable, CancellationToken cancellationToken)
   at nCarry.Web.Controllers.SalesInvoiceController.GenerateInvoiceNumber() in /Users/<USER>/Source/nCarry/nCarry/nCarry.Web/Controllers/SalesInvoiceController.cs:line 294
   at nCarry.Web.Controllers.SalesInvoiceController.CreateFromOrder(Nullable`1 orderId) in /Users/<USER>/Source/nCarry/nCarry/nCarry.Web/Controllers/SalesInvoiceController.cs:line 80
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.TaskOfIActionResultExecutor.Execute(ActionContext actionContext, IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeActionMethodAsync>g__Awaited|12_0(ControllerActionInvoker invoker, ValueTask`1 actionResultValueTask)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeInnerFilterAsync>g__Awaited|13_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextResourceFilter>g__Awaited|25_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.Rethrow(ResourceExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeFilterPipelineAsync>g__Awaited|20_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Session.SessionMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Session.SessionMiddleware.Invoke(HttpContext context)
   at Serilog.AspNetCore.RequestLoggingMiddleware.Invoke(HttpContext httpContext)
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
ClientConnectionId:8ca2e480-36e9-4eec-87ff-46a58db222e9
Error Number:207,State:1,Class:16
2025-07-29 22:11:06.861 +03:00 [FTL] Application terminated unexpectedly
Microsoft.Extensions.Hosting.HostAbortedException: The host was aborted.
   at Microsoft.Extensions.Hosting.HostFactoryResolver.HostingListener.ThrowHostAborted()
   at Microsoft.Extensions.Hosting.HostFactoryResolver.HostingListener.OnNext(KeyValuePair`2 value)
   at System.Diagnostics.DiagnosticListener.Write(String name, Object value)
   at System.Diagnostics.DiagnosticSource.Write[T](String name, T value)
   at Microsoft.Extensions.Hosting.HostBuilder.ResolveHost(IServiceProvider serviceProvider, DiagnosticListener diagnosticListener)
   at Microsoft.Extensions.Hosting.HostApplicationBuilder.Build()
   at Microsoft.AspNetCore.Builder.WebApplicationBuilder.Build()
   at Program.<Main>$(String[] args) in /Users/<USER>/Source/nCarry/nCarry/nCarry.Web/Program.cs:line 56
2025-07-29 23:13:37.979 +03:00 [INF] Starting database seeding.
2025-07-29 23:13:41.380 +03:00 [INF] Admin user already exists. Seeding is not required.
2025-07-29 23:14:09.701 +03:00 [INF] Starting database seeding.
2025-07-29 23:14:13.259 +03:00 [INF] Admin user already exists. Seeding is not required.
2025-07-29 23:14:36.669 +03:00 [WRN] Failed to determine the https port for redirect.
2025-07-29 23:14:36.745 +03:00 [INF] HTTP GET / responded 302 in 80.3172 ms
2025-07-29 23:14:37.078 +03:00 [INF] HTTP GET /Account/Login responded 200 in 316.1187 ms
2025-07-29 23:14:47.789 +03:00 [INF] HTTP HEAD / responded 302 in 2.7927 ms
2025-07-29 23:14:53.685 +03:00 [INF] HTTP GET / responded 302 in 0.8565 ms
2025-07-29 23:14:53.702 +03:00 [INF] HTTP GET /Account/Login responded 200 in 15.0466 ms
2025-07-29 23:15:14.133 +03:00 [INF] HTTP POST /Account/Login responded 302 in 850.8694 ms
2025-07-29 23:15:16.932 +03:00 [INF] HTTP GET /Dashboard responded 200 in 2794.8186 ms
2025-07-29 23:15:16.952 +03:00 [INF] HTTP GET /nCarry.Web.styles.css responded 200 in 13.7038 ms
2025-07-29 23:15:17.200 +03:00 [INF] HTTP GET /Dashboard/GetChartData responded 200 in 205.7913 ms
2025-07-29 23:15:37.428 +03:00 [INF] HTTP GET /ProductCategory responded 200 in 247.7548 ms
2025-07-29 23:15:47.442 +03:00 [INF] HTTP GET /ProductCategory/Create responded 200 in 38.9352 ms
2025-07-29 23:16:16.758 +03:00 [INF] HTTP POST /ProductCategory/Create responded 302 in 360.4968 ms
2025-07-29 23:16:16.865 +03:00 [INF] HTTP GET /ProductCategory responded 200 in 105.0030 ms
2025-07-29 23:16:35.151 +03:00 [INF] HTTP GET /UnitOfMeasure responded 200 in 210.7409 ms
2025-07-29 23:16:54.829 +03:00 [INF] HTTP GET /Product responded 200 in 324.6050 ms
2025-07-29 23:17:04.874 +03:00 [INF] HTTP GET /Product/Create responded 200 in 704.7047 ms
2025-07-29 23:19:47.565 +03:00 [INF] HTTP POST /Product/Create responded 302 in 328.6743 ms
2025-07-29 23:19:47.701 +03:00 [INF] HTTP GET /Product responded 200 in 133.3104 ms
2025-07-29 23:20:15.189 +03:00 [INF] HTTP GET /Supplier responded 200 in 263.2265 ms
2025-07-29 23:20:43.699 +03:00 [INF] HTTP GET /PurchaseOrder responded 200 in 356.5215 ms
2025-07-29 23:20:52.769 +03:00 [INF] HTTP GET /PurchaseOrder/Create responded 200 in 965.3417 ms
2025-07-29 23:23:20.494 +03:00 [ERR] Failed executing DbCommand (92ms) [Parameters=[@p0='?' (Precision = 18) (Scale = 4) (DbType = Decimal), @p1='?' (Size = 3), @p2='?' (Size = 4000), @p3='?' (Precision = 18) (Scale = 2) (DbType = Decimal), @p4='?' (Precision = 5) (Scale = 2) (DbType = Decimal), @p5='?' (Precision = 18) (Scale = 4) (DbType = Decimal), @p6='?' (Size = 20), @p7='?' (DbType = DateTime2), @p8='?' (DbType = Int32), @p9='?' (Precision = 18) (Scale = 2) (DbType = Decimal), @p10='?' (Size = 500), @p11='?' (Precision = 18) (Scale = 4) (DbType = Decimal), @p12='?' (DbType = Int32), @p13='?' (Size = 255), @p14='?' (DbType = DateTime2), @p15='?' (DbType = Int32), @p16='?' (Precision = 18) (Scale = 4) (DbType = Decimal), @p17='?' (DbType = DateTime2), @p18='?' (DbType = Int32), @p19='?' (Size = 100), @p20='?' (Precision = 18) (Scale = 2) (DbType = Decimal), @p21='?' (Precision = 5) (Scale = 2) (DbType = Decimal), @p22='?' (DbType = Int32), @p23='?' (Precision = 18) (Scale = 2) (DbType = Decimal)], CommandType='"Text"', CommandTimeout='30']
SET IMPLICIT_TRANSACTIONS OFF;
SET NOCOUNT ON;
INSERT INTO [PurchaseOrderItem] ([CancelledQuantity], [Currency], [Description], [DiscountAmount], [DiscountPercent], [InvoicedQuantity], [ItemStatus], [LastReceiptDate], [LineNumber], [LineTotal], [Notes], [OrderedQuantity], [ProductID], [ProductName], [PromisedDate], [PurchaseOrderID], [ReceivedQuantity], [RequiredDate], [SortOrder], [SupplierProductCode], [TaxAmount], [TaxRate], [UOMID], [UnitPrice])
OUTPUT INSERTED.[PurchaseOrderItemID]
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10, @p11, @p12, @p13, @p14, @p15, @p16, @p17, @p18, @p19, @p20, @p21, @p22, @p23);
2025-07-29 23:23:20.614 +03:00 [ERR] An exception occurred in the database while saving changes for context type 'nCarry.Web.Data.ApplicationDbContext'.
Microsoft.EntityFrameworkCore.DbUpdateException: An error occurred while saving the entity changes. See the inner exception for details.
 ---> Microsoft.Data.SqlClient.SqlException (0x80131904): Invalid column name 'Currency'.
   at System.Threading.Tasks.ContinuationResultTaskFromResultTask`2.InnerInvoke()
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
ClientConnectionId:02ef028d-8c8f-4dbc-ba81-9331092a455a
Error Number:207,State:1,Class:16
   --- End of inner exception stack trace ---
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.SqlServer.Update.Internal.SqlServerModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalDatabase.SaveChangesAsync(IList`1 entries, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(IList`1 entriesToSave, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(StateManager stateManager, Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.SqlServer.Storage.Internal.SqlServerExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.DbContext.SaveChangesAsync(Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
Microsoft.EntityFrameworkCore.DbUpdateException: An error occurred while saving the entity changes. See the inner exception for details.
 ---> Microsoft.Data.SqlClient.SqlException (0x80131904): Invalid column name 'Currency'.
   at System.Threading.Tasks.ContinuationResultTaskFromResultTask`2.InnerInvoke()
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
ClientConnectionId:02ef028d-8c8f-4dbc-ba81-9331092a455a
Error Number:207,State:1,Class:16
   --- End of inner exception stack trace ---
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.SqlServer.Update.Internal.SqlServerModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalDatabase.SaveChangesAsync(IList`1 entries, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(IList`1 entriesToSave, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(StateManager stateManager, Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.SqlServer.Storage.Internal.SqlServerExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.DbContext.SaveChangesAsync(Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
2025-07-29 23:23:21.482 +03:00 [INF] HTTP POST /PurchaseOrder/Create responded 200 in 1944.0420 ms
2025-07-29 23:23:21.491 +03:00 [INF] HTTP GET /nCarry.Web.styles.css responded 304 in 2.9356 ms
2025-07-29 23:25:35.505 +03:00 [INF] HTTP GET /PurchaseOrder responded 200 in 222.5296 ms
2025-07-29 23:25:45.811 +03:00 [INF] HTTP GET /PurchaseOrder/Create responded 200 in 841.4206 ms
2025-07-29 23:27:11.269 +03:00 [INF] HTTP POST /PurchaseOrder/Create responded 302 in 1655.3371 ms
2025-07-29 23:27:11.516 +03:00 [INF] HTTP GET /PurchaseOrder/Details/7 responded 200 in 245.1982 ms
2025-07-29 23:27:43.426 +03:00 [INF] HTTP GET /GoodsReceipt responded 200 in 265.6887 ms
2025-07-29 23:27:52.904 +03:00 [INF] HTTP GET /GoodsReceipt/Create responded 200 in 417.5397 ms
2025-07-29 23:28:04.011 +03:00 [INF] HTTP GET /GoodsReceipt/Create responded 200 in 881.1712 ms
2025-07-29 23:30:01.117 +03:00 [INF] HTTP POST /Account/Logout responded 302 in 12.5999 ms
2025-07-29 23:30:01.126 +03:00 [INF] HTTP GET /Account/Login responded 200 in 5.4583 ms
2025-07-29 23:30:20.102 +03:00 [INF] HTTP POST /Account/Login responded 302 in 330.3351 ms
2025-07-29 23:30:22.173 +03:00 [INF] HTTP GET /Dashboard responded 200 in 2068.8953 ms
2025-07-29 23:30:22.182 +03:00 [INF] HTTP GET /nCarry.Web.styles.css responded 304 in 2.5080 ms
2025-07-29 23:30:22.309 +03:00 [INF] HTTP GET /Dashboard/GetChartData responded 200 in 103.0804 ms
2025-07-29 23:30:28.491 +03:00 [INF] HTTP GET /GoodsReceipt responded 200 in 382.9710 ms
2025-07-29 23:30:35.171 +03:00 [INF] HTTP GET /GoodsReceipt/Create responded 200 in 586.1943 ms
2025-07-29 23:31:11.207 +03:00 [INF] HTTP GET /Inventory responded 200 in 561.3008 ms
2025-07-29 23:31:30.976 +03:00 [INF] HTTP GET /Customer responded 200 in 148.8661 ms
2025-07-29 23:31:49.482 +03:00 [INF] HTTP GET /SalesOrder/Create responded 200 in 415.1107 ms
2025-07-29 23:32:16.315 +03:00 [INF] HTTP POST /SalesOrder/Create responded 302 in 517.4954 ms
2025-07-29 23:32:16.458 +03:00 [INF] HTTP GET /SalesOrder responded 200 in 140.2893 ms
2025-07-29 23:32:26.051 +03:00 [INF] HTTP GET /SalesOrder/Details/30 responded 200 in 232.1700 ms
2025-07-29 23:32:45.997 +03:00 [INF] HTTP GET /SalesInvoice/CreateFromOrder responded 200 in 388.8710 ms
2025-07-29 23:32:55.763 +03:00 [INF] HTTP POST /SalesInvoice/Create responded 302 in 1666.0770 ms
2025-07-29 23:32:56.288 +03:00 [ERR] Failed executing DbCommand (481ms) [Parameters=[@__id_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [s2].[InvoiceID], [s2].[BalanceAmount], [s2].[BillingAddressID], [s2].[CreatedByUserID], [s2].[CreatedDate], [s2].[Currency], [s2].[CustomerID], [s2].[CustomerPO], [s2].[DeletedByUserID], [s2].[DeletedDate], [s2].[DeliveryNoteNumber], [s2].[DiscountAmount], [s2].[DueDate], [s2].[ExchangeRate], [s2].[FooterText], [s2].[InternalNotes], [s2].[InvoiceDate], [s2].[InvoiceNumber], [s2].[InvoiceStatus], [s2].[InvoiceType], [s2].[IsDeleted], [s2].[LastPaymentDate], [s2].[Notes], [s2].[OrderID], [s2].[PaidAmount], [s2].[PaymentStatus], [s2].[PaymentTerms], [s2].[PostedByUserID], [s2].[PostedDate], [s2].[ShippingAddressID], [s2].[ShippingAmount], [s2].[SubTotal], [s2].[TaxAmount], [s2].[TotalAmount], [s2].[UpdatedByUserID], [s2].[UpdatedDate], [s2].[CustomerID0], [s2].[BlockedByUserID], [s2].[BlockedDate], [s2].[BlockedReason], [s2].[ContactEmail], [s2].[ContactLandline], [s2].[ContactMobile], [s2].[ContactPhone], [s2].[CreatedByUserID0], [s2].[CreatedDate0], [s2].[CreditLimit], [s2].[CreditReviewDate], [s2].[CreditStatus], [s2].[CurrencyCode], [s2].[CustomerAddress], [s2].[CustomerAddress2], [s2].[CustomerCity], [s2].[CustomerCode], [s2].[CustomerCountry], [s2].[CustomerGroup], [s2].[CustomerName], [s2].[CustomerPostCode], [s2].[CustomerSince], [s2].[CustomerState], [s2].[CustomerStatus], [s2].[CustomerType], [s2].[DeletedByUserID0], [s2].[DeletedDate0], [s2].[DeliveryInstructions], [s2].[DeliveryTimePreference], [s2].[DiscountPercent], [s2].[InternalNotes0], [s2].[IsActive], [s2].[IsDeleted0], [s2].[LastOrderDate], [s2].[LeadSource], [s2].[LoyaltyPoints], [s2].[Notes0], [s2].[OutstandingBalance], [s2].[PaymentMethodID], [s2].[PaymentTerm], [s2].[PreferredCarrierID], [s2].[PriceListID], [s2].[Rating], [s2].[ReferenceCode], [s2].[ResponsibleContact], [s2].[SalesRepID], [s2].[TaxExempt], [s2].[TaxNumber], [s2].[TerritoryID], [s2].[TotalOrderValue], [s2].[TradingName], [s2].[UpdatedByUserID0], [s2].[UpdatedDate0], [s2].[Website], [s2].[YTDSales], [s2].[OrderID0], [s2].[BalanceAmount0], [s2].[BillingAddressID0], [s2].[CarrierID], [s2].[CreatedByUserID1], [s2].[CreatedDate1], [s2].[Currency0], [s2].[CustomerID1], [s2].[CustomerPO0], [s2].[DeletedByUserID1], [s2].[DeletedDate1], [s2].[DeliveredDate], [s2].[DeliveryInstructions0], [s2].[DiscountAmount0], [s2].[ExchangeRate0], [s2].[FreightTerms], [s2].[InternalNotes1], [s2].[IsDeleted1], [s2].[Notes1], [s2].[OrderDate], [s2].[OrderNumber], [s2].[OrderStatus], [s2].[OrderType], [s2].[PaidAmount0], [s2].[PaymentDueDate], [s2].[PaymentTerms0], [s2].[PickingPriority], [s2].[PromisedDate], [s2].[QuoteID], [s2].[RequestedDate], [s2].[SalesRepID0], [s2].[ShippedDate], [s2].[ShippingAddressID0], [s2].[ShippingAmount0], [s2].[ShippingMethod], [s2].[SubTotal0], [s2].[TaxAmount0], [s2].[TotalAmount0], [s2].[TrackingNumber], [s2].[UpdatedByUserID1], [s2].[UpdatedDate1], [s2].[WarehouseID], [s3].[InvoiceItemID], [s3].[AccountCode], [s3].[CreatedByUserID], [s3].[CreatedDate], [s3].[Description], [s3].[DiscountAmount], [s3].[DiscountPercent], [s3].[InvoiceID], [s3].[LineNumber], [s3].[LineTotal], [s3].[Notes], [s3].[OrderItemID], [s3].[ProductCode], [s3].[ProductID], [s3].[ProductName], [s3].[Quantity], [s3].[SortOrder], [s3].[TaxAmount], [s3].[TaxRate], [s3].[UOMID], [s3].[UnitPrice], [s3].[UpdatedByUserID], [s3].[UpdatedDate], [s3].[VariantID], [s3].[ProductID0], [s3].[AllowBackorder], [s3].[AllowPreorder], [s3].[Brand], [s3].[CategoryID], [s3].[CommodityCode], [s3].[CountryOfOrigin], [s3].[CreatedByUserID0], [s3].[CreatedDate0], [s3].[Currency], [s3].[DeletedByUserID], [s3].[DeletedDate], [s3].[Description0], [s3].[DimensionUOMID], [s3].[HSCode], [s3].[HasVariants], [s3].[Height], [s3].[InventoryUOMID], [s3].[IsActive], [s3].[IsDeleted], [s3].[IsKit], [s3].[IsTaxExempt], [s3].[LeadTimeDays], [s3].[Length], [s3].[ListPrice], [s3].[Manufacturer], [s3].[ManufacturerPartNumber], [s3].[MaxStockLevel], [s3].[MinStockLevel], [s3].[Notes0], [s3].[ProductCode0], [s3].[ProductName0], [s3].[ProductType], [s3].[PurchaseUOMID], [s3].[ReorderPoint], [s3].[ReorderQuantity], [s3].[RequiresBatch], [s3].[RequiresSerial], [s3].[SalesUOMID], [s3].[SellPrice], [s3].[ShelfLifeDays], [s3].[ShortDescription], [s3].[StandardCost], [s3].[Status], [s3].[TaxCodeID], [s3].[TrackInventory], [s3].[UpdatedByUserID0], [s3].[UpdatedDate0], [s3].[Volume], [s3].[VolumeUOMID], [s3].[WarrantyDays], [s3].[Weight], [s3].[WeightUOMID], [s3].[Width], [s3].[UOMID0], [s3].[BaseUnit], [s3].[ConversionFactor], [s3].[CreatedDate1], [s3].[DecimalPlaces], [s3].[IsActive0], [s3].[Symbol], [s3].[UOMCode], [s3].[UOMName], [s3].[UOMType], [s3].[UpdatedDate1]
FROM (
    SELECT TOP(1) [s].[InvoiceID], [s].[BalanceAmount], [s].[BillingAddressID], [s].[CreatedByUserID], [s].[CreatedDate], [s].[Currency], [s].[CustomerID], [s].[CustomerPO], [s].[DeletedByUserID], [s].[DeletedDate], [s].[DeliveryNoteNumber], [s].[DiscountAmount], [s].[DueDate], [s].[ExchangeRate], [s].[FooterText], [s].[InternalNotes], [s].[InvoiceDate], [s].[InvoiceNumber], [s].[InvoiceStatus], [s].[InvoiceType], [s].[IsDeleted], [s].[LastPaymentDate], [s].[Notes], [s].[OrderID], [s].[PaidAmount], [s].[PaymentStatus], [s].[PaymentTerms], [s].[PostedByUserID], [s].[PostedDate], [s].[ShippingAddressID], [s].[ShippingAmount], [s].[SubTotal], [s].[TaxAmount], [s].[TotalAmount], [s].[UpdatedByUserID], [s].[UpdatedDate], [c].[CustomerID] AS [CustomerID0], [c].[BlockedByUserID], [c].[BlockedDate], [c].[BlockedReason], [c].[ContactEmail], [c].[ContactLandline], [c].[ContactMobile], [c].[ContactPhone], [c].[CreatedByUserID] AS [CreatedByUserID0], [c].[CreatedDate] AS [CreatedDate0], [c].[CreditLimit], [c].[CreditReviewDate], [c].[CreditStatus], [c].[CurrencyCode], [c].[CustomerAddress], [c].[CustomerAddress2], [c].[CustomerCity], [c].[CustomerCode], [c].[CustomerCountry], [c].[CustomerGroup], [c].[CustomerName], [c].[CustomerPostCode], [c].[CustomerSince], [c].[CustomerState], [c].[CustomerStatus], [c].[CustomerType], [c].[DeletedByUserID] AS [DeletedByUserID0], [c].[DeletedDate] AS [DeletedDate0], [c].[DeliveryInstructions], [c].[DeliveryTimePreference], [c].[DiscountPercent], [c].[InternalNotes] AS [InternalNotes0], [c].[IsActive], [c].[IsDeleted] AS [IsDeleted0], [c].[LastOrderDate], [c].[LeadSource], [c].[LoyaltyPoints], [c].[Notes] AS [Notes0], [c].[OutstandingBalance], [c].[PaymentMethodID], [c].[PaymentTerm], [c].[PreferredCarrierID], [c].[PriceListID], [c].[Rating], [c].[ReferenceCode], [c].[ResponsibleContact], [c].[SalesRepID], [c].[TaxExempt], [c].[TaxNumber], [c].[TerritoryID], [c].[TotalOrderValue], [c].[TradingName], [c].[UpdatedByUserID] AS [UpdatedByUserID0], [c].[UpdatedDate] AS [UpdatedDate0], [c].[Website], [c].[YTDSales], [s0].[OrderID] AS [OrderID0], [s0].[BalanceAmount] AS [BalanceAmount0], [s0].[BillingAddressID] AS [BillingAddressID0], [s0].[CarrierID], [s0].[CreatedByUserID] AS [CreatedByUserID1], [s0].[CreatedDate] AS [CreatedDate1], [s0].[Currency] AS [Currency0], [s0].[CustomerID] AS [CustomerID1], [s0].[CustomerPO] AS [CustomerPO0], [s0].[DeletedByUserID] AS [DeletedByUserID1], [s0].[DeletedDate] AS [DeletedDate1], [s0].[DeliveredDate], [s0].[DeliveryInstructions] AS [DeliveryInstructions0], [s0].[DiscountAmount] AS [DiscountAmount0], [s0].[ExchangeRate] AS [ExchangeRate0], [s0].[FreightTerms], [s0].[InternalNotes] AS [InternalNotes1], [s0].[IsDeleted] AS [IsDeleted1], [s0].[Notes] AS [Notes1], [s0].[OrderDate], [s0].[OrderNumber], [s0].[OrderStatus], [s0].[OrderType], [s0].[PaidAmount] AS [PaidAmount0], [s0].[PaymentDueDate], [s0].[PaymentTerms] AS [PaymentTerms0], [s0].[PickingPriority], [s0].[PromisedDate], [s0].[QuoteID], [s0].[RequestedDate], [s0].[SalesRepID] AS [SalesRepID0], [s0].[ShippedDate], [s0].[ShippingAddressID] AS [ShippingAddressID0], [s0].[ShippingAmount] AS [ShippingAmount0], [s0].[ShippingMethod], [s0].[SubTotal] AS [SubTotal0], [s0].[TaxAmount] AS [TaxAmount0], [s0].[TotalAmount] AS [TotalAmount0], [s0].[TrackingNumber], [s0].[UpdatedByUserID] AS [UpdatedByUserID1], [s0].[UpdatedDate] AS [UpdatedDate1], [s0].[WarehouseID]
    FROM [SalesInvoice] AS [s]
    INNER JOIN [Customer] AS [c] ON [s].[CustomerID] = [c].[CustomerID]
    LEFT JOIN [SalesOrder] AS [s0] ON [s].[OrderID] = [s0].[OrderID]
    WHERE [s].[InvoiceID] = @__id_0 AND [s].[IsDeleted] = CAST(0 AS bit)
) AS [s2]
LEFT JOIN (
    SELECT [s1].[InvoiceItemID], [s1].[AccountCode], [s1].[CreatedByUserID], [s1].[CreatedDate], [s1].[Description], [s1].[DiscountAmount], [s1].[DiscountPercent], [s1].[InvoiceID], [s1].[LineNumber], [s1].[LineTotal], [s1].[Notes], [s1].[OrderItemID], [s1].[ProductCode], [s1].[ProductID], [s1].[ProductName], [s1].[Quantity], [s1].[SortOrder], [s1].[TaxAmount], [s1].[TaxRate], [s1].[UOMID], [s1].[UnitPrice], [s1].[UpdatedByUserID], [s1].[UpdatedDate], [s1].[VariantID], [p].[ProductID] AS [ProductID0], [p].[AllowBackorder], [p].[AllowPreorder], [p].[Brand], [p].[CategoryID], [p].[CommodityCode], [p].[CountryOfOrigin], [p].[CreatedByUserID] AS [CreatedByUserID0], [p].[CreatedDate] AS [CreatedDate0], [p].[Currency], [p].[DeletedByUserID], [p].[DeletedDate], [p].[Description] AS [Description0], [p].[DimensionUOMID], [p].[HSCode], [p].[HasVariants], [p].[Height], [p].[InventoryUOMID], [p].[IsActive], [p].[IsDeleted], [p].[IsKit], [p].[IsTaxExempt], [p].[LeadTimeDays], [p].[Length], [p].[ListPrice], [p].[Manufacturer], [p].[ManufacturerPartNumber], [p].[MaxStockLevel], [p].[MinStockLevel], [p].[Notes] AS [Notes0], [p].[ProductCode] AS [ProductCode0], [p].[ProductName] AS [ProductName0], [p].[ProductType], [p].[PurchaseUOMID], [p].[ReorderPoint], [p].[ReorderQuantity], [p].[RequiresBatch], [p].[RequiresSerial], [p].[SalesUOMID], [p].[SellPrice], [p].[ShelfLifeDays], [p].[ShortDescription], [p].[StandardCost], [p].[Status], [p].[TaxCodeID], [p].[TrackInventory], [p].[UpdatedByUserID] AS [UpdatedByUserID0], [p].[UpdatedDate] AS [UpdatedDate0], [p].[Volume], [p].[VolumeUOMID], [p].[WarrantyDays], [p].[Weight], [p].[WeightUOMID], [p].[Width], [u].[UOMID] AS [UOMID0], [u].[BaseUnit], [u].[ConversionFactor], [u].[CreatedDate] AS [CreatedDate1], [u].[DecimalPlaces], [u].[IsActive] AS [IsActive0], [u].[Symbol], [u].[UOMCode], [u].[UOMName], [u].[UOMType], [u].[UpdatedDate] AS [UpdatedDate1]
    FROM [SalesInvoiceItem] AS [s1]
    INNER JOIN [Product] AS [p] ON [s1].[ProductID] = [p].[ProductID]
    INNER JOIN [UnitOfMeasure] AS [u] ON [s1].[UOMID] = [u].[UOMID]
) AS [s3] ON [s2].[InvoiceID] = [s3].[InvoiceID]
ORDER BY [s2].[InvoiceID], [s2].[CustomerID0], [s2].[OrderID0], [s3].[InvoiceItemID], [s3].[ProductID0]
2025-07-29 23:32:56.290 +03:00 [ERR] An exception occurred while iterating over the results of a query for context type 'nCarry.Web.Data.ApplicationDbContext'.
Microsoft.Data.SqlClient.SqlException (0x80131904): Invalid column name 'CreatedByUserID'.
Invalid column name 'CreatedDate'.
Invalid column name 'UpdatedByUserID'.
Invalid column name 'UpdatedDate'.
   at System.Threading.Tasks.ContinuationResultTaskFromResultTask`2.InnerInvoke()
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.InitializeReaderAsync(AsyncEnumerator enumerator, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.SqlServer.Storage.Internal.SqlServerExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
ClientConnectionId:02ef028d-8c8f-4dbc-ba81-9331092a455a
Error Number:207,State:1,Class:16
Microsoft.Data.SqlClient.SqlException (0x80131904): Invalid column name 'CreatedByUserID'.
Invalid column name 'CreatedDate'.
Invalid column name 'UpdatedByUserID'.
Invalid column name 'UpdatedDate'.
   at System.Threading.Tasks.ContinuationResultTaskFromResultTask`2.InnerInvoke()
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.InitializeReaderAsync(AsyncEnumerator enumerator, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.SqlServer.Storage.Internal.SqlServerExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
ClientConnectionId:02ef028d-8c8f-4dbc-ba81-9331092a455a
Error Number:207,State:1,Class:16
2025-07-29 23:32:56.293 +03:00 [ERR] HTTP GET /SalesInvoice/Details/2 responded 500 in 527.9302 ms
Microsoft.Data.SqlClient.SqlException (0x80131904): Invalid column name 'CreatedByUserID'.
Invalid column name 'CreatedDate'.
Invalid column name 'UpdatedByUserID'.
Invalid column name 'UpdatedDate'.
   at System.Threading.Tasks.ContinuationResultTaskFromResultTask`2.InnerInvoke()
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.InitializeReaderAsync(AsyncEnumerator enumerator, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.SqlServer.Storage.Internal.SqlServerExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
   at Microsoft.EntityFrameworkCore.Query.ShapedQueryCompilingExpressionVisitor.SingleOrDefaultAsync[TSource](IAsyncEnumerable`1 asyncEnumerable, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.ShapedQueryCompilingExpressionVisitor.SingleOrDefaultAsync[TSource](IAsyncEnumerable`1 asyncEnumerable, CancellationToken cancellationToken)
   at nCarry.Web.Controllers.SalesInvoiceController.Details(Nullable`1 id) in /Users/<USER>/Source/nCarry/nCarry/nCarry.Web/Controllers/SalesInvoiceController.cs:line 42
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.TaskOfIActionResultExecutor.Execute(ActionContext actionContext, IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeActionMethodAsync>g__Awaited|12_0(ControllerActionInvoker invoker, ValueTask`1 actionResultValueTask)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeInnerFilterAsync>g__Awaited|13_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextResourceFilter>g__Awaited|25_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.Rethrow(ResourceExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeFilterPipelineAsync>g__Awaited|20_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Session.SessionMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Session.SessionMiddleware.Invoke(HttpContext context)
   at Serilog.AspNetCore.RequestLoggingMiddleware.Invoke(HttpContext httpContext)
ClientConnectionId:02ef028d-8c8f-4dbc-ba81-9331092a455a
Error Number:207,State:1,Class:16
2025-07-29 23:32:56.299 +03:00 [ERR] An unhandled exception has occurred while executing the request.
Microsoft.Data.SqlClient.SqlException (0x80131904): Invalid column name 'CreatedByUserID'.
Invalid column name 'CreatedDate'.
Invalid column name 'UpdatedByUserID'.
Invalid column name 'UpdatedDate'.
   at System.Threading.Tasks.ContinuationResultTaskFromResultTask`2.InnerInvoke()
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.InitializeReaderAsync(AsyncEnumerator enumerator, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.SqlServer.Storage.Internal.SqlServerExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
   at Microsoft.EntityFrameworkCore.Query.ShapedQueryCompilingExpressionVisitor.SingleOrDefaultAsync[TSource](IAsyncEnumerable`1 asyncEnumerable, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.ShapedQueryCompilingExpressionVisitor.SingleOrDefaultAsync[TSource](IAsyncEnumerable`1 asyncEnumerable, CancellationToken cancellationToken)
   at nCarry.Web.Controllers.SalesInvoiceController.Details(Nullable`1 id) in /Users/<USER>/Source/nCarry/nCarry/nCarry.Web/Controllers/SalesInvoiceController.cs:line 42
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.TaskOfIActionResultExecutor.Execute(ActionContext actionContext, IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeActionMethodAsync>g__Awaited|12_0(ControllerActionInvoker invoker, ValueTask`1 actionResultValueTask)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeInnerFilterAsync>g__Awaited|13_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextResourceFilter>g__Awaited|25_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.Rethrow(ResourceExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeFilterPipelineAsync>g__Awaited|20_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Session.SessionMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Session.SessionMiddleware.Invoke(HttpContext context)
   at Serilog.AspNetCore.RequestLoggingMiddleware.Invoke(HttpContext httpContext)
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
ClientConnectionId:02ef028d-8c8f-4dbc-ba81-9331092a455a
Error Number:207,State:1,Class:16
2025-07-29 23:33:21.191 +03:00 [INF] HTTP GET /SalesInvoice responded 200 in 240.3131 ms
2025-07-29 23:33:27.652 +03:00 [WRN] Compiling a query which loads related collections for more than one collection navigation, either via 'Include' or through projection, but no 'QuerySplittingBehavior' has been configured. By default, Entity Framework will use 'QuerySplittingBehavior.SingleQuery', which can potentially result in slow query performance. See https://go.microsoft.com/fwlink/?linkid=2134277 for more information. To identify the query that's triggering this warning call 'ConfigureWarnings(w => w.Throw(RelationalEventId.MultipleCollectionIncludeWarning))'.
2025-07-29 23:33:28.002 +03:00 [ERR] Failed executing DbCommand (286ms) [Parameters=[@__id_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [s2].[InvoiceID], [s2].[BalanceAmount], [s2].[BillingAddressID], [s2].[CreatedByUserID], [s2].[CreatedDate], [s2].[Currency], [s2].[CustomerID], [s2].[CustomerPO], [s2].[DeletedByUserID], [s2].[DeletedDate], [s2].[DeliveryNoteNumber], [s2].[DiscountAmount], [s2].[DueDate], [s2].[ExchangeRate], [s2].[FooterText], [s2].[InternalNotes], [s2].[InvoiceDate], [s2].[InvoiceNumber], [s2].[InvoiceStatus], [s2].[InvoiceType], [s2].[IsDeleted], [s2].[LastPaymentDate], [s2].[Notes], [s2].[OrderID], [s2].[PaidAmount], [s2].[PaymentStatus], [s2].[PaymentTerms], [s2].[PostedByUserID], [s2].[PostedDate], [s2].[ShippingAddressID], [s2].[ShippingAmount], [s2].[SubTotal], [s2].[TaxAmount], [s2].[TotalAmount], [s2].[UpdatedByUserID], [s2].[UpdatedDate], [s2].[CustomerID0], [s2].[BlockedByUserID], [s2].[BlockedDate], [s2].[BlockedReason], [s2].[ContactEmail], [s2].[ContactLandline], [s2].[ContactMobile], [s2].[ContactPhone], [s2].[CreatedByUserID0], [s2].[CreatedDate0], [s2].[CreditLimit], [s2].[CreditReviewDate], [s2].[CreditStatus], [s2].[CurrencyCode], [s2].[CustomerAddress], [s2].[CustomerAddress2], [s2].[CustomerCity], [s2].[CustomerCode], [s2].[CustomerCountry], [s2].[CustomerGroup], [s2].[CustomerName], [s2].[CustomerPostCode], [s2].[CustomerSince], [s2].[CustomerState], [s2].[CustomerStatus], [s2].[CustomerType], [s2].[DeletedByUserID0], [s2].[DeletedDate0], [s2].[DeliveryInstructions], [s2].[DeliveryTimePreference], [s2].[DiscountPercent], [s2].[InternalNotes0], [s2].[IsActive], [s2].[IsDeleted0], [s2].[LastOrderDate], [s2].[LeadSource], [s2].[LoyaltyPoints], [s2].[Notes0], [s2].[OutstandingBalance], [s2].[PaymentMethodID], [s2].[PaymentTerm], [s2].[PreferredCarrierID], [s2].[PriceListID], [s2].[Rating], [s2].[ReferenceCode], [s2].[ResponsibleContact], [s2].[SalesRepID], [s2].[TaxExempt], [s2].[TaxNumber], [s2].[TerritoryID], [s2].[TotalOrderValue], [s2].[TradingName], [s2].[UpdatedByUserID0], [s2].[UpdatedDate0], [s2].[Website], [s2].[YTDSales], [s2].[OrderID0], [c0].[AddressID], [c0].[Address1], [c0].[Address2], [c0].[AddressName], [c0].[AddressType], [c0].[City], [c0].[ContactName], [c0].[ContactPhone], [c0].[Country], [c0].[CreatedDate], [c0].[CustomerID], [c0].[IsActive], [c0].[IsDefault], [c0].[PostCode], [c0].[State], [c0].[UpdatedDate], [s2].[BalanceAmount0], [s2].[BillingAddressID0], [s2].[CarrierID], [s2].[CreatedByUserID1], [s2].[CreatedDate1], [s2].[Currency0], [s2].[CustomerID1], [s2].[CustomerPO0], [s2].[DeletedByUserID1], [s2].[DeletedDate1], [s2].[DeliveredDate], [s2].[DeliveryInstructions0], [s2].[DiscountAmount0], [s2].[ExchangeRate0], [s2].[FreightTerms], [s2].[InternalNotes1], [s2].[IsDeleted1], [s2].[Notes1], [s2].[OrderDate], [s2].[OrderNumber], [s2].[OrderStatus], [s2].[OrderType], [s2].[PaidAmount0], [s2].[PaymentDueDate], [s2].[PaymentTerms0], [s2].[PickingPriority], [s2].[PromisedDate], [s2].[QuoteID], [s2].[RequestedDate], [s2].[SalesRepID0], [s2].[ShippedDate], [s2].[ShippingAddressID0], [s2].[ShippingAmount0], [s2].[ShippingMethod], [s2].[SubTotal0], [s2].[TaxAmount0], [s2].[TotalAmount0], [s2].[TrackingNumber], [s2].[UpdatedByUserID1], [s2].[UpdatedDate1], [s2].[WarehouseID], [s3].[InvoiceItemID], [s3].[AccountCode], [s3].[CreatedByUserID], [s3].[CreatedDate], [s3].[Description], [s3].[DiscountAmount], [s3].[DiscountPercent], [s3].[InvoiceID], [s3].[LineNumber], [s3].[LineTotal], [s3].[Notes], [s3].[OrderItemID], [s3].[ProductCode], [s3].[ProductID], [s3].[ProductName], [s3].[Quantity], [s3].[SortOrder], [s3].[TaxAmount], [s3].[TaxRate], [s3].[UOMID], [s3].[UnitPrice], [s3].[UpdatedByUserID], [s3].[UpdatedDate], [s3].[VariantID], [s3].[ProductID0], [s3].[AllowBackorder], [s3].[AllowPreorder], [s3].[Brand], [s3].[CategoryID], [s3].[CommodityCode], [s3].[CountryOfOrigin], [s3].[CreatedByUserID0], [s3].[CreatedDate0], [s3].[Currency], [s3].[DeletedByUserID], [s3].[DeletedDate], [s3].[Description0], [s3].[DimensionUOMID], [s3].[HSCode], [s3].[HasVariants], [s3].[Height], [s3].[InventoryUOMID], [s3].[IsActive], [s3].[IsDeleted], [s3].[IsKit], [s3].[IsTaxExempt], [s3].[LeadTimeDays], [s3].[Length], [s3].[ListPrice], [s3].[Manufacturer], [s3].[ManufacturerPartNumber], [s3].[MaxStockLevel], [s3].[MinStockLevel], [s3].[Notes0], [s3].[ProductCode0], [s3].[ProductName0], [s3].[ProductType], [s3].[PurchaseUOMID], [s3].[ReorderPoint], [s3].[ReorderQuantity], [s3].[RequiresBatch], [s3].[RequiresSerial], [s3].[SalesUOMID], [s3].[SellPrice], [s3].[ShelfLifeDays], [s3].[ShortDescription], [s3].[StandardCost], [s3].[Status], [s3].[TaxCodeID], [s3].[TrackInventory], [s3].[UpdatedByUserID0], [s3].[UpdatedDate0], [s3].[Volume], [s3].[VolumeUOMID], [s3].[WarrantyDays], [s3].[Weight], [s3].[WeightUOMID], [s3].[Width], [s3].[UOMID0], [s3].[BaseUnit], [s3].[ConversionFactor], [s3].[CreatedDate1], [s3].[DecimalPlaces], [s3].[IsActive0], [s3].[Symbol], [s3].[UOMCode], [s3].[UOMName], [s3].[UOMType], [s3].[UpdatedDate1]
FROM (
    SELECT TOP(1) [s].[InvoiceID], [s].[BalanceAmount], [s].[BillingAddressID], [s].[CreatedByUserID], [s].[CreatedDate], [s].[Currency], [s].[CustomerID], [s].[CustomerPO], [s].[DeletedByUserID], [s].[DeletedDate], [s].[DeliveryNoteNumber], [s].[DiscountAmount], [s].[DueDate], [s].[ExchangeRate], [s].[FooterText], [s].[InternalNotes], [s].[InvoiceDate], [s].[InvoiceNumber], [s].[InvoiceStatus], [s].[InvoiceType], [s].[IsDeleted], [s].[LastPaymentDate], [s].[Notes], [s].[OrderID], [s].[PaidAmount], [s].[PaymentStatus], [s].[PaymentTerms], [s].[PostedByUserID], [s].[PostedDate], [s].[ShippingAddressID], [s].[ShippingAmount], [s].[SubTotal], [s].[TaxAmount], [s].[TotalAmount], [s].[UpdatedByUserID], [s].[UpdatedDate], [c].[CustomerID] AS [CustomerID0], [c].[BlockedByUserID], [c].[BlockedDate], [c].[BlockedReason], [c].[ContactEmail], [c].[ContactLandline], [c].[ContactMobile], [c].[ContactPhone], [c].[CreatedByUserID] AS [CreatedByUserID0], [c].[CreatedDate] AS [CreatedDate0], [c].[CreditLimit], [c].[CreditReviewDate], [c].[CreditStatus], [c].[CurrencyCode], [c].[CustomerAddress], [c].[CustomerAddress2], [c].[CustomerCity], [c].[CustomerCode], [c].[CustomerCountry], [c].[CustomerGroup], [c].[CustomerName], [c].[CustomerPostCode], [c].[CustomerSince], [c].[CustomerState], [c].[CustomerStatus], [c].[CustomerType], [c].[DeletedByUserID] AS [DeletedByUserID0], [c].[DeletedDate] AS [DeletedDate0], [c].[DeliveryInstructions], [c].[DeliveryTimePreference], [c].[DiscountPercent], [c].[InternalNotes] AS [InternalNotes0], [c].[IsActive], [c].[IsDeleted] AS [IsDeleted0], [c].[LastOrderDate], [c].[LeadSource], [c].[LoyaltyPoints], [c].[Notes] AS [Notes0], [c].[OutstandingBalance], [c].[PaymentMethodID], [c].[PaymentTerm], [c].[PreferredCarrierID], [c].[PriceListID], [c].[Rating], [c].[ReferenceCode], [c].[ResponsibleContact], [c].[SalesRepID], [c].[TaxExempt], [c].[TaxNumber], [c].[TerritoryID], [c].[TotalOrderValue], [c].[TradingName], [c].[UpdatedByUserID] AS [UpdatedByUserID0], [c].[UpdatedDate] AS [UpdatedDate0], [c].[Website], [c].[YTDSales], [s0].[OrderID] AS [OrderID0], [s0].[BalanceAmount] AS [BalanceAmount0], [s0].[BillingAddressID] AS [BillingAddressID0], [s0].[CarrierID], [s0].[CreatedByUserID] AS [CreatedByUserID1], [s0].[CreatedDate] AS [CreatedDate1], [s0].[Currency] AS [Currency0], [s0].[CustomerID] AS [CustomerID1], [s0].[CustomerPO] AS [CustomerPO0], [s0].[DeletedByUserID] AS [DeletedByUserID1], [s0].[DeletedDate] AS [DeletedDate1], [s0].[DeliveredDate], [s0].[DeliveryInstructions] AS [DeliveryInstructions0], [s0].[DiscountAmount] AS [DiscountAmount0], [s0].[ExchangeRate] AS [ExchangeRate0], [s0].[FreightTerms], [s0].[InternalNotes] AS [InternalNotes1], [s0].[IsDeleted] AS [IsDeleted1], [s0].[Notes] AS [Notes1], [s0].[OrderDate], [s0].[OrderNumber], [s0].[OrderStatus], [s0].[OrderType], [s0].[PaidAmount] AS [PaidAmount0], [s0].[PaymentDueDate], [s0].[PaymentTerms] AS [PaymentTerms0], [s0].[PickingPriority], [s0].[PromisedDate], [s0].[QuoteID], [s0].[RequestedDate], [s0].[SalesRepID] AS [SalesRepID0], [s0].[ShippedDate], [s0].[ShippingAddressID] AS [ShippingAddressID0], [s0].[ShippingAmount] AS [ShippingAmount0], [s0].[ShippingMethod], [s0].[SubTotal] AS [SubTotal0], [s0].[TaxAmount] AS [TaxAmount0], [s0].[TotalAmount] AS [TotalAmount0], [s0].[TrackingNumber], [s0].[UpdatedByUserID] AS [UpdatedByUserID1], [s0].[UpdatedDate] AS [UpdatedDate1], [s0].[WarehouseID]
    FROM [SalesInvoice] AS [s]
    INNER JOIN [Customer] AS [c] ON [s].[CustomerID] = [c].[CustomerID]
    LEFT JOIN [SalesOrder] AS [s0] ON [s].[OrderID] = [s0].[OrderID]
    WHERE [s].[InvoiceID] = @__id_0 AND [s].[IsDeleted] = CAST(0 AS bit)
) AS [s2]
LEFT JOIN [CustomerAddress] AS [c0] ON [s2].[CustomerID0] = [c0].[CustomerID]
LEFT JOIN (
    SELECT [s1].[InvoiceItemID], [s1].[AccountCode], [s1].[CreatedByUserID], [s1].[CreatedDate], [s1].[Description], [s1].[DiscountAmount], [s1].[DiscountPercent], [s1].[InvoiceID], [s1].[LineNumber], [s1].[LineTotal], [s1].[Notes], [s1].[OrderItemID], [s1].[ProductCode], [s1].[ProductID], [s1].[ProductName], [s1].[Quantity], [s1].[SortOrder], [s1].[TaxAmount], [s1].[TaxRate], [s1].[UOMID], [s1].[UnitPrice], [s1].[UpdatedByUserID], [s1].[UpdatedDate], [s1].[VariantID], [p].[ProductID] AS [ProductID0], [p].[AllowBackorder], [p].[AllowPreorder], [p].[Brand], [p].[CategoryID], [p].[CommodityCode], [p].[CountryOfOrigin], [p].[CreatedByUserID] AS [CreatedByUserID0], [p].[CreatedDate] AS [CreatedDate0], [p].[Currency], [p].[DeletedByUserID], [p].[DeletedDate], [p].[Description] AS [Description0], [p].[DimensionUOMID], [p].[HSCode], [p].[HasVariants], [p].[Height], [p].[InventoryUOMID], [p].[IsActive], [p].[IsDeleted], [p].[IsKit], [p].[IsTaxExempt], [p].[LeadTimeDays], [p].[Length], [p].[ListPrice], [p].[Manufacturer], [p].[ManufacturerPartNumber], [p].[MaxStockLevel], [p].[MinStockLevel], [p].[Notes] AS [Notes0], [p].[ProductCode] AS [ProductCode0], [p].[ProductName] AS [ProductName0], [p].[ProductType], [p].[PurchaseUOMID], [p].[ReorderPoint], [p].[ReorderQuantity], [p].[RequiresBatch], [p].[RequiresSerial], [p].[SalesUOMID], [p].[SellPrice], [p].[ShelfLifeDays], [p].[ShortDescription], [p].[StandardCost], [p].[Status], [p].[TaxCodeID], [p].[TrackInventory], [p].[UpdatedByUserID] AS [UpdatedByUserID0], [p].[UpdatedDate] AS [UpdatedDate0], [p].[Volume], [p].[VolumeUOMID], [p].[WarrantyDays], [p].[Weight], [p].[WeightUOMID], [p].[Width], [u].[UOMID] AS [UOMID0], [u].[BaseUnit], [u].[ConversionFactor], [u].[CreatedDate] AS [CreatedDate1], [u].[DecimalPlaces], [u].[IsActive] AS [IsActive0], [u].[Symbol], [u].[UOMCode], [u].[UOMName], [u].[UOMType], [u].[UpdatedDate] AS [UpdatedDate1]
    FROM [SalesInvoiceItem] AS [s1]
    INNER JOIN [Product] AS [p] ON [s1].[ProductID] = [p].[ProductID]
    INNER JOIN [UnitOfMeasure] AS [u] ON [s1].[UOMID] = [u].[UOMID]
) AS [s3] ON [s2].[InvoiceID] = [s3].[InvoiceID]
ORDER BY [s2].[InvoiceID], [s2].[CustomerID0], [s2].[OrderID0], [c0].[AddressID], [s3].[InvoiceItemID], [s3].[ProductID0]
2025-07-29 23:33:28.003 +03:00 [ERR] An exception occurred while iterating over the results of a query for context type 'nCarry.Web.Data.ApplicationDbContext'.
Microsoft.Data.SqlClient.SqlException (0x80131904): Invalid column name 'CreatedByUserID'.
Invalid column name 'CreatedDate'.
Invalid column name 'UpdatedByUserID'.
Invalid column name 'UpdatedDate'.
   at System.Threading.Tasks.ContinuationResultTaskFromResultTask`2.InnerInvoke()
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.InitializeReaderAsync(AsyncEnumerator enumerator, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.SqlServer.Storage.Internal.SqlServerExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
ClientConnectionId:02ef028d-8c8f-4dbc-ba81-9331092a455a
Error Number:207,State:1,Class:16
Microsoft.Data.SqlClient.SqlException (0x80131904): Invalid column name 'CreatedByUserID'.
Invalid column name 'CreatedDate'.
Invalid column name 'UpdatedByUserID'.
Invalid column name 'UpdatedDate'.
   at System.Threading.Tasks.ContinuationResultTaskFromResultTask`2.InnerInvoke()
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.InitializeReaderAsync(AsyncEnumerator enumerator, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.SqlServer.Storage.Internal.SqlServerExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
ClientConnectionId:02ef028d-8c8f-4dbc-ba81-9331092a455a
Error Number:207,State:1,Class:16
2025-07-29 23:33:28.005 +03:00 [ERR] HTTP GET /SalesInvoice/Print/2 responded 500 in 451.0736 ms
Microsoft.Data.SqlClient.SqlException (0x80131904): Invalid column name 'CreatedByUserID'.
Invalid column name 'CreatedDate'.
Invalid column name 'UpdatedByUserID'.
Invalid column name 'UpdatedDate'.
   at System.Threading.Tasks.ContinuationResultTaskFromResultTask`2.InnerInvoke()
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.InitializeReaderAsync(AsyncEnumerator enumerator, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.SqlServer.Storage.Internal.SqlServerExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
   at Microsoft.EntityFrameworkCore.Query.ShapedQueryCompilingExpressionVisitor.SingleOrDefaultAsync[TSource](IAsyncEnumerable`1 asyncEnumerable, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.ShapedQueryCompilingExpressionVisitor.SingleOrDefaultAsync[TSource](IAsyncEnumerable`1 asyncEnumerable, CancellationToken cancellationToken)
   at nCarry.Web.Controllers.SalesInvoiceController.Print(Nullable`1 id) in /Users/<USER>/Source/nCarry/nCarry/nCarry.Web/Controllers/SalesInvoiceController.cs:line 257
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.TaskOfIActionResultExecutor.Execute(ActionContext actionContext, IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeActionMethodAsync>g__Awaited|12_0(ControllerActionInvoker invoker, ValueTask`1 actionResultValueTask)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeInnerFilterAsync>g__Awaited|13_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextResourceFilter>g__Awaited|25_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.Rethrow(ResourceExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeFilterPipelineAsync>g__Awaited|20_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Session.SessionMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Session.SessionMiddleware.Invoke(HttpContext context)
   at Serilog.AspNetCore.RequestLoggingMiddleware.Invoke(HttpContext httpContext)
ClientConnectionId:02ef028d-8c8f-4dbc-ba81-9331092a455a
Error Number:207,State:1,Class:16
2025-07-29 23:33:28.010 +03:00 [ERR] An unhandled exception has occurred while executing the request.
Microsoft.Data.SqlClient.SqlException (0x80131904): Invalid column name 'CreatedByUserID'.
Invalid column name 'CreatedDate'.
Invalid column name 'UpdatedByUserID'.
Invalid column name 'UpdatedDate'.
   at System.Threading.Tasks.ContinuationResultTaskFromResultTask`2.InnerInvoke()
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.InitializeReaderAsync(AsyncEnumerator enumerator, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.SqlServer.Storage.Internal.SqlServerExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
   at Microsoft.EntityFrameworkCore.Query.ShapedQueryCompilingExpressionVisitor.SingleOrDefaultAsync[TSource](IAsyncEnumerable`1 asyncEnumerable, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.ShapedQueryCompilingExpressionVisitor.SingleOrDefaultAsync[TSource](IAsyncEnumerable`1 asyncEnumerable, CancellationToken cancellationToken)
   at nCarry.Web.Controllers.SalesInvoiceController.Print(Nullable`1 id) in /Users/<USER>/Source/nCarry/nCarry/nCarry.Web/Controllers/SalesInvoiceController.cs:line 257
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.TaskOfIActionResultExecutor.Execute(ActionContext actionContext, IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeActionMethodAsync>g__Awaited|12_0(ControllerActionInvoker invoker, ValueTask`1 actionResultValueTask)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeInnerFilterAsync>g__Awaited|13_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextResourceFilter>g__Awaited|25_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.Rethrow(ResourceExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeFilterPipelineAsync>g__Awaited|20_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Session.SessionMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Session.SessionMiddleware.Invoke(HttpContext context)
   at Serilog.AspNetCore.RequestLoggingMiddleware.Invoke(HttpContext httpContext)
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
ClientConnectionId:02ef028d-8c8f-4dbc-ba81-9331092a455a
Error Number:207,State:1,Class:16
2025-07-29 23:38:21.444 +03:00 [INF] HTTP GET / responded 302 in 0.5017 ms
2025-07-29 23:38:26.586 +03:00 [INF] HTTP GET / responded 302 in 0.6741 ms
2025-07-29 23:38:28.247 +03:00 [INF] HTTP GET /Dashboard responded 200 in 1658.4247 ms
2025-07-29 23:38:28.259 +03:00 [INF] HTTP GET /nCarry.Web.styles.css responded 304 in 0.3285 ms
2025-07-29 23:38:28.456 +03:00 [INF] HTTP GET /Dashboard/GetChartData responded 200 in 161.0169 ms
2025-07-29 23:38:41.259 +03:00 [INF] HTTP GET /GoodsReceipt responded 200 in 101.6595 ms
2025-07-29 23:38:50.179 +03:00 [INF] HTTP GET /GoodsReceipt/Create responded 200 in 295.9083 ms
2025-07-29 23:39:03.678 +03:00 [INF] HTTP GET /GoodsReceipt/Create responded 200 in 557.1038 ms
undListenSocket(EndPoint endpoint)
   at Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets.SocketConnectionListener.Bind()
   --- End of inner exception stack trace ---
   at Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets.SocketConnectionListener.Bind()
   at Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets.SocketTransportFactory.BindAsync(EndPoint endpoint, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.Infrastructure.TransportManager.BindAsync(EndPoint endPoint, ConnectionDelegate connectionDelegate, EndpointConfig endpointConfig, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.KestrelServerImpl.<>c__DisplayClass28_0`1.<<StartAsync>g__OnBind|0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.AddressBinder.BindEndpointAsync(ListenOptions endpoint, AddressBindContext context, CancellationToken cancellationToken)
   --- End of inner exception stack trace ---
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.AddressBinder.BindEndpointAsync(ListenOptions endpoint, AddressBindContext context, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.LocalhostListenOptions.BindAsync(AddressBindContext context, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.AddressBinder.AddressesStrategy.BindAsync(AddressBindContext context, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.AddressBinder.BindAsync(ListenOptions[] listenOptions, AddressBindContext context, Func`2 useHttps, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.KestrelServerImpl.BindAsync(CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.KestrelServerImpl.StartAsync[TContext](IHttpApplication`1 application, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Hosting.GenericWebHostService.StartAsync(CancellationToken cancellationToken)
   at Microsoft.Extensions.Hosting.Internal.Host.<StartAsync>b__15_1(IHostedService service, CancellationToken token)
   at Microsoft.Extensions.Hosting.Internal.Host.ForeachService[T](IEnumerable`1 services, CancellationToken token, Boolean concurrent, Boolean abortOnFirstException, List`1 exceptions, Func`3 operation)
2025-07-29 23:38:19.777 +03:00 [FTL] Application terminated unexpectedly
System.IO.IOException: Failed to bind to address http://127.0.0.1:5298: address already in use.
 ---> Microsoft.AspNetCore.Connections.AddressInUseException: Address already in use
 ---> System.Net.Sockets.SocketException (48): Address already in use
   at System.Net.Sockets.Socket.DoBind(EndPoint endPointSnapshot, SocketAddress socketAddress)
   at System.Net.Sockets.Socket.Bind(EndPoint localEP)
   at Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets.SocketTransportOptions.CreateDefaultBoundListenSocket(EndPoint endpoint)
   at Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets.SocketConnectionListener.Bind()
   --- End of inner exception stack trace ---
   at Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets.SocketConnectionListener.Bind()
   at Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets.SocketTransportFactory.BindAsync(EndPoint endpoint, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.Infrastructure.TransportManager.BindAsync(EndPoint endPoint, ConnectionDelegate connectionDelegate, EndpointConfig endpointConfig, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.KestrelServerImpl.<>c__DisplayClass28_0`1.<<StartAsync>g__OnBind|0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.AddressBinder.BindEndpointAsync(ListenOptions endpoint, AddressBindContext context, CancellationToken cancellationToken)
   --- End of inner exception stack trace ---
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.AddressBinder.BindEndpointAsync(ListenOptions endpoint, AddressBindContext context, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.LocalhostListenOptions.BindAsync(AddressBindContext context, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.AddressBinder.AddressesStrategy.BindAsync(AddressBindContext context, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.AddressBinder.BindAsync(ListenOptions[] listenOptions, AddressBindContext context, Func`2 useHttps, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.KestrelServerImpl.BindAsync(CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.KestrelServerImpl.StartAsync[TContext](IHttpApplication`1 application, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Hosting.GenericWebHostService.StartAsync(CancellationToken cancellationToken)
   at Microsoft.Extensions.Hosting.Internal.Host.<StartAsync>b__15_1(IHostedService service, CancellationToken token)
   at Microsoft.Extensions.Hosting.Internal.Host.ForeachService[T](IEnumerable`1 services, CancellationToken token, Boolean concurrent, Boolean abortOnFirstException, List`1 exceptions, Func`3 operation)
   at Microsoft.Extensions.Hosting.Internal.Host.StartAsync(CancellationToken cancellationToken)
   at Microsoft.Extensions.Hosting.HostingAbstractionsHostExtensions.RunAsync(IHost host, CancellationToken token)
   at Microsoft.Extensions.Hosting.HostingAbstractionsHostExtensions.RunAsync(IHost host, CancellationToken token)
   at Microsoft.Extensions.Hosting.HostingAbstractionsHostExtensions.Run(IHost host)
   at Program.<Main>$(String[] args) in /Users/<USER>/Source/nCarry/nCarry/nCarry.Web/Program.cs:line 82
2025-07-29 23:40:16.304 +03:00 [INF] Starting database seeding.
2025-07-29 23:40:19.819 +03:00 [INF] Admin user already exists. Seeding is not required.
2025-07-29 23:40:23.917 +03:00 [WRN] Failed to determine the https port for redirect.
2025-07-29 23:40:23.982 +03:00 [INF] HTTP GET / responded 302 in 68.8148 ms
2025-07-29 23:40:32.719 +03:00 [INF] HTTP GET /GoodsReceipt/Create responded 200 in 1661.7861 ms
2025-07-29 23:41:31.304 +03:00 [INF] HTTP POST /GoodsReceipt/Create responded 200 in 569.2525 ms
2025-07-29 23:42:18.779 +03:00 [INF] HTTP GET /GoodsReceipt responded 200 in 317.2787 ms
2025-07-29 23:45:15.076 +03:00 [INF] Starting database seeding.
2025-07-29 23:45:19.119 +03:00 [INF] Admin user already exists. Seeding is not required.
2025-07-29 23:45:28.423 +03:00 [WRN] Failed to determine the https port for redirect.
2025-07-29 23:45:28.478 +03:00 [INF] HTTP GET / responded 302 in 58.6076 ms
2025-07-29 23:45:37.800 +03:00 [INF] HTTP GET /GoodsReceipt/Create responded 200 in 1622.0655 ms
2025-07-29 23:45:54.398 +03:00 [INF] HTTP POST /GoodsReceipt/Create responded 200 in 806.1210 ms
2025-07-29 23:47:21.303 +03:00 [INF] Starting database seeding.
2025-07-29 23:47:25.704 +03:00 [INF] Admin user already exists. Seeding is not required.
2025-07-29 23:47:29.008 +03:00 [WRN] Failed to determine the https port for redirect.
2025-07-29 23:47:29.068 +03:00 [INF] HTTP GET / responded 302 in 63.6409 ms
2025-07-29 23:47:38.740 +03:00 [INF] HTTP GET /GoodsReceipt/Create responded 200 in 1690.6762 ms
2025-07-29 23:47:38.758 +03:00 [INF] HTTP GET /nCarry.Web.styles.css responded 304 in 4.3111 ms
2025-07-29 23:47:56.888 +03:00 [ERR] An exception occurred in the database while saving changes for context type 'nCarry.Web.Data.ApplicationDbContext'.
Microsoft.EntityFrameworkCore.DbUpdateException: An error occurred while saving the entity changes. See the inner exception for details.
 ---> Microsoft.Data.SqlClient.SqlException (0x80131904): The INSERT statement conflicted with the FOREIGN KEY constraint "FK_GoodsReceipt_Supplier". The conflict occurred in database "nCarryDB", table "dbo.Supplier", column 'SupplierID'.
   at Microsoft.Data.SqlClient.SqlConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.SqlInternalConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.TdsParser.ThrowExceptionAndWarning(TdsParserStateObject stateObj, Boolean callerHasConnectionLock, Boolean asyncClose)
   at Microsoft.Data.SqlClient.TdsParser.TryRun(RunBehavior runBehavior, SqlCommand cmdHandler, SqlDataReader dataStream, BulkCopySimpleResultSet bulkCopyHandler, TdsParserStateObject stateObj, Boolean& dataReady)
   at Microsoft.Data.SqlClient.SqlDataReader.TryHasMoreRows(Boolean& moreRows)
   at Microsoft.Data.SqlClient.SqlDataReader.TryReadInternal(Boolean setTimeout, Boolean& more)
   at Microsoft.Data.SqlClient.SqlDataReader.ReadAsyncExecute(Task task, Object state)
   at Microsoft.Data.SqlClient.SqlDataReader.InvokeAsyncCall[T](SqlDataReaderBaseAsyncCallContext`1 context)
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Update.AffectedCountModificationCommandBatch.ConsumeResultSetAsync(Int32 startCommandIndex, RelationalDataReader reader, CancellationToken cancellationToken)
ClientConnectionId:c38b3ca8-8e5e-4775-8717-0bc9293ea68c
Error Number:547,State:0,Class:16
   --- End of inner exception stack trace ---
   at Microsoft.EntityFrameworkCore.Update.AffectedCountModificationCommandBatch.ConsumeResultSetAsync(Int32 startCommandIndex, RelationalDataReader reader, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.AffectedCountModificationCommandBatch.ConsumeAsync(RelationalDataReader reader, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.SqlServer.Update.Internal.SqlServerModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalDatabase.SaveChangesAsync(IList`1 entries, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(IList`1 entriesToSave, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(StateManager stateManager, Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.SqlServer.Storage.Internal.SqlServerExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.DbContext.SaveChangesAsync(Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
Microsoft.EntityFrameworkCore.DbUpdateException: An error occurred while saving the entity changes. See the inner exception for details.
 ---> Microsoft.Data.SqlClient.SqlException (0x80131904): The INSERT statement conflicted with the FOREIGN KEY constraint "FK_GoodsReceipt_Supplier". The conflict occurred in database "nCarryDB", table "dbo.Supplier", column 'SupplierID'.
   at Microsoft.Data.SqlClient.SqlConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.SqlInternalConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.TdsParser.ThrowExceptionAndWarning(TdsParserStateObject stateObj, Boolean callerHasConnectionLock, Boolean asyncClose)
   at Microsoft.Data.SqlClient.TdsParser.TryRun(RunBehavior runBehavior, SqlCommand cmdHandler, SqlDataReader dataStream, BulkCopySimpleResultSet bulkCopyHandler, TdsParserStateObject stateObj, Boolean& dataReady)
   at Microsoft.Data.SqlClient.SqlDataReader.TryHasMoreRows(Boolean& moreRows)
   at Microsoft.Data.SqlClient.SqlDataReader.TryReadInternal(Boolean setTimeout, Boolean& more)
   at Microsoft.Data.SqlClient.SqlDataReader.ReadAsyncExecute(Task task, Object state)
   at Microsoft.Data.SqlClient.SqlDataReader.InvokeAsyncCall[T](SqlDataReaderBaseAsyncCallContext`1 context)
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Update.AffectedCountModificationCommandBatch.ConsumeResultSetAsync(Int32 startCommandIndex, RelationalDataReader reader, CancellationToken cancellationToken)
ClientConnectionId:c38b3ca8-8e5e-4775-8717-0bc9293ea68c
Error Number:547,State:0,Class:16
   --- End of inner exception stack trace ---
   at Microsoft.EntityFrameworkCore.Update.AffectedCountModificationCommandBatch.ConsumeResultSetAsync(Int32 startCommandIndex, RelationalDataReader reader, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.AffectedCountModificationCommandBatch.ConsumeAsync(RelationalDataReader reader, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.SqlServer.Update.Internal.SqlServerModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalDatabase.SaveChangesAsync(IList`1 entries, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(IList`1 entriesToSave, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(StateManager stateManager, Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.SqlServer.Storage.Internal.SqlServerExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.DbContext.SaveChangesAsync(Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
2025-07-29 23:47:57.330 +03:00 [INF] HTTP POST /GoodsReceipt/Create responded 200 in 1368.8866 ms
2025-07-29 23:49:21.335 +03:00 [INF] Starting database seeding.
2025-07-29 23:49:24.621 +03:00 [INF] Admin user already exists. Seeding is not required.
2025-07-29 23:49:31.589 +03:00 [WRN] Failed to determine the https port for redirect.
2025-07-29 23:49:31.652 +03:00 [INF] HTTP GET / responded 302 in 66.6127 ms
2025-07-29 23:49:41.722 +03:00 [INF] HTTP GET /GoodsReceipt/Create responded 200 in 1692.6975 ms
2025-07-29 23:49:59.309 +03:00 [ERR] Failed executing DbCommand (76ms) [Parameters=[@p19='?' (Precision = 18) (Scale = 4) (DbType = Decimal), @p20='?' (Size = 50), @p21='?' (Size = 500), @p22='?' (DbType = DateTime2), @p23='?' (DbType = Int32), @p24='?' (DbType = Int32), @p25='?' (DbType = Int32), @p26='?' (Size = 500), @p27='?' (DbType = Int32), @p28='?' (Size = 255), @p29='?' (DbType = Int32), @p30='?' (Size = 20), @p31='?' (Precision = 18) (Scale = 4) (DbType = Decimal), @p32='?' (Precision = 18) (Scale = 4) (DbType = Decimal), @p33='?' (Size = 500), @p34='?' (Size = 50), @p35='?' (DbType = Int32), @p36='?' (DbType = Int32), @p37='?' (Precision = 18) (Scale = 2) (DbType = Decimal), @p38='?' (DbType = Int32), @p39='?' (Precision = 18) (Scale = 4) (DbType = Decimal), @p40='?' (Size = 50), @p41='?' (Size = 500), @p42='?' (DbType = DateTime2), @p43='?' (DbType = Int32), @p44='?' (DbType = Int32), @p45='?' (DbType = Int32), @p46='?' (Size = 500), @p47='?' (DbType = Int32), @p48='?' (Size = 255), @p49='?' (DbType = Int32), @p50='?' (Size = 20), @p51='?' (Precision = 18) (Scale = 4) (DbType = Decimal), @p52='?' (Precision = 18) (Scale = 4) (DbType = Decimal), @p53='?' (Size = 500), @p54='?' (Size = 50), @p55='?' (DbType = Int32), @p56='?' (DbType = Int32), @p57='?' (Precision = 18) (Scale = 2) (DbType = Decimal), @p58='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SET IMPLICIT_TRANSACTIONS OFF;
SET NOCOUNT ON;
MERGE [GoodsReceiptItem] USING (
VALUES (@p19, @p20, @p21, @p22, @p23, @p24, @p25, @p26, @p27, @p28, @p29, @p30, @p31, @p32, @p33, @p34, @p35, @p36, @p37, @p38, 0),
(@p39, @p40, @p41, @p42, @p43, @p44, @p45, @p46, @p47, @p48, @p49, @p50, @p51, @p52, @p53, @p54, @p55, @p56, @p57, @p58, 1)) AS i ([AcceptedQuantity], [BatchNumber], [Description], [ExpiryDate], [ReceiptID], [LineNumber], [LocationID], [Notes], [ProductID], [ProductName], [PurchaseOrderItemID], [QualityStatus], [ReceivedQuantity], [RejectedQuantity], [RejectionReason], [SerialNumber], [SortOrder], [UOMID], [UnitPrice], [VariantID], _Position) ON 1=0
WHEN NOT MATCHED THEN
INSERT ([AcceptedQuantity], [BatchNumber], [Description], [ExpiryDate], [ReceiptID], [LineNumber], [LocationID], [Notes], [ProductID], [ProductName], [PurchaseOrderItemID], [QualityStatus], [ReceivedQuantity], [RejectedQuantity], [RejectionReason], [SerialNumber], [SortOrder], [UOMID], [UnitPrice], [VariantID])
VALUES (i.[AcceptedQuantity], i.[BatchNumber], i.[Description], i.[ExpiryDate], i.[ReceiptID], i.[LineNumber], i.[LocationID], i.[Notes], i.[ProductID], i.[ProductName], i.[PurchaseOrderItemID], i.[QualityStatus], i.[ReceivedQuantity], i.[RejectedQuantity], i.[RejectionReason], i.[SerialNumber], i.[SortOrder], i.[UOMID], i.[UnitPrice], i.[VariantID])
OUTPUT INSERTED.[ReceiptItemID], i._Position;
2025-07-29 23:49:59.459 +03:00 [ERR] An exception occurred in the database while saving changes for context type 'nCarry.Web.Data.ApplicationDbContext'.
Microsoft.EntityFrameworkCore.DbUpdateException: An error occurred while saving the entity changes. See the inner exception for details.
 ---> Microsoft.Data.SqlClient.SqlException (0x80131904): Invalid column name 'Description'.
Invalid column name 'UnitPrice'.
Invalid column name 'VariantID'.
   at Microsoft.Data.SqlClient.SqlCommand.<>c.<ExecuteDbDataReaderAsync>b__211_0(Task`1 result)
   at System.Threading.Tasks.ContinuationResultTaskFromResultTask`2.InnerInvoke()
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
ClientConnectionId:f4b46ae0-cdcd-449a-b595-784a04bd0be9
Error Number:207,State:1,Class:16
   --- End of inner exception stack trace ---
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.SqlServer.Update.Internal.SqlServerModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalDatabase.SaveChangesAsync(IList`1 entries, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(IList`1 entriesToSave, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(StateManager stateManager, Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.SqlServer.Storage.Internal.SqlServerExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.DbContext.SaveChangesAsync(Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
Microsoft.EntityFrameworkCore.DbUpdateException: An error occurred while saving the entity changes. See the inner exception for details.
 ---> Microsoft.Data.SqlClient.SqlException (0x80131904): Invalid column name 'Description'.
Invalid column name 'UnitPrice'.
Invalid column name 'VariantID'.
   at Microsoft.Data.SqlClient.SqlCommand.<>c.<ExecuteDbDataReaderAsync>b__211_0(Task`1 result)
   at System.Threading.Tasks.ContinuationResultTaskFromResultTask`2.InnerInvoke()
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
ClientConnectionId:f4b46ae0-cdcd-449a-b595-784a04bd0be9
Error Number:207,State:1,Class:16
   --- End of inner exception stack trace ---
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.SqlServer.Update.Internal.SqlServerModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalDatabase.SaveChangesAsync(IList`1 entries, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(IList`1 entriesToSave, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(StateManager stateManager, Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.SqlServer.Storage.Internal.SqlServerExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.DbContext.SaveChangesAsync(Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
2025-07-29 23:49:59.857 +03:00 [INF] HTTP POST /GoodsReceipt/Create responded 200 in 1601.0086 ms
2025-07-29 23:52:06.488 +03:00 [INF] HTTP GET /GoodsReceipt/Create responded 200 in 477.5362 ms
2025-07-29 23:52:19.413 +03:00 [INF] HTTP GET /GoodsReceipt/Create responded 200 in 320.6714 ms
2025-07-29 23:52:23.722 +03:00 [INF] HTTP GET /GoodsReceipt/Create responded 200 in 314.4797 ms
2025-07-29 23:52:38.835 +03:00 [INF] HTTP GET /GoodsReceipt/Create responded 200 in 509.4323 ms
2025-07-29 23:53:21.033 +03:00 [ERR] An exception occurred in the database while saving changes for context type 'nCarry.Web.Data.ApplicationDbContext'.
Microsoft.EntityFrameworkCore.DbUpdateException: An error occurred while saving the entity changes. See the inner exception for details.
 ---> Microsoft.Data.SqlClient.SqlException (0x80131904): Cannot insert the value NULL into column 'UnitCost', table 'nCarryDB.dbo.GoodsReceiptItem'; column does not allow nulls. UPDATE fails.
   at Microsoft.Data.SqlClient.SqlConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.SqlInternalConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.TdsParser.ThrowExceptionAndWarning(TdsParserStateObject stateObj, Boolean callerHasConnectionLock, Boolean asyncClose)
   at Microsoft.Data.SqlClient.TdsParser.TryRun(RunBehavior runBehavior, SqlCommand cmdHandler, SqlDataReader dataStream, BulkCopySimpleResultSet bulkCopyHandler, TdsParserStateObject stateObj, Boolean& dataReady)
   at Microsoft.Data.SqlClient.SqlDataReader.TryHasMoreRows(Boolean& moreRows)
   at Microsoft.Data.SqlClient.SqlDataReader.TryReadInternal(Boolean setTimeout, Boolean& more)
   at Microsoft.Data.SqlClient.SqlDataReader.ReadAsyncExecute(Task task, Object state)
   at Microsoft.Data.SqlClient.SqlDataReader.InvokeAsyncCall[T](SqlDataReaderBaseAsyncCallContext`1 context)
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Update.AffectedCountModificationCommandBatch.ConsumeResultSetAsync(Int32 startCommandIndex, RelationalDataReader reader, CancellationToken cancellationToken)
ClientConnectionId:f4b46ae0-cdcd-449a-b595-784a04bd0be9
Error Number:515,State:2,Class:16
   --- End of inner exception stack trace ---
   at Microsoft.EntityFrameworkCore.Update.AffectedCountModificationCommandBatch.ConsumeResultSetAsync(Int32 startCommandIndex, RelationalDataReader reader, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.AffectedCountModificationCommandBatch.ConsumeAsync(RelationalDataReader reader, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.SqlServer.Update.Internal.SqlServerModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalDatabase.SaveChangesAsync(IList`1 entries, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(IList`1 entriesToSave, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(StateManager stateManager, Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.SqlServer.Storage.Internal.SqlServerExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.DbContext.SaveChangesAsync(Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
Microsoft.EntityFrameworkCore.DbUpdateException: An error occurred while saving the entity changes. See the inner exception for details.
 ---> Microsoft.Data.SqlClient.SqlException (0x80131904): Cannot insert the value NULL into column 'UnitCost', table 'nCarryDB.dbo.GoodsReceiptItem'; column does not allow nulls. UPDATE fails.
   at Microsoft.Data.SqlClient.SqlConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.SqlInternalConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.TdsParser.ThrowExceptionAndWarning(TdsParserStateObject stateObj, Boolean callerHasConnectionLock, Boolean asyncClose)
   at Microsoft.Data.SqlClient.TdsParser.TryRun(RunBehavior runBehavior, SqlCommand cmdHandler, SqlDataReader dataStream, BulkCopySimpleResultSet bulkCopyHandler, TdsParserStateObject stateObj, Boolean& dataReady)
   at Microsoft.Data.SqlClient.SqlDataReader.TryHasMoreRows(Boolean& moreRows)
   at Microsoft.Data.SqlClient.SqlDataReader.TryReadInternal(Boolean setTimeout, Boolean& more)
   at Microsoft.Data.SqlClient.SqlDataReader.ReadAsyncExecute(Task task, Object state)
   at Microsoft.Data.SqlClient.SqlDataReader.InvokeAsyncCall[T](SqlDataReaderBaseAsyncCallContext`1 context)
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Update.AffectedCountModificationCommandBatch.ConsumeResultSetAsync(Int32 startCommandIndex, RelationalDataReader reader, CancellationToken cancellationToken)
ClientConnectionId:f4b46ae0-cdcd-449a-b595-784a04bd0be9
Error Number:515,State:2,Class:16
   --- End of inner exception stack trace ---
   at Microsoft.EntityFrameworkCore.Update.AffectedCountModificationCommandBatch.ConsumeResultSetAsync(Int32 startCommandIndex, RelationalDataReader reader, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.AffectedCountModificationCommandBatch.ConsumeAsync(RelationalDataReader reader, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.SqlServer.Update.Internal.SqlServerModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalDatabase.SaveChangesAsync(IList`1 entries, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(IList`1 entriesToSave, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(StateManager stateManager, Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.SqlServer.Storage.Internal.SqlServerExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.DbContext.SaveChangesAsync(Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
2025-07-29 23:53:21.384 +03:00 [INF] HTTP POST /GoodsReceipt/Create responded 200 in 934.5880 ms
2025-07-29 23:53:56.705 +03:00 [INF] HTTP GET /GoodsReceipt/Create responded 200 in 402.3768 ms
2025-07-29 23:54:39.304 +03:00 [ERR] An exception occurred in the database while saving changes for context type 'nCarry.Web.Data.ApplicationDbContext'.
Microsoft.EntityFrameworkCore.DbUpdateException: An error occurred while saving the entity changes. See the inner exception for details.
 ---> Microsoft.Data.SqlClient.SqlException (0x80131904): Cannot insert the value NULL into column 'UnitCost', table 'nCarryDB.dbo.GoodsReceiptItem'; column does not allow nulls. UPDATE fails.
   at Microsoft.Data.SqlClient.SqlConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.SqlInternalConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.TdsParser.ThrowExceptionAndWarning(TdsParserStateObject stateObj, Boolean callerHasConnectionLock, Boolean asyncClose)
   at Microsoft.Data.SqlClient.TdsParser.TryRun(RunBehavior runBehavior, SqlCommand cmdHandler, SqlDataReader dataStream, BulkCopySimpleResultSet bulkCopyHandler, TdsParserStateObject stateObj, Boolean& dataReady)
   at Microsoft.Data.SqlClient.SqlDataReader.TryHasMoreRows(Boolean& moreRows)
   at Microsoft.Data.SqlClient.SqlDataReader.TryReadInternal(Boolean setTimeout, Boolean& more)
   at Microsoft.Data.SqlClient.SqlDataReader.ReadAsyncExecute(Task task, Object state)
   at Microsoft.Data.SqlClient.SqlDataReader.InvokeAsyncCall[T](SqlDataReaderBaseAsyncCallContext`1 context)
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Update.AffectedCountModificationCommandBatch.ConsumeResultSetAsync(Int32 startCommandIndex, RelationalDataReader reader, CancellationToken cancellationToken)
ClientConnectionId:f4b46ae0-cdcd-449a-b595-784a04bd0be9
Error Number:515,State:2,Class:16
   --- End of inner exception stack trace ---
   at Microsoft.EntityFrameworkCore.Update.AffectedCountModificationCommandBatch.ConsumeResultSetAsync(Int32 startCommandIndex, RelationalDataReader reader, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.AffectedCountModificationCommandBatch.ConsumeAsync(RelationalDataReader reader, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.SqlServer.Update.Internal.SqlServerModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalDatabase.SaveChangesAsync(IList`1 entries, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(IList`1 entriesToSave, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(StateManager stateManager, Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.SqlServer.Storage.Internal.SqlServerExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.DbContext.SaveChangesAsync(Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
Microsoft.EntityFrameworkCore.DbUpdateException: An error occurred while saving the entity changes. See the inner exception for details.
 ---> Microsoft.Data.SqlClient.SqlException (0x80131904): Cannot insert the value NULL into column 'UnitCost', table 'nCarryDB.dbo.GoodsReceiptItem'; column does not allow nulls. UPDATE fails.
   at Microsoft.Data.SqlClient.SqlConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.SqlInternalConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.TdsParser.ThrowExceptionAndWarning(TdsParserStateObject stateObj, Boolean callerHasConnectionLock, Boolean asyncClose)
   at Microsoft.Data.SqlClient.TdsParser.TryRun(RunBehavior runBehavior, SqlCommand cmdHandler, SqlDataReader dataStream, BulkCopySimpleResultSet bulkCopyHandler, TdsParserStateObject stateObj, Boolean& dataReady)
   at Microsoft.Data.SqlClient.SqlDataReader.TryHasMoreRows(Boolean& moreRows)
   at Microsoft.Data.SqlClient.SqlDataReader.TryReadInternal(Boolean setTimeout, Boolean& more)
   at Microsoft.Data.SqlClient.SqlDataReader.ReadAsyncExecute(Task task, Object state)
   at Microsoft.Data.SqlClient.SqlDataReader.InvokeAsyncCall[T](SqlDataReaderBaseAsyncCallContext`1 context)
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Update.AffectedCountModificationCommandBatch.ConsumeResultSetAsync(Int32 startCommandIndex, RelationalDataReader reader, CancellationToken cancellationToken)
ClientConnectionId:f4b46ae0-cdcd-449a-b595-784a04bd0be9
Error Number:515,State:2,Class:16
   --- End of inner exception stack trace ---
   at Microsoft.EntityFrameworkCore.Update.AffectedCountModificationCommandBatch.ConsumeResultSetAsync(Int32 startCommandIndex, RelationalDataReader reader, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.AffectedCountModificationCommandBatch.ConsumeAsync(RelationalDataReader reader, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.SqlServer.Update.Internal.SqlServerModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalDatabase.SaveChangesAsync(IList`1 entries, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(IList`1 entriesToSave, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(StateManager stateManager, Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.SqlServer.Storage.Internal.SqlServerExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.DbContext.SaveChangesAsync(Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
2025-07-29 23:54:39.656 +03:00 [INF] HTTP POST /GoodsReceipt/Create responded 200 in 930.2076 ms
2025-07-29 23:55:24.654 +03:00 [INF] HTTP GET /GoodsReceipt/Create responded 200 in 416.2363 ms
INF] Admin user already exists. Seeding is not required.
2025-07-29 23:55:18.549 +03:00 [ERR] Hosting failed to start
System.IO.IOException: Failed to bind to address http://127.0.0.1:5298: address already in use.
 ---> Microsoft.AspNetCore.Connections.AddressInUseException: Address already in use
 ---> System.Net.Sockets.SocketException (48): Address already in use
   at System.Net.Sockets.Socket.DoBind(EndPoint endPointSnapshot, SocketAddress socketAddress)
   at System.Net.Sockets.Socket.Bind(EndPoint localEP)
   at Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets.SocketTransportOptions.CreateDefaultBoundListenSocket(EndPoint endpoint)
   at Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets.SocketConnectionListener.Bind()
   --- End of inner exception stack trace ---
   at Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets.SocketConnectionListener.Bind()
   at Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets.SocketTransportFactory.BindAsync(EndPoint endpoint, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.Infrastructure.TransportManager.BindAsync(EndPoint endPoint, ConnectionDelegate connectionDelegate, EndpointConfig endpointConfig, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.KestrelServerImpl.<>c__DisplayClass28_0`1.<<StartAsync>g__OnBind|0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.AddressBinder.BindEndpointAsync(ListenOptions endpoint, AddressBindContext context, CancellationToken cancellationToken)
   --- End of inner exception stack trace ---
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.AddressBinder.BindEndpointAsync(ListenOptions endpoint, AddressBindContext context, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.LocalhostListenOptions.BindAsync(AddressBindContext context, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.AddressBinder.AddressesStrategy.BindAsync(AddressBindContext context, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.AddressBinder.BindAsync(ListenOptions[] listenOptions, AddressBindContext context, Func`2 useHttps, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.KestrelServerImpl.BindAsync(CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.KestrelServerImpl.StartAsync[TContext](IHttpApplication`1 application, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Hosting.GenericWebHostService.StartAsync(CancellationToken cancellationToken)
   at Microsoft.Extensions.Hosting.Internal.Host.<StartAsync>b__15_1(IHostedService service, CancellationToken token)
   at Microsoft.Extensions.Hosting.Internal.Host.ForeachService[T](IEnumerable`1 services, CancellationToken token, Boolean concurrent, Boolean abortOnFirstException, List`1 exceptions, Func`3 operation)
2025-07-29 23:55:18.560 +03:00 [FTL] Application terminated unexpectedly
System.IO.IOException: Failed to bind to address http://127.0.0.1:5298: address already in use.
 ---> Microsoft.AspNetCore.Connections.AddressInUseException: Address already in use
 ---> System.Net.Sockets.SocketException (48): Address already in use
   at System.Net.Sockets.Socket.DoBind(EndPoint endPointSnapshot, SocketAddress socketAddress)
   at System.Net.Sockets.Socket.Bind(EndPoint localEP)
   at Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets.SocketTransportOptions.CreateDefaultBoundListenSocket(EndPoint endpoint)
   at Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets.SocketConnectionListener.Bind()
   --- End of inner exception stack trace ---
   at Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets.SocketConnectionListener.Bind()
   at Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets.SocketTransportFactory.BindAsync(EndPoint endpoint, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.Infrastructure.TransportManager.BindAsync(EndPoint endPoint, ConnectionDelegate connectionDelegate, EndpointConfig endpointConfig, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.KestrelServerImpl.<>c__DisplayClass28_0`1.<<StartAsync>g__OnBind|0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.AddressBinder.BindEndpointAsync(ListenOptions endpoint, AddressBindContext context, CancellationToken cancellationToken)
   --- End of inner exception stack trace ---
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.AddressBinder.BindEndpointAsync(ListenOptions endpoint, AddressBindContext context, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.LocalhostListenOptions.BindAsync(AddressBindContext context, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.AddressBinder.AddressesStrategy.BindAsync(AddressBindContext context, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.AddressBinder.BindAsync(ListenOptions[] listenOptions, AddressBindContext context, Func`2 useHttps, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.KestrelServerImpl.BindAsync(CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.KestrelServerImpl.StartAsync[TContext](IHttpApplication`1 application, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Hosting.GenericWebHostService.StartAsync(CancellationToken cancellationToken)
   at Microsoft.Extensions.Hosting.Internal.Host.<StartAsync>b__15_1(IHostedService service, CancellationToken token)
   at Microsoft.Extensions.Hosting.Internal.Host.ForeachService[T](IEnumerable`1 services, CancellationToken token, Boolean concurrent, Boolean abortOnFirstException, List`1 exceptions, Func`3 operation)
   at Microsoft.Extensions.Hosting.Internal.Host.StartAsync(CancellationToken cancellationToken)
   at Microsoft.Extensions.Hosting.HostingAbstractionsHostExtensions.RunAsync(IHost host, CancellationToken token)
   at Microsoft.Extensions.Hosting.HostingAbstractionsHostExtensions.RunAsync(IHost host, CancellationToken token)
   at Microsoft.Extensions.Hosting.HostingAbstractionsHostExtensions.Run(IHost host)
   at Program.<Main>$(String[] args) in /Users/<USER>/Source/nCarry/nCarry/nCarry.Web/Program.cs:line 82
2025-07-29 23:56:27.516 +03:00 [INF] Starting database seeding.
2025-07-29 23:56:30.950 +03:00 [INF] Admin user already exists. Seeding is not required.
2025-07-29 23:57:39.591 +03:00 [INF] Starting database seeding.
2025-07-29 23:57:42.698 +03:00 [INF] Admin user already exists. Seeding is not required.
2025-07-29 23:58:36.157 +03:00 [WRN] Failed to determine the https port for redirect.
2025-07-29 23:58:36.215 +03:00 [INF] HTTP GET /GoodsReceipt/Create responded 302 in 62.0905 ms
2025-07-29 23:58:36.441 +03:00 [INF] HTTP GET /Account/Login responded 200 in 215.8948 ms
2025-07-29 23:59:41.278 +03:00 [INF] HTTP GET /Account/Login responded 200 in 22.5866 ms
2025-07-29 23:59:58.856 +03:00 [INF] HTTP POST /Account/Login responded 302 in 901.6918 ms
