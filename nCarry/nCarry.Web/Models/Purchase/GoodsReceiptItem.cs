using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace nCarry.Web.Models.Purchase
{
    [Table("GoodsReceiptItem")]
    public class GoodsReceiptItem
    {
        [Key]
        [Column("ReceiptItemID")]
        public int GoodsReceiptItemID { get; set; }

        [Required]
        [Column("ReceiptID")]
        public int GoodsReceiptID { get; set; }

        public int LineNumber { get; set; }

        public int? PurchaseOrderItemID { get; set; }

        [Required]
        public int ProductID { get; set; }

        public int? VariantID { get; set; }

        [StringLength(500)]
        public string? Description { get; set; }

        [Required]
        public decimal ReceivedQuantity { get; set; }

        public decimal AcceptedQuantity { get; set; } = 0;

        public decimal RejectedQuantity { get; set; } = 0;

        [Required]
        public int UOMID { get; set; }

        [Required]
        public decimal UnitCost { get; set; }

        public decimal? UnitPrice { get; set; }

        [Required]
        [StringLength(255)]
        public string ProductName { get; set; } = string.Empty;

        public int? LocationID { get; set; }

        [StringLength(50)]
        public string? BatchNumber { get; set; }

        [StringLength(50)]
        public string? SerialNumber { get; set; }

        public DateTime? ExpiryDate { get; set; }

        [StringLength(20)]
        public string? QualityStatus { get; set; }

        [StringLength(500)]
        public string? RejectionReason { get; set; }

        [StringLength(500)]
        public string? Notes { get; set; }

        public int SortOrder { get; set; } = 100;

        // Tax information
        public decimal TaxRate { get; set; } = 20;

        public decimal TaxAmount { get; set; }

        public decimal LineTotal { get; set; }

        // Navigation properties
        [ForeignKey("GoodsReceiptID")]
        public virtual GoodsReceipt GoodsReceipt { get; set; } = null!;

        [ForeignKey("ProductID")]
        public virtual Products.Product Product { get; set; } = null!;

        [ForeignKey("UOMID")]
        public virtual Products.UnitOfMeasure UOM { get; set; } = null!;

        [ForeignKey("LocationID")]
        public virtual Warehouse.WarehouseLocation? Location { get; set; }
    }
}