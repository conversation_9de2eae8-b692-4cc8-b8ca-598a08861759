using System.ComponentModel.DataAnnotations;
using Microsoft.AspNetCore.Mvc.Rendering;

namespace nCarry.Web.Models.ViewModels
{
    public class SalesOrderCreateViewModel
    {
        [Required]
        [Display(Name = "Customer")]
        public int CustomerID { get; set; }

        [Display(Name = "Customer PO")]
        [StringLength(100)]
        public string? CustomerPO { get; set; }

        [Display(Name = "Quote")]
        public int? QuoteID { get; set; }

        [Display(Name = "Sales Rep")]
        public int? SalesRepID { get; set; }

        [Required]
        [Display(Name = "Order Type")]
        [StringLength(20)]
        public string OrderType { get; set; } = "Standard";

        [Display(Name = "Requested Date")]
        [DataType(DataType.Date)]
        public DateTime? RequestedDate { get; set; }

        [Display(Name = "Promised Date")]
        [DataType(DataType.Date)]
        public DateTime? PromisedDate { get; set; }

        [Display(Name = "Billing Address")]
        public int? BillingAddressID { get; set; }

        [Display(Name = "Shipping Address")]
        public int? ShippingAddressID { get; set; }

        [Display(Name = "Delivery Instructions")]
        public string? DeliveryInstructions { get; set; }

        [Display(Name = "Shipping Method")]
        [StringLength(100)]
        public string? ShippingMethod { get; set; }

        [Display(Name = "Freight Terms")]
        [StringLength(50)]
        public string? FreightTerms { get; set; }

        [Display(Name = "Payment Terms")]
        [StringLength(100)]
        public string? PaymentTerms { get; set; }

        [Display(Name = "Currency")]
        [StringLength(3)]
        public string Currency { get; set; } = "GBP";

        [Display(Name = "Exchange Rate")]
        [Range(0.01, 999999)]
        public decimal ExchangeRate { get; set; } = 1;

        [Display(Name = "Warehouse")]
        public int? WarehouseID { get; set; }

        [Display(Name = "Picking Priority")]
        [Range(1, 999)]
        public int PickingPriority { get; set; } = 100;

        [Display(Name = "Notes")]
        public string? Notes { get; set; }

        [Display(Name = "Internal Notes")]
        public string? InternalNotes { get; set; }

        // Select lists for dropdowns
        public SelectList? Customers { get; set; }
        public SelectList? Quotes { get; set; }
        public SelectList? SalesReps { get; set; }
        public SelectList? CustomerAddresses { get; set; }
        public SelectList? Warehouses { get; set; }
    }

    public class SalesOrderEditViewModel
    {
        [Required]
        public int OrderID { get; set; }

        [Required]
        [Display(Name = "Order Number")]
        [StringLength(50)]
        public string OrderNumber { get; set; } = string.Empty;

        [Required]
        [Display(Name = "Customer")]
        public int CustomerID { get; set; }

        [Display(Name = "Customer PO")]
        [StringLength(100)]
        public string? CustomerPO { get; set; }

        [Display(Name = "Quote")]
        public int? QuoteID { get; set; }

        [Display(Name = "Sales Rep")]
        public int? SalesRepID { get; set; }

        [Required]
        [Display(Name = "Order Status")]
        [StringLength(20)]
        public string OrderStatus { get; set; } = "Pending";

        [Required]
        [Display(Name = "Order Type")]
        [StringLength(20)]
        public string OrderType { get; set; } = "Standard";

        [Display(Name = "Requested Date")]
        [DataType(DataType.Date)]
        public DateTime? RequestedDate { get; set; }

        [Display(Name = "Promised Date")]
        [DataType(DataType.Date)]
        public DateTime? PromisedDate { get; set; }

        [Display(Name = "Billing Address")]
        public int? BillingAddressID { get; set; }

        [Display(Name = "Shipping Address")]
        public int? ShippingAddressID { get; set; }

        [Display(Name = "Delivery Instructions")]
        public string? DeliveryInstructions { get; set; }

        [Display(Name = "Shipping Method")]
        [StringLength(100)]
        public string? ShippingMethod { get; set; }

        [Display(Name = "Tracking Number")]
        [StringLength(100)]
        public string? TrackingNumber { get; set; }

        [Display(Name = "Freight Terms")]
        [StringLength(50)]
        public string? FreightTerms { get; set; }

        [Display(Name = "Payment Terms")]
        [StringLength(100)]
        public string? PaymentTerms { get; set; }

        [Display(Name = "Payment Due Date")]
        [DataType(DataType.Date)]
        public DateTime? PaymentDueDate { get; set; }

        [Display(Name = "Currency")]
        [StringLength(3)]
        public string Currency { get; set; } = "GBP";

        [Display(Name = "Exchange Rate")]
        [Range(0.01, 999999)]
        public decimal ExchangeRate { get; set; } = 1;

        [Display(Name = "Warehouse")]
        public int? WarehouseID { get; set; }

        [Display(Name = "Picking Priority")]
        [Range(1, 999)]
        public int PickingPriority { get; set; } = 100;

        [Display(Name = "Notes")]
        public string? Notes { get; set; }

        [Display(Name = "Internal Notes")]
        public string? InternalNotes { get; set; }

        // Order items
        public List<SalesOrderItemViewModel> Items { get; set; } = new List<SalesOrderItemViewModel>();

        // Select lists for dropdowns
        public SelectList? Customers { get; set; }
        public SelectList? Quotes { get; set; }
        public SelectList? SalesReps { get; set; }
        public SelectList? CustomerAddresses { get; set; }
        public SelectList? Warehouses { get; set; }
        public SelectList? Products { get; set; }
        public SelectList? UnitOfMeasures { get; set; }
    }

    public class SalesOrderItemViewModel
    {
        public int? OrderItemID { get; set; }
        public int OrderID { get; set; }
        public int LineNumber { get; set; }

        [Required]
        [Display(Name = "Product")]
        public int ProductID { get; set; }

        [Display(Name = "Variant")]
        public int? VariantID { get; set; }

        [Display(Name = "Description")]
        public string? Description { get; set; }

        [Required]
        [Display(Name = "Quantity")]
        [Range(0.001, 999999)]
        public decimal OrderedQuantity { get; set; }

        [Required]
        [Display(Name = "Unit")]
        public int UOMID { get; set; }

        [Required]
        [Display(Name = "Unit Price")]
        [Range(0, 999999)]
        public decimal UnitPrice { get; set; }

        [Display(Name = "Discount %")]
        [Range(0, 100)]
        public decimal DiscountPercent { get; set; } = 0;

        [Display(Name = "Discount Amount")]
        [Range(0, 999999)]
        public decimal DiscountAmount { get; set; } = 0;

        [Display(Name = "Tax Rate %")]
        [Range(0, 100)]
        public decimal TaxRate { get; set; } = 20;

        [Display(Name = "Warehouse")]
        public int? WarehouseID { get; set; }

        [Display(Name = "Notes")]
        [StringLength(500)]
        public string? Notes { get; set; }

        // Read-only calculated fields
        public decimal TaxAmount { get; set; }
        public decimal LineTotal { get; set; }
        public string? ProductCode { get; set; }
        public string? ProductName { get; set; }
        public string? UOMName { get; set; }

        // Select lists
        public SelectList? Products { get; set; }
        public SelectList? ProductVariants { get; set; }
        public SelectList? UnitOfMeasures { get; set; }
        public SelectList? Warehouses { get; set; }
    }
}