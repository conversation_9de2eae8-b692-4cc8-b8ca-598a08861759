using System.ComponentModel.DataAnnotations;

namespace nCarry.Web.Models.ViewModels
{
    public class SalesInvoiceCreateViewModel
    {
        [Display(Name = "Invoice Date")]
        [Required]
        public DateTime InvoiceDate { get; set; }

        [Display(Name = "Due Date")]
        [Required]
        public DateTime DueDate { get; set; }

        [Display(Name = "Customer")]
        [Required]
        public int CustomerID { get; set; }

        [Display(Name = "Sales Order")]
        public int? OrderID { get; set; }

        [Display(Name = "Invoice Type")]
        [Required]
        public string InvoiceType { get; set; } = "Sales Invoice";

        [Display(Name = "Currency")]
        [Required]
        public string Currency { get; set; } = "GBP";

        [Display(Name = "Exchange Rate")]
        [Required]
        public decimal ExchangeRate { get; set; } = 1.00m;

        [Display(Name = "Payment Terms")]
        public string? PaymentTerms { get; set; }

        [Display(Name = "Notes")]
        public string? Notes { get; set; }

        [Display(Name = "Internal Notes")]
        public string? InternalNotes { get; set; }

        [Display(Name = "Footer Text")]
        public string? FooterText { get; set; }

        [Display(Name = "Customer PO")]
        public string? CustomerPO { get; set; }

        [Display(Name = "Invoice Number")]
        public string? InvoiceNumber { get; set; }

        [Display(Name = "Customer Name")]
        public string? CustomerName { get; set; }

        [Display(Name = "Shipping Amount")]
        public decimal ShippingAmount { get; set; } = 0;

        public List<SalesInvoiceItemViewModel> Items { get; set; } = new List<SalesInvoiceItemViewModel>();
    }

    public class SalesInvoiceItemViewModel
    {
        public int ProductID { get; set; }
        public string ProductCode { get; set; } = string.Empty;
        public string ProductName { get; set; } = string.Empty;
        
        [Required]
        public decimal Quantity { get; set; }
        
        [Required]
        public int UOMID { get; set; }

        public string? UOMCode { get; set; }
        
        [Required]
        public decimal UnitPrice { get; set; }
        
        public decimal DiscountPercent { get; set; }
        public decimal DiscountAmount { get; set; }
        
        [Required]
        public decimal TaxRate { get; set; } = 20;
        
        public decimal TaxAmount { get; set; }
        public decimal LineTotal { get; set; }
    }

    public class SalesInvoiceEditViewModel : SalesInvoiceCreateViewModel
    {
        [Required]
        public int InvoiceID { get; set; }
    }
}