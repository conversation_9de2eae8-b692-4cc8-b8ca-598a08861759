using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace nCarry.Web.Models.Sales
{
    [Table("SalesInvoiceItem")]
    public class SalesInvoiceItem
    {
        [Key]
        public int InvoiceItemID { get; set; }

        [Required]
        public int InvoiceID { get; set; }

        public int LineNumber { get; set; }

        public int? OrderItemID { get; set; }

        [Required]
        public int ProductID { get; set; }

        public int? VariantID { get; set; }

        [StringLength(50)]
        public string? ProductCode { get; set; }
        
        [StringLength(200)]
        public string? ProductName { get; set; }
        
        [StringLength(500)]
        public string? Description { get; set; }
        
        public string ItemDescription => Description ?? ProductName ?? "";

        [Required]
        public decimal Quantity { get; set; }

        [Required]
        public int UOMID { get; set; }

        [Required]
        public decimal UnitPrice { get; set; }

        public decimal DiscountPercent { get; set; } = 0;

        public decimal DiscountAmount { get; set; } = 0;

        public decimal TaxRate { get; set; } = 0;

        public decimal TaxAmount { get; set; } = 0;

        public decimal LineTotal { get; set; } = 0;

        [StringLength(50)]
        public string? AccountCode { get; set; }

        [StringLength(255)]
        public string? Notes { get; set; }
        
        public int SortOrder { get; set; }

        public DateTime CreatedDate { get; set; } = DateTime.Now;

        public int? CreatedByUserID { get; set; }

        public DateTime? UpdatedDate { get; set; }

        public int? UpdatedByUserID { get; set; }

        // Navigation properties
        [ForeignKey("InvoiceID")]
        public virtual SalesInvoice SalesInvoice { get; set; } = null!;

        [ForeignKey("ProductID")]
        public virtual Products.Product Product { get; set; } = null!;

        [ForeignKey("UOMID")]
        public virtual Products.UnitOfMeasure UnitOfMeasure { get; set; } = null!;
    }
}