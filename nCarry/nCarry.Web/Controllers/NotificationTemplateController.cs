using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Rendering;
using Microsoft.EntityFrameworkCore;
using nCarry.Web.Data;
using nCarry.Web.Models.System;

namespace nCarry.Web.Controllers
{
    [Authorize]
    public class NotificationTemplateController : Controller
    {
        private readonly ApplicationDbContext _context;

        public NotificationTemplateController(ApplicationDbContext context)
        {
            _context = context;
        }

        // GET: NotificationTemplate
        public async Task<IActionResult> Index(string? category, string? type, bool? isActive)
        {
            var templatesQuery = _context.NotificationTemplates.AsQueryable();

            if (!string.IsNullOrEmpty(category))
            {
                templatesQuery = templatesQuery.Where(nt => nt.Category == category);
                ViewBag.SelectedCategory = category;
            }

            if (!string.IsNullOrEmpty(type))
            {
                templatesQuery = templatesQuery.Where(nt => nt.NotificationType == type);
                ViewBag.SelectedType = type;
            }

            if (isActive.HasValue)
            {
                templatesQuery = templatesQuery.Where(nt => nt.IsActive == isActive.Value);
                ViewBag.SelectedIsActive = isActive.Value;
            }

            var templates = await templatesQuery
                .OrderBy(nt => nt.Category)
                .ThenBy(nt => nt.NotificationType)
                .ThenBy(nt => nt.EventTrigger)
                .ToListAsync();

            // Populate filter dropdowns
            var categories = await _context.NotificationTemplates
                .Select(nt => nt.Category)
                .Distinct()
                .OrderBy(c => c)
                .ToListAsync();

            var types = await _context.NotificationTemplates
                .Select(nt => nt.NotificationType)
                .Distinct()
                .OrderBy(t => t)
                .ToListAsync();

            ViewBag.Categories = new SelectList(categories.Select(c => new { Value = c, Text = c }), "Value", "Text", category);
            ViewBag.Types = new SelectList(types.Select(t => new { Value = t, Text = t }), "Value", "Text", type);

            ViewBag.ActiveFilter = new List<SelectListItem>
            {
                new SelectListItem { Value = "", Text = "All" },
                new SelectListItem { Value = "true", Text = "Active", Selected = isActive == true },
                new SelectListItem { Value = "false", Text = "Inactive", Selected = isActive == false }
            };

            return View(templates);
        }

        // GET: NotificationTemplate/Details/5
        public async Task<IActionResult> Details(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var notificationTemplate = await _context.NotificationTemplates
                .FirstOrDefaultAsync(m => m.TemplateID == id);

            if (notificationTemplate == null)
            {
                return NotFound();
            }

            return View(notificationTemplate);
        }

        // GET: NotificationTemplate/Create
        public IActionResult Create()
        {
            PopulateDropdowns();
            
            var notificationTemplate = new NotificationTemplate
            {
                IsActive = true,
                Priority = "Medium"
            };

            return View(notificationTemplate);
        }

        // POST: NotificationTemplate/Create
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Create([Bind("Category,NotificationType,EventTrigger,Title,Message,Variables,Recipients,Priority,ExpiryHours,IsActive,Description")] NotificationTemplate notificationTemplate)
        {
            if (ModelState.IsValid)
            {
                // Check if template already exists for this combination
                if (await _context.NotificationTemplates.AnyAsync(nt => nt.Category == notificationTemplate.Category && 
                    nt.NotificationType == notificationTemplate.NotificationType && nt.EventTrigger == notificationTemplate.EventTrigger))
                {
                    ModelState.AddModelError("EventTrigger", "Notification template already exists for this category, type, and event combination.");
                    PopulateDropdowns();
                    return View(notificationTemplate);
                }

                notificationTemplate.CreatedDate = DateTime.Now;
                notificationTemplate.UpdatedDate = DateTime.Now;

                _context.Add(notificationTemplate);
                await _context.SaveChangesAsync();
                
                TempData["Success"] = "Notification template created successfully.";
                return RedirectToAction(nameof(Index));
            }

            PopulateDropdowns();
            return View(notificationTemplate);
        }

        // GET: NotificationTemplate/Edit/5
        public async Task<IActionResult> Edit(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var notificationTemplate = await _context.NotificationTemplates.FindAsync(id);
            if (notificationTemplate == null)
            {
                return NotFound();
            }

            PopulateDropdowns();
            return View(notificationTemplate);
        }

        // POST: NotificationTemplate/Edit/5
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Edit(int id, [Bind("TemplateID,Category,NotificationType,EventTrigger,Title,Message,Variables,Recipients,Priority,ExpiryHours,IsActive,Description,CreatedDate,CreatedByUserID")] NotificationTemplate notificationTemplate)
        {
            if (id != notificationTemplate.TemplateID)
            {
                return NotFound();
            }

            if (ModelState.IsValid)
            {
                try
                {
                    // Check if template already exists for this combination (excluding current record)
                    if (await _context.NotificationTemplates.AnyAsync(nt => nt.Category == notificationTemplate.Category && 
                        nt.NotificationType == notificationTemplate.NotificationType && nt.EventTrigger == notificationTemplate.EventTrigger && nt.TemplateID != id))
                    {
                        ModelState.AddModelError("EventTrigger", "Notification template already exists for this category, type, and event combination.");
                        PopulateDropdowns();
                        return View(notificationTemplate);
                    }

                    notificationTemplate.UpdatedDate = DateTime.Now;
                    _context.Update(notificationTemplate);
                    await _context.SaveChangesAsync();
                    
                    TempData["Success"] = "Notification template updated successfully.";
                }
                catch (DbUpdateConcurrencyException)
                {
                    if (!NotificationTemplateExists(notificationTemplate.TemplateID))
                    {
                        return NotFound();
                    }
                    else
                    {
                        throw;
                    }
                }
                return RedirectToAction(nameof(Index));
            }

            PopulateDropdowns();
            return View(notificationTemplate);
        }

        // GET: NotificationTemplate/Delete/5
        public async Task<IActionResult> Delete(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var notificationTemplate = await _context.NotificationTemplates
                .FirstOrDefaultAsync(m => m.TemplateID == id);
                
            if (notificationTemplate == null)
            {
                return NotFound();
            }

            return View(notificationTemplate);
        }

        // POST: NotificationTemplate/Delete/5
        [HttpPost, ActionName("Delete")]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> DeleteConfirmed(int id)
        {
            var notificationTemplate = await _context.NotificationTemplates.FindAsync(id);
            if (notificationTemplate != null)
            {
                _context.NotificationTemplates.Remove(notificationTemplate);
                await _context.SaveChangesAsync();
                
                TempData["Success"] = "Notification template deleted successfully.";
            }

            return RedirectToAction(nameof(Index));
        }

        // GET: NotificationTemplate/Preview/5
        public async Task<IActionResult> Preview(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var notificationTemplate = await _context.NotificationTemplates.FindAsync(id);
            if (notificationTemplate == null)
            {
                return NotFound();
            }

            // Generate sample data for preview
            var sampleData = GenerateSampleData(notificationTemplate.Category);
            var previewTitle = ReplaceVariables(notificationTemplate.Title ?? string.Empty, sampleData);
            var previewMessage = ReplaceVariables(notificationTemplate.Message ?? string.Empty, sampleData);

            ViewBag.PreviewTitle = previewTitle;
            ViewBag.PreviewMessage = previewMessage;
            ViewBag.SampleData = sampleData;

            return View(notificationTemplate);
        }

        // POST: NotificationTemplate/TestSend
        [HttpPost]
        public async Task<IActionResult> TestSend(int templateId, string testUser)
        {
            if (string.IsNullOrEmpty(testUser))
            {
                return Json(new { success = false, message = "Please provide a test user." });
            }

            var template = await _context.NotificationTemplates.FindAsync(templateId);
            if (template == null)
            {
                return Json(new { success = false, message = "Template not found." });
            }

            try
            {
                // Generate sample data and create test notification
                var sampleData = GenerateSampleData(template.Category);
                var title = ReplaceVariables(template.Title ?? string.Empty, sampleData);
                var message = ReplaceVariables(template.Message ?? string.Empty, sampleData);

                // Here you would integrate with your notification service
                // await _notificationService.SendNotificationAsync(testUser, title, message, template.NotificationType);

                return Json(new { success = true, message = "Test notification sent successfully." });
            }
            catch (Exception ex)
            {
                return Json(new { success = false, message = $"Failed to send test notification: {ex.Message}" });
            }
        }

        // GET: NotificationTemplate/LoadDefaults
        public async Task<IActionResult> LoadDefaults()
        {
            await LoadDefaultNotificationTemplates();
            TempData["Success"] = "Default notification templates loaded successfully.";
            return RedirectToAction(nameof(Index));
        }

        // GET: NotificationTemplate/Variables
        public IActionResult Variables(string category)
        {
            var variables = GetAvailableVariables(category);
            return Json(variables);
        }

        private Dictionary<string, string> GenerateSampleData(string category)
        {
            var sampleData = new Dictionary<string, string>
            {
                {"CompanyName", "nCarry Wholesale"},
                {"CurrentDate", DateTime.Now.ToString("dd/MM/yyyy")},
                {"CurrentTime", DateTime.Now.ToString("HH:mm")},
                {"UserName", "admin"}
            };

            switch (category.ToLower())
            {
                case "sales":
                    var salesSample = new Dictionary<string, string>
                    {
                        {"CustomerName", "John Smith"},
                        {"OrderNumber", "SO20240629001"},
                        {"OrderTotal", "$1,250.00"},
                        {"OrderStatus", "Confirmed"}
                    };
                    foreach (var kvp in salesSample)
                        sampleData[kvp.Key] = kvp.Value;
                    break;

                case "inventory":
                    var inventorySample = new Dictionary<string, string>
                    {
                        {"ProductName", "Sample Product"},
                        {"ProductCode", "P001234"},
                        {"CurrentStock", "5"},
                        {"MinimumStock", "10"},
                        {"WarehouseName", "Main Warehouse"}
                    };
                    foreach (var kvp in inventorySample)
                        sampleData[kvp.Key] = kvp.Value;
                    break;

                case "shipment":
                    var shipmentSample = new Dictionary<string, string>
                    {
                        {"TrackingNumber", "SH20240629001"},
                        {"CarrierName", "Express Logistics"},
                        {"RecipientName", "Jane Doe"},
                        {"DeliveryStatus", "Out for Delivery"}
                    };
                    foreach (var kvp in shipmentSample)
                        sampleData[kvp.Key] = kvp.Value;
                    break;

                case "system":
                    var systemSample = new Dictionary<string, string>
                    {
                        {"AlertType", "System Warning"},
                        {"ErrorMessage", "Connection timeout"},
                        {"SystemComponent", "Database"},
                        {"Severity", "Medium"}
                    };
                    foreach (var kvp in systemSample)
                        sampleData[kvp.Key] = kvp.Value;
                    break;
            }

            return sampleData;
        }

        private string ReplaceVariables(string content, Dictionary<string, string> variables)
        {
            if (string.IsNullOrEmpty(content))
                return content;

            foreach (var variable in variables)
            {
                content = content.Replace($"{{{variable.Key}}}", variable.Value);
            }

            return content;
        }

        private Dictionary<string, string> GetAvailableVariables(string category)
        {
            var variables = new Dictionary<string, string>
            {
                {"CompanyName", "Company name"},
                {"CurrentDate", "Current date"},
                {"CurrentTime", "Current time"},
                {"UserName", "User name"}
            };

            switch (category?.ToLower())
            {
                case "sales":
                    var salesVars = new Dictionary<string, string>
                    {
                        {"CustomerName", "Customer name"},
                        {"OrderNumber", "Order number"},
                        {"OrderTotal", "Order total amount"},
                        {"OrderStatus", "Order status"}
                    };
                    foreach (var kvp in salesVars)
                        variables[kvp.Key] = kvp.Value;
                    break;

                case "inventory":
                    var inventoryVars = new Dictionary<string, string>
                    {
                        {"ProductName", "Product name"},
                        {"ProductCode", "Product code"},
                        {"CurrentStock", "Current stock level"},
                        {"MinimumStock", "Minimum stock level"},
                        {"WarehouseName", "Warehouse name"}
                    };
                    foreach (var kvp in inventoryVars)
                        variables[kvp.Key] = kvp.Value;
                    break;

                case "shipment":
                    var shipmentVars = new Dictionary<string, string>
                    {
                        {"TrackingNumber", "Tracking number"},
                        {"CarrierName", "Carrier name"},
                        {"RecipientName", "Recipient name"},
                        {"DeliveryStatus", "Delivery status"}
                    };
                    foreach (var kvp in shipmentVars)
                        variables[kvp.Key] = kvp.Value;
                    break;

                case "system":
                    var systemVars = new Dictionary<string, string>
                    {
                        {"AlertType", "Alert type"},
                        {"ErrorMessage", "Error message"},
                        {"SystemComponent", "System component"},
                        {"Severity", "Severity level"}
                    };
                    foreach (var kvp in systemVars)
                        variables[kvp.Key] = kvp.Value;
                    break;
            }

            return variables;
        }

        private async Task LoadDefaultNotificationTemplates()
        {
            var defaultTemplates = new List<NotificationTemplate>
            {
                // Sales Notifications
                new NotificationTemplate 
                { 
                    Category = "Sales", 
                    NotificationType = "Info", 
                    EventTrigger = "OrderCreated", 
                    Title = "New Order Created - {OrderNumber}", 
                    Message = "A new order {OrderNumber} has been created for customer {CustomerName} with total amount {OrderTotal}.", 
                    Variables = "OrderNumber,CustomerName,OrderTotal", 
                    Recipients = "Sales Team", 
                    Priority = "Medium", 
                    ExpiryHours = 24, 
                    IsActive = true, 
                    Description = "Notification when a new sales order is created" 
                },

                // Inventory Notifications
                new NotificationTemplate 
                { 
                    Category = "Inventory", 
                    NotificationType = "Warning", 
                    EventTrigger = "LowStock", 
                    Title = "Low Stock Alert - {ProductName}", 
                    Message = "Product {ProductName} ({ProductCode}) in {WarehouseName} is running low. Current stock: {CurrentStock}, Minimum: {MinimumStock}.", 
                    Variables = "ProductName,ProductCode,WarehouseName,CurrentStock,MinimumStock", 
                    Recipients = "Inventory Team", 
                    Priority = "High", 
                    ExpiryHours = 48, 
                    IsActive = true, 
                    Description = "Notification when product stock is below minimum level" 
                },

                // Shipment Notifications
                new NotificationTemplate 
                { 
                    Category = "Shipment", 
                    NotificationType = "Info", 
                    EventTrigger = "ShipmentDelivered", 
                    Title = "Shipment Delivered - {TrackingNumber}", 
                    Message = "Shipment {TrackingNumber} has been successfully delivered to {RecipientName} via {CarrierName}.", 
                    Variables = "TrackingNumber,RecipientName,CarrierName", 
                    Recipients = "Customer Service", 
                    Priority = "Low", 
                    ExpiryHours = 72, 
                    IsActive = true, 
                    Description = "Notification when a shipment is delivered" 
                },

                // System Notifications
                new NotificationTemplate 
                { 
                    Category = "System", 
                    NotificationType = "Error", 
                    EventTrigger = "SystemError", 
                    Title = "System Alert - {AlertType}", 
                    Message = "System component {SystemComponent} reported an error: {ErrorMessage}. Severity: {Severity}.", 
                    Variables = "AlertType,SystemComponent,ErrorMessage,Severity", 
                    Recipients = "IT Team", 
                    Priority = "High", 
                    ExpiryHours = 12, 
                    IsActive = true, 
                    Description = "Notification for system errors and alerts" 
                }
            };

            // Remove existing templates
            var existingTemplates = await _context.NotificationTemplates.ToListAsync();
            _context.NotificationTemplates.RemoveRange(existingTemplates);

            // Add new templates
            foreach (var template in defaultTemplates)
            {
                template.CreatedDate = DateTime.Now;
                template.UpdatedDate = DateTime.Now;
            }

            _context.NotificationTemplates.AddRange(defaultTemplates);
            await _context.SaveChangesAsync();
        }

        private bool NotificationTemplateExists(int id)
        {
            return _context.NotificationTemplates.Any(e => e.TemplateID == id);
        }

        private void PopulateDropdowns()
        {
            ViewBag.Categories = new List<SelectListItem>
            {
                new SelectListItem { Value = "Sales", Text = "Sales" },
                new SelectListItem { Value = "Purchase", Text = "Purchase" },
                new SelectListItem { Value = "Inventory", Text = "Inventory" },
                new SelectListItem { Value = "Shipment", Text = "Shipment" },
                new SelectListItem { Value = "System", Text = "System" },
                new SelectListItem { Value = "Financial", Text = "Financial" },
                new SelectListItem { Value = "User", Text = "User" }
            };

            ViewBag.NotificationTypes = new List<SelectListItem>
            {
                new SelectListItem { Value = "Info", Text = "Information" },
                new SelectListItem { Value = "Warning", Text = "Warning" },
                new SelectListItem { Value = "Error", Text = "Error" },
                new SelectListItem { Value = "Success", Text = "Success" }
            };

            ViewBag.Priorities = new List<SelectListItem>
            {
                new SelectListItem { Value = "Low", Text = "Low" },
                new SelectListItem { Value = "Medium", Text = "Medium" },
                new SelectListItem { Value = "High", Text = "High" },
                new SelectListItem { Value = "Critical", Text = "Critical" }
            };

            ViewBag.EventTriggers = new List<SelectListItem>
            {
                new SelectListItem { Value = "OrderCreated", Text = "Order Created" },
                new SelectListItem { Value = "OrderConfirmed", Text = "Order Confirmed" },
                new SelectListItem { Value = "OrderShipped", Text = "Order Shipped" },
                new SelectListItem { Value = "OrderDelivered", Text = "Order Delivered" },
                new SelectListItem { Value = "LowStock", Text = "Low Stock" },
                new SelectListItem { Value = "OutOfStock", Text = "Out of Stock" },
                new SelectListItem { Value = "StockReceived", Text = "Stock Received" },
                new SelectListItem { Value = "ShipmentCreated", Text = "Shipment Created" },
                new SelectListItem { Value = "ShipmentDelivered", Text = "Shipment Delivered" },
                new SelectListItem { Value = "SystemError", Text = "System Error" },
                new SelectListItem { Value = "UserLogin", Text = "User Login" },
                new SelectListItem { Value = "UserLogout", Text = "User Logout" }
            };
        }
    }
}