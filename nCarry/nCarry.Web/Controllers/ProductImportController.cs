using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using nCarry.Web.Data;
using nCarry.Web.Models.Products;
using System.Text;

namespace nCarry.Web.Controllers
{
    [Authorize]
    public class ProductImportController : Controller
    {
        private readonly ApplicationDbContext _context;

        public ProductImportController(ApplicationDbContext context)
        {
            _context = context;
        }

        // GET: ProductImport
        public IActionResult Index()
        {
            return View();
        }

        // POST: ProductImport/ImportFrozenSeafood
        [HttpPost]
        public async Task<IActionResult> ImportFrozenSeafood()
        {
            try
            {
                // First, ensure we have the Frozen Seafood category
                var category = await _context.ProductCategories
                    .FirstOrDefaultAsync(c => c.CategoryCode == "SEAFOOD");

                if (category == null)
                {
                    category = new ProductCategory
                    {
                        CategoryCode = "SEAFOOD",
                        CategoryName = "Frozen Seafood",
                        Description = "Frozen seafood products including fish, prawns, and shellfish",
                        IsActive = true,
                        CreatedDate = DateTime.Now,
                        UpdatedDate = DateTime.Now
                    };
                    _context.ProductCategories.Add(category);
                    await _context.SaveChangesAsync();
                }

                // Get UOM IDs
                var boxUOM = await _context.UnitOfMeasures.FirstOrDefaultAsync(u => u.UOMCode == "BOX");
                var palletUOM = await _context.UnitOfMeasures.FirstOrDefaultAsync(u => u.UOMCode == "PALLET");
                var eachUOM = await _context.UnitOfMeasures.FirstOrDefaultAsync(u => u.UOMCode == "EA");

                if (boxUOM == null || palletUOM == null || eachUOM == null)
                {
                    TempData["Error"] = "Required units of measure not found. Please ensure BOX, PALLET, and EA units exist.";
                    return RedirectToAction(nameof(Index));
                }

                // Products to import
                var products = new[]
                {
                    new { Name = "Black Cod", Code = "BCOD001", UOM = "PALLET", Quantity = "1" },
                    new { Name = "Baby Squid 10-20", Code = "BSQD001", UOM = "BOX", Quantity = "8" },
                    new { Name = "Baby Squid 20-40", Code = "BSQD002", UOM = "PALLET", Quantity = "1.5" },
                    new { Name = "Battered Cod 170/220 Pacific", Code = "BATCOD001", UOM = "BOX", Quantity = "29" },
                    new { Name = "Blanch Bait", Code = "BLBAIT001", UOM = "PALLET", Quantity = "3" },
                    new { Name = "Breaded Butterfly Prawns", Code = "BPRAWN001", UOM = "PALLET", Quantity = "0.5" },
                    new { Name = "Breaded Scampi", Code = "BSCAMPI001", UOM = "PALLET", Quantity = "1" },
                    new { Name = "Breaded Tempura", Code = "BTEMP001", UOM = "BOX", Quantity = "21" },
                    new { Name = "Breaded Torpedo", Code = "BTORP001", UOM = "PALLET", Quantity = "1" },
                    new { Name = "Californian Baby Squid", Code = "CALSQD001", UOM = "BOX", Quantity = "16" },
                    new { Name = "Chili Mango", Code = "CHMANGO001", UOM = "BOX", Quantity = "25" },
                    new { Name = "Clams Meat", Code = "CLAM001", UOM = "BOX", Quantity = "2" },
                    new { Name = "Cocktail", Code = "COCK001", UOM = "PALLET", Quantity = "4.5" },
                    new { Name = "Cocktail XL", Code = "COCKXL001", UOM = "BOX", Quantity = "24" },
                    new { Name = "Doversole", Code = "DSOLE001", UOM = "BOX", Quantity = "5" },
                    new { Name = "Fish Finger", Code = "FFING001", UOM = "BOX", Quantity = "9" },
                    new { Name = "Fresh Sword Fish", Code = "SWORD001", UOM = "EA", Quantity = "0" },
                    new { Name = "Haddock", Code = "HADD001", UOM = "BOX", Quantity = "30" },
                    new { Name = "Halibut Steak", Code = "HALI001", UOM = "BOX", Quantity = "7" },
                    new { Name = "Head On Shell On 4-6", Code = "HOSO46", UOM = "BOX", Quantity = "10" },
                    new { Name = "Head On Shell On 6-8", Code = "HOSO68", UOM = "BOX", Quantity = "32" },
                    new { Name = "Head On Shell On 8-12", Code = "HOSO812", UOM = "PALLET", Quantity = "1" },
                    new { Name = "Head On Shell On 13-15", Code = "HOSO1315", UOM = "BOX", Quantity = "18" },
                    new { Name = "Head On Shell On 16-20", Code = "HOSO1620", UOM = "PALLET", Quantity = "1.3" },
                    new { Name = "HeadLees 6-8", Code = "HL68", UOM = "PALLET", Quantity = "1" },
                    new { Name = "HeadLees 8-12", Code = "HL812", UOM = "PALLET", Quantity = "0.5" },
                    new { Name = "Holmes Cocktail 90-130", Code = "HCOCK001", UOM = "BOX", Quantity = "5" },
                    new { Name = "Lobster Meat", Code = "LOBM001", UOM = "BOX", Quantity = "21" },
                    new { Name = "Lobster Tail 6 oz", Code = "LOBT001", UOM = "BOX", Quantity = "8" },
                    new { Name = "Octopus 2-3", Code = "OCT23", UOM = "PALLET", Quantity = "1" },
                    new { Name = "Octopus 3-4", Code = "OCT34", UOM = "PALLET", Quantity = "1.5" },
                    new { Name = "Pankocot Squid Rings", Code = "PSQR001", UOM = "PALLET", Quantity = "2" },
                    new { Name = "Rawpeeled 8-12", Code = "RP812", UOM = "BOX", Quantity = "34" },
                    new { Name = "Rawpeeled 13-15", Code = "RP1315", UOM = "BOX", Quantity = "4" },
                    new { Name = "Rawpeeled 16-20", Code = "RP1620", UOM = "PALLET", Quantity = "0.5" },
                    new { Name = "Rawpeeled 26-30", Code = "RP2630", UOM = "PALLET", Quantity = "1" },
                    new { Name = "Rawpeeled Pacific West 8-12", Code = "RPPW812", UOM = "PALLET", Quantity = "0.5" },
                    new { Name = "Raw Peeled Tail On 13-15", Code = "RPTO1315", UOM = "PALLET", Quantity = "0.5" },
                    new { Name = "Raw Peeled Pacific 16/20", Code = "RPP1620", UOM = "BOX", Quantity = "13" },
                    new { Name = "Raw Peeled 31/40", Code = "RP3140", UOM = "BOX", Quantity = "7" },
                    new { Name = "Salt & Pepper Pacific West", Code = "SPPW001", UOM = "BOX", Quantity = "32" },
                    new { Name = "Salt & Pepper Little Fisher", Code = "SPLF001", UOM = "BOX", Quantity = "13" },
                    new { Name = "Squid Tubes Aqua Hawk", Code = "STAH001", UOM = "PALLET", Quantity = "2" },
                    new { Name = "Squid Tubes Gigas Clear Sea", Code = "STGCS001", UOM = "PALLET", Quantity = "6" },
                    new { Name = "Squid Tubes Little Fisher", Code = "STLF001", UOM = "PALLET", Quantity = "2" },
                    new { Name = "Vacuum Clams", Code = "VCLAM001", UOM = "PALLET", Quantity = "0.5" },
                    new { Name = "Vacuum Mussels", Code = "VMUSS001", UOM = "PALLET", Quantity = "2.5" },
                    new { Name = "Thor Cod 8-16", Code = "TCOD816", UOM = "BOX", Quantity = "6" },
                    new { Name = "Thor Cod 16-32", Code = "TCOD1632", UOM = "PALLET", Quantity = "3" },
                    new { Name = "Thor Cod 32+", Code = "TCOD32", UOM = "BOX", Quantity = "14" },
                    new { Name = "Gadus Cod 16-32", Code = "GCOD1632", UOM = "PALLET", Quantity = "2" },
                    new { Name = "Whole Sardines", Code = "WSARD001", UOM = "BOX", Quantity = "12" },
                    new { Name = "Salmon Fillet", Code = "SALMF001", UOM = "BOX", Quantity = "160" },
                    new { Name = "Whole Salmon", Code = "WSALM001", UOM = "BOX", Quantity = "31" },
                    new { Name = "Bass Fillet 100-140", Code = "BASSF100", UOM = "BOX", Quantity = "107" },
                    new { Name = "Bass Fillet 140-180", Code = "BASSF140", UOM = "BOX", Quantity = "38" },
                    new { Name = "Bass Fillet 180-220", Code = "BASSF180", UOM = "BOX", Quantity = "33" },
                    new { Name = "Sea Bream Fillet 60-90", Code = "SBRF60", UOM = "BOX", Quantity = "10" },
                    new { Name = "Sea Bream Fillet 90-130", Code = "SBRF90", UOM = "BOX", Quantity = "11" },
                    new { Name = "Sea Bream Fillet 130-170", Code = "SBRF130", UOM = "BOX", Quantity = "4" },
                    new { Name = "Haddock 7-8", Code = "HADD78", UOM = "BOX", Quantity = "180" },
                    new { Name = "Haddock 8-10", Code = "HADD810", UOM = "BOX", Quantity = "102" },
                    new { Name = "Haddock 10-12", Code = "HADD1012", UOM = "BOX", Quantity = "52" }
                };

                int importedCount = 0;
                var sb = new StringBuilder();

                foreach (var item in products)
                {
                    // Check if product already exists
                    var existingProduct = await _context.Products
                        .FirstOrDefaultAsync(p => p.ProductCode == item.Code);

                    if (existingProduct == null)
                    {
                        // Determine UOM ID
                        int uomId = item.UOM switch
                        {
                            "BOX" => boxUOM.UOMID,
                            "PALLET" => palletUOM.UOMID,
                            _ => eachUOM.UOMID
                        };

                        // Generate random prices (you can adjust these ranges)
                        decimal standardCost = item.UOM switch
                        {
                            "PALLET" => Random.Shared.Next(800, 2500),
                            "BOX" => Random.Shared.Next(50, 300),
                            _ => Random.Shared.Next(10, 100)
                        };
                        decimal listPrice = standardCost * 1.25m;
                        decimal sellPrice = standardCost * 1.35m;

                        var product = new Product
                        {
                            ProductCode = item.Code,
                            ProductName = item.Name,
                            Description = $"Premium frozen {item.Name}",
                            CategoryID = category.CategoryID,
                            ProductType = "Standard",
                            Status = "Active",
                            TrackInventory = true,
                            InventoryUOMID = uomId,
                            PurchaseUOMID = uomId,
                            SalesUOMID = uomId,
                            StandardCost = standardCost,
                            ListPrice = listPrice,
                            SellPrice = sellPrice,
                            Currency = "GBP",
                            MinStockLevel = 5,
                            ReorderPoint = 10,
                            ReorderQuantity = decimal.Parse(item.Quantity),
                            LeadTimeDays = 7,
                            CountryOfOrigin = "Various",
                            IsActive = true,
                            CreatedDate = DateTime.Now,
                            UpdatedDate = DateTime.Now
                        };

                        _context.Products.Add(product);
                        importedCount++;
                        sb.AppendLine($"Imported: {item.Name} ({item.Code})");
                    }
                    else
                    {
                        sb.AppendLine($"Skipped (already exists): {item.Name} ({item.Code})");
                    }
                }

                await _context.SaveChangesAsync();

                TempData["Success"] = $"Import completed. {importedCount} products imported.";
                ViewBag.ImportLog = sb.ToString();
                
                return View("ImportResult", sb.ToString());
            }
            catch (Exception ex)
            {
                TempData["Error"] = $"Error during import: {ex.Message}";
                return RedirectToAction(nameof(Index));
            }
        }

        // POST: ProductImport/CreateTestPurchaseOrder
        [HttpPost]
        public async Task<IActionResult> CreateTestPurchaseOrder()
        {
            try
            {
                // Get a supplier
                var supplier = await _context.Suppliers
                    .FirstOrDefaultAsync(s => s.IsActive);

                if (supplier == null)
                {
                    TempData["Error"] = "No active supplier found. Please create a supplier first.";
                    return RedirectToAction(nameof(Index));
                }

                // Get some seafood products
                var products = await _context.Products
                    .Include(p => p.Category)
                    .Where(p => p.Category.CategoryCode == "SEAFOOD" && p.IsActive)
                    .Take(10)
                    .ToListAsync();

                if (!products.Any())
                {
                    TempData["Error"] = "No seafood products found. Please import products first.";
                    return RedirectToAction(nameof(Index));
                }

                // Redirect to PurchaseOrder/Create with selected supplier
                TempData["Info"] = $"Creating purchase order for {supplier.SupplierName} with {products.Count} seafood products.";
                return RedirectToAction("Create", "PurchaseOrder", new { supplierId = supplier.SupplierID });
            }
            catch (Exception ex)
            {
                TempData["Error"] = $"Error creating purchase order: {ex.Message}";
                return RedirectToAction(nameof(Index));
            }
        }
    }
}