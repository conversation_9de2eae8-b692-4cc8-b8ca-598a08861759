using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Rendering;
using Microsoft.EntityFrameworkCore;
using nCarry.Web.Data;
using nCarry.Web.Models.System;
using System.Security.Cryptography;
using System.Text;

namespace nCarry.Web.Controllers
{
    [Authorize]
    public class APIKeyController : Controller
    {
        private readonly ApplicationDbContext _context;

        public APIKeyController(ApplicationDbContext context)
        {
            _context = context;
        }

        // GET: APIKey
        public async Task<IActionResult> Index(string? service, bool? isActive, string? searchTerm)
        {
            var apiKeysQuery = _context.APIKeys
                .Include(ak => ak.Company)
                .AsQueryable();

            if (!string.IsNullOrEmpty(service))
            {
                apiKeysQuery = apiKeysQuery.Where(ak => ak.Service == service);
                ViewBag.SelectedService = service;
            }

            if (isActive.HasValue)
            {
                apiKeysQuery = apiKeysQuery.Where(ak => ak.IsActive == isActive.Value);
                ViewBag.SelectedIsActive = isActive.Value;
            }

            if (!string.IsNullOrEmpty(searchTerm))
            {
                apiKeysQuery = apiKeysQuery.Where(ak => 
                    ak.KeyName.Contains(searchTerm) || 
                    (ak.Description != null && ak.Description.Contains(searchTerm)) ||
                    (ak.Company != null && ak.Company.CompanyName.Contains(searchTerm)));
                ViewBag.SearchTerm = searchTerm;
            }

            var apiKeys = await apiKeysQuery
                .OrderBy(ak => ak.Service)
                .ThenBy(ak => ak.KeyName)
                .ToListAsync();

            // Populate filter dropdowns
            var services = await _context.APIKeys
                .Select(ak => ak.Service)
                .Distinct()
                .OrderBy(s => s)
                .ToListAsync();

            ViewBag.Services = new SelectList(services.Select(s => new { Value = s, Text = s }), "Value", "Text", service);

            ViewBag.ActiveFilter = new List<SelectListItem>
            {
                new SelectListItem { Value = "", Text = "All" },
                new SelectListItem { Value = "true", Text = "Active", Selected = isActive == true },
                new SelectListItem { Value = "false", Text = "Inactive", Selected = isActive == false }
            };

            return View(apiKeys);
        }

        // GET: APIKey/Details/5
        public async Task<IActionResult> Details(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var apiKey = await _context.APIKeys
                .Include(ak => ak.Company)
                .FirstOrDefaultAsync(m => m.APIKeyID == id);

            if (apiKey == null)
            {
                return NotFound();
            }

            // Get usage statistics
            var usageStats = await GetUsageStatistics(id.Value);
            ViewBag.UsageStats = usageStats;

            return View(apiKey);
        }

        // GET: APIKey/Create
        public IActionResult Create()
        {
            PopulateDropdowns();
            
            var apiKey = new APIKey
            {
                IsActive = true,
                RateLimitPerHour = 1000,
                ExpiryDate = DateTime.Now.AddYears(1)
            };

            return View(apiKey);
        }

        // POST: APIKey/Create
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Create([Bind("CompanyID,Service,KeyName,Description,Permissions,RateLimitPerHour,IPWhitelist,ExpiryDate,IsActive")] APIKey apiKey)
        {
            if (ModelState.IsValid)
            {
                // Generate API Key
                apiKey.KeyValue = GenerateAPIKey();
                apiKey.KeyHash = HashAPIKey(apiKey.KeyValue);
                apiKey.CreatedDate = DateTime.Now;
                apiKey.LastUsedDate = null;

                _context.Add(apiKey);
                await _context.SaveChangesAsync();
                
                TempData["Success"] = "API Key created successfully. Key: " + apiKey.KeyValue;
                TempData["NewAPIKey"] = apiKey.KeyValue; // Store for display
                return RedirectToAction(nameof(Details), new { id = apiKey.APIKeyID });
            }

            PopulateDropdowns();
            return View(apiKey);
        }

        // GET: APIKey/Edit/5
        public async Task<IActionResult> Edit(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var apiKey = await _context.APIKeys.FindAsync(id);
            if (apiKey == null)
            {
                return NotFound();
            }

            PopulateDropdowns();
            return View(apiKey);
        }

        // POST: APIKey/Edit/5
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Edit(int id, [Bind("APIKeyID,CompanyID,Service,KeyName,Description,Permissions,RateLimitPerHour,IPWhitelist,ExpiryDate,IsActive,CreatedDate,KeyHash")] APIKey apiKey)
        {
            if (id != apiKey.APIKeyID)
            {
                return NotFound();
            }

            if (ModelState.IsValid)
            {
                try
                {
                    // Preserve the original KeyValue and LastUsedDate
                    var original = await _context.APIKeys.AsNoTracking().FirstOrDefaultAsync(ak => ak.APIKeyID == id);
                    if (original != null)
                    {
                        apiKey.KeyValue = original.KeyValue;
                        apiKey.LastUsedDate = original.LastUsedDate;
                    }

                    _context.Update(apiKey);
                    await _context.SaveChangesAsync();
                    
                    TempData["Success"] = "API Key updated successfully.";
                }
                catch (DbUpdateConcurrencyException)
                {
                    if (!APIKeyExists(apiKey.APIKeyID))
                    {
                        return NotFound();
                    }
                    else
                    {
                        throw;
                    }
                }
                return RedirectToAction(nameof(Index));
            }

            PopulateDropdowns();
            return View(apiKey);
        }

        // GET: APIKey/Delete/5
        public async Task<IActionResult> Delete(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var apiKey = await _context.APIKeys
                .Include(ak => ak.Company)
                .FirstOrDefaultAsync(m => m.APIKeyID == id);
                
            if (apiKey == null)
            {
                return NotFound();
            }

            return View(apiKey);
        }

        // POST: APIKey/Delete/5
        [HttpPost, ActionName("Delete")]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> DeleteConfirmed(int id)
        {
            var apiKey = await _context.APIKeys.FindAsync(id);
            if (apiKey != null)
            {
                _context.APIKeys.Remove(apiKey);
                await _context.SaveChangesAsync();
                
                TempData["Success"] = "API Key deleted successfully.";
            }

            return RedirectToAction(nameof(Index));
        }

        // POST: APIKey/Regenerate
        [HttpPost]
        public async Task<IActionResult> Regenerate(int id)
        {
            var apiKey = await _context.APIKeys.FindAsync(id);
            if (apiKey == null)
            {
                return Json(new { success = false, message = "API Key not found." });
            }

            // Generate new API Key
            apiKey.KeyValue = GenerateAPIKey();
            apiKey.KeyHash = HashAPIKey(apiKey.KeyValue);
            apiKey.CreatedDate = DateTime.Now;
            apiKey.LastUsedDate = null;

            _context.Update(apiKey);
            await _context.SaveChangesAsync();

            return Json(new { success = true, message = "API Key regenerated successfully.", newKey = apiKey.KeyValue });
        }

        // POST: APIKey/ToggleStatus
        [HttpPost]
        public async Task<IActionResult> ToggleStatus(int id)
        {
            var apiKey = await _context.APIKeys.FindAsync(id);
            if (apiKey == null)
            {
                return Json(new { success = false, message = "API Key not found." });
            }

            apiKey.IsActive = !apiKey.IsActive;
            _context.Update(apiKey);
            await _context.SaveChangesAsync();

            return Json(new { success = true, isActive = apiKey.IsActive });
        }

        // GET: APIKey/Usage/5
        public async Task<IActionResult> Usage(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var apiKey = await _context.APIKeys
                .Include(ak => ak.Company)
                .FirstOrDefaultAsync(m => m.APIKeyID == id);

            if (apiKey == null)
            {
                return NotFound();
            }

            // Get detailed usage statistics
            var usageData = await GetDetailedUsageData(id.Value);
            ViewBag.UsageData = usageData;

            return View(apiKey);
        }

        // GET: APIKey/TestConnection/5
        public async Task<IActionResult> TestConnection(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var apiKey = await _context.APIKeys.FindAsync(id);
            if (apiKey == null)
            {
                return NotFound();
            }

            return View(apiKey);
        }

        // POST: APIKey/TestEndpoint
        [HttpPost]
        public async Task<IActionResult> TestEndpoint(int apiKeyId, string endpoint, string method)
        {
            var apiKey = await _context.APIKeys.FindAsync(apiKeyId);
            if (apiKey == null)
            {
                return Json(new { success = false, message = "API Key not found." });
            }

            // Simulate API test
            var testResult = new
            {
                success = true,
                statusCode = 200,
                responseTime = "125ms",
                headers = new Dictionary<string, string>
                {
                    { "X-API-Key", "***" + apiKey.KeyValue!.Substring(apiKey.KeyValue.Length - 4) },
                    { "X-RateLimit-Limit", apiKey.RateLimitPerHour.ToString() ?? "1000" },
                    { "X-RateLimit-Remaining", ((apiKey.RateLimitPerHour ?? 1000) - 1).ToString() }
                },
                response = new
                {
                    status = "success",
                    message = "API connection successful",
                    timestamp = DateTime.Now,
                    service = apiKey.Service
                }
            };

            return Json(testResult);
        }

        private string GenerateAPIKey()
        {
            const string prefix = "ncarry_";
            const string chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789";
            var random = new Random();
            var key = new string(Enumerable.Repeat(chars, 32)
                .Select(s => s[random.Next(s.Length)]).ToArray());
            
            return prefix + key;
        }

        private string HashAPIKey(string apiKey)
        {
            using (SHA256 sha256Hash = SHA256.Create())
            {
                byte[] bytes = sha256Hash.ComputeHash(Encoding.UTF8.GetBytes(apiKey));
                StringBuilder builder = new StringBuilder();
                for (int i = 0; i < bytes.Length; i++)
                {
                    builder.Append(bytes[i].ToString("x2"));
                }
                return builder.ToString();
            }
        }

        private async Task<Dictionary<string, object>> GetUsageStatistics(int apiKeyId)
        {
            // Simulate usage statistics
            var stats = new Dictionary<string, object>
            {
                { "TotalRequests", new Random().Next(1000, 50000) },
                { "RequestsToday", new Random().Next(10, 500) },
                { "RequestsThisMonth", new Random().Next(500, 5000) },
                { "AverageResponseTime", new Random().Next(50, 300) + "ms" },
                { "SuccessRate", new Random().Next(95, 100) + "%" },
                { "LastUsed", DateTime.Now.AddMinutes(-new Random().Next(1, 1440)) }
            };

            return await Task.FromResult(stats);
        }

        private async Task<object> GetDetailedUsageData(int apiKeyId)
        {
            // Simulate detailed usage data
            var random = new Random();
            var hourlyData = Enumerable.Range(0, 24).Select(hour => new
            {
                Hour = hour,
                Requests = random.Next(0, 200),
                Errors = random.Next(0, 5)
            }).ToList();

            var endpointData = new[]
            {
                new { Endpoint = "/api/products", Requests = random.Next(100, 1000), AvgTime = random.Next(50, 150) },
                new { Endpoint = "/api/orders", Requests = random.Next(200, 1500), AvgTime = random.Next(100, 200) },
                new { Endpoint = "/api/inventory", Requests = random.Next(50, 500), AvgTime = random.Next(75, 125) },
                new { Endpoint = "/api/customers", Requests = random.Next(150, 800), AvgTime = random.Next(60, 120) }
            };

            return await Task.FromResult(new { HourlyData = hourlyData, EndpointData = endpointData });
        }

        private bool APIKeyExists(int id)
        {
            return _context.APIKeys.Any(e => e.APIKeyID == id);
        }

        private void PopulateDropdowns()
        {
            ViewBag.Companies = new SelectList(_context.Companies.OrderBy(c => c.CompanyName), "CompanyID", "CompanyName");

            ViewBag.Services = new List<SelectListItem>
            {
                new SelectListItem { Value = "API", Text = "General API" },
                new SelectListItem { Value = "Webhook", Text = "Webhook Integration" },
                new SelectListItem { Value = "EDI", Text = "EDI Integration" },
                new SelectListItem { Value = "Mobile", Text = "Mobile App" },
                new SelectListItem { Value = "Partner", Text = "Partner Integration" },
                new SelectListItem { Value = "Internal", Text = "Internal Service" }
            };

            ViewBag.PermissionOptions = new List<SelectListItem>
            {
                new SelectListItem { Value = "read", Text = "Read Only" },
                new SelectListItem { Value = "write", Text = "Read/Write" },
                new SelectListItem { Value = "delete", Text = "Full Access" },
                new SelectListItem { Value = "products:read", Text = "Products - Read" },
                new SelectListItem { Value = "products:write", Text = "Products - Write" },
                new SelectListItem { Value = "orders:read", Text = "Orders - Read" },
                new SelectListItem { Value = "orders:write", Text = "Orders - Write" },
                new SelectListItem { Value = "inventory:read", Text = "Inventory - Read" },
                new SelectListItem { Value = "inventory:write", Text = "Inventory - Write" },
                new SelectListItem { Value = "customers:read", Text = "Customers - Read" },
                new SelectListItem { Value = "customers:write", Text = "Customers - Write" }
            };
        }
    }
}