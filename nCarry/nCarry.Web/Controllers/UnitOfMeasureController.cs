using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using nCarry.Web.Data;
using nCarry.Web.Models.Products;

namespace nCarry.Web.Controllers
{
    [Authorize]
    public class UnitOfMeasureController : Controller
    {
        private readonly ApplicationDbContext _context;

        public UnitOfMeasureController(ApplicationDbContext context)
        {
            _context = context;
        }

        // GET: UnitOfMeasure
        public async Task<IActionResult> Index()
        {
            var uoms = await _context.UnitOfMeasures
                .OrderBy(u => u.UOMType)
                .ThenBy(u => u.UOMName)
                .ToListAsync();

            return View(uoms);
        }

        // GET: UnitOfMeasure/Details/5
        public async Task<IActionResult> Details(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var uom = await _context.UnitOfMeasures
                .FirstOrDefaultAsync(m => m.UOMID == id);

            if (uom == null)
            {
                return NotFound();
            }

            return View(uom);
        }

        // GET: UnitOfMeasure/Create
        public IActionResult Create()
        {
            return View();
        }

        // POST: UnitOfMeasure/Create
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Create(UnitOfMeasure uom)
        {
            if (ModelState.IsValid)
            {
                // Set creation metadata
                uom.CreatedDate = DateTime.UtcNow;
                uom.UpdatedDate = DateTime.UtcNow;

                _context.Add(uom);
                await _context.SaveChangesAsync();
                TempData["Success"] = "Unit of Measure created successfully!";
                return RedirectToAction(nameof(Index));
            }

            return View(uom);
        }

        // GET: UnitOfMeasure/Edit/5
        public async Task<IActionResult> Edit(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var uom = await _context.UnitOfMeasures.FindAsync(id);
            if (uom == null)
            {
                return NotFound();
            }

            return View(uom);
        }

        // POST: UnitOfMeasure/Edit/5
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Edit(int id, UnitOfMeasure uom)
        {
            if (id != uom.UOMID)
            {
                return NotFound();
            }

            if (ModelState.IsValid)
            {
                try
                {
                    var existingUom = await _context.UnitOfMeasures.FindAsync(id);
                    if (existingUom == null)
                    {
                        return NotFound();
                    }

                    // Update fields
                    existingUom.UOMCode = uom.UOMCode;
                    existingUom.UOMName = uom.UOMName;
                    existingUom.UOMType = uom.UOMType;
                    existingUom.BaseUnit = uom.BaseUnit;
                    existingUom.ConversionFactor = uom.ConversionFactor;
                    existingUom.DecimalPlaces = uom.DecimalPlaces;
                    existingUom.Symbol = uom.Symbol;
                    existingUom.IsActive = uom.IsActive;
                    existingUom.UpdatedDate = DateTime.UtcNow;

                    await _context.SaveChangesAsync();
                    TempData["Success"] = "Unit of Measure updated successfully!";
                }
                catch (DbUpdateConcurrencyException)
                {
                    if (!UOMExists(uom.UOMID))
                    {
                        return NotFound();
                    }
                    else
                    {
                        throw;
                    }
                }
                return RedirectToAction(nameof(Index));
            }

            return View(uom);
        }

        // GET: UnitOfMeasure/Delete/5
        public async Task<IActionResult> Delete(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var uom = await _context.UnitOfMeasures
                .FirstOrDefaultAsync(m => m.UOMID == id);

            if (uom == null)
            {
                return NotFound();
            }

            return View(uom);
        }

        // POST: UnitOfMeasure/Delete/5
        [HttpPost, ActionName("Delete")]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> DeleteConfirmed(int id)
        {
            var uom = await _context.UnitOfMeasures.FindAsync(id);
            
            if (uom == null)
            {
                return NotFound();
            }

            // Check if UOM is used by any products
            var productsUsingUom = await _context.Products
                .Where(p => p.InventoryUOMID == id || p.PurchaseUOMID == id || p.SalesUOMID == id || 
                           p.WeightUOMID == id || p.DimensionUOMID == id || p.VolumeUOMID == id)
                .AnyAsync();

            if (productsUsingUom)
            {
                TempData["Error"] = "Cannot delete Unit of Measure that is being used by products!";
                return RedirectToAction(nameof(Index));
            }

            _context.UnitOfMeasures.Remove(uom);
            await _context.SaveChangesAsync();
            
            TempData["Success"] = "Unit of Measure deleted successfully!";
            return RedirectToAction(nameof(Index));
        }

        private bool UOMExists(int id)
        {
            return _context.UnitOfMeasures.Any(e => e.UOMID == id);
        }
    }
}