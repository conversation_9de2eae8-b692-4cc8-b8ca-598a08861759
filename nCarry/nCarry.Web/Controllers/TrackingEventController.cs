using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Rendering;
using Microsoft.EntityFrameworkCore;
using nCarry.Web.Data;
using nCarry.Web.Models.Logistics;

namespace nCarry.Web.Controllers
{
    [Authorize]
    public class TrackingEventController : Controller
    {
        private readonly ApplicationDbContext _context;

        public TrackingEventController(ApplicationDbContext context)
        {
            _context = context;
        }

        // GET: TrackingEvent
        public async Task<IActionResult> Index(int? shipmentId, string? status, DateTime? fromDate, DateTime? toDate)
        {
            var eventsQuery = _context.ShipmentTrackings
                .Include(te => te.Shipment)
                .AsQueryable();

            if (shipmentId.HasValue)
            {
                eventsQuery = eventsQuery.Where(te => te.ShipmentID == shipmentId.Value);
                ViewBag.SelectedShipmentId = shipmentId.Value;
                
                var shipment = await _context.Shipments.FindAsync(shipmentId.Value);
                ViewBag.ShipmentNumber = shipment?.ShipmentNumber;
            }

            if (!string.IsNullOrEmpty(status))
            {
                eventsQuery = eventsQuery.Where(te => te.Status == status);
                ViewBag.SelectedStatus = status;
            }

            if (fromDate.HasValue)
            {
                eventsQuery = eventsQuery.Where(te => te.EventDate >= fromDate.Value);
                ViewBag.FromDate = fromDate.Value;
            }

            if (toDate.HasValue)
            {
                eventsQuery = eventsQuery.Where(te => te.EventDate <= toDate.Value);
                ViewBag.ToDate = toDate.Value;
            }

            var events = await eventsQuery
                .OrderByDescending(te => te.EventDate)
                .ToListAsync();

            // Populate filter dropdowns
            ViewBag.StatusList = new List<SelectListItem>
            {
                new SelectListItem { Value = "", Text = "All Statuses" },
                new SelectListItem { Value = "Pending", Text = "Pending", Selected = status == "Pending" },
                new SelectListItem { Value = "Picked", Text = "Picked", Selected = status == "Picked" },
                new SelectListItem { Value = "InTransit", Text = "In Transit", Selected = status == "InTransit" },
                new SelectListItem { Value = "OutForDelivery", Text = "Out for Delivery", Selected = status == "OutForDelivery" },
                new SelectListItem { Value = "Delivered", Text = "Delivered", Selected = status == "Delivered" },
                new SelectListItem { Value = "Failed", Text = "Failed", Selected = status == "Failed" },
                new SelectListItem { Value = "Returned", Text = "Returned", Selected = status == "Returned" }
            };

            return View(events);
        }

        // GET: TrackingEvent/Details/5
        public async Task<IActionResult> Details(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var trackingEvent = await _context.ShipmentTrackings
                .Include(te => te.Shipment)
                .FirstOrDefaultAsync(m => m.TrackingID == id);

            if (trackingEvent == null)
            {
                return NotFound();
            }

            return View(trackingEvent);
        }

        // GET: TrackingEvent/Create
        public async Task<IActionResult> Create(int? shipmentId)
        {
            await PopulateDropdowns(shipmentId);
            
            var trackingEvent = new ShipmentTracking
            {
                EventDate = DateTime.Now
            };
            
            if (shipmentId.HasValue)
            {
                trackingEvent.ShipmentID = shipmentId.Value;
                
                // Get current shipment status as default
                var shipment = await _context.Shipments.FindAsync(shipmentId.Value);
                if (shipment != null)
                {
                    trackingEvent.Status = shipment.Status;
                }
            }

            return View(trackingEvent);
        }

        // POST: TrackingEvent/Create
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Create([Bind("ShipmentID,EventDate,Status,Location,Description,Notes")] ShipmentTracking trackingEvent)
        {
            if (ModelState.IsValid)
            {
                trackingEvent.CreatedDate = DateTime.Now;
                trackingEvent.UpdatedDate = DateTime.Now;

                _context.Add(trackingEvent);

                // Update shipment status if this is the latest event
                var shipment = await _context.Shipments.FindAsync(trackingEvent.ShipmentID);
                if (shipment != null && trackingEvent.EventDate >= shipment.UpdatedDate)
                {
                    shipment.Status = trackingEvent.Status;
                    shipment.UpdatedDate = DateTime.Now;
                    _context.Update(shipment);
                }

                await _context.SaveChangesAsync();
                
                TempData["Success"] = "Tracking event created successfully.";
                return RedirectToAction("Details", "Shipment", new { id = trackingEvent.ShipmentID });
            }

            await PopulateDropdowns(trackingEvent.ShipmentID);
            return View(trackingEvent);
        }

        // GET: TrackingEvent/Edit/5
        public async Task<IActionResult> Edit(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var trackingEvent = await _context.ShipmentTrackings.FindAsync(id);
            if (trackingEvent == null)
            {
                return NotFound();
            }

            await PopulateDropdowns(trackingEvent.ShipmentID);
            return View(trackingEvent);
        }

        // POST: TrackingEvent/Edit/5
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Edit(int id, [Bind("EventID,ShipmentID,EventDate,Status,Location,Description,Notes,CreatedDate,CreatedByUserID")] ShipmentTracking trackingEvent)
        {
            if (id != trackingEvent.TrackingID)
            {
                return NotFound();
            }

            if (ModelState.IsValid)
            {
                try
                {
                    trackingEvent.UpdatedDate = DateTime.Now;
                    _context.Update(trackingEvent);
                    await _context.SaveChangesAsync();
                    
                    TempData["Success"] = "Tracking event updated successfully.";
                }
                catch (DbUpdateConcurrencyException)
                {
                    if (!TrackingEventExists(trackingEvent.TrackingID))
                    {
                        return NotFound();
                    }
                    else
                    {
                        throw;
                    }
                }
                return RedirectToAction("Details", "Shipment", new { id = trackingEvent.ShipmentID });
            }

            await PopulateDropdowns(trackingEvent.ShipmentID);
            return View(trackingEvent);
        }

        // GET: TrackingEvent/Delete/5
        public async Task<IActionResult> Delete(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var trackingEvent = await _context.ShipmentTrackings
                .Include(te => te.Shipment)
                .FirstOrDefaultAsync(m => m.TrackingID == id);
                
            if (trackingEvent == null)
            {
                return NotFound();
            }

            return View(trackingEvent);
        }

        // POST: TrackingEvent/Delete/5
        [HttpPost, ActionName("Delete")]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> DeleteConfirmed(int id)
        {
            var trackingEvent = await _context.ShipmentTrackings.FindAsync(id);
            if (trackingEvent != null)
            {
                var shipmentId = trackingEvent.ShipmentID;
                _context.ShipmentTrackings.Remove(trackingEvent);
                await _context.SaveChangesAsync();
                
                TempData["Success"] = "Tracking event deleted successfully.";
                return RedirectToAction("Details", "Shipment", new { id = shipmentId });
            }

            return RedirectToAction(nameof(Index));
        }

        // GET: TrackingEvent/Timeline/5
        public async Task<IActionResult> Timeline(int shipmentId)
        {
            var shipment = await _context.Shipments
                .Include(s => s.Customer)
                .Include(s => s.Carrier)
                .Include(s => s.Service)
                .FirstOrDefaultAsync(s => s.ShipmentID == shipmentId);

            if (shipment == null)
            {
                return NotFound();
            }

            var events = await _context.ShipmentTrackings
                .Where(te => te.ShipmentID == shipmentId)
                .OrderBy(te => te.EventDate)
                .ToListAsync();

            ViewBag.Shipment = shipment;
            return View(events);
        }

        // POST: TrackingEvent/AddQuickEvent
        [HttpPost]
        public async Task<IActionResult> AddQuickEvent(int shipmentId, string status, string location)
        {
            var trackingEvent = new ShipmentTracking
            {
                ShipmentID = shipmentId,
                EventDate = DateTime.Now,
                Status = status,
                Location = location ?? "Unknown",
                Description = $"Quick status update to {status}",
                CreatedDate = DateTime.Now,
                UpdatedDate = DateTime.Now
            };

            _context.ShipmentTrackings.Add(trackingEvent);

            // Update shipment status
            var shipment = await _context.Shipments.FindAsync(shipmentId);
            if (shipment != null)
            {
                shipment.Status = status;
                shipment.UpdatedDate = DateTime.Now;
                _context.Update(shipment);
            }

            await _context.SaveChangesAsync();

            return Json(new { success = true, message = "Status updated successfully." });
        }

        // GET: TrackingEvent/GetEventsForShipment/5
        [HttpGet]
        public async Task<IActionResult> GetEventsForShipment(int shipmentId)
        {
            var events = await _context.ShipmentTrackings
                .Where(te => te.ShipmentID == shipmentId)
                .OrderByDescending(te => te.EventDate)
                .Select(te => new
                {
                    te.EventID,
                    te.EventDate,
                    te.Status,
                    te.Location,
                    te.Description
                })
                .ToListAsync();

            return Json(events);
        }

        private bool TrackingEventExists(int id)
        {
            return _context.ShipmentTrackings.Any(e => e.TrackingID == id);
        }

        private async Task PopulateDropdowns(int? shipmentId = null)
        {
            if (shipmentId.HasValue)
            {
                var shipment = await _context.Shipments
                    .Include(s => s.Customer)
                    .FirstOrDefaultAsync(s => s.ShipmentID == shipmentId.Value);
                
                if (shipment != null)
                {
                    ViewBag.ShipmentNumber = shipment.ShipmentNumber;
                    ViewBag.CustomerName = shipment.Customer?.CustomerName;
                }
            }

            ViewBag.Shipments = new SelectList(
                await _context.Shipments.OrderByDescending(s => s.ShipmentDate).Take(100).ToListAsync(),
                "ShipmentID", "ShipmentNumber", shipmentId);

            ViewBag.StatusList = new List<SelectListItem>
            {
                new SelectListItem { Value = "Pending", Text = "Pending" },
                new SelectListItem { Value = "Picked", Text = "Picked" },
                new SelectListItem { Value = "InTransit", Text = "In Transit" },
                new SelectListItem { Value = "OutForDelivery", Text = "Out for Delivery" },
                new SelectListItem { Value = "Delivered", Text = "Delivered" },
                new SelectListItem { Value = "Failed", Text = "Failed" },
                new SelectListItem { Value = "Returned", Text = "Returned" }
            };
        }
    }
}