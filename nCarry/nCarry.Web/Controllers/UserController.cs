using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using nCarry.Web.Data;
using nCarry.Web.Models;
using nCarry.Web.Models.System;
using nCarry.Web.Services;

namespace nCarry.Web.Controllers
{
    [Authorize]
    public class UserController : Controller
    {
        private readonly ApplicationDbContext _context;
        private readonly IAuthenticationService _authService;

        public UserController(ApplicationDbContext context, IAuthenticationService authService)
        {
            _context = context;
            _authService = authService;
        }

        // GET: User
        public async Task<IActionResult> Index()
        {
            var users = await _context.Users
                .Where(u => !u.IsDeleted)
                .OrderBy(u => u.Username)
                .ToListAsync();
            return View(users);
        }

        // GET: User/Details/5
        public async Task<IActionResult> Details(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var user = await _context.Users
                .Include(u => u.UserRoles)
                    .ThenInclude(ur => ur.Role)
                .FirstOrDefaultAsync(m => m.UserID == id && !m.IsDeleted);
            
            if (user == null)
            {
                return NotFound();
            }

            return View(user);
        }

        // GET: User/Create
        public IActionResult Create()
        {
            ViewBag.Roles = _context.Roles.Where(r => r.IsActive).ToList();
            return View();
        }

        // POST: User/Create
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Create(UserCreateViewModel model)
        {
            if (ModelState.IsValid)
            {
                var user = await _authService.RegisterAsync(
                    model.Username,
                    model.Email,
                    model.Password,
                    model.FirstName,
                    model.LastName);

                if (user != null)
                {
                    // Update additional fields
                    user.JobTitle = model.JobTitle;
                    user.Department = model.Department;
                    user.PhoneNumber = model.PhoneNumber;
                    user.MobileNumber = model.MobileNumber;
                    user.IsActive = model.IsActive;
                    
                    _context.Update(user);

                    // Assign roles
                    if (model.SelectedRoles != null && model.SelectedRoles.Any())
                    {
                        foreach (var roleId in model.SelectedRoles)
                        {
                            var userRole = new UserRole
                            {
                                UserID = user.UserID,
                                RoleID = roleId,
                                IsActive = true,
                                AssignedByUserID = int.Parse(User.FindFirst(System.Security.Claims.ClaimTypes.NameIdentifier)?.Value ?? "0")
                            };
                            _context.UserRoles.Add(userRole);
                        }
                    }

                    await _context.SaveChangesAsync();
                    TempData["Success"] = "User created successfully.";
                    return RedirectToAction(nameof(Index));
                }

                ModelState.AddModelError(string.Empty, "Username or email already exists.");
            }

            ViewBag.Roles = _context.Roles.Where(r => r.IsActive).ToList();
            return View(model);
        }

        // GET: User/Edit/5
        public async Task<IActionResult> Edit(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var user = await _context.Users
                .Include(u => u.UserRoles)
                .FirstOrDefaultAsync(u => u.UserID == id && !u.IsDeleted);
            
            if (user == null)
            {
                return NotFound();
            }

            var model = new UserEditViewModel
            {
                UserID = user.UserID,
                Username = user.Username,
                Email = user.Email,
                FirstName = user.FirstName,
                LastName = user.LastName,
                JobTitle = user.JobTitle,
                Department = user.Department,
                PhoneNumber = user.PhoneNumber,
                MobileNumber = user.MobileNumber,
                IsActive = user.IsActive,
                MustChangePassword = user.MustChangePassword,
                SelectedRoles = user.UserRoles.Select(ur => ur.RoleID).ToList()
            };

            ViewBag.Roles = _context.Roles.Where(r => r.IsActive).ToList();
            return View(model);
        }

        // POST: User/Edit/5
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Edit(int id, UserEditViewModel model)
        {
            if (id != model.UserID)
            {
                return NotFound();
            }

            if (ModelState.IsValid)
            {
                try
                {
                    var user = await _context.Users
                        .Include(u => u.UserRoles)
                        .FirstOrDefaultAsync(u => u.UserID == id && !u.IsDeleted);
                    
                    if (user == null)
                    {
                        return NotFound();
                    }

                    // Update user fields
                    user.Email = model.Email;
                    user.FirstName = model.FirstName;
                    user.LastName = model.LastName;
                    user.DisplayName = $"{model.FirstName} {model.LastName}";
                    user.JobTitle = model.JobTitle;
                    user.Department = model.Department;
                    user.PhoneNumber = model.PhoneNumber;
                    user.MobileNumber = model.MobileNumber;
                    user.IsActive = model.IsActive;
                    user.MustChangePassword = model.MustChangePassword;
                    user.UpdatedDate = DateTime.Now;
                    user.UpdatedByUserID = int.Parse(User.FindFirst(System.Security.Claims.ClaimTypes.NameIdentifier)?.Value ?? "0");

                    // Update password if provided
                    if (!string.IsNullOrEmpty(model.NewPassword))
                    {
                        var salt = _authService.GenerateSalt();
                        user.PasswordSalt = salt;
                        user.PasswordHash = _authService.HashPassword(model.NewPassword, salt);
                        user.PasswordChangedDate = DateTime.Now;
                    }

                    // Update roles
                    _context.UserRoles.RemoveRange(user.UserRoles);
                    if (model.SelectedRoles != null && model.SelectedRoles.Any())
                    {
                        foreach (var roleId in model.SelectedRoles)
                        {
                            var userRole = new UserRole
                            {
                                UserID = user.UserID,
                                RoleID = roleId,
                                IsActive = true,
                                AssignedByUserID = int.Parse(User.FindFirst(System.Security.Claims.ClaimTypes.NameIdentifier)?.Value ?? "0")
                            };
                            _context.UserRoles.Add(userRole);
                        }
                    }

                    _context.Update(user);
                    await _context.SaveChangesAsync();
                    
                    TempData["Success"] = "User updated successfully.";
                    return RedirectToAction(nameof(Index));
                }
                catch (DbUpdateConcurrencyException)
                {
                    if (!UserExists(model.UserID))
                    {
                        return NotFound();
                    }
                    else
                    {
                        throw;
                    }
                }
            }

            ViewBag.Roles = _context.Roles.Where(r => r.IsActive).ToList();
            return View(model);
        }

        // GET: User/Delete/5
        public async Task<IActionResult> Delete(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var user = await _context.Users
                .Include(u => u.UserRoles)
                    .ThenInclude(ur => ur.Role)
                .FirstOrDefaultAsync(m => m.UserID == id && !m.IsDeleted);
            
            if (user == null)
            {
                return NotFound();
            }

            return View(user);
        }

        // POST: User/Delete/5
        [HttpPost, ActionName("Delete")]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> DeleteConfirmed(int id)
        {
            var user = await _context.Users.FindAsync(id);
            if (user != null)
            {
                // Soft delete
                user.IsDeleted = true;
                user.IsActive = false;
                user.DeletedDate = DateTime.Now;
                user.DeletedByUserID = int.Parse(User.FindFirst(System.Security.Claims.ClaimTypes.NameIdentifier)?.Value ?? "0");
                
                _context.Update(user);
                await _context.SaveChangesAsync();
                
                TempData["Success"] = "User deleted successfully.";
            }

            return RedirectToAction(nameof(Index));
        }

        // GET: User/ResetPassword/5
        public async Task<IActionResult> ResetPassword(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var user = await _context.Users.FindAsync(id);
            if (user == null || user.IsDeleted)
            {
                return NotFound();
            }

            ViewBag.Username = user.Username;
            return View(new ResetPasswordViewModel { UserID = user.UserID });
        }

        // POST: User/ResetPassword/5
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> ResetPassword(ResetPasswordViewModel model)
        {
            if (ModelState.IsValid)
            {
                var user = await _context.Users.FindAsync(model.UserID);
                if (user != null && !user.IsDeleted)
                {
                    var salt = _authService.GenerateSalt();
                    user.PasswordSalt = salt;
                    user.PasswordHash = _authService.HashPassword(model.NewPassword, salt);
                    user.PasswordChangedDate = DateTime.Now;
                    user.MustChangePassword = true;
                    user.UpdatedDate = DateTime.Now;
                    user.UpdatedByUserID = int.Parse(User.FindFirst(System.Security.Claims.ClaimTypes.NameIdentifier)?.Value ?? "0");

                    _context.Update(user);
                    await _context.SaveChangesAsync();
                    
                    TempData["Success"] = "Password reset successfully. User must change password on next login.";
                    return RedirectToAction(nameof(Index));
                }
            }

            return View(model);
        }

        private bool UserExists(int id)
        {
            return _context.Users.Any(e => e.UserID == id);
        }
    }
}