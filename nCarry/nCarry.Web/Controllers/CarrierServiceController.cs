using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Rendering;
using Microsoft.EntityFrameworkCore;
using nCarry.Web.Data;
using nCarry.Web.Models.Logistics;

namespace nCarry.Web.Controllers
{
    [Authorize]
    public class CarrierServiceController : Controller
    {
        private readonly ApplicationDbContext _context;

        public CarrierServiceController(ApplicationDbContext context)
        {
            _context = context;
        }

        // GET: CarrierService
        public async Task<IActionResult> Index(int? carrierId)
        {
            var servicesQuery = _context.CarrierServices
                .Include(cs => cs.Carrier)
                .AsQueryable();

            if (carrierId.HasValue)
            {
                servicesQuery = servicesQuery.Where(cs => cs.CarrierID == carrierId.Value);
                ViewBag.SelectedCarrierId = carrierId.Value;
                ViewBag.SelectedCarrierName = await _context.Carriers
                    .Where(c => c.CarrierID == carrierId.Value)
                    .Select(c => c.CarrierName)
                    .FirstOrDefaultAsync();
            }

            var services = await servicesQuery
                .OrderBy(cs => cs.Carrier.CarrierName)
                .ThenBy(cs => cs.ServiceName)
                .ToListAsync();

            // Populate carrier filter dropdown
            ViewBag.Carriers = new SelectList(
                await _context.Carriers.Where(c => c.IsActive).OrderBy(c => c.CarrierName).ToListAsync(),
                "CarrierID", "CarrierName", carrierId);

            return View(services);
        }

        // GET: CarrierService/Details/5
        public async Task<IActionResult> Details(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var carrierService = await _context.CarrierServices
                .Include(cs => cs.Carrier)
                .Include(cs => cs.FreightRates)
                .FirstOrDefaultAsync(m => m.ServiceID == id);

            if (carrierService == null)
            {
                return NotFound();
            }

            return View(carrierService);
        }

        // GET: CarrierService/Create
        public async Task<IActionResult> Create(int? carrierId)
        {
            await PopulateDropdowns(carrierId);
            
            var service = new CarrierService();
            if (carrierId.HasValue)
            {
                service.CarrierID = carrierId.Value;
            }

            return View(service);
        }

        // POST: CarrierService/Create
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Create([Bind("CarrierID,ServiceCode,ServiceName,ServiceType,TransitDays,CutoffTime,IsInternational,MaxWeight,MaxLength,MaxWidth,MaxHeight,IsActive")] CarrierService carrierService)
        {
            if (ModelState.IsValid)
            {
                // Check if service code already exists for this carrier
                if (await _context.CarrierServices.AnyAsync(cs => cs.CarrierID == carrierService.CarrierID && cs.ServiceCode == carrierService.ServiceCode))
                {
                    ModelState.AddModelError("ServiceCode", "Service code already exists for this carrier.");
                    await PopulateDropdowns(carrierService.CarrierID);
                    return View(carrierService);
                }

                carrierService.CreatedDate = DateTime.Now;
                carrierService.UpdatedDate = DateTime.Now;

                _context.Add(carrierService);
                await _context.SaveChangesAsync();
                
                TempData["Success"] = "Carrier service created successfully.";
                return RedirectToAction(nameof(Index), new { carrierId = carrierService.CarrierID });
            }

            await PopulateDropdowns(carrierService.CarrierID);
            return View(carrierService);
        }

        // GET: CarrierService/Edit/5
        public async Task<IActionResult> Edit(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var carrierService = await _context.CarrierServices.FindAsync(id);
            if (carrierService == null)
            {
                return NotFound();
            }

            await PopulateDropdowns(carrierService.CarrierID);
            return View(carrierService);
        }

        // POST: CarrierService/Edit/5
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Edit(int id, [Bind("ServiceID,CarrierID,ServiceCode,ServiceName,ServiceType,TransitDays,CutoffTime,IsInternational,MaxWeight,MaxLength,MaxWidth,MaxHeight,IsActive,CreatedDate,CreatedByUserID")] CarrierService carrierService)
        {
            if (id != carrierService.ServiceID)
            {
                return NotFound();
            }

            if (ModelState.IsValid)
            {
                try
                {
                    // Check if service code already exists for this carrier (excluding current record)
                    if (await _context.CarrierServices.AnyAsync(cs => cs.CarrierID == carrierService.CarrierID && cs.ServiceCode == carrierService.ServiceCode && cs.ServiceID != id))
                    {
                        ModelState.AddModelError("ServiceCode", "Service code already exists for this carrier.");
                        await PopulateDropdowns(carrierService.CarrierID);
                        return View(carrierService);
                    }

                    carrierService.UpdatedDate = DateTime.Now;

                    _context.Update(carrierService);
                    await _context.SaveChangesAsync();
                    
                    TempData["Success"] = "Carrier service updated successfully.";
                }
                catch (DbUpdateConcurrencyException)
                {
                    if (!CarrierServiceExists(carrierService.ServiceID))
                    {
                        return NotFound();
                    }
                    else
                    {
                        throw;
                    }
                }
                return RedirectToAction(nameof(Index), new { carrierId = carrierService.CarrierID });
            }

            await PopulateDropdowns(carrierService.CarrierID);
            return View(carrierService);
        }

        // GET: CarrierService/Delete/5
        public async Task<IActionResult> Delete(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var carrierService = await _context.CarrierServices
                .Include(cs => cs.Carrier)
                .FirstOrDefaultAsync(m => m.ServiceID == id);
                
            if (carrierService == null)
            {
                return NotFound();
            }

            // Check if service has freight rates or shipments
            var hasRates = await _context.FreightRates.AnyAsync(fr => fr.ServiceID == id);
            var hasShipments = await _context.Shipments.AnyAsync(s => s.ServiceID == id);
            
            ViewBag.HasRates = hasRates;
            ViewBag.HasShipments = hasShipments;

            return View(carrierService);
        }

        // POST: CarrierService/Delete/5
        [HttpPost, ActionName("Delete")]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> DeleteConfirmed(int id)
        {
            var carrierService = await _context.CarrierServices.FindAsync(id);
            if (carrierService != null)
            {
                // Check if service has freight rates or shipments
                var hasRates = await _context.FreightRates.AnyAsync(fr => fr.ServiceID == id);
                var hasShipments = await _context.Shipments.AnyAsync(s => s.ServiceID == id);
                
                if (hasRates || hasShipments)
                {
                    TempData["Error"] = "Cannot delete carrier service with existing freight rates or shipments.";
                    return RedirectToAction(nameof(Index), new { carrierId = carrierService.CarrierID });
                }

                var carrierId = carrierService.CarrierID;
                _context.CarrierServices.Remove(carrierService);
                await _context.SaveChangesAsync();
                
                TempData["Success"] = "Carrier service deleted successfully.";
                return RedirectToAction(nameof(Index), new { carrierId });
            }

            return RedirectToAction(nameof(Index));
        }

        private bool CarrierServiceExists(int id)
        {
            return _context.CarrierServices.Any(e => e.ServiceID == id);
        }

        private async Task PopulateDropdowns(int? carrierId = null)
        {
            ViewBag.Carriers = new SelectList(
                await _context.Carriers.Where(c => c.IsActive).OrderBy(c => c.CarrierName).ToListAsync(),
                "CarrierID", "CarrierName", carrierId);

            ViewBag.ServiceTypes = new List<SelectListItem>
            {
                new SelectListItem { Value = "Standard", Text = "Standard" },
                new SelectListItem { Value = "Express", Text = "Express" },
                new SelectListItem { Value = "Overnight", Text = "Overnight" },
                new SelectListItem { Value = "Economy", Text = "Economy" },
                new SelectListItem { Value = "International", Text = "International" }
            };
        }
    }
}