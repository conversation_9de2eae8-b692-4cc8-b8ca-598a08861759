using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using nCarry.Web.Data;
using nCarry.Web.Models.System;
using nCarry.Web.Models.Customers;
using nCarry.Web.Models.Suppliers;
using nCarry.Web.Models.Products;
using nCarry.Web.Models.Sales;
using nCarry.Web.Models.Warehouse;
using nCarry.Web.Models.Financial;

namespace nCarry.Web.Controllers
{
    [Authorize(Roles = "Administrator")]
    public class SeedDataController : Controller
    {
        private readonly ApplicationDbContext _context;

        public SeedDataController(ApplicationDbContext context)
        {
            _context = context;
        }

        public async Task<IActionResult> Index()
        {
            var stats = new
            {
                Companies = await _context.Companies.CountAsync(),
                Branches = await _context.Branches.CountAsync(),
                Customers = await _context.Customers.CountAsync(),
                Suppliers = await _context.Suppliers.CountAsync(),
                Products = await _context.Products.CountAsync(),
                Categories = await _context.ProductCategories.CountAsync(),
                SalesOrders = await _context.SalesOrders.CountAsync(),
                Warehouses = await _context.Warehouses.CountAsync()
            };

            ViewBag.Stats = stats;
            return View();
        }

        [HttpPost]
        public async Task<IActionResult> SeedData()
        {
            using var transaction = await _context.Database.BeginTransactionAsync();
            try
            {
                // 1. Seed Companies
                await SeedCompanies();
                
                // 2. Seed Branches
                await SeedBranches();
                
                // 3. Seed Currencies
                await SeedCurrencies();
                
                // 4. Seed Tax Codes
                await SeedTaxCodes();
                
                // 5. Seed Payment Methods
                await SeedPaymentMethods();
                
                // 6. Seed Unit of Measures
                await SeedUnitOfMeasures();
                
                // 7. Seed Product Categories
                await SeedProductCategories();
                
                // 8. Seed Customers
                await SeedCustomers();
                
                // 9. Seed Suppliers
                await SeedSuppliers();
                
                // 10. Seed Warehouses
                await SeedWarehouses();
                
                // 11. Seed Products
                await SeedProducts();
                
                // 12. Seed Initial Inventory
                await SeedInventory();
                
                // 13. Seed Sales Orders
                await SeedSalesOrders();

                await transaction.CommitAsync();
                TempData["Success"] = "Sample data seeded successfully!";
            }
            catch (Exception ex)
            {
                await transaction.RollbackAsync();
                TempData["Error"] = $"Error seeding data: {ex.Message}";
            }

            return RedirectToAction(nameof(Index));
        }

        private async Task SeedCompanies()
        {
            if (await _context.Companies.AnyAsync()) return;

            var companies = new[]
            {
                new Company
                {
                    CompanyCode = "NC001",
                    CompanyName = "nCarry Logistics Ltd",
                    LegalName = "nCarry Logistics Limited",
                    TaxNumber = "GB123456789",
                    RegistrationNumber = "REG123456",
                    Website = "www.ncarry.com",
                    Email = "<EMAIL>",
                    Phone = "+44 20 1234 5678",
                    IsActive = true
                }
            };

            _context.Companies.AddRange(companies);
            await _context.SaveChangesAsync();
        }

        private async Task SeedBranches()
        {
            if (await _context.Branches.AnyAsync()) return;

            var company = await _context.Companies.FirstAsync();
            
            var branches = new[]
            {
                new Branch
                {
                    BranchCode = "BR001",
                    BranchName = "London Main Branch",
                    CompanyID = company.CompanyID,
                    BranchType = "Warehouse",
                    Address1 = "123 Logistics Way",
                    City = "London",
                    PostCode = "E1 6AN",
                    Country = "United Kingdom",
                    Phone = "+44 20 1111 1111",
                    Email = "<EMAIL>",
                    IsDefault = true,
                    IsActive = true
                },
                new Branch
                {
                    BranchCode = "BR002",
                    BranchName = "Manchester Branch",
                    CompanyID = company.CompanyID,
                    BranchType = "Distribution",
                    Address1 = "456 Supply Street",
                    City = "Manchester",
                    PostCode = "M1 2AB",
                    Country = "United Kingdom",
                    Phone = "+44 ************",
                    Email = "<EMAIL>",
                    IsDefault = false,
                    IsActive = true
                }
            };

            _context.Branches.AddRange(branches);
            await _context.SaveChangesAsync();
        }

        private async Task SeedCurrencies()
        {
            if (await _context.Currencies.AnyAsync()) return;

            var currencies = new[]
            {
                new Models.Financial.Currency
                {
                    CurrencyCode = "GBP",
                    CurrencyName = "British Pound Sterling",
                    Symbol = "£",
                    DecimalPlaces = 2,
                    IsBaseCurrency = true,
                    IsActive = true
                },
                new Models.Financial.Currency
                {
                    CurrencyCode = "USD",
                    CurrencyName = "US Dollar",
                    Symbol = "$",
                    DecimalPlaces = 2,
                    IsBaseCurrency = false,
                    IsActive = true
                },
                new Models.Financial.Currency
                {
                    CurrencyCode = "EUR",
                    CurrencyName = "Euro",
                    Symbol = "€",
                    DecimalPlaces = 2,
                    IsBaseCurrency = false,
                    IsActive = true
                }
            };

            _context.Currencies.AddRange(currencies);
            await _context.SaveChangesAsync();
        }

        private async Task SeedTaxCodes()
        {
            if (await _context.TaxCodes.AnyAsync()) return;

            var taxCodes = new[]
            {
                new Models.Financial.TaxCode
                {
                    TaxCodeValue = "VAT20",
                    TaxCodeName = "Standard VAT 20%",
                    Description = "Standard VAT 20%",
                    TaxRate = 20.00m,
                    TaxType = "Sales",
                    IsActive = true
                },
                new Models.Financial.TaxCode
                {
                    TaxCodeValue = "VAT0",
                    TaxCodeName = "Zero Rated VAT",
                    Description = "Zero Rated VAT",
                    TaxRate = 0.00m,
                    TaxType = "Sales",
                    IsActive = true
                }
            };

            _context.TaxCodes.AddRange(taxCodes);
            await _context.SaveChangesAsync();
        }

        private async Task SeedPaymentMethods()
        {
            if (await _context.PaymentMethods.AnyAsync()) return;

            var methods = new[]
            {
                new Models.Financial.PaymentMethod
                {
                    MethodCode = "CASH",
                    MethodName = "Cash Payment",
                    MethodType = "Cash",
                    IsActive = true,
                    RequiresBankDetails = false,
                    ProcessingDays = 0
                },
                new Models.Financial.PaymentMethod
                {
                    MethodCode = "BACS",
                    MethodName = "Bank Transfer",
                    MethodType = "BankTransfer",
                    IsActive = true,
                    RequiresBankDetails = true,
                    ProcessingDays = 3
                }
            };

            _context.PaymentMethods.AddRange(methods);
            await _context.SaveChangesAsync();
        }

        private async Task SeedUnitOfMeasures()
        {
            if (await _context.UnitOfMeasures.AnyAsync()) return;

            var uoms = new[]
            {
                new UnitOfMeasure
                {
                    UOMCode = "PC",
                    UOMName = "Piece",
                    UOMType = "Quantity",
                    ConversionFactor = 1.00m,
                    IsActive = true
                },
                new UnitOfMeasure
                {
                    UOMCode = "BOX",
                    UOMName = "Box",
                    UOMType = "Quantity",
                    ConversionFactor = 12.00m,
                    // Base unit conversion is handled by ConversionFactor
                    IsActive = true
                }
            };

            _context.UnitOfMeasures.AddRange(uoms);
            await _context.SaveChangesAsync();
        }

        private async Task SeedProductCategories()
        {
            if (await _context.ProductCategories.AnyAsync()) return;

            var categories = new[]
            {
                new ProductCategory
                {
                    CategoryCode = "ELEC",
                    CategoryName = "Electronics",
                    Level = 1,
                    CategoryPath = "/ELEC/",
                    IsActive = true
                },
                new ProductCategory
                {
                    CategoryCode = "FURN",
                    CategoryName = "Furniture",
                    Level = 1,
                    CategoryPath = "/FURN/",
                    IsActive = true
                }
            };

            _context.ProductCategories.AddRange(categories);
            await _context.SaveChangesAsync();

            // Add subcategories
            var elec = await _context.ProductCategories.FirstAsync(c => c.CategoryCode == "ELEC");
            var furn = await _context.ProductCategories.FirstAsync(c => c.CategoryCode == "FURN");

            var subCategories = new[]
            {
                new ProductCategory
                {
                    CategoryCode = "COMP",
                    CategoryName = "Computers",
                    ParentCategoryID = elec.CategoryID,
                    Level = 2,
                    CategoryPath = "/ELEC/COMP/",
                    IsActive = true
                },
                new ProductCategory
                {
                    CategoryCode = "OFFICE",
                    CategoryName = "Office Furniture",
                    ParentCategoryID = furn.CategoryID,
                    Level = 2,
                    CategoryPath = "/FURN/OFFICE/",
                    IsActive = true
                }
            };

            _context.ProductCategories.AddRange(subCategories);
            await _context.SaveChangesAsync();
        }

        private async Task SeedCustomers()
        {
            if (await _context.Customers.AnyAsync()) return;

            var customers = new[]
            {
                new Customer
                {
                    CustomerCode = "CUST001",
                    CustomerName = "Tech Solutions Ltd",
                    CustomerType = "Business",
                    TaxNumber = "GB111222333",
                    CreditLimit = 50000.00m,
                    PaymentTerm = 30,
                    CurrencyCode = "GBP",
                    IsActive = true
                },
                new Customer
                {
                    CustomerCode = "CUST002",
                    CustomerName = "Office Supplies Direct",
                    CustomerType = "Business",
                    TaxNumber = "GB444555666",
                    CreditLimit = 75000.00m,
                    PaymentTerm = 45,
                    CurrencyCode = "GBP",
                    IsActive = true
                },
                new Customer
                {
                    CustomerCode = "CUST003",
                    CustomerName = "Retail World PLC",
                    CustomerType = "Business",
                    TaxNumber = "GB777888999",
                    CreditLimit = 100000.00m,
                    PaymentTerm = 60,
                    CurrencyCode = "GBP",
                    IsActive = true
                },
                new Customer
                {
                    CustomerCode = "CUST004",
                    CustomerName = "Smart Systems Inc",
                    CustomerType = "Business",
                    TaxNumber = "GB123123123",
                    CreditLimit = 60000.00m,
                    PaymentTerm = 30,
                    CurrencyCode = "GBP",
                    IsActive = true
                },
                new Customer
                {
                    CustomerCode = "CUST005",
                    CustomerName = "Digital Store UK",
                    CustomerType = "Business",
                    TaxNumber = "GB456456456",
                    CreditLimit = 80000.00m,
                    PaymentTerm = 45,
                    CurrencyCode = "GBP",
                    IsActive = true
                }
            };

            _context.Customers.AddRange(customers);
            await _context.SaveChangesAsync();
        }

        private async Task SeedSuppliers()
        {
            if (await _context.Suppliers.AnyAsync()) return;

            var suppliers = new[]
            {
                new Supplier
                {
                    SupplierCode = "SUPP001",
                    SupplierName = "Global Electronics Manufacturing",
                    TaxNumber = "GB999888777",
                    PaymentTermDays = 60,
                    PreferredCurrency = "GBP",
                    IsActive = true
                },
                new Supplier
                {
                    SupplierCode = "SUPP002",
                    SupplierName = "Computer Components Ltd",
                    TaxNumber = "GB666555444",
                    PaymentTermDays = 45,
                    PreferredCurrency = "GBP",
                    IsActive = true
                },
                new Supplier
                {
                    SupplierCode = "SUPP003",
                    SupplierName = "Furniture Factory Direct",
                    TaxNumber = "GB333222111",
                    PaymentTermDays = 30,
                    PreferredCurrency = "GBP",
                    IsActive = true
                }
            };

            _context.Suppliers.AddRange(suppliers);
            await _context.SaveChangesAsync();
        }

        private async Task SeedWarehouses()
        {
            if (await _context.Warehouses.AnyAsync()) return;

            var branch = await _context.Branches.FirstAsync();

            var warehouses = new[]
            {
                new Warehouse
                {
                    WarehouseCode = "WH001",
                    WarehouseName = "Main Distribution Center",
                    WarehouseType = "Distribution",
                    StorageCapacity = 10000,
                    CurrentUtilization = 50, // 50% utilized
                    Address1 = "123 Logistics Way",
                    City = "London",
                    PostCode = "E1 6AN",
                    Country = "United Kingdom",
                    IsActive = true
                }
            };

            _context.Warehouses.AddRange(warehouses);
            await _context.SaveChangesAsync();
        }

        private async Task SeedProducts()
        {
            if (await _context.Products.AnyAsync()) return;

            var compCategory = await _context.ProductCategories.FirstAsync(c => c.CategoryCode == "COMP");
            var furnCategory = await _context.ProductCategories.FirstAsync(c => c.CategoryCode == "OFFICE");
            var supplier1 = await _context.Suppliers.FirstAsync(s => s.SupplierCode == "SUPP001");
            var supplier2 = await _context.Suppliers.FirstAsync(s => s.SupplierCode == "SUPP003");
            var uom = await _context.UnitOfMeasures.FirstAsync(u => u.UOMCode == "PC");

            var products = new[]
            {
                new Product
                {
                    ProductCode = "LAPTOP001",
                    ProductName = "Business Laptop Pro 15\"",
                    CategoryID = compCategory.CategoryID,
                    InventoryUOMID = uom.UOMID,
                    ListPrice = 899.99m,
                    StandardCost = 650.00m,
                    MinStockLevel = 10,
                    MaxStockLevel = 50,
                    ReorderPoint = 15,
                    LeadTimeDays = 7,
                    IsActive = true
                },
                new Product
                {
                    ProductCode = "DESKTOP001",
                    ProductName = "Office Desktop PC",
                    CategoryID = compCategory.CategoryID,
                    InventoryUOMID = uom.UOMID,
                    ListPrice = 599.99m,
                    StandardCost = 420.00m,
                    MinStockLevel = 5,
                    MaxStockLevel = 25,
                    ReorderPoint = 8,
                    LeadTimeDays = 5,
                    IsActive = true
                },
                new Product
                {
                    ProductCode = "DESK001",
                    ProductName = "Executive Office Desk",
                    CategoryID = furnCategory.CategoryID,
                    InventoryUOMID = uom.UOMID,
                    ListPrice = 399.99m,
                    StandardCost = 250.00m,
                    MinStockLevel = 3,
                    MaxStockLevel = 15,
                    ReorderPoint = 5,
                    LeadTimeDays = 14,
                    IsActive = true
                },
                new Product
                {
                    ProductCode = "CHAIR001",
                    ProductName = "Ergonomic Office Chair",
                    CategoryID = furnCategory.CategoryID,
                    InventoryUOMID = uom.UOMID,
                    ListPrice = 249.99m,
                    StandardCost = 150.00m,
                    MinStockLevel = 5,
                    MaxStockLevel = 30,
                    ReorderPoint = 10,
                    LeadTimeDays = 10,
                    IsActive = true
                },
                new Product
                {
                    ProductCode = "MONITOR001",
                    ProductName = "27\" LED Monitor",
                    CategoryID = compCategory.CategoryID,
                    InventoryUOMID = uom.UOMID,
                    ListPrice = 299.99m,
                    StandardCost = 200.00m,
                    MinStockLevel = 8,
                    MaxStockLevel = 40,
                    ReorderPoint = 12,
                    LeadTimeDays = 5,
                    IsActive = true
                }
            };

            _context.Products.AddRange(products);
            await _context.SaveChangesAsync();
        }

        private async Task SeedInventory()
        {
            if (await _context.Inventory.AnyAsync()) return;

            var warehouse = await _context.Warehouses.FirstAsync();
            var products = await _context.Products.ToListAsync();

            // Create warehouse locations
            var locations = new[]
            {
                new WarehouseLocation
                {
                    WarehouseID = warehouse.WarehouseID,
                    LocationCode = "A-01-01",
                    LocationName = "Aisle A Bay 01 Level 01",
                    LocationType = "Storage",
                    Aisle = "A",
                    Bay = "01",
                    Level = "01",
                    Capacity = 100,
                    CurrentOccupancy = 50,
                    IsActive = true
                },
                new WarehouseLocation
                {
                    WarehouseID = warehouse.WarehouseID,
                    LocationCode = "A-01-02",
                    LocationName = "Aisle A Bay 01 Level 02",
                    LocationType = "Storage",
                    Aisle = "A",
                    Bay = "01",
                    Level = "02",
                    Capacity = 100,
                    CurrentOccupancy = 30,
                    IsActive = true
                }
            };

            _context.WarehouseLocations.AddRange(locations);
            await _context.SaveChangesAsync();

            var location1 = locations[0];
            var location2 = locations[1];

            // Add inventory
            var inventoryItems = new[]
            {
                new Inventory
                {
                    WarehouseID = warehouse.WarehouseID,
                    LocationID = location1.LocationID,
                    ProductID = products[0].ProductID, // Laptop
                    QuantityOnHand = 25,
                    QuantityReserved = 5,
                    QuantityAvailable = 20,
                    LastCountDate = DateTime.Now
                },
                new Inventory
                {
                    WarehouseID = warehouse.WarehouseID,
                    LocationID = location1.LocationID,
                    ProductID = products[1].ProductID, // Desktop
                    QuantityOnHand = 15,
                    QuantityReserved = 2,
                    QuantityAvailable = 13,
                    LastCountDate = DateTime.Now
                },
                new Inventory
                {
                    WarehouseID = warehouse.WarehouseID,
                    LocationID = location2.LocationID,
                    ProductID = products[2].ProductID, // Desk
                    QuantityOnHand = 8,
                    QuantityReserved = 1,
                    QuantityAvailable = 7,
                    LastCountDate = DateTime.Now
                },
                new Inventory
                {
                    WarehouseID = warehouse.WarehouseID,
                    LocationID = location2.LocationID,
                    ProductID = products[3].ProductID, // Chair
                    QuantityOnHand = 20,
                    QuantityReserved = 3,
                    QuantityAvailable = 17,
                    LastCountDate = DateTime.Now
                },
                new Inventory
                {
                    WarehouseID = warehouse.WarehouseID,
                    LocationID = location1.LocationID,
                    ProductID = products[4].ProductID, // Monitor
                    QuantityOnHand = 5, // Low stock!
                    QuantityReserved = 1,
                    QuantityAvailable = 4,
                    LastCountDate = DateTime.Now
                }
            };

            _context.Inventory.AddRange(inventoryItems);
            await _context.SaveChangesAsync();
        }

        private async Task SeedSalesOrders()
        {
            if (await _context.SalesOrders.AnyAsync()) return;

            var branch = await _context.Branches.FirstAsync();
            var customers = await _context.Customers.ToListAsync();
            var products = await _context.Products.ToListAsync();
            var user = await _context.Users.FirstAsync();

            var orders = new[]
            {
                new SalesOrder
                {
                    OrderNumber = "SO-2025-0001",
                    CustomerID = customers[0].CustomerID,
                    OrderDate = DateTime.Today,
                    RequestedDate = DateTime.Today.AddDays(7),
                    OrderStatus = "Confirmed",
                    SubTotal = 2500.00m,
                    TaxAmount = 500.00m,
                    TotalAmount = 3000.00m,
                    Currency = "GBP",
                    PaymentTerms = "Net 30",
                    CreatedByUserID = user.UserID
                },
                new SalesOrder
                {
                    OrderNumber = "SO-2025-0002",
                    CustomerID = customers[1].CustomerID,
                    OrderDate = DateTime.Today.AddDays(-1),
                    RequestedDate = DateTime.Today.AddDays(5),
                    OrderStatus = "Processing",
                    SubTotal = 1800.00m,
                    TaxAmount = 360.00m,
                    TotalAmount = 2160.00m,
                    Currency = "GBP",
                    PaymentTerms = "Net 45",
                    CreatedByUserID = user.UserID
                },
                new SalesOrder
                {
                    OrderNumber = "SO-2025-0003",
                    CustomerID = customers[2].CustomerID,
                    OrderDate = DateTime.Today.AddDays(-2),
                    RequestedDate = DateTime.Today.AddDays(3),
                    OrderStatus = "Pending",
                    SubTotal = 1200.00m,
                    TaxAmount = 240.00m,
                    TotalAmount = 1440.00m,
                    Currency = "GBP",
                    PaymentTerms = "Net 60",
                    CreatedByUserID = user.UserID
                }
            };

            _context.SalesOrders.AddRange(orders);
            await _context.SaveChangesAsync();

            // Add order items
            var orderItems = new[]
            {
                new SalesOrderItem
                {
                    OrderID = orders[0].SalesOrderID,
                    ProductID = products[0].ProductID, // Laptop
                    OrderedQuantity = 2,
                    UnitPrice = 899.99m,
                    TaxAmount = 359.99m,
                    LineTotal = 2159.96m
                },
                new SalesOrderItem
                {
                    OrderID = orders[0].SalesOrderID,
                    ProductID = products[3].ProductID, // Chair
                    OrderedQuantity = 2,
                    UnitPrice = 249.99m,
                    TaxAmount = 99.99m,
                    LineTotal = 599.96m
                },
                new SalesOrderItem
                {
                    OrderID = orders[1].SalesOrderID,
                    ProductID = products[1].ProductID, // Desktop
                    OrderedQuantity = 3,
                    UnitPrice = 599.99m,
                    TaxAmount = 359.99m,
                    LineTotal = 2159.96m
                },
                new SalesOrderItem
                {
                    OrderID = orders[2].SalesOrderID,
                    ProductID = products[2].ProductID, // Desk
                    OrderedQuantity = 3,
                    UnitPrice = 399.99m,
                    TaxAmount = 239.99m,
                    LineTotal = 1439.96m
                }
            };

            _context.SalesOrderItems.AddRange(orderItems);
            await _context.SaveChangesAsync();
        }
    }
}