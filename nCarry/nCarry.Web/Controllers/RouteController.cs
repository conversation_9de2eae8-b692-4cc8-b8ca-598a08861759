using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Rendering;
using Microsoft.EntityFrameworkCore;
using nCarry.Web.Data;
using nCarry.Web.Models.Logistics;
using RouteModel = nCarry.Web.Models.Logistics.DeliveryRoute;

namespace nCarry.Web.Controllers
{
    [Authorize]
    public class RouteController : Controller
    {
        private readonly ApplicationDbContext _context;

        public RouteController(ApplicationDbContext context)
        {
            _context = context;
        }

        // GET: Route
        public async Task<IActionResult> Index(int? branchId, int? zoneId, int? vehicleId)
        {
            var routesQuery = _context.DeliveryRoutes
                .Include(r => r.Branch)
                .Include(r => r.Zone)
                .Include(r => r.Vehicle)
                .Include(r => r.Driver)
                .AsQueryable();

            if (branchId.HasValue)
            {
                routesQuery = routesQuery.Where(r => r.BranchID == branchId.Value);
                ViewBag.SelectedBranchId = branchId.Value;
            }

            if (zoneId.HasValue)
            {
                routesQuery = routesQuery.Where(r => r.ZoneID == zoneId.Value);
                ViewBag.SelectedZoneId = zoneId.Value;
            }

            if (vehicleId.HasValue)
            {
                routesQuery = routesQuery.Where(r => r.VehicleID == vehicleId.Value);
                ViewBag.SelectedVehicleId = vehicleId.Value;
            }

            var routes = await routesQuery
                .OrderBy(r => r.Branch != null ? r.Branch.BranchName : string.Empty)
                .ThenBy(r => r.RouteCode)
                .ToListAsync();

            // Populate filter dropdowns
            ViewBag.Branches = new SelectList(
                await _context.Branches.Where(b => b.IsActive).OrderBy(b => b.BranchName).ToListAsync(),
                "BranchID", "BranchName", branchId);

            ViewBag.Zones = new SelectList(
                await _context.DeliveryZones.Where(z => z.IsActive && (!branchId.HasValue || z.BranchID == branchId.Value))
                    .Include(z => z.Branch)
                    .OrderBy(z => z.ZoneName).ToListAsync(),
                "ZoneID", "ZoneName", zoneId);

            ViewBag.Vehicles = new SelectList(
                await _context.Vehicles.Where(v => v.IsActive && (!branchId.HasValue || v.BranchID == branchId.Value))
                    .OrderBy(v => v.VehicleNumber).ToListAsync(),
                "VehicleID", "VehicleNumber", vehicleId);

            return View(routes);
        }

        // GET: Route/Details/5
        public async Task<IActionResult> Details(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var route = await _context.DeliveryRoutes
                .Include(r => r.Branch)
                .Include(r => r.Zone)
                .Include(r => r.Vehicle)
                .Include(r => r.Driver)
                .Include(r => r.Shipments)
                .FirstOrDefaultAsync(m => m.RouteID == id);

            if (route == null)
            {
                return NotFound();
            }

            return View(route);
        }

        // GET: Route/Create
        public async Task<IActionResult> Create(int? branchId, int? zoneId)
        {
            await PopulateDropdowns(branchId, zoneId);
            
            var route = new RouteModel();
            if (branchId.HasValue)
            {
                route.BranchID = branchId.Value;
            }
            if (zoneId.HasValue)
            {
                route.ZoneID = zoneId.Value;
            }

            return View(route);
        }

        // POST: Route/Create
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Create([Bind("RouteCode,RouteName,RouteType,DriverName,VehicleNumber,VehicleType,VehicleCapacity,MaxStops,EstimatedDuration,MaxDistance,MaxWeight,CoverageZones,PostalCodes,OptimizeForDistance,OptimizeForTime,AllowOvernight,StartTime,EndTime,OperatingDays,Status,Notes")] RouteModel route)
        {
            if (ModelState.IsValid)
            {
                // Check if route code already exists for this branch
                if (await _context.DeliveryRoutes.AnyAsync(r => r.BranchID == route.BranchID && r.RouteCode == route.RouteCode))
                {
                    ModelState.AddModelError("RouteCode", "Route code already exists for this branch.");
                    await PopulateDropdowns(route.BranchID, route.ZoneID);
                    return View(route);
                }

                route.CreatedDate = DateTime.Now;
                route.UpdatedDate = DateTime.Now;

                _context.Add(route);
                await _context.SaveChangesAsync();
                
                TempData["Success"] = "Route created successfully.";
                return RedirectToAction(nameof(Index), new { branchId = route.BranchID });
            }

            await PopulateDropdowns(route.BranchID, route.ZoneID);
            return View(route);
        }

        // GET: Route/Edit/5
        public async Task<IActionResult> Edit(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var route = await _context.DeliveryRoutes.FindAsync(id);
            if (route == null)
            {
                return NotFound();
            }

            await PopulateDropdowns(route.BranchID, route.ZoneID);
            return View(route);
        }

        // POST: Route/Edit/5
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Edit(int id, [Bind("RouteID,RouteCode,RouteName,RouteType,DriverName,VehicleNumber,VehicleType,VehicleCapacity,MaxStops,EstimatedDuration,MaxDistance,MaxWeight,CoverageZones,PostalCodes,OptimizeForDistance,OptimizeForTime,AllowOvernight,StartTime,EndTime,OperatingDays,Status,Notes,CreatedDate,CreatedByUserID")] RouteModel route)
        {
            if (id != route.RouteID)
            {
                return NotFound();
            }

            if (ModelState.IsValid)
            {
                try
                {
                    // Check if route code already exists for this branch (excluding current record)
                    if (await _context.DeliveryRoutes.AnyAsync(r => r.BranchID == route.BranchID && r.RouteCode == route.RouteCode && r.RouteID != id))
                    {
                        ModelState.AddModelError("RouteCode", "Route code already exists for this branch.");
                        await PopulateDropdowns(route.BranchID, route.ZoneID);
                        return View(route);
                    }

                    route.UpdatedDate = DateTime.Now;

                    _context.Update(route);
                    await _context.SaveChangesAsync();
                    
                    TempData["Success"] = "Route updated successfully.";
                }
                catch (DbUpdateConcurrencyException)
                {
                    if (!RouteExists(route.RouteID))
                    {
                        return NotFound();
                    }
                    else
                    {
                        throw;
                    }
                }
                return RedirectToAction(nameof(Index), new { branchId = route.BranchID });
            }

            await PopulateDropdowns(route.BranchID, route.ZoneID);
            return View(route);
        }

        // GET: Route/Delete/5
        public async Task<IActionResult> Delete(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var route = await _context.DeliveryRoutes
                .Include(r => r.Branch)
                .Include(r => r.Zone)
                .Include(r => r.Vehicle)
                .Include(r => r.Driver)
                .FirstOrDefaultAsync(m => m.RouteID == id);
                
            if (route == null)
            {
                return NotFound();
            }

            // Check if route has shipments
            var hasShipments = await _context.Shipments.AnyAsync(s => s.RouteID == id);
            ViewBag.HasShipments = hasShipments;

            return View(route);
        }

        // POST: Route/Delete/5
        [HttpPost, ActionName("Delete")]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> DeleteConfirmed(int id)
        {
            var route = await _context.DeliveryRoutes.FindAsync(id);
            if (route != null)
            {
                // Check if route has shipments
                var hasShipments = await _context.Shipments.AnyAsync(s => s.RouteID == id);
                
                if (hasShipments)
                {
                    TempData["Error"] = "Cannot delete route with existing shipments.";
                    return RedirectToAction(nameof(Index), new { branchId = route.BranchID });
                }

                var branchId = route.BranchID;
                _context.DeliveryRoutes.Remove(route);
                await _context.SaveChangesAsync();
                
                TempData["Success"] = "Route deleted successfully.";
                return RedirectToAction(nameof(Index), new { branchId });
            }

            return RedirectToAction(nameof(Index));
        }

        // GET: Route/Optimize
        public async Task<IActionResult> Optimize(int? branchId, DateTime? date)
        {
            if (!date.HasValue)
            {
                date = DateTime.Today;
            }

            await PopulateDropdowns(branchId);
            ViewBag.SelectedDate = date.Value;

            if (branchId.HasValue)
            {
                // Get pending shipments for the selected branch and date
                var pendingShipments = await _context.Shipments
                    .Include(s => s.DeliveryZone)
                    .Where(s => s.BranchID == branchId.Value && 
                               s.ShipmentDate.Date == date.Value.Date &&
                               s.Status == "Pending")
                    .ToListAsync();

                // Get available routes for the branch
                var availableRoutes = await _context.DeliveryRoutes
                    .Include(r => r.Zone)
                    .Include(r => r.Vehicle)
                    .Where(r => r.BranchID == branchId.Value && r.IsActive)
                    .ToListAsync();

                ViewBag.PendingShipments = pendingShipments;
                ViewBag.AvailableRoutes = availableRoutes;
            }

            return View();
        }

        // POST: Route/OptimizeRoutes
        [HttpPost]
        public async Task<IActionResult> OptimizeRoutes(int branchId, DateTime date)
        {
            // Simple route optimization logic
            var pendingShipments = await _context.Shipments
                .Include(s => s.DeliveryZone)
                .Where(s => s.BranchID == branchId && 
                           s.ShipmentDate.Date == date.Date &&
                           s.Status == "Pending")
                .ToListAsync();

            var availableRoutes = await _context.DeliveryRoutes
                .Include(r => r.Zone)
                .Include(r => r.Vehicle)
                .Where(r => r.BranchID == branchId && r.IsActive)
                .ToListAsync();

            // Group shipments by delivery zone and assign to routes
            var optimizedAssignments = new List<dynamic>();
            
            foreach (var route in availableRoutes)
            {
                var zoneShipments = pendingShipments
                    .Where(s => s.DeliveryZoneID == route.ZoneID)
                    .OrderBy(s => s.Priority)
                    .ToList();

                if (zoneShipments.Any())
                {
                    decimal totalWeight = zoneShipments.Sum(s => s.Weight);
                    decimal totalVolume = zoneShipments.Sum(s => s.Volume);

                    optimizedAssignments.Add(new
                    {
                        Route = route,
                        Shipments = zoneShipments,
                        TotalWeight = totalWeight,
                        TotalVolume = totalVolume,
                        IsOverCapacity = (totalWeight > route.MaxCapacityWeight) ||
                                        (totalVolume > route.MaxCapacityVolume)
                    });
                }
            }

            TempData["Success"] = $"Route optimization completed. {optimizedAssignments.Count} routes assigned.";
            return Json(new { success = true, assignments = optimizedAssignments });
        }

        private bool RouteExists(int id)
        {
            return _context.DeliveryRoutes.Any(e => e.RouteID == id);
        }

        private async Task PopulateDropdowns(int? branchId = null, int? zoneId = null)
        {
            ViewBag.Branches = new SelectList(
                await _context.Branches.Where(b => b.IsActive).OrderBy(b => b.BranchName).ToListAsync(),
                "BranchID", "BranchName", branchId);

            ViewBag.Zones = new SelectList(
                await _context.DeliveryZones.Where(z => z.IsActive && (!branchId.HasValue || z.BranchID == branchId.Value))
                    .OrderBy(z => z.ZoneName).ToListAsync(),
                "ZoneID", "ZoneName", zoneId);

            ViewBag.Vehicles = new SelectList(
                await _context.Vehicles.Where(v => v.IsActive && (!branchId.HasValue || v.BranchID == branchId.Value))
                    .OrderBy(v => v.VehicleNumber).ToListAsync(),
                "VehicleID", "VehicleNumber");

            ViewBag.Drivers = new SelectList(
                await _context.Drivers.Where(d => d.IsActive && (!branchId.HasValue || d.BranchID == branchId.Value))
                    .OrderBy(d => d.DriverName).ToListAsync(),
                "DriverID", "DriverName");
        }
    }
}