using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Rendering;
using Microsoft.EntityFrameworkCore;
using nCarry.Web.Data;
using nCarry.Web.Models.Financial;

namespace nCarry.Web.Controllers
{
    [Authorize]
    public class PaymentController : Controller
    {
        private readonly ApplicationDbContext _context;

        public PaymentController(ApplicationDbContext context)
        {
            _context = context;
        }

        // GET: Payment
        public async Task<IActionResult> Index()
        {
            var payments = await _context.Payments
                .Include(p => p.Customer)
                .Include(p => p.Supplier)
                .Include(p => p.PaymentMethod)
                .Include(p => p.Currency)
                .Where(p => !p.IsDeleted)
                .OrderByDescending(p => p.PaymentDate)
                .ToListAsync();
            return View(payments);
        }

        // GET: Payment/Details/5
        public async Task<IActionResult> Details(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var payment = await _context.Payments
                .Include(p => p.Customer)
                .Include(p => p.Supplier)
                .Include(p => p.PaymentMethod)
                .Include(p => p.Currency)
                .FirstOrDefaultAsync(m => m.PaymentID == id && !m.IsDeleted);
            
            if (payment == null)
            {
                return NotFound();
            }

            return View(payment);
        }

        // GET: Payment/Create
        public IActionResult Create()
        {
            PopulateDropdowns();
            return View();
        }

        // POST: Payment/Create
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Create(Payment payment)
        {
            if (ModelState.IsValid)
            {
                // Generate payment number
                payment.PaymentNumber = await GeneratePaymentNumber();
                
                // Calculate base amount
                if (payment.ExchangeRate > 0)
                {
                    payment.BaseAmount = payment.Amount * payment.ExchangeRate;
                }
                
                // Set audit fields
                payment.CreatedDate = DateTime.Now;
                payment.CreatedByUserID = int.Parse(User.FindFirst(System.Security.Claims.ClaimTypes.NameIdentifier)?.Value ?? "0");
                
                _context.Add(payment);
                await _context.SaveChangesAsync();
                
                TempData["Success"] = "Payment created successfully.";
                return RedirectToAction(nameof(Index));
            }
            
            PopulateDropdowns(payment);
            return View(payment);
        }

        // GET: Payment/Edit/5
        public async Task<IActionResult> Edit(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var payment = await _context.Payments
                .FirstOrDefaultAsync(p => p.PaymentID == id && !p.IsDeleted);
            
            if (payment == null)
            {
                return NotFound();
            }
            
            // Check if payment is already allocated
            if (payment.AllocatedAmount > 0)
            {
                TempData["Error"] = "Cannot edit payment that has been allocated to invoices.";
                return RedirectToAction(nameof(Index));
            }
            
            PopulateDropdowns(payment);
            return View(payment);
        }

        // POST: Payment/Edit/5
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Edit(int id, Payment payment)
        {
            if (id != payment.PaymentID)
            {
                return NotFound();
            }

            var existingPayment = await _context.Payments
                .FirstOrDefaultAsync(p => p.PaymentID == id && !p.IsDeleted);
            
            if (existingPayment == null)
            {
                return NotFound();
            }
            
            // Check if payment is already allocated
            if (existingPayment.AllocatedAmount > 0)
            {
                TempData["Error"] = "Cannot edit payment that has been allocated to invoices.";
                return RedirectToAction(nameof(Index));
            }

            if (ModelState.IsValid)
            {
                try
                {
                    // Update fields
                    existingPayment.PaymentDate = payment.PaymentDate;
                    existingPayment.PaymentType = payment.PaymentType;
                    existingPayment.CustomerID = payment.CustomerID;
                    existingPayment.SupplierID = payment.SupplierID;
                    existingPayment.PaymentMethodID = payment.PaymentMethodID;
                    existingPayment.PaymentStatus = payment.PaymentStatus;
                    existingPayment.BankID = payment.BankID;
                    existingPayment.CheckNumber = payment.CheckNumber;
                    existingPayment.Amount = payment.Amount;
                    existingPayment.CurrencyID = payment.CurrencyID;
                    existingPayment.ExchangeRate = payment.ExchangeRate;
                    existingPayment.Notes = payment.Notes;
                    
                    // Calculate base amount
                    if (payment.ExchangeRate > 0)
                    {
                        existingPayment.BaseAmount = payment.Amount * payment.ExchangeRate;
                    }
                    
                    // Set audit fields
                    existingPayment.UpdatedDate = DateTime.Now;
                    existingPayment.UpdatedByUserID = int.Parse(User.FindFirst(System.Security.Claims.ClaimTypes.NameIdentifier)?.Value ?? "0");
                    
                    _context.Update(existingPayment);
                    await _context.SaveChangesAsync();
                    
                    TempData["Success"] = "Payment updated successfully.";
                    return RedirectToAction(nameof(Index));
                }
                catch (DbUpdateConcurrencyException)
                {
                    if (!PaymentExists(payment.PaymentID))
                    {
                        return NotFound();
                    }
                    else
                    {
                        throw;
                    }
                }
            }
            
            PopulateDropdowns(payment);
            return View(payment);
        }

        // GET: Payment/Delete/5
        public async Task<IActionResult> Delete(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var payment = await _context.Payments
                .Include(p => p.Customer)
                .Include(p => p.Supplier)
                .Include(p => p.PaymentMethod)
                .Include(p => p.Currency)
                .FirstOrDefaultAsync(m => m.PaymentID == id && !m.IsDeleted);
            
            if (payment == null)
            {
                return NotFound();
            }
            
            // Check if payment is already allocated
            if (payment.AllocatedAmount > 0)
            {
                TempData["Error"] = "Cannot delete payment that has been allocated to invoices.";
                return RedirectToAction(nameof(Index));
            }

            return View(payment);
        }

        // POST: Payment/Delete/5
        [HttpPost, ActionName("Delete")]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> DeleteConfirmed(int id)
        {
            var payment = await _context.Payments
                .FirstOrDefaultAsync(p => p.PaymentID == id && !p.IsDeleted);
            
            if (payment != null)
            {
                // Check if payment is already allocated
                if (payment.AllocatedAmount > 0)
                {
                    TempData["Error"] = "Cannot delete payment that has been allocated to invoices.";
                    return RedirectToAction(nameof(Index));
                }
                
                // Soft delete
                payment.IsDeleted = true;
                payment.DeletedDate = DateTime.Now;
                payment.DeletedByUserID = int.Parse(User.FindFirst(System.Security.Claims.ClaimTypes.NameIdentifier)?.Value ?? "0");
                
                _context.Update(payment);
                await _context.SaveChangesAsync();
                
                TempData["Success"] = "Payment deleted successfully.";
            }

            return RedirectToAction(nameof(Index));
        }

        private bool PaymentExists(int id)
        {
            return _context.Payments.Any(e => e.PaymentID == id && !e.IsDeleted);
        }
        
        private void PopulateDropdowns(Payment? payment = null)
        {
            // Payment types
            ViewBag.PaymentTypes = new SelectList(new[]
            {
                new { Value = "Customer", Text = "Customer Payment" },
                new { Value = "Supplier", Text = "Supplier Payment" }
            }, "Value", "Text", payment?.PaymentType);
            
            // Payment statuses
            ViewBag.PaymentStatuses = new SelectList(new[]
            {
                new { Value = "Pending", Text = "Pending" },
                new { Value = "Processing", Text = "Processing" },
                new { Value = "Completed", Text = "Completed" },
                new { Value = "Failed", Text = "Failed" },
                new { Value = "Cancelled", Text = "Cancelled" },
                new { Value = "Reversed", Text = "Reversed" }
            }, "Value", "Text", payment?.PaymentStatus);
            
            // Customers
            ViewBag.CustomerID = new SelectList(
                _context.Customers.Where(c => c.IsActive && !c.IsDeleted).OrderBy(c => c.CustomerName),
                "CustomerID", "CustomerName", payment?.CustomerID);
            
            // Suppliers
            ViewBag.SupplierID = new SelectList(
                _context.Suppliers.Where(s => s.IsActive && !s.IsDeleted).OrderBy(s => s.SupplierName),
                "SupplierID", "SupplierName", payment?.SupplierID);
            
            // Payment methods
            ViewBag.PaymentMethodID = new SelectList(
                _context.PaymentMethods.Where(pm => pm.IsActive).OrderBy(pm => pm.MethodName),
                "PaymentMethodID", "MethodName", payment?.PaymentMethodID);
            
            // Currencies
            ViewBag.CurrencyID = new SelectList(
                _context.Currencies.Where(c => c.IsActive).OrderBy(c => c.CurrencyCode),
                "CurrencyID", "CurrencyCode", payment?.CurrencyID);
        }
        
        private async Task<string> GeneratePaymentNumber()
        {
            var lastPayment = await _context.Payments
                .OrderByDescending(p => p.PaymentID)
                .FirstOrDefaultAsync();
            
            int nextNumber = 1;
            if (lastPayment != null)
            {
                // Extract number from last payment number
                var lastNumber = lastPayment.PaymentNumber.Replace("PMT", "").Replace("-", "");
                if (int.TryParse(lastNumber, out int number))
                {
                    nextNumber = number + 1;
                }
            }
            
            return $"PMT-{DateTime.Now:yyyy}-{nextNumber:D6}";
        }
    }
}