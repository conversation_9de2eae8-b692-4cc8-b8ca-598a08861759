using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using nCarry.Web.Data;
using nCarry.Web.Models.System;
using System.Text.Json;

namespace nCarry.Web.Controllers
{
    [Authorize]
    public class KPIController : Controller
    {
        private readonly ApplicationDbContext _context;

        public KPIController(ApplicationDbContext context)
        {
            _context = context;
        }

        // GET: KPI
        public async Task<IActionResult> Index(string? category, string? type, bool? isActive)
        {
            var kpisQuery = _context.KPIs.AsQueryable();

            if (!string.IsNullOrEmpty(category))
            {
                kpisQuery = kpisQuery.Where(k => k.Category == category);
                ViewBag.SelectedCategory = category;
            }

            // KPIType is a NotMapped property, so we can't filter in the database query
            // We'll filter after retrieving the data

            if (isActive.HasValue)
            {
                kpisQuery = kpisQuery.Where(k => k.IsActive == isActive.Value);
                ViewBag.SelectedIsActive = isActive.Value;
            }

            var kpis = await kpisQuery
                .OrderBy(k => k.Category)
                .ThenBy(k => k.KPIName)  // Remove DisplayOrder as it's NotMapped
                .ToListAsync();

            // Filter by type after retrieving from database since KPIType is NotMapped
            if (!string.IsNullOrEmpty(type))
            {
                kpis = kpis.Where(k => k.KPIType == type).ToList();
                ViewBag.SelectedType = type;
            }

            // Populate filter dropdowns
            PopulateFilterDropdowns();

            return View(kpis);
        }

        // GET: KPI/Details/5
        public async Task<IActionResult> Details(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var kpi = await _context.KPIs.FirstOrDefaultAsync(m => m.KPIID == id);
            if (kpi == null)
            {
                return NotFound();
            }

            // Calculate current value
            ViewBag.CurrentValue = await CalculateKPIValue(kpi);
            ViewBag.TrendData = await GetKPITrendData(kpi);

            return View(kpi);
        }

        // GET: KPI/Create
        public IActionResult Create()
        {
            PopulateDropdowns();
            
            var kpi = new KPI
            {
                IsActive = true,
                RefreshInterval = 60,
                DisplayOrder = 1,
                DataFormat = "Number"
            };

            return View(kpi);
        }

        // POST: KPI/Create
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Create([Bind("KPICode,Category,KPIName,Description,KPIType,DataSource,SqlQuery,CalculationMethod,TargetValue,MinValue,MaxValue,Unit,DataFormat,RefreshInterval,DashboardWidget,ChartType,DisplayOrder,ThresholdSettings,IsActive")] KPI kpi)
        {
            if (ModelState.IsValid)
            {
                // Generate KPICode if not provided
                if (string.IsNullOrEmpty(kpi.KPICode))
                {
                    // Generate a unique code based on category and name
                    var prefix = kpi.Category.Substring(0, Math.Min(3, kpi.Category.Length)).ToUpper();
                    var suffix = DateTime.Now.ToString("yyyyMMddHHmmss");
                    kpi.KPICode = $"{prefix}-{suffix}";
                }

                kpi.CreatedDate = DateTime.Now;
                kpi.UpdatedDate = DateTime.Now;

                _context.Add(kpi);
                await _context.SaveChangesAsync();
                
                TempData["Success"] = "KPI created successfully.";
                return RedirectToAction(nameof(Index));
            }

            PopulateDropdowns();
            return View(kpi);
        }

        // GET: KPI/Edit/5
        public async Task<IActionResult> Edit(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var kpi = await _context.KPIs.FindAsync(id);
            if (kpi == null)
            {
                return NotFound();
            }

            PopulateDropdowns();
            return View(kpi);
        }

        // POST: KPI/Edit/5
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Edit(int id, [Bind("KPIID,KPICode,Category,KPIName,Description,KPIType,DataSource,SqlQuery,CalculationMethod,TargetValue,MinValue,MaxValue,Unit,DataFormat,RefreshInterval,DashboardWidget,ChartType,DisplayOrder,ThresholdSettings,IsActive,CreatedDate,CreatedByUserID")] KPI kpi)
        {
            if (id != kpi.KPIID)
            {
                return NotFound();
            }

            if (ModelState.IsValid)
            {
                try
                {
                    kpi.UpdatedDate = DateTime.Now;
                    _context.Update(kpi);
                    await _context.SaveChangesAsync();
                    
                    TempData["Success"] = "KPI updated successfully.";
                }
                catch (DbUpdateConcurrencyException)
                {
                    if (!KPIExists(kpi.KPIID))
                    {
                        return NotFound();
                    }
                    else
                    {
                        throw;
                    }
                }
                return RedirectToAction(nameof(Index));
            }

            PopulateDropdowns();
            return View(kpi);
        }

        // GET: KPI/Delete/5
        public async Task<IActionResult> Delete(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var kpi = await _context.KPIs.FirstOrDefaultAsync(m => m.KPIID == id);
            if (kpi == null)
            {
                return NotFound();
            }

            return View(kpi);
        }

        // POST: KPI/Delete/5
        [HttpPost, ActionName("Delete")]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> DeleteConfirmed(int id)
        {
            var kpi = await _context.KPIs.FindAsync(id);
            if (kpi != null)
            {
                _context.KPIs.Remove(kpi);
                await _context.SaveChangesAsync();
                
                TempData["Success"] = "KPI deleted successfully.";
            }

            return RedirectToAction(nameof(Index));
        }

        // GET: KPI/Configure
        public async Task<IActionResult> Configure()
        {
            var dashboardConfig = await GetDashboardConfiguration();
            return View(dashboardConfig);
        }

        // POST: KPI/SaveConfiguration
        [HttpPost]
        public async Task<IActionResult> SaveConfiguration([FromBody] DashboardConfiguration config)
        {
            try
            {
                // Save dashboard configuration
                await SaveDashboardConfiguration(config);
                return Json(new { success = true, message = "Dashboard configuration saved successfully." });
            }
            catch (Exception ex)
            {
                return Json(new { success = false, message = ex.Message });
            }
        }

        // GET: KPI/GetValue/5
        public async Task<IActionResult> GetValue(int id)
        {
            var kpi = await _context.KPIs.FindAsync(id);
            if (kpi == null)
            {
                return Json(new { success = false, message = "KPI not found." });
            }

            var value = await CalculateKPIValue(kpi);
            var formatted = FormatKPIValue(value, kpi);
            var status = GetKPIStatus(value, kpi);

            return Json(new 
            { 
                success = true, 
                value = value, 
                formattedValue = formatted,
                status = status,
                timestamp = DateTime.Now
            });
        }

        // GET: KPI/GetChartData/5
        public async Task<IActionResult> GetChartData(int id, string period = "day")
        {
            var kpi = await _context.KPIs.FindAsync(id);
            if (kpi == null)
            {
                return Json(new { success = false, message = "KPI not found." });
            }

            var chartData = await GetKPIChartData(kpi, period);
            return Json(new { success = true, data = chartData });
        }

        // GET: KPI/TestQuery
        public async Task<IActionResult> TestQuery(string query, string dataSource)
        {
            try
            {
                var result = await ExecuteKPIQuery(query, dataSource);
                return Json(new { success = true, result = result, message = "Query executed successfully." });
            }
            catch (Exception ex)
            {
                return Json(new { success = false, message = $"Query error: {ex.Message}" });
            }
        }

        // GET: KPI/LoadDefaults
        public async Task<IActionResult> LoadDefaults()
        {
            await LoadDefaultKPIs();
            TempData["Success"] = "Default KPIs loaded successfully.";
            return RedirectToAction(nameof(Index));
        }

        private Task<decimal> CalculateKPIValue(KPI kpi)
        {
            // Simulate KPI calculation based on data source
            var random = new Random();
            decimal result = 0;
            
            switch (kpi.DataSource)
            {
                case "Database":
                    // Execute SQL query
                    if (!string.IsNullOrEmpty(kpi.SqlQuery))
                    {
                        // In real implementation, execute the query
                        result = random.Next(100, 1000);
                    }
                    break;
                    
                case "API":
                    // Call external API
                    result = random.Next(50, 500);
                    break;
                    
                case "Calculated":
                    // Apply calculation method
                    result = random.Next(75, 750);
                    break;
                    
                default:
                    result = random.Next(0, 100);
                    break;
            }
            
            return Task.FromResult(result);
        }

        private string FormatKPIValue(decimal value, KPI kpi)
        {
            switch (kpi.DataFormat)
            {
                case "Currency":
                    return $"${value:N2}";
                case "Percentage":
                    return $"{value:N1}%";
                case "Number":
                    return value.ToString("N0");
                case "Decimal":
                    return value.ToString("N2");
                default:
                    return value.ToString();
            }
        }

        private string GetKPIStatus(decimal value, KPI kpi)
        {
            if (!string.IsNullOrEmpty(kpi.ThresholdSettings))
            {
                try
                {
                    var thresholds = JsonSerializer.Deserialize<Dictionary<string, decimal>>(kpi.ThresholdSettings);
                    
                    if (thresholds != null)
                    {
                        if (thresholds.ContainsKey("danger") && value <= thresholds["danger"])
                            return "danger";
                        if (thresholds.ContainsKey("warning") && value <= thresholds["warning"])
                            return "warning";
                        if (thresholds.ContainsKey("success") && value >= thresholds["success"])
                            return "success";
                    }
                }
                catch { }
            }
            
            return "info";
        }

        private async Task<object> GetKPITrendData(KPI kpi)
        {
            // Simulate trend data
            var random = new Random();
            var labels = new List<string>();
            var data = new List<decimal>();
            
            for (int i = 6; i >= 0; i--)
            {
                labels.Add(DateTime.Now.AddDays(-i).ToString("MMM dd"));
                data.Add(random.Next(50, 150));
            }
            
            return await Task.FromResult(new { labels, data });
        }

        private async Task<object> GetKPIChartData(KPI kpi, string period)
        {
            // Simulate chart data based on period
            var random = new Random();
            var labels = new List<string>();
            var datasets = new List<object>();
            
            switch (period)
            {
                case "hour":
                    for (int i = 23; i >= 0; i--)
                    {
                        labels.Add($"{i}:00");
                    }
                    break;
                case "day":
                    for (int i = 6; i >= 0; i--)
                    {
                        labels.Add(DateTime.Now.AddDays(-i).ToString("MMM dd"));
                    }
                    break;
                case "month":
                    for (int i = 11; i >= 0; i--)
                    {
                        labels.Add(DateTime.Now.AddMonths(-i).ToString("MMM"));
                    }
                    break;
            }
            
            var data = labels.Select(_ => (decimal)random.Next(100, 500)).ToList();
            
            datasets.Add(new
            {
                label = kpi.KPIName,
                data = data,
                borderColor = "rgb(75, 192, 192)",
                backgroundColor = "rgba(75, 192, 192, 0.2)"
            });
            
            return await Task.FromResult(new { labels, datasets });
        }

        private async Task<object> ExecuteKPIQuery(string query, string dataSource)
        {
            // Simulate query execution
            await Task.Delay(100);
            return new { value = new Random().Next(100, 1000), rows = 1 };
        }

        private async Task<DashboardConfiguration> GetDashboardConfiguration()
        {
            // Load dashboard configuration from database or cache
            var config = new DashboardConfiguration
            {
                RefreshInterval = 60,
                Theme = "light",
                Layout = "grid",
                Widgets = new List<DashboardWidget>
                {
                    new DashboardWidget { WidgetId = "sales-today", Position = 1, Size = "small" },
                    new DashboardWidget { WidgetId = "revenue-chart", Position = 2, Size = "large" },
                    new DashboardWidget { WidgetId = "inventory-status", Position = 3, Size = "medium" }
                }
            };
            
            return await Task.FromResult(config);
        }

        private async Task SaveDashboardConfiguration(DashboardConfiguration config)
        {
            // Save dashboard configuration to database
            await Task.Delay(100);
        }

        private async Task LoadDefaultKPIs()
        {
            var defaultKPIs = new List<KPI>
            {
                // Sales KPIs
                new KPI 
                { 
                    KPICode = "SAL-DAILY-001",
                    Category = "Sales", 
                    KPIName = "Daily Sales", 
                    Description = "Total sales amount for today", 
                    KPIType = "Metric", 
                    DataSource = "Database", 
                    SqlQuery = "SELECT SUM(TotalAmount) FROM SalesOrder WHERE CAST(OrderDate AS DATE) = CAST(GETDATE() AS DATE)",
                    TargetValue = 50000,
                    Unit = "$",
                    DataFormat = "Currency",
                    RefreshInterval = 300,
                    DashboardWidget = "SalesCard",
                    ChartType = "Line",
                    DisplayOrder = 1,
                    ThresholdSettings = "{\"danger\": 10000, \"warning\": 30000, \"success\": 50000}",
                    IsActive = true
                },
                new KPI 
                { 
                    KPICode = "SAL-ORDER-001",
                    Category = "Sales", 
                    KPIName = "Order Count", 
                    Description = "Number of orders today", 
                    KPIType = "Counter", 
                    DataSource = "Database", 
                    SqlQuery = "SELECT COUNT(*) FROM SalesOrder WHERE CAST(OrderDate AS DATE) = CAST(GETDATE() AS DATE)",
                    TargetValue = 100,
                    DataFormat = "Number",
                    RefreshInterval = 300,
                    DashboardWidget = "CounterCard",
                    DisplayOrder = 2,
                    IsActive = true
                },
                
                // Inventory KPIs
                new KPI 
                { 
                    KPICode = "INV-STOCK-001",
                    Category = "Inventory", 
                    KPIName = "Low Stock Items", 
                    Description = "Products below minimum stock level", 
                    KPIType = "Alert", 
                    DataSource = "Database", 
                    SqlQuery = "SELECT COUNT(*) FROM Inventory WHERE QuantityOnHand < MinimumStock",
                    MaxValue = 50,
                    DataFormat = "Number",
                    RefreshInterval = 600,
                    DashboardWidget = "AlertCard",
                    DisplayOrder = 1,
                    ThresholdSettings = "{\"danger\": 20, \"warning\": 10, \"success\": 5}",
                    IsActive = true
                },
                
                // Customer KPIs
                new KPI 
                { 
                    KPICode = "CUS-NEW-001",
                    Category = "Customer", 
                    KPIName = "New Customers", 
                    Description = "New customers this month", 
                    KPIType = "Growth", 
                    DataSource = "Database", 
                    SqlQuery = "SELECT COUNT(*) FROM Customer WHERE MONTH(CreatedDate) = MONTH(GETDATE()) AND YEAR(CreatedDate) = YEAR(GETDATE())",
                    TargetValue = 50,
                    DataFormat = "Number",
                    RefreshInterval = 3600,
                    DashboardWidget = "GrowthCard",
                    ChartType = "Bar",
                    DisplayOrder = 1,
                    IsActive = true
                },
                
                // Performance KPIs
                new KPI 
                { 
                    KPICode = "PER-FULFILL-001",
                    Category = "Performance", 
                    KPIName = "Order Fulfillment Rate", 
                    Description = "Percentage of orders fulfilled on time", 
                    KPIType = "Percentage", 
                    DataSource = "Calculated", 
                    CalculationMethod = "OnTimeDeliveries / TotalDeliveries * 100",
                    TargetValue = 95,
                    MinValue = 0,
                    MaxValue = 100,
                    Unit = "%",
                    DataFormat = "Percentage",
                    RefreshInterval = 1800,
                    DashboardWidget = "GaugeChart",
                    ChartType = "Gauge",
                    DisplayOrder = 1,
                    ThresholdSettings = "{\"danger\": 80, \"warning\": 90, \"success\": 95}",
                    IsActive = true
                }
            };

            // Remove existing KPIs
            var existingKPIs = await _context.KPIs.ToListAsync();
            _context.KPIs.RemoveRange(existingKPIs);

            // Add new KPIs
            foreach (var kpi in defaultKPIs)
            {
                kpi.CreatedDate = DateTime.Now;
                kpi.UpdatedDate = DateTime.Now;
            }

            _context.KPIs.AddRange(defaultKPIs);
            await _context.SaveChangesAsync();
        }

        private bool KPIExists(int id)
        {
            return _context.KPIs.Any(e => e.KPIID == id);
        }

        private void PopulateDropdowns()
        {
            ViewBag.Categories = new List<string> { "Sales", "Purchase", "Inventory", "Customer", "Supplier", "Financial", "Performance", "Logistics" };
            ViewBag.KPITypes = new List<string> { "Metric", "Counter", "Percentage", "Growth", "Alert", "Trend", "Ratio" };
            ViewBag.DataSources = new List<string> { "Database", "API", "Calculated", "Manual", "External" };
            ViewBag.DataFormats = new List<string> { "Number", "Currency", "Percentage", "Decimal", "Time", "Text" };
            ViewBag.ChartTypes = new List<string> { "Line", "Bar", "Pie", "Doughnut", "Area", "Gauge", "Card" };
            ViewBag.DashboardWidgets = new List<string> { "SalesCard", "CounterCard", "AlertCard", "GrowthCard", "ChartWidget", "GaugeChart", "TableWidget" };
        }

        private void PopulateFilterDropdowns()
        {
            var categories = _context.KPIs.Select(k => k.Category).Distinct().OrderBy(c => c).ToList();
            
            // KPIType is NotMapped, so we need to get all KPIs first, then extract types
            var allKpis = _context.KPIs.ToList();
            var types = allKpis.Select(k => k.KPIType).Distinct().OrderBy(t => t).ToList();

            ViewBag.Categories = categories;
            ViewBag.Types = types;
            ViewBag.ActiveFilter = new List<object>
            {
                new { Value = "", Text = "All" },
                new { Value = "true", Text = "Active" },
                new { Value = "false", Text = "Inactive" }
            };
        }
    }

    // Helper classes
    public class DashboardConfiguration
    {
        public int RefreshInterval { get; set; }
        public string Theme { get; set; } = "light";
        public string Layout { get; set; } = "grid";
        public List<DashboardWidget> Widgets { get; set; } = new List<DashboardWidget>();
    }

    public class DashboardWidget
    {
        public string WidgetId { get; set; } = string.Empty;
        public int Position { get; set; }
        public string Size { get; set; } = "medium";
    }
}