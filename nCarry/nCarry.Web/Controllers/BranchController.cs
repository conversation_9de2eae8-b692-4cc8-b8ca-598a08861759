using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Rendering;
using Microsoft.EntityFrameworkCore;
using nCarry.Web.Data;
using nCarry.Web.Models.System;

namespace nCarry.Web.Controllers
{
    [Authorize]
    public class BranchController : Controller
    {
        private readonly ApplicationDbContext _context;

        public BranchController(ApplicationDbContext context)
        {
            _context = context;
        }

        // GET: Branch
        public async Task<IActionResult> Index(int? companyId)
        {
            var branchesQuery = _context.Branches
                .Include(b => b.Company)
                .Include(b => b.Warehouse)
                .AsQueryable();

            if (companyId.HasValue)
            {
                branchesQuery = branchesQuery.Where(b => b.CompanyID == companyId.Value);
                ViewBag.SelectedCompanyId = companyId.Value;
                ViewBag.SelectedCompanyName = await _context.Companies
                    .Where(c => c.CompanyID == companyId.Value)
                    .Select(c => c.CompanyName)
                    .FirstOrDefaultAsync();
            }

            var branches = await branchesQuery
                .OrderBy(b => b.Company.CompanyName)
                .ThenBy(b => b.BranchName)
                .ToListAsync();

            // Populate company filter dropdown
            ViewBag.Companies = new SelectList(
                await _context.Companies.OrderBy(c => c.CompanyName).ToListAsync(),
                "CompanyID", "CompanyName", companyId);

            return View(branches);
        }

        // GET: Branch/Details/5
        public async Task<IActionResult> Details(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var branch = await _context.Branches
                .Include(b => b.Company)
                .Include(b => b.Warehouse)
                .FirstOrDefaultAsync(m => m.BranchID == id);

            if (branch == null)
            {
                return NotFound();
            }

            return View(branch);
        }

        // GET: Branch/Create
        public async Task<IActionResult> Create(int? companyId)
        {
            await PopulateDropdowns(companyId);
            
            var branch = new Branch();
            if (companyId.HasValue)
            {
                branch.CompanyID = companyId.Value;
            }

            return View(branch);
        }

        // POST: Branch/Create
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Create([Bind("CompanyID,BranchCode,BranchName,BranchType,WarehouseID,Address1,Address2,City,State,PostCode,Country,Phone,Email,ManagerName,TimeZone,IsActive,IsDefault")] Branch branch)
        {
            if (ModelState.IsValid)
            {
                // Check if branch code already exists for this company
                if (await _context.Branches.AnyAsync(b => b.CompanyID == branch.CompanyID && b.BranchCode == branch.BranchCode))
                {
                    ModelState.AddModelError("BranchCode", "Branch code already exists for this company.");
                    await PopulateDropdowns(branch.CompanyID);
                    return View(branch);
                }

                // If setting as default, unset other defaults for this company
                if (branch.IsDefault)
                {
                    var existingDefaults = await _context.Branches
                        .Where(b => b.CompanyID == branch.CompanyID && b.IsDefault)
                        .ToListAsync();
                    
                    foreach (var b in existingDefaults)
                    {
                        b.IsDefault = false;
                    }
                }

                branch.CreatedDate = DateTime.Now;
                branch.UpdatedDate = DateTime.Now;

                _context.Add(branch);
                await _context.SaveChangesAsync();
                
                TempData["Success"] = "Branch created successfully.";
                return RedirectToAction(nameof(Index), new { companyId = branch.CompanyID });
            }

            await PopulateDropdowns(branch.CompanyID);
            return View(branch);
        }

        // GET: Branch/Edit/5
        public async Task<IActionResult> Edit(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var branch = await _context.Branches.FindAsync(id);
            if (branch == null)
            {
                return NotFound();
            }

            await PopulateDropdowns(branch.CompanyID);
            return View(branch);
        }

        // POST: Branch/Edit/5
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Edit(int id, [Bind("BranchID,CompanyID,BranchCode,BranchName,BranchType,WarehouseID,Address1,Address2,City,State,PostCode,Country,Phone,Email,ManagerName,TimeZone,IsActive,IsDefault,CreatedDate")] Branch branch)
        {
            if (id != branch.BranchID)
            {
                return NotFound();
            }

            if (ModelState.IsValid)
            {
                try
                {
                    // Check if branch code already exists for this company (excluding current record)
                    if (await _context.Branches.AnyAsync(b => b.CompanyID == branch.CompanyID && b.BranchCode == branch.BranchCode && b.BranchID != id))
                    {
                        ModelState.AddModelError("BranchCode", "Branch code already exists for this company.");
                        await PopulateDropdowns(branch.CompanyID);
                        return View(branch);
                    }

                    // If setting as default, unset other defaults for this company
                    if (branch.IsDefault)
                    {
                        var existingDefaults = await _context.Branches
                            .Where(b => b.CompanyID == branch.CompanyID && b.IsDefault && b.BranchID != id)
                            .ToListAsync();
                        
                        foreach (var b in existingDefaults)
                        {
                            b.IsDefault = false;
                        }
                    }

                    branch.UpdatedDate = DateTime.Now;

                    _context.Update(branch);
                    await _context.SaveChangesAsync();
                    
                    TempData["Success"] = "Branch updated successfully.";
                }
                catch (DbUpdateConcurrencyException)
                {
                    if (!BranchExists(branch.BranchID))
                    {
                        return NotFound();
                    }
                    else
                    {
                        throw;
                    }
                }
                return RedirectToAction(nameof(Index), new { companyId = branch.CompanyID });
            }

            await PopulateDropdowns(branch.CompanyID);
            return View(branch);
        }

        // GET: Branch/Delete/5
        public async Task<IActionResult> Delete(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var branch = await _context.Branches
                .Include(b => b.Company)
                .Include(b => b.Warehouse)
                .FirstOrDefaultAsync(m => m.BranchID == id);
                
            if (branch == null)
            {
                return NotFound();
            }

            return View(branch);
        }

        // POST: Branch/Delete/5
        [HttpPost, ActionName("Delete")]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> DeleteConfirmed(int id)
        {
            var branch = await _context.Branches.FindAsync(id);
            if (branch != null)
            {
                var companyId = branch.CompanyID;
                _context.Branches.Remove(branch);
                await _context.SaveChangesAsync();
                
                TempData["Success"] = "Branch deleted successfully.";
                return RedirectToAction(nameof(Index), new { companyId });
            }

            return RedirectToAction(nameof(Index));
        }

        private bool BranchExists(int id)
        {
            return _context.Branches.Any(e => e.BranchID == id);
        }

        private async Task PopulateDropdowns(int? companyId = null)
        {
            ViewBag.Companies = new SelectList(
                await _context.Companies.OrderBy(c => c.CompanyName).ToListAsync(),
                "CompanyID", "CompanyName", companyId);

            ViewBag.Warehouses = new SelectList(
                await _context.Warehouses.Where(w => w.IsActive).OrderBy(w => w.WarehouseName).ToListAsync(),
                "WarehouseID", "WarehouseName");

            ViewBag.BranchTypes = new List<SelectListItem>
            {
                new SelectListItem { Value = "Office", Text = "Office" },
                new SelectListItem { Value = "Warehouse", Text = "Warehouse" },
                new SelectListItem { Value = "Store", Text = "Store" },
                new SelectListItem { Value = "Factory", Text = "Factory" }
            };

            ViewBag.TimeZones = new List<SelectListItem>
            {
                new SelectListItem { Value = "GMT Standard Time", Text = "GMT Standard Time" },
                new SelectListItem { Value = "UTC", Text = "UTC" },
                new SelectListItem { Value = "Eastern Standard Time", Text = "Eastern Standard Time" },
                new SelectListItem { Value = "Central Standard Time", Text = "Central Standard Time" },
                new SelectListItem { Value = "Mountain Standard Time", Text = "Mountain Standard Time" },
                new SelectListItem { Value = "Pacific Standard Time", Text = "Pacific Standard Time" },
                new SelectListItem { Value = "W. Europe Standard Time", Text = "W. Europe Standard Time" }
            };
        }
    }
}