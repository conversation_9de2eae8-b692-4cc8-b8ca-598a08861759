using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Rendering;
using Microsoft.EntityFrameworkCore;
using nCarry.Web.Data;
using nCarry.Web.Models.Warehouse;
using nCarry.Web.Models.Logistics;
using PackingList = nCarry.Web.Models.Logistics.PackingSlip;

namespace nCarry.Web.Controllers
{
    [Authorize]
    public class PackingListController : Controller
    {
        private readonly ApplicationDbContext _context;

        public PackingListController(ApplicationDbContext context)
        {
            _context = context;
        }

        // GET: PackingList
        public async Task<IActionResult> Index(int? warehouseId, string? status, DateTime? fromDate, DateTime? toDate)
        {
            var packingListsQuery = _context.PackingLists
                .Include(pl => pl.Warehouse)
                .Include(pl => pl.PickingList)
                .Include(pl => pl.Items)
                .AsQueryable();

            if (warehouseId.HasValue)
            {
                packingListsQuery = packingListsQuery.Where(pl => pl.WarehouseID == warehouseId.Value);
                ViewBag.SelectedWarehouseId = warehouseId.Value;
            }

            if (!string.IsNullOrEmpty(status))
            {
                packingListsQuery = packingListsQuery.Where(pl => pl.Status == status);
                ViewBag.SelectedStatus = status;
            }

            if (fromDate.HasValue)
            {
                packingListsQuery = packingListsQuery.Where(pl => pl.PackingDate >= fromDate.Value);
                ViewBag.FromDate = fromDate.Value;
            }

            if (toDate.HasValue)
            {
                packingListsQuery = packingListsQuery.Where(pl => pl.PackingDate <= toDate.Value);
                ViewBag.ToDate = toDate.Value;
            }

            var packingLists = await packingListsQuery
                .OrderByDescending(pl => pl.PackingDate)
                .ThenBy(pl => pl.PackingSlipNumber)
                .ToListAsync();

            // Populate filter dropdowns
            ViewBag.Warehouses = new SelectList(
                await _context.Warehouses.Where(w => w.IsActive).OrderBy(w => w.WarehouseName).ToListAsync(),
                "WarehouseID", "WarehouseName", warehouseId);

            ViewBag.StatusList = new List<SelectListItem>
            {
                new SelectListItem { Value = "", Text = "All Statuses" },
                new SelectListItem { Value = "Draft", Text = "Draft", Selected = status == "Draft" },
                new SelectListItem { Value = "InProgress", Text = "In Progress", Selected = status == "InProgress" },
                new SelectListItem { Value = "Completed", Text = "Completed", Selected = status == "Completed" },
                new SelectListItem { Value = "Shipped", Text = "Shipped", Selected = status == "Shipped" },
                new SelectListItem { Value = "Cancelled", Text = "Cancelled", Selected = status == "Cancelled" }
            };

            return View(packingLists);
        }

        // GET: PackingList/Details/5
        public async Task<IActionResult> Details(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var packingList = await _context.PackingLists
                .Include(pl => pl.Warehouse)
                .Include(pl => pl.PickingList)
                .Include(pl => pl.Items)
                    .ThenInclude(pi => pi.Product)
                .Include(pl => pl.Boxes)
                .FirstOrDefaultAsync(m => m.PackingSlipID == id);

            if (packingList == null)
            {
                return NotFound();
            }

            return View(packingList);
        }

        // GET: PackingList/Create
        public async Task<IActionResult> Create(int? warehouseId, int? pickingListId)
        {
            await PopulateDropdowns(warehouseId, pickingListId);
            
            var packingList = new PackingList
            {
                PackingDate = DateTime.Today,
                Status = "Draft"
            };
            
            if (warehouseId.HasValue)
            {
                packingList.WarehouseID = warehouseId.Value;
            }
            
            if (pickingListId.HasValue)
            {
                packingList.PickingListID = pickingListId.Value;
            }

            // Generate packing list number
            packingList.PackingSlipNumber = await GeneratePackingSlipNumber();

            return View(packingList);
        }

        // POST: PackingList/Create
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Create([Bind("WarehouseID,PickingListID,PackingSlipNumber,PackingDate,PackedByUserID,Notes,Status")] PackingSlip packingList)
        {
            if (ModelState.IsValid)
            {
                // Check if packing list number already exists
                if (await _context.PackingLists.AnyAsync(pl => pl.PackingSlipNumber == packingList.PackingSlipNumber))
                {
                    packingList.PackingSlipNumber = await GeneratePackingSlipNumber();
                }

                packingList.CreatedDate = DateTime.Now;
                packingList.UpdatedDate = DateTime.Now;

                _context.Add(packingList);
                await _context.SaveChangesAsync();

                // If created from picking list, copy items
                if (packingList.PickingListID.HasValue)
                {
                    await CopyItemsFromPickingList(packingList.PackingSlipID, packingList.PickingListID.Value);
                }
                
                TempData["Success"] = "Packing list created successfully.";
                return RedirectToAction(nameof(Details), new { id = packingList.PackingSlipID });
            }

            await PopulateDropdowns(packingList.WarehouseID, packingList.PickingListID);
            return View(packingList);
        }

        // GET: PackingList/Edit/5
        public async Task<IActionResult> Edit(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var packingList = await _context.PackingLists.FindAsync(id);
            if (packingList == null)
            {
                return NotFound();
            }

            await PopulateDropdowns(packingList.WarehouseID, packingList.PickingListID);
            return View(packingList);
        }

        // POST: PackingList/Edit/5
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Edit(int id, [Bind("PackingSlipID,WarehouseID,PickingListID,PackingSlipNumber,PackingDate,PackedByUserID,Notes,Status,CreatedDate,CreatedByUserID")] PackingSlip packingList)
        {
            if (id != packingList.PackingSlipID)
            {
                return NotFound();
            }

            if (ModelState.IsValid)
            {
                try
                {
                    packingList.UpdatedDate = DateTime.Now;
                    _context.Update(packingList);
                    await _context.SaveChangesAsync();
                    
                    TempData["Success"] = "Packing list updated successfully.";
                }
                catch (DbUpdateConcurrencyException)
                {
                    if (!PackingListExists(packingList.PackingSlipID))
                    {
                        return NotFound();
                    }
                    else
                    {
                        throw;
                    }
                }
                return RedirectToAction(nameof(Details), new { id = packingList.PackingSlipID });
            }

            await PopulateDropdowns(packingList.WarehouseID, packingList.PickingListID);
            return View(packingList);
        }

        // POST: PackingList/UpdateStatus
        [HttpPost]
        public async Task<IActionResult> UpdateStatus(int packingListId, string status)
        {
            var packingList = await _context.PackingLists.FindAsync(packingListId);
            if (packingList == null)
            {
                return Json(new { success = false, message = "Packing list not found." });
            }

            packingList.Status = status;
            packingList.UpdatedDate = DateTime.Now;

            if (status == "InProgress" && !packingList.StartedDate.HasValue)
            {
                packingList.StartedDate = DateTime.Now;
            }
            else if (status == "Completed" && !packingList.CompletedDate.HasValue)
            {
                packingList.CompletedDate = DateTime.Now;
            }
            else if (status == "Shipped" && !packingList.ShippedDate.HasValue)
            {
                packingList.ShippedDate = DateTime.Now;
            }

            _context.Update(packingList);
            await _context.SaveChangesAsync();

            return Json(new { success = true, message = "Status updated successfully." });
        }

        // GET: PackingList/CreateFromPicking
        public async Task<IActionResult> CreateFromPicking()
        {
            var completedPickingLists = await _context.PickingLists
                .Include(pl => pl.Warehouse)
                .Where(pl => pl.Status == "Completed" && 
                           !_context.PackingLists.Any(pack => pack.PickingListID == pl.PickingListID))
                .OrderByDescending(pl => pl.CompletedDate)
                .ToListAsync();

            ViewBag.PickingLists = new SelectList(completedPickingLists, "PickingListID", "PickingListNumber");
            return View(completedPickingLists);
        }

        // POST: PackingList/CreateFromPicking
        [HttpPost]
        public async Task<IActionResult> CreateFromPicking(int pickingListId)
        {
            var pickingList = await _context.PickingLists
                .Include(pl => pl.Warehouse)
                .FirstOrDefaultAsync(pl => pl.PickingListID == pickingListId);

            if (pickingList == null)
            {
                TempData["Error"] = "Picking list not found.";
                return RedirectToAction(nameof(CreateFromPicking));
            }

            if (pickingList.Status != "Completed")
            {
                TempData["Error"] = "Only completed picking lists can be used for packing.";
                return RedirectToAction(nameof(CreateFromPicking));
            }

            // Check if packing list already exists for this picking list
            var existingPackingList = await _context.PackingLists
                .FirstOrDefaultAsync(pl => pl.PickingListID == pickingListId);

            if (existingPackingList != null)
            {
                TempData["Error"] = "Packing list already exists for this picking list.";
                return RedirectToAction(nameof(Details), new { id = existingPackingList.PackingSlipID });
            }

            // Create packing list
            var packingList = new PackingList
            {
                WarehouseID = pickingList.WarehouseID,
                PickingListID = pickingListId,
                PackingSlipNumber = await GeneratePackingSlipNumber(),
                PackingDate = DateTime.Today,
                Status = "Draft",
                Notes = $"Created from picking list {pickingList.PickingListNumber}",
                CreatedDate = DateTime.Now,
                UpdatedDate = DateTime.Now
            };

            _context.PackingLists.Add(packingList);
            await _context.SaveChangesAsync();

            // Copy items from picking list
            await CopyItemsFromPickingList(packingList.PackingSlipID, pickingListId);

            TempData["Success"] = "Packing list created successfully from picking list.";
            return RedirectToAction(nameof(Details), new { id = packingList.PackingSlipID });
        }

        // GET: PackingList/Print/5
        public async Task<IActionResult> Print(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var packingList = await _context.PackingLists
                .Include(pl => pl.Warehouse)
                .Include(pl => pl.PickingList)
                .Include(pl => pl.Items)
                    .ThenInclude(pi => pi.Product)
                .Include(pl => pl.Boxes)
                .FirstOrDefaultAsync(m => m.PackingSlipID == id);

            if (packingList == null)
            {
                return NotFound();
            }

            return View(packingList);
        }

        // GET: PackingList/PackageOptimization/5
        public async Task<IActionResult> PackageOptimization(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var packingList = await _context.PackingLists
                .Include(pl => pl.Items)
                    .ThenInclude(pi => pi.Product)
                .FirstOrDefaultAsync(pl => pl.PackingSlipID == id);

            if (packingList == null)
            {
                return NotFound();
            }

            // Simple package optimization algorithm
            var packages = new List<dynamic>();
            var currentPackage = new { Items = new List<dynamic>(), Weight = 0.0m, Volume = 0.0m };
            const decimal maxWeight = 25.0m; // 25kg max per package
            const decimal maxVolume = 0.1m; // 0.1m³ max per package

            foreach (var item in packingList.Items)
            {
                var itemWeight = item.Product?.Weight ?? 1.0m;
                var itemVolume = (item.Product?.Length ?? 0.1m) * (item.Product?.Width ?? 0.1m) * (item.Product?.Height ?? 0.1m) / 1000000; // Convert cm³ to m³

                if (currentPackage.Weight + itemWeight > maxWeight || currentPackage.Volume + itemVolume > maxVolume)
                {
                    if (currentPackage.Items.Any())
                    {
                        packages.Add(currentPackage);
                    }
                    currentPackage = new { Items = new List<dynamic>(), Weight = 0.0m, Volume = 0.0m };
                }

                currentPackage.Items.Add(new { Item = item, Weight = itemWeight, Volume = itemVolume });
                currentPackage = new { 
                    Items = currentPackage.Items, 
                    Weight = currentPackage.Weight + itemWeight, 
                    Volume = currentPackage.Volume + itemVolume 
                };
            }

            if (currentPackage.Items.Any())
            {
                packages.Add(currentPackage);
            }

            ViewBag.Boxes = packages;
            return View(packingList);
        }

        private async Task CopyItemsFromPickingList(int packingListId, int pickingListId)
        {
            var pickingItems = await _context.PickingItems
                .Where(pi => pi.PickingListID == pickingListId && pi.Status == "Completed")
                .ToListAsync();

            var packingItems = pickingItems.Select(pi => new PackingSlipItem
            {
                PackingSlipID = packingListId,
                ProductID = pi.ProductID,
                RequiredQuantity = pi.PickedQuantity,
                PackedQuantity = 0,
                Status = "Pending",
                CreatedDate = DateTime.Now,
                UpdatedDate = DateTime.Now
            }).ToList();

            _context.PackingSlipItems.AddRange(packingItems);
            await _context.SaveChangesAsync();
        }

        private async Task<string> GeneratePackingSlipNumber()
        {
            var prefix = "PK";
            var date = DateTime.Now.ToString("yyyyMMdd");
            
            var lastNumber = await _context.PackingLists
                .Where(pl => pl.PackingSlipNumber.StartsWith(prefix + date))
                .OrderByDescending(pl => pl.PackingSlipNumber)
                .Select(pl => pl.PackingSlipNumber)
                .FirstOrDefaultAsync();

            int sequence = 1;
            if (!string.IsNullOrEmpty(lastNumber))
            {
                var lastSequence = lastNumber.Substring((prefix + date).Length);
                if (int.TryParse(lastSequence, out int parsed))
                {
                    sequence = parsed + 1;
                }
            }

            return $"{prefix}{date}{sequence:D3}";
        }

        private bool PackingListExists(int id)
        {
            return _context.PackingLists.Any(e => e.PackingSlipID == id);
        }

        private async Task PopulateDropdowns(int? warehouseId = null, int? pickingListId = null)
        {
            ViewBag.Warehouses = new SelectList(
                await _context.Warehouses.Where(w => w.IsActive).OrderBy(w => w.WarehouseName).ToListAsync(),
                "WarehouseID", "WarehouseName", warehouseId);

            ViewBag.PickingLists = new SelectList(
                await _context.PickingLists.Where(pl => pl.Status == "Completed" && (!warehouseId.HasValue || pl.WarehouseID == warehouseId.Value))
                    .OrderByDescending(pl => pl.CompletedDate).ToListAsync(),
                "PickingListID", "PickingListNumber", pickingListId);

            ViewBag.Users = new SelectList(
                await _context.Users.Where(u => u.IsActive).OrderBy(u => u.UserName).ToListAsync(),
                "UserID", "UserName");

            ViewBag.StatusList = new List<SelectListItem>
            {
                new SelectListItem { Value = "Draft", Text = "Draft" },
                new SelectListItem { Value = "InProgress", Text = "In Progress" },
                new SelectListItem { Value = "Completed", Text = "Completed" },
                new SelectListItem { Value = "Shipped", Text = "Shipped" },
                new SelectListItem { Value = "Cancelled", Text = "Cancelled" }
            };
        }
    }
}