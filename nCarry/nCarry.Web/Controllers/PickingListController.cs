using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Rendering;
using Microsoft.EntityFrameworkCore;
using nCarry.Web.Data;
using nCarry.Web.Models.Logistics;

namespace nCarry.Web.Controllers
{
    [Authorize]
    public class PickingListController : Controller
    {
        private readonly ApplicationDbContext _context;

        public PickingListController(ApplicationDbContext context)
        {
            _context = context;
        }

        // GET: PickingList
        public async Task<IActionResult> Index(int? warehouseId, string? status, DateTime? fromDate, DateTime? toDate)
        {
            var pickingListsQuery = _context.PickingLists
                .Include(pl => pl.Warehouse)
                .Include(pl => pl.Items)
                .AsQueryable();

            if (warehouseId.HasValue)
            {
                pickingListsQuery = pickingListsQuery.Where(pl => pl.WarehouseID == warehouseId.Value);
                ViewBag.SelectedWarehouseId = warehouseId.Value;
            }

            if (!string.IsNullOrEmpty(status))
            {
                pickingListsQuery = pickingListsQuery.Where(pl => pl.Status == status);
                ViewBag.SelectedStatus = status;
            }

            if (fromDate.HasValue)
            {
                pickingListsQuery = pickingListsQuery.Where(pl => pl.PickingDate >= fromDate.Value);
                ViewBag.FromDate = fromDate.Value;
            }

            if (toDate.HasValue)
            {
                pickingListsQuery = pickingListsQuery.Where(pl => pl.PickingDate <= toDate.Value);
                ViewBag.ToDate = toDate.Value;
            }

            var pickingLists = await pickingListsQuery
                .OrderByDescending(pl => pl.PickingDate)
                .ThenBy(pl => pl.PickingListNumber)
                .ToListAsync();

            // Populate filter dropdowns
            ViewBag.Warehouses = new SelectList(
                await _context.Warehouses.Where(w => w.IsActive).OrderBy(w => w.WarehouseName).ToListAsync(),
                "WarehouseID", "WarehouseName", warehouseId);

            ViewBag.StatusList = new List<SelectListItem>
            {
                new SelectListItem { Value = "", Text = "All Statuses" },
                new SelectListItem { Value = "Draft", Text = "Draft", Selected = status == "Draft" },
                new SelectListItem { Value = "Released", Text = "Released", Selected = status == "Released" },
                new SelectListItem { Value = "InProgress", Text = "In Progress", Selected = status == "InProgress" },
                new SelectListItem { Value = "Completed", Text = "Completed", Selected = status == "Completed" },
                new SelectListItem { Value = "Cancelled", Text = "Cancelled", Selected = status == "Cancelled" }
            };

            return View(pickingLists);
        }

        // GET: PickingList/Details/5
        public async Task<IActionResult> Details(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var pickingList = await _context.PickingLists
                .Include(pl => pl.Warehouse)
                .Include(pl => pl.Items)
                    .ThenInclude(pi => pi.Product)
                .Include(pl => pl.Items)
                    .ThenInclude(pi => pi.Location)
                .FirstOrDefaultAsync(m => m.PickingListID == id);

            if (pickingList == null)
            {
                return NotFound();
            }

            return View(pickingList);
        }

        // GET: PickingList/Create
        public async Task<IActionResult> Create(int? warehouseId)
        {
            await PopulateDropdowns(warehouseId);
            
            var pickingList = new PickingList
            {
                PickingDate = DateTime.Today,
                Status = "Draft",
                Priority = "Medium"
            };
            
            if (warehouseId.HasValue)
            {
                pickingList.WarehouseID = warehouseId.Value;
            }

            // Generate picking list number
            pickingList.PickingListNumber = await GeneratePickingListNumber();

            return View(pickingList);
        }

        // POST: PickingList/Create
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Create([Bind("WarehouseID,PickingListNumber,PickingDate,AssignedToUserID,Priority,Notes,Status")] PickingList pickingList)
        {
            if (ModelState.IsValid)
            {
                // Check if picking list number already exists
                if (await _context.PickingLists.AnyAsync(pl => pl.PickingListNumber == pickingList.PickingListNumber))
                {
                    pickingList.PickingListNumber = await GeneratePickingListNumber();
                }

                pickingList.CreatedDate = DateTime.Now;
                pickingList.UpdatedDate = DateTime.Now;

                _context.Add(pickingList);
                await _context.SaveChangesAsync();
                
                TempData["Success"] = "Picking list created successfully.";
                return RedirectToAction(nameof(Details), new { id = pickingList.PickingListID });
            }

            await PopulateDropdowns(pickingList.WarehouseID);
            return View(pickingList);
        }

        // GET: PickingList/Edit/5
        public async Task<IActionResult> Edit(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var pickingList = await _context.PickingLists.FindAsync(id);
            if (pickingList == null)
            {
                return NotFound();
            }

            await PopulateDropdowns(pickingList.WarehouseID);
            return View(pickingList);
        }

        // POST: PickingList/Edit/5
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Edit(int id, [Bind("PickingListID,WarehouseID,PickingListNumber,PickingDate,AssignedToUserID,Priority,Notes,Status,CreatedDate,CreatedByUserID")] PickingList pickingList)
        {
            if (id != pickingList.PickingListID)
            {
                return NotFound();
            }

            if (ModelState.IsValid)
            {
                try
                {
                    pickingList.UpdatedDate = DateTime.Now;
                    _context.Update(pickingList);
                    await _context.SaveChangesAsync();
                    
                    TempData["Success"] = "Picking list updated successfully.";
                }
                catch (DbUpdateConcurrencyException)
                {
                    if (!PickingListExists(pickingList.PickingListID))
                    {
                        return NotFound();
                    }
                    else
                    {
                        throw;
                    }
                }
                return RedirectToAction(nameof(Details), new { id = pickingList.PickingListID });
            }

            await PopulateDropdowns(pickingList.WarehouseID);
            return View(pickingList);
        }

        // POST: PickingList/UpdateStatus
        [HttpPost]
        public async Task<IActionResult> UpdateStatus(int pickingListId, string status)
        {
            var pickingList = await _context.PickingLists.FindAsync(pickingListId);
            if (pickingList == null)
            {
                return Json(new { success = false, message = "Picking list not found." });
            }

            pickingList.Status = status;
            pickingList.UpdatedDate = DateTime.Now;

            if (status == "InProgress" && !pickingList.StartedDate.HasValue)
            {
                pickingList.StartedDate = DateTime.Now;
            }
            else if (status == "Completed" && !pickingList.CompletedDate.HasValue)
            {
                pickingList.CompletedDate = DateTime.Now;
            }

            _context.Update(pickingList);
            await _context.SaveChangesAsync();

            return Json(new { success = true, message = "Status updated successfully." });
        }

        // GET: PickingList/Generate
        public async Task<IActionResult> Generate(int? warehouseId)
        {
            await PopulateDropdowns(warehouseId);
            return View();
        }

        // POST: PickingList/Generate
        [HttpPost]
        public async Task<IActionResult> Generate(int warehouseId, DateTime fromDate, DateTime toDate, string priority)
        {
            // Get pending sales orders for the warehouse within date range
            var pendingOrders = await _context.SalesOrders
                .Include(so => so.Items)
                    .ThenInclude(soi => soi.Product)
                .Where(so => so.WarehouseID == warehouseId &&
                           so.OrderDate >= fromDate &&
                           so.OrderDate <= toDate &&
                           so.Status == "Confirmed")
                .ToListAsync();

            if (!pendingOrders.Any())
            {
                TempData["Error"] = "No pending orders found for the specified criteria.";
                return RedirectToAction(nameof(Generate));
            }

            // Create picking list
            var pickingList = new PickingList
            {
                WarehouseID = warehouseId,
                PickingListNumber = await GeneratePickingListNumber(),
                PickingDate = DateTime.Today,
                Priority = priority ?? "Medium",
                Status = "Draft",
                Notes = $"Auto-generated from {pendingOrders.Count} orders ({fromDate:dd/MM/yyyy} - {toDate:dd/MM/yyyy})",
                CreatedDate = DateTime.Now,
                UpdatedDate = DateTime.Now
            };

            _context.PickingLists.Add(pickingList);
            await _context.SaveChangesAsync();

            // Group items by product and location
            var groupedItems = new Dictionary<(int ProductID, int? LocationID), decimal>();
            
            foreach (var order in pendingOrders)
            {
                foreach (var item in order.Items)
                {
                    // Find the best location for this product (with sufficient stock)
                    var inventory = await _context.Inventories
                        .Where(i => i.ProductID == item.ProductID && 
                                   i.WarehouseID == warehouseId && 
                                   i.QuantityOnHand >= item.Quantity)
                        .OrderBy(i => i.Location != null ? i.Location.LocationCode : "")
                        .FirstOrDefaultAsync();

                    var locationId = inventory?.LocationID;
                    var key = (item.ProductID, locationId);

                    if (groupedItems.ContainsKey(key))
                    {
                        groupedItems[key] += item.Quantity;
                    }
                    else
                    {
                        groupedItems[key] = item.Quantity;
                    }
                }
            }

            // Create picking items
            var pickingItems = new List<PickingListItem>();
            foreach (var groupedItem in groupedItems)
            {
                var pickingItem = new PickingListItem
                {
                    PickingListID = pickingList.PickingListID,
                    ProductID = groupedItem.Key.ProductID,
                    LocationID = groupedItem.Key.LocationID,
                    RequiredQuantity = groupedItem.Value,
                    PickedQuantity = 0,
                    Status = "Pending",
                    CreatedDate = DateTime.Now,
                    UpdatedDate = DateTime.Now
                };

                pickingItems.Add(pickingItem);
            }

            _context.PickingListItems.AddRange(pickingItems);
            await _context.SaveChangesAsync();

            TempData["Success"] = $"Picking list generated successfully with {pickingItems.Count} items from {pendingOrders.Count} orders.";
            return RedirectToAction(nameof(Details), new { id = pickingList.PickingListID });
        }

        // GET: PickingList/Print/5
        public async Task<IActionResult> Print(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var pickingList = await _context.PickingLists
                .Include(pl => pl.Warehouse)
                .Include(pl => pl.Items)
                    .ThenInclude(pi => pi.Product)
                .Include(pl => pl.Items)
                    .ThenInclude(pi => pi.Location)
                .FirstOrDefaultAsync(m => m.PickingListID == id);

            if (pickingList == null)
            {
                return NotFound();
            }

            return View(pickingList);
        }

        private async Task<string> GeneratePickingListNumber()
        {
            var prefix = "PL";
            var date = DateTime.Now.ToString("yyyyMMdd");
            
            var lastNumber = await _context.PickingLists
                .Where(pl => pl.PickingListNumber.StartsWith(prefix + date))
                .OrderByDescending(pl => pl.PickingListNumber)
                .Select(pl => pl.PickingListNumber)
                .FirstOrDefaultAsync();

            int sequence = 1;
            if (!string.IsNullOrEmpty(lastNumber))
            {
                var lastSequence = lastNumber.Substring((prefix + date).Length);
                if (int.TryParse(lastSequence, out int parsed))
                {
                    sequence = parsed + 1;
                }
            }

            return $"{prefix}{date}{sequence:D3}";
        }

        private bool PickingListExists(int id)
        {
            return _context.PickingLists.Any(e => e.PickingListID == id);
        }

        private async Task PopulateDropdowns(int? warehouseId = null)
        {
            ViewBag.Warehouses = new SelectList(
                await _context.Warehouses.Where(w => w.IsActive).OrderBy(w => w.WarehouseName).ToListAsync(),
                "WarehouseID", "WarehouseName", warehouseId);

            ViewBag.Users = new SelectList(
                await _context.Users.Where(u => u.IsActive).OrderBy(u => u.UserName).ToListAsync(),
                "UserID", "UserName");

            ViewBag.StatusList = new List<SelectListItem>
            {
                new SelectListItem { Value = "Draft", Text = "Draft" },
                new SelectListItem { Value = "Released", Text = "Released" },
                new SelectListItem { Value = "InProgress", Text = "In Progress" },
                new SelectListItem { Value = "Completed", Text = "Completed" },
                new SelectListItem { Value = "Cancelled", Text = "Cancelled" }
            };

            ViewBag.PriorityList = new List<SelectListItem>
            {
                new SelectListItem { Value = "Low", Text = "Low" },
                new SelectListItem { Value = "Medium", Text = "Medium" },
                new SelectListItem { Value = "High", Text = "High" },
                new SelectListItem { Value = "Urgent", Text = "Urgent" }
            };
        }
    }
}