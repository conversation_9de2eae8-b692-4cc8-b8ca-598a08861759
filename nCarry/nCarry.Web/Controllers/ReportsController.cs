using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using nCarry.Web.Data;
using nCarry.Web.Models.ViewModels;
using System.Globalization;

namespace nCarry.Web.Controllers
{
    [Authorize]
    public class ReportsController : Controller
    {
        private readonly ApplicationDbContext _context;

        public ReportsController(ApplicationDbContext context)
        {
            _context = context;
        }

        // GET: Reports
        public IActionResult Index()
        {
            return View();
        }

        // GET: Reports/SalesReport
        public async Task<IActionResult> SalesReport(DateTime? startDate, DateTime? endDate)
        {
            if (!startDate.HasValue)
                startDate = DateTime.Today.AddMonths(-1);
            if (!endDate.HasValue)
                endDate = DateTime.Today;

            var salesData = await _context.SalesOrders
                .Include(s => s.Customer)
                .Where(s => s.OrderDate >= startDate && s.OrderDate <= endDate && !s.IsDeleted)
                .OrderByDescending(s => s.OrderDate)
                .ToListAsync();

            var viewModel = new SalesReportViewModel
            {
                StartDate = startDate.Value,
                EndDate = endDate.Value,
                Orders = salesData,
                TotalOrders = salesData.Count,
                TotalAmount = salesData.Sum(s => s.TotalAmount),
                AverageOrderValue = salesData.Any() ? salesData.Average(s => s.TotalAmount) : 0,
                
                // Daily sales for chart
                DailySales = salesData
                    .GroupBy(s => s.OrderDate.Date)
                    .Select(g => new DailySalesData
                    {
                        Date = g.Key,
                        Amount = g.Sum(s => s.TotalAmount),
                        OrderCount = g.Count()
                    })
                    .OrderBy(d => d.Date)
                    .ToList(),

                // Sales by status
                SalesByStatus = salesData
                    .GroupBy(s => s.OrderStatus)
                    .Select(g => new StatusData
                    {
                        Status = g.Key,
                        Count = g.Count(),
                        Amount = g.Sum(s => s.TotalAmount)
                    })
                    .ToList()
            };

            return View(viewModel);
        }

        // GET: Reports/InventoryReport
        public async Task<IActionResult> InventoryReport(int? warehouseId)
        {
            var inventoryQuery = _context.Inventories
                .Include(i => i.Product)
                    .ThenInclude(p => p.Category)
                .Include(i => i.Warehouse)
                .AsQueryable();

            if (warehouseId.HasValue)
            {
                inventoryQuery = inventoryQuery.Where(i => i.WarehouseID == warehouseId.Value);
            }

            var inventoryData = await inventoryQuery.ToListAsync();

            var viewModel = new InventoryReportViewModel
            {
                WarehouseId = warehouseId,
                InventoryItems = inventoryData,
                TotalItems = inventoryData.Count,
                TotalQuantity = inventoryData.Sum(i => i.QuantityOnHand),
                TotalValue = inventoryData.Sum(i => i.QuantityOnHand * (i.AverageCost ?? 0)),
                
                // Stock levels by category
                StockByCategory = inventoryData
                    .GroupBy(i => i.Product.Category?.CategoryName ?? "Uncategorized")
                    .Select(g => new CategoryStockData
                    {
                        Category = g.Key,
                        Quantity = g.Sum(i => i.QuantityOnHand),
                        Value = g.Sum(i => i.QuantityOnHand * (i.AverageCost ?? 0))
                    })
                    .OrderByDescending(c => c.Value)
                    .ToList(),

                // Low stock items (less than 10% of max or below reorder point)
                LowStockItems = inventoryData
                    .Where(i => i.Product.ReorderPoint.HasValue && i.QuantityOnHand <= i.Product.ReorderPoint.Value)
                    .OrderBy(i => i.QuantityOnHand)
                    .Take(20)
                    .ToList(),

                // Over stock items (more than 200% of max stock level)
                OverStockItems = inventoryData
                    .Where(i => i.Product.MaxStockLevel.HasValue && i.QuantityOnHand > i.Product.MaxStockLevel.Value * 2)
                    .OrderByDescending(i => i.QuantityOnHand)
                    .Take(20)
                    .ToList()
            };

            // Populate warehouse dropdown
            ViewBag.Warehouses = await _context.Warehouses
                .Where(w => w.IsActive)
                .OrderBy(w => w.WarehouseName)
                .ToListAsync();

            return View(viewModel);
        }

        // GET: Reports/CustomerReport
        public async Task<IActionResult> CustomerReport(DateTime? startDate, DateTime? endDate)
        {
            if (!startDate.HasValue)
                startDate = DateTime.Today.AddMonths(-3);
            if (!endDate.HasValue)
                endDate = DateTime.Today;

            var customerData = await _context.SalesOrders
                .Include(s => s.Customer)
                .Where(s => s.OrderDate >= startDate && s.OrderDate <= endDate && !s.IsDeleted)
                .GroupBy(s => s.Customer)
                .Select(g => new CustomerReportData
                {
                    Customer = g.Key,
                    OrderCount = g.Count(),
                    TotalAmount = g.Sum(s => s.TotalAmount),
                    AverageOrderValue = g.Average(s => s.TotalAmount),
                    LastOrderDate = g.Max(s => s.OrderDate)
                })
                .OrderByDescending(c => c.TotalAmount)
                .Take(50)
                .ToListAsync();

            var viewModel = new CustomerReportViewModel
            {
                StartDate = startDate.Value,
                EndDate = endDate.Value,
                CustomerData = customerData,
                TotalCustomers = customerData.Count,
                TotalRevenue = customerData.Sum(c => c.TotalAmount)
            };

            return View(viewModel);
        }

        // GET: Reports/ProductReport
        public async Task<IActionResult> ProductReport(DateTime? startDate, DateTime? endDate)
        {
            if (!startDate.HasValue)
                startDate = DateTime.Today.AddMonths(-3);
            if (!endDate.HasValue)
                endDate = DateTime.Today;

            var productSales = await _context.SalesOrderItems
                .Include(si => si.Product)
                    .ThenInclude(p => p.Category)
                .Include(si => si.SalesOrder)
                .Where(si => si.SalesOrder.OrderDate >= startDate && 
                           si.SalesOrder.OrderDate <= endDate && 
                           !si.SalesOrder.IsDeleted)
                .GroupBy(si => si.Product)
                .Select(g => new ProductReportData
                {
                    Product = g.Key,
                    QuantitySold = g.Sum(si => si.OrderedQuantity),
                    TotalRevenue = g.Sum(si => si.LineTotal),
                    OrderCount = g.Select(si => si.OrderID).Distinct().Count(),
                    AveragePrice = g.Average(si => si.UnitPrice)
                })
                .OrderByDescending(p => p.TotalRevenue)
                .Take(50)
                .ToListAsync();

            var viewModel = new ProductReportViewModel
            {
                StartDate = startDate.Value,
                EndDate = endDate.Value,
                ProductData = productSales,
                TotalProducts = productSales.Count,
                TotalQuantity = productSales.Sum(p => p.QuantitySold),
                TotalRevenue = productSales.Sum(p => p.TotalRevenue)
            };

            return View(viewModel);
        }

        // GET: Reports/ProfitLoss
        public async Task<IActionResult> ProfitLoss(DateTime? startDate, DateTime? endDate)
        {
            if (!startDate.HasValue)
                startDate = new DateTime(DateTime.Today.Year, DateTime.Today.Month, 1);
            if (!endDate.HasValue)
                endDate = DateTime.Today;

            // Revenue from sales
            var salesRevenue = await _context.SalesOrders
                .Where(s => s.OrderDate >= startDate && s.OrderDate <= endDate && !s.IsDeleted)
                .SumAsync(s => s.TotalAmount);

            // Cost of goods sold
            var salesItems = await _context.SalesOrderItems
                .Include(si => si.SalesOrder)
                .Where(si => si.SalesOrder.OrderDate >= startDate && 
                           si.SalesOrder.OrderDate <= endDate && 
                           !si.SalesOrder.IsDeleted)
                .ToListAsync();

            var costOfGoodsSold = salesItems.Sum(si => si.OrderedQuantity * (si.UnitCost ?? 0));

            // Purchase costs
            var purchaseCosts = await _context.PurchaseOrders
                .Where(p => p.OrderDate >= startDate && p.OrderDate <= endDate && !p.IsDeleted)
                .SumAsync(p => p.TotalAmount);

            var viewModel = new ProfitLossViewModel
            {
                StartDate = startDate.Value,
                EndDate = endDate.Value,
                Revenue = salesRevenue,
                CostOfGoodsSold = costOfGoodsSold,
                GrossProfit = salesRevenue - costOfGoodsSold,
                GrossProfitMargin = salesRevenue > 0 ? ((salesRevenue - costOfGoodsSold) / salesRevenue) * 100 : 0,
                PurchaseCosts = purchaseCosts,
                
                // Monthly breakdown
                MonthlyData = await GetMonthlyProfitLossData(startDate.Value, endDate.Value)
            };

            return View(viewModel);
        }

        private async Task<List<MonthlyProfitLossData>> GetMonthlyProfitLossData(DateTime startDate, DateTime endDate)
        {
            var monthlyData = new List<MonthlyProfitLossData>();
            
            var currentDate = new DateTime(startDate.Year, startDate.Month, 1);
            while (currentDate <= endDate)
            {
                var monthEnd = currentDate.AddMonths(1).AddDays(-1);
                
                var monthRevenue = await _context.SalesOrders
                    .Where(s => s.OrderDate >= currentDate && s.OrderDate <= monthEnd && !s.IsDeleted)
                    .SumAsync(s => s.TotalAmount);

                var monthSalesItems = await _context.SalesOrderItems
                    .Include(si => si.SalesOrder)
                    .Where(si => si.SalesOrder.OrderDate >= currentDate && 
                               si.SalesOrder.OrderDate <= monthEnd && 
                               !si.SalesOrder.IsDeleted)
                    .ToListAsync();

                var monthCOGS = monthSalesItems.Sum(si => si.OrderedQuantity * (si.UnitCost ?? 0));

                monthlyData.Add(new MonthlyProfitLossData
                {
                    Month = currentDate.ToString("MMM yyyy"),
                    Revenue = monthRevenue,
                    CostOfGoodsSold = monthCOGS,
                    GrossProfit = monthRevenue - monthCOGS
                });

                currentDate = currentDate.AddMonths(1);
            }

            return monthlyData;
        }
    }
}