using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using nCarry.Web.Data;
using nCarry.Web.Models;
using nCarry.Web.Models.Customers;
using System.Linq;

namespace nCarry.Web.Controllers
{
    [Authorize]
    public class CustomerController : Controller
    {
        private readonly ApplicationDbContext _context;

        public CustomerController(ApplicationDbContext context)
        {
            _context = context;
        }

        // GET: Customer
        public async Task<IActionResult> Index()
        {
            var customers = await _context.Customers
                .Where(c => !c.IsDeleted)
                .OrderBy(c => c.CustomerName)
                .ToListAsync();
            return View(customers);
        }

        // GET: Customer/Details/5
        public async Task<IActionResult> Details(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var customer = await _context.Customers
                .Include(c => c.Addresses)
                .FirstOrDefaultAsync(m => m.CustomerID == id && !m.IsDeleted);
            
            if (customer == null)
            {
                return NotFound();
            }

            return View(customer);
        }

        // GET: Customer/Create
        public IActionResult Create()
        {
            var generatedCode = GenerateCustomerCode();
            Console.WriteLine($"Generated Customer Code: {generatedCode}");
            
            var model = new CustomerCreateViewModel
            {
                CustomerCode = generatedCode,
                PaymentTerm = 30,
                CreditLimit = 0,
                IsActive = true
            };
            return View(model);
        }

        // POST: Customer/Create
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Create(CustomerCreateViewModel model)
        {
            // Ensure CustomerCode is always set
            if (string.IsNullOrWhiteSpace(model.CustomerCode))
            {
                model.CustomerCode = GenerateCustomerCode();
            }

            // Check for duplicate CustomerCode
            if (!string.IsNullOrEmpty(model.CustomerCode) && 
                await _context.Customers.AnyAsync(c => c.CustomerCode == model.CustomerCode && !c.IsDeleted))
            {
                ModelState.AddModelError("CustomerCode", "A customer with this code already exists.");
            }

            if (ModelState.IsValid)
            {
                var customer = new Customer
                {
                    CustomerCode = !string.IsNullOrWhiteSpace(model.CustomerCode) ? model.CustomerCode : GenerateCustomerCode(),
                    CustomerName = model.CustomerName,
                    TradingName = string.IsNullOrWhiteSpace(model.TradingName) ? null : model.TradingName,
                    TaxNumber = string.IsNullOrWhiteSpace(model.TaxNumber) ? null : model.TaxNumber,
                    CustomerType = model.CustomerType ?? "Company",
                    ContactEmail = string.IsNullOrWhiteSpace(model.Email) ? null : model.Email,
                    ContactPhone = string.IsNullOrWhiteSpace(model.Phone) ? null : model.Phone,
                    Website = string.IsNullOrWhiteSpace(model.Website) ? null : model.Website,
                    PaymentTerm = model.PaymentTerm,
                    CreditLimit = model.CreditLimit,
                    DiscountPercent = model.DiscountPercent,
                    Notes = string.IsNullOrWhiteSpace(model.Notes) ? null : model.Notes,
                    IsActive = model.IsActive,
                    CreatedDate = DateTime.Now,
                    UpdatedDate = DateTime.Now,
                    CreatedByUserID = int.Parse(User.FindFirst(System.Security.Claims.ClaimTypes.NameIdentifier)?.Value ?? "0")
                };

                // Log the customer data being saved
                Console.WriteLine($"Saving customer: Code={customer.CustomerCode}, Name={customer.CustomerName}, Type={customer.CustomerType}");

                try
                {
                    _context.Add(customer);
                    await _context.SaveChangesAsync();

                    // Add primary address if provided
                    if (!string.IsNullOrEmpty(model.Address1))
                    {
                        var address = new CustomerAddress
                        {
                            CustomerID = customer.CustomerID,
                            AddressType = "Billing",
                            IsDefault = true,
                            Address1 = model.Address1!,
                            Address2 = model.Address2,
                            City = model.City,
                            State = model.StateProvince,
                            PostCode = model.PostalCode,
                            Country = model.Country,
                            IsActive = true,
                            CreatedDate = DateTime.Now,
                            UpdatedDate = DateTime.Now
                        };
                        _context.CustomerAddresses.Add(address);
                        await _context.SaveChangesAsync();
                    }

                    TempData["Success"] = "Customer created successfully.";
                    return RedirectToAction(nameof(Index));
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"Error saving customer: {ex.Message}");
                    if (ex.InnerException != null)
                    {
                        Console.WriteLine($"Inner exception: {ex.InnerException.Message}");
                    }
                    
                    // Check if it's a unique constraint violation
                    if (ex.Message.Contains("UNIQUE KEY constraint") || 
                        (ex.InnerException?.Message.Contains("UNIQUE KEY constraint") == true))
                    {
                        ModelState.AddModelError("", "A customer with this information already exists. Please check the Customer Code, Email, or Tax Number.");
                    }
                    else
                    {
                        ModelState.AddModelError("", $"An error occurred while saving the customer: {ex.Message}");
                    }
                }
            }

            return View(model);
        }

        // GET: Customer/Edit/5
        public async Task<IActionResult> Edit(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var customer = await _context.Customers
                .Include(c => c.Addresses)
                .FirstOrDefaultAsync(c => c.CustomerID == id && !c.IsDeleted);
            
            if (customer == null)
            {
                return NotFound();
            }

            var primaryAddress = customer.Addresses.FirstOrDefault(a => a.IsDefault && a.IsActive);
            
            var model = new CustomerEditViewModel
            {
                CustomerID = customer.CustomerID,
                CustomerCode = customer.CustomerCode,
                CustomerName = customer.CustomerName,
                TradingName = customer.TradingName,
                TaxNumber = customer.TaxNumber,
                CustomerType = customer.CustomerType ?? "Wholesale",
                Email = customer.ContactEmail,
                Phone = customer.ContactPhone,
                Website = customer.Website,
                PaymentTerm = customer.PaymentTerm ?? 30,
                CreditLimit = customer.CreditLimit,
                DiscountPercent = customer.DiscountPercent,
                Notes = customer.Notes,
                IsActive = customer.IsActive,
                Address1 = primaryAddress?.Address1,
                Address2 = primaryAddress?.Address2,
                City = primaryAddress?.City,
                StateProvince = primaryAddress?.State,
                PostalCode = primaryAddress?.PostCode,
                Country = primaryAddress?.Country ?? "United Kingdom"
            };

            return View(model);
        }

        // POST: Customer/Edit/5
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Edit(int id, CustomerEditViewModel model)
        {
            if (id != model.CustomerID)
            {
                return NotFound();
            }

            // Check for duplicate CustomerCode (excluding current customer)
            if (!string.IsNullOrEmpty(model.CustomerCode) && 
                await _context.Customers.AnyAsync(c => c.CustomerCode == model.CustomerCode && 
                                                 c.CustomerID != model.CustomerID && !c.IsDeleted))
            {
                ModelState.AddModelError("CustomerCode", "A customer with this code already exists.");
            }

            if (ModelState.IsValid)
            {
                try
                {
                    var customer = await _context.Customers
                        .Include(c => c.Addresses)
                        .FirstOrDefaultAsync(c => c.CustomerID == id && !c.IsDeleted);
                    
                    if (customer == null)
                    {
                        return NotFound();
                    }

                    // Update customer fields
                    customer.CustomerName = model.CustomerName;
                    customer.TradingName = model.TradingName;
                    customer.TaxNumber = model.TaxNumber;
                    customer.CustomerType = model.CustomerType;
                    customer.ContactEmail = model.Email;
                    customer.ContactPhone = model.Phone;
                    customer.Website = model.Website;
                    customer.PaymentTerm = model.PaymentTerm;
                    customer.CreditLimit = model.CreditLimit;
                    customer.DiscountPercent = model.DiscountPercent;
                    customer.Notes = model.Notes;
                    customer.IsActive = model.IsActive;
                    customer.UpdatedDate = DateTime.Now;
                    customer.UpdatedByUserID = int.Parse(User.FindFirst(System.Security.Claims.ClaimTypes.NameIdentifier)?.Value ?? "0");

                    // Update primary address
                    var primaryAddress = customer.Addresses.FirstOrDefault(a => a.IsDefault && a.IsActive);
                    if (primaryAddress != null)
                    {
                        primaryAddress.Address1 = model.Address1 ?? string.Empty;
                        primaryAddress.Address2 = model.Address2;
                        primaryAddress.City = model.City;
                        primaryAddress.State = model.StateProvince;
                        primaryAddress.PostCode = model.PostalCode;
                        primaryAddress.Country = model.Country;
                        primaryAddress.UpdatedDate = DateTime.Now;
                    }
                    else if (!string.IsNullOrEmpty(model.Address1))
                    {
                        // Create new primary address if none exists
                        var address = new CustomerAddress
                        {
                            CustomerID = customer.CustomerID,
                            AddressType = "Billing",
                            IsDefault = true,
                            Address1 = model.Address1,
                            Address2 = model.Address2,
                            City = model.City,
                            State = model.StateProvince,
                            PostCode = model.PostalCode,
                            Country = model.Country,
                            IsActive = true,
                            CreatedDate = DateTime.Now,
                            UpdatedDate = DateTime.Now
                        };
                        _context.CustomerAddresses.Add(address);
                    }

                    _context.Update(customer);
                    await _context.SaveChangesAsync();
                    
                    TempData["Success"] = "Customer updated successfully.";
                    return RedirectToAction(nameof(Index));
                }
                catch (DbUpdateConcurrencyException)
                {
                    if (!CustomerExists(model.CustomerID))
                    {
                        return NotFound();
                    }
                    else
                    {
                        throw;
                    }
                }
            }

            return View(model);
        }

        // GET: Customer/Delete/5
        public async Task<IActionResult> Delete(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var customer = await _context.Customers
                .FirstOrDefaultAsync(m => m.CustomerID == id && !m.IsDeleted);
            
            if (customer == null)
            {
                return NotFound();
            }

            return View(customer);
        }

        // POST: Customer/Delete/5
        [HttpPost, ActionName("Delete")]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> DeleteConfirmed(int id)
        {
            var customer = await _context.Customers.FindAsync(id);
            if (customer != null)
            {
                // Soft delete
                customer.IsDeleted = true;
                customer.IsActive = false;
                customer.DeletedDate = DateTime.Now;
                customer.DeletedByUserID = int.Parse(User.FindFirst(System.Security.Claims.ClaimTypes.NameIdentifier)?.Value ?? "0");
                
                _context.Update(customer);
                await _context.SaveChangesAsync();
                
                TempData["Success"] = "Customer deleted successfully.";
            }

            return RedirectToAction(nameof(Index));
        }

        private bool CustomerExists(int id)
        {
            return _context.Customers.Any(e => e.CustomerID == id);
        }

        private string GenerateCustomerCode()
        {
            try
            {
                // Get the last customer code that follows the CUST-#### pattern
                var lastCustomer = _context.Customers
                    .Where(c => !c.IsDeleted && c.CustomerCode != null && c.CustomerCode.StartsWith("CUST-"))
                    .AsEnumerable() // Switch to client-side evaluation for complex string operations
                    .Where(c => c.CustomerCode.Length > 5 && c.CustomerCode.Substring(5).All(char.IsDigit))
                    .OrderByDescending(c => int.Parse(c.CustomerCode.Substring(5)))
                    .FirstOrDefault();

                if (lastCustomer == null)
                {
                    return "CUST-1001";
                }

                // Extract number from last code (format: CUST-1001)
                var codeWithoutPrefix = lastCustomer.CustomerCode.Substring(5);
                
                if (int.TryParse(codeWithoutPrefix, out int lastNumber))
                {
                    // Generate next available code
                    var nextNumber = lastNumber + 1;
                    var newCode = $"CUST-{nextNumber}";
                    
                    // Ensure the generated code doesn't already exist
                    while (_context.Customers.Any(c => !c.IsDeleted && c.CustomerCode == newCode))
                    {
                        nextNumber++;
                        newCode = $"CUST-{nextNumber}";
                    }
                    
                    return newCode;
                }
                
                // Fallback if parsing fails
                return "CUST-1001";
            }
            catch (Exception ex)
            {
                // Log the error and return a fallback
                Console.WriteLine($"Error generating customer code: {ex.Message}");
                return $"CUST-{DateTime.Now.Ticks % 10000}";
            }
        }
    }
}
