using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Rendering;
using Microsoft.EntityFrameworkCore;
using nCarry.Web.Data;
using nCarry.Web.Models.Purchase;
using nCarry.Web.Models.Financial;
using System.Security.Claims;

namespace nCarry.Web.Controllers
{
    [Authorize]
    public class SupplierInvoiceController : Controller
    {
        private readonly ApplicationDbContext _context;

        public SupplierInvoiceController(ApplicationDbContext context)
        {
            _context = context;
        }

        // GET: SupplierInvoice
        public async Task<IActionResult> Index(int? supplierId, string? status, DateTime? fromDate, DateTime? toDate)
        {
            var query = _context.SupplierInvoices
                .Include(s => s.Supplier)
                .Include(s => s.CreatedByUser)
                .AsQueryable();

            if (supplierId.HasValue)
            {
                query = query.Where(s => s.SupplierID == supplierId.Value);
                ViewBag.SelectedSupplierId = supplierId.Value;
            }

            if (!string.IsNullOrEmpty(status))
            {
                query = query.Where(s => s.Status == status);
                ViewBag.SelectedStatus = status;
            }

            if (fromDate.HasValue)
            {
                query = query.Where(s => s.InvoiceDate >= fromDate.Value);
                ViewBag.FromDate = fromDate.Value;
            }

            if (toDate.HasValue)
            {
                query = query.Where(s => s.InvoiceDate <= toDate.Value);
                ViewBag.ToDate = toDate.Value;
            }

            var invoices = await query
                .OrderByDescending(s => s.InvoiceDate)
                .ThenByDescending(s => s.InvoiceNumber)
                .ToListAsync();

            await PopulateFilterDropdowns();
            return View(invoices);
        }

        // GET: SupplierInvoice/Details/5
        public async Task<IActionResult> Details(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var invoice = await _context.SupplierInvoices
                .Include(s => s.Supplier)
                .Include(s => s.CreatedByUser)
                .Include(s => s.Items)
                    .ThenInclude(i => i.Product)
                .Include(s => s.Items)
                    .ThenInclude(i => i.UOM)
                .FirstOrDefaultAsync(m => m.SupplierInvoiceID == id);

            if (invoice == null)
            {
                return NotFound();
            }

            // Get related payments
            ViewBag.Payments = await _context.PaymentAllocations
                .Include(pa => pa.Payment)
                .Where(pa => pa.InvoiceType == "Purchase" && pa.InvoiceID == id)
                .ToListAsync();

            return View(invoice);
        }

        // GET: SupplierInvoice/Create
        public async Task<IActionResult> Create(int? supplierId, int? purchaseOrderId, int? goodsReceiptId)
        {
            var model = new SupplierInvoice
            {
                InvoiceDate = DateTime.Today,
                DueDate = DateTime.Today.AddDays(30),
                Status = "Draft",
                Currency = "GBP"
            };

            if (supplierId.HasValue)
            {
                model.SupplierID = supplierId.Value;
            }

            // Pre-populate from Goods Receipt
            if (goodsReceiptId.HasValue)
            {
                var gr = await _context.GoodsReceipts
                    .Include(g => g.PurchaseOrder)
                        .ThenInclude(p => p!.Supplier)
                    .Include(g => g.Items)
                        .ThenInclude(i => i.Product)
                    .FirstOrDefaultAsync(g => g.GoodsReceiptID == goodsReceiptId.Value);

                if (gr != null)
                {
                    model.SupplierID = gr.PurchaseOrder?.SupplierID ?? 0;
                    model.PurchaseOrderID = gr.PurchaseOrderID;
                    model.GoodsReceiptID = goodsReceiptId.Value;
                    
                    // Pre-populate items from GR
                    model.Items = gr.Items.Select(gri => new SupplierInvoiceItem
                    {
                        ProductID = gri.ProductID,
                        ProductName = gri.ProductName,
                        Quantity = gri.ReceivedQuantity,
                        UOMID = gri.UOMID,
                        UnitPrice = gri.UnitCost,
                        TaxRate = gri.TaxRate,
                        GoodsReceiptItemID = gri.GoodsReceiptItemID
                    }).ToList();

                    ViewBag.GoodsReceipt = gr;
                }
            }
            // Pre-populate from Purchase Order
            else if (purchaseOrderId.HasValue)
            {
                var po = await _context.PurchaseOrders
                    .Include(p => p.Supplier)
                    .Include(p => p.PurchaseOrderItems)
                        .ThenInclude(i => i.Product)
                    .FirstOrDefaultAsync(p => p.PurchaseOrderID == purchaseOrderId.Value);

                if (po != null)
                {
                    model.SupplierID = po.SupplierID;
                    model.PurchaseOrderID = purchaseOrderId.Value;
                    model.Currency = po.Currency;
                    
                    // Pre-populate items from PO (only uninvoiced quantities)
                    model.Items = po.PurchaseOrderItems.Where(i => i.ReceivedQuantity > i.InvoicedQuantity).Select(poi => new SupplierInvoiceItem
                    {
                        PurchaseOrderItemID = poi.PurchaseOrderItemID,
                        ProductID = poi.ProductID,
                        ProductName = poi.ProductName,
                        Quantity = poi.ReceivedQuantity - poi.InvoicedQuantity,
                        UOMID = poi.UOMID,
                        UnitPrice = poi.UnitPrice,
                        TaxRate = poi.TaxRate
                    }).ToList();

                    ViewBag.PurchaseOrder = (object)po;
                }
            }

            await PopulateDropdowns();
            return View(model);
        }

        // POST: SupplierInvoice/Create
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Create(SupplierInvoice invoice, List<SupplierInvoiceItem> items)
        {
            if (ModelState.IsValid)
            {
                using var transaction = await _context.Database.BeginTransactionAsync();
                try
                {
                    // Set audit fields
                    var userId = int.Parse(User.FindFirst(ClaimTypes.NameIdentifier)?.Value ?? "0");
                    invoice.CreatedByUserID = userId;
                    
                    // Generate invoice number if not provided
                    if (string.IsNullOrEmpty(invoice.InvoiceNumber))
                    {
                        invoice.InvoiceNumber = await GenerateInvoiceNumber();
                    }

                    // Calculate totals
                    decimal subTotal = 0;
                    decimal taxTotal = 0;

                    foreach (var item in items.Where(i => i.Quantity > 0))
                    {
                        item.LineTotal = item.Quantity * item.UnitPrice;
                        item.DiscountAmount = item.LineTotal * item.DiscountPercent / 100;
                        item.LineTotal -= item.DiscountAmount;
                        item.TaxAmount = item.LineTotal * item.TaxRate / 100;
                        
                        subTotal += item.LineTotal;
                        taxTotal += item.TaxAmount;

                        invoice.Items.Add(item);
                    }

                    invoice.SubTotal = subTotal;
                    invoice.TaxAmount = taxTotal;
                    invoice.TotalAmount = subTotal + taxTotal;
                    // OutstandingAmount is a computed property, no need to set it

                    _context.Add(invoice);
                    await _context.SaveChangesAsync();

                    // Update Purchase Order invoiced quantities
                    if (invoice.PurchaseOrderID.HasValue)
                    {
                        foreach (var item in invoice.Items)
                        {
                            if (item.PurchaseOrderItemID.HasValue)
                            {
                                var poItem = await _context.PurchaseOrderItems
                                    .FirstOrDefaultAsync(i => i.PurchaseOrderItemID == item.PurchaseOrderItemID.Value);
                                
                                if (poItem != null)
                                {
                                    poItem.InvoicedQuantity += item.Quantity;
                                }
                            }
                        }

                        // Update PO status
                        var po = await _context.PurchaseOrders
                            .Include(p => p.PurchaseOrderItems)
                            .FirstOrDefaultAsync(p => p.PurchaseOrderID == invoice.PurchaseOrderID.Value);

                        if (po != null)
                        {
                            var allInvoiced = po.PurchaseOrderItems.All(i => i.InvoicedQuantity >= i.ReceivedQuantity);
                            var partiallyInvoiced = po.PurchaseOrderItems.Any(i => i.InvoicedQuantity > 0);

                            if (allInvoiced && po.OrderStatus == "Received")
                            {
                                po.OrderStatus = "Completed";
                            }
                            else if (partiallyInvoiced)
                            {
                                po.OrderStatus = "PartiallyInvoiced";
                            }
                        }
                    }

                    await _context.SaveChangesAsync();
                    await transaction.CommitAsync();

                    TempData["Success"] = "Supplier Invoice created successfully!";
                    return RedirectToAction(nameof(Details), new { id = invoice.SupplierInvoiceID });
                }
                catch (Exception ex)
                {
                    await transaction.RollbackAsync();
                    ModelState.AddModelError("", "Error creating invoice: " + ex.Message);
                }
            }

            await PopulateDropdowns();
            return View(invoice);
        }

        // GET: SupplierInvoice/Approve/5
        public async Task<IActionResult> Approve(int id)
        {
            var invoice = await _context.SupplierInvoices
                .Include(s => s.Supplier)
                .FirstOrDefaultAsync(i => i.SupplierInvoiceID == id);

            if (invoice == null)
            {
                return NotFound();
            }

            if (invoice.Status != "Draft")
            {
                TempData["Warning"] = "Only draft invoices can be approved.";
                return RedirectToAction(nameof(Details), new { id });
            }

            return View(invoice);
        }

        // POST: SupplierInvoice/Approve/5
        [HttpPost]
        [ActionName("Approve")]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> ApproveConfirmed(int id)
        {
            var invoice = await _context.SupplierInvoices.FindAsync(id);
            if (invoice == null)
            {
                return NotFound();
            }

            invoice.Status = "Approved";
            invoice.ApprovedDate = DateTime.Now;
            invoice.ApprovedByUserID = int.Parse(User.FindFirst(ClaimTypes.NameIdentifier)?.Value ?? "0");
            invoice.UpdatedDate = DateTime.Now;

            await _context.SaveChangesAsync();

            TempData["Success"] = "Invoice approved successfully!";
            return RedirectToAction(nameof(Details), new { id });
        }

        // GET: SupplierInvoice/CreatePayment/5
        public async Task<IActionResult> CreatePayment(int id)
        {
            var invoice = await _context.SupplierInvoices
                .Include(s => s.Supplier)
                .FirstOrDefaultAsync(i => i.SupplierInvoiceID == id);

            if (invoice == null)
            {
                return NotFound();
            }

            if (invoice.OutstandingAmount <= 0)
            {
                TempData["Warning"] = "This invoice has been fully paid.";
                return RedirectToAction(nameof(Details), new { id });
            }

            var payment = new Payment
            {
                PaymentDate = DateTime.Today,
                PaymentType = "Outgoing",
                Amount = invoice.OutstandingAmount,
                CurrencyID = 1, // Default to GBP, should be looked up
                PaymentReference = invoice.SupplierInvoiceNumber,
                Notes = $"Payment for invoice {invoice.SupplierInvoiceNumber}"
            };

            ViewBag.Invoice = invoice;
            ViewBag.PaymentMethods = new SelectList(
                await _context.PaymentMethods.Where(pm => pm.IsActive).ToListAsync(),
                "PaymentMethodID",
                "MethodName"
            );

            return View(payment);
        }

        // Helper methods
        private async Task<string> GenerateInvoiceNumber()
        {
            var prefix = "SINV";
            var date = DateTime.Now.ToString("yyyyMMdd");
            
            var lastNumber = await _context.SupplierInvoices
                .Where(s => s.InvoiceNumber.StartsWith(prefix + date))
                .OrderByDescending(s => s.InvoiceNumber)
                .Select(s => s.InvoiceNumber)
                .FirstOrDefaultAsync();

            int sequence = 1;
            if (!string.IsNullOrEmpty(lastNumber) && lastNumber.Length > (prefix + date).Length)
            {
                var lastSequence = lastNumber.Substring((prefix + date).Length);
                if (int.TryParse(lastSequence, out int parsed))
                {
                    sequence = parsed + 1;
                }
            }

            return $"{prefix}{date}{sequence:D4}";
        }

        private async Task PopulateDropdowns()
        {
            ViewBag.Suppliers = new SelectList(
                await _context.Suppliers
                    .Where(s => s.IsActive)
                    .OrderBy(s => s.SupplierName)
                    .ToListAsync(),
                "SupplierID",
                "SupplierName"
            );

            ViewBag.PurchaseOrders = new SelectList(
                await _context.PurchaseOrders
                    .Include(p => p.Supplier)
                    .Where(p => p.OrderStatus == "Received" || p.OrderStatus == "PartiallyInvoiced")
                    .OrderByDescending(p => p.OrderDate)
                    .Select(p => new
                    {
                        p.PurchaseOrderID,
                        DisplayText = p.PurchaseOrderNumber + " - " + p.Supplier.SupplierName
                    })
                    .ToListAsync(),
                "PurchaseOrderID",
                "DisplayText"
            );

            ViewBag.TaxCodes = new SelectList(
                await _context.TaxCodes.Where(t => t.IsActive).ToListAsync(),
                "TaxCodeID",
                "TaxCodeName"
            );
        }

        private async Task PopulateFilterDropdowns()
        {
            ViewBag.Suppliers = new SelectList(
                await _context.Suppliers
                    .Where(s => s.IsActive)
                    .OrderBy(s => s.SupplierName)
                    .ToListAsync(),
                "SupplierID",
                "SupplierName"
            );

            ViewBag.StatusList = new List<SelectListItem>
            {
                new SelectListItem { Value = "", Text = "All Statuses" },
                new SelectListItem { Value = "Draft", Text = "Draft" },
                new SelectListItem { Value = "Approved", Text = "Approved" },
                new SelectListItem { Value = "Paid", Text = "Paid" },
                new SelectListItem { Value = "PartiallyPaid", Text = "Partially Paid" },
                new SelectListItem { Value = "Cancelled", Text = "Cancelled" }
            };
        }
    }
}