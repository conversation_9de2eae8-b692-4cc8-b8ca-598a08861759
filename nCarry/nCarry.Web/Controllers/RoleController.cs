using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using nCarry.Web.Data;
using nCarry.Web.Models.System;

namespace nCarry.Web.Controllers
{
    [Authorize]
    public class RoleController : Controller
    {
        private readonly ApplicationDbContext _context;

        public RoleController(ApplicationDbContext context)
        {
            _context = context;
        }

        // GET: Role
        public async Task<IActionResult> Index()
        {
            var roles = await _context.Roles
                .Include(r => r.UserRoles)
                .Include(r => r.RolePermissions)
                .OrderBy(r => r.RoleName)
                .ToListAsync();
            return View(roles);
        }

        // GET: Role/Details/5
        public async Task<IActionResult> Details(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var role = await _context.Roles
                .Include(r => r.UserRoles)
                    .ThenInclude(ur => ur.User)
                .Include(r => r.RolePermissions)
                    .ThenInclude(rp => rp.Permission)
                .FirstOrDefaultAsync(m => m.RoleID == id);
            
            if (role == null)
            {
                return NotFound();
            }

            return View(role);
        }

        // GET: Role/Create
        public IActionResult Create()
        {
            ViewBag.Permissions = _context.Permissions.Where(p => p.IsActive).OrderBy(p => p.Module).ThenBy(p => p.PermissionName).ToList();
            return View();
        }

        // POST: Role/Create
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Create(Role role, int[] selectedPermissions)
        {
            if (ModelState.IsValid)
            {
                role.CreatedDate = DateTime.Now;
                role.CreatedByUserID = int.Parse(User.FindFirst(System.Security.Claims.ClaimTypes.NameIdentifier)?.Value ?? "0");
                
                _context.Add(role);
                await _context.SaveChangesAsync();

                // Add permissions
                if (selectedPermissions != null && selectedPermissions.Length > 0)
                {
                    foreach (var permissionId in selectedPermissions)
                    {
                        var rolePermission = new RolePermission
                        {
                            RoleID = role.RoleID,
                            PermissionID = permissionId,
                            GrantedDate = DateTime.Now,
                            GrantedByUserID = int.Parse(User.FindFirst(System.Security.Claims.ClaimTypes.NameIdentifier)?.Value ?? "0")
                        };
                        _context.RolePermissions.Add(rolePermission);
                    }
                    await _context.SaveChangesAsync();
                }

                TempData["Success"] = "Role created successfully.";
                return RedirectToAction(nameof(Index));
            }

            ViewBag.Permissions = _context.Permissions.Where(p => p.IsActive).OrderBy(p => p.Module).ThenBy(p => p.PermissionName).ToList();
            return View(role);
        }

        // GET: Role/Edit/5
        public async Task<IActionResult> Edit(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var role = await _context.Roles
                .Include(r => r.RolePermissions)
                .FirstOrDefaultAsync(r => r.RoleID == id);
            
            if (role == null)
            {
                return NotFound();
            }

            if (role.IsSystemRole)
            {
                TempData["Error"] = "System roles cannot be edited.";
                return RedirectToAction(nameof(Index));
            }

            ViewBag.Permissions = _context.Permissions.Where(p => p.IsActive).OrderBy(p => p.Module).ThenBy(p => p.PermissionName).ToList();
            ViewBag.SelectedPermissions = role.RolePermissions.Select(rp => rp.PermissionID).ToList();
            
            return View(role);
        }

        // POST: Role/Edit/5
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Edit(int id, Role role, int[] selectedPermissions)
        {
            if (id != role.RoleID)
            {
                return NotFound();
            }

            var existingRole = await _context.Roles
                .Include(r => r.RolePermissions)
                .FirstOrDefaultAsync(r => r.RoleID == id);
            
            if (existingRole == null)
            {
                return NotFound();
            }

            if (existingRole.IsSystemRole)
            {
                TempData["Error"] = "System roles cannot be edited.";
                return RedirectToAction(nameof(Index));
            }

            if (ModelState.IsValid)
            {
                try
                {
                    existingRole.RoleName = role.RoleName;
                    existingRole.RoleDescription = role.RoleDescription;
                    existingRole.IsActive = role.IsActive;
                    existingRole.UpdatedDate = DateTime.Now;
                    existingRole.UpdatedByUserID = int.Parse(User.FindFirst(System.Security.Claims.ClaimTypes.NameIdentifier)?.Value ?? "0");

                    // Update permissions
                    _context.RolePermissions.RemoveRange(existingRole.RolePermissions);
                    
                    if (selectedPermissions != null && selectedPermissions.Length > 0)
                    {
                        foreach (var permissionId in selectedPermissions)
                        {
                            var rolePermission = new RolePermission
                            {
                                RoleID = role.RoleID,
                                PermissionID = permissionId,
                                GrantedDate = DateTime.Now,
                                GrantedByUserID = int.Parse(User.FindFirst(System.Security.Claims.ClaimTypes.NameIdentifier)?.Value ?? "0")
                            };
                            _context.RolePermissions.Add(rolePermission);
                        }
                    }

                    _context.Update(existingRole);
                    await _context.SaveChangesAsync();
                    
                    TempData["Success"] = "Role updated successfully.";
                    return RedirectToAction(nameof(Index));
                }
                catch (DbUpdateConcurrencyException)
                {
                    if (!RoleExists(role.RoleID))
                    {
                        return NotFound();
                    }
                    else
                    {
                        throw;
                    }
                }
            }

            ViewBag.Permissions = _context.Permissions.Where(p => p.IsActive).OrderBy(p => p.Module).ThenBy(p => p.PermissionName).ToList();
            ViewBag.SelectedPermissions = selectedPermissions?.ToList() ?? new List<int>();
            
            return View(role);
        }

        // GET: Role/Delete/5
        public async Task<IActionResult> Delete(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var role = await _context.Roles
                .Include(r => r.UserRoles)
                .Include(r => r.RolePermissions)
                .FirstOrDefaultAsync(m => m.RoleID == id);
            
            if (role == null)
            {
                return NotFound();
            }

            if (role.IsSystemRole)
            {
                TempData["Error"] = "System roles cannot be deleted.";
                return RedirectToAction(nameof(Index));
            }

            return View(role);
        }

        // POST: Role/Delete/5
        [HttpPost, ActionName("Delete")]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> DeleteConfirmed(int id)
        {
            var role = await _context.Roles
                .Include(r => r.UserRoles)
                .FirstOrDefaultAsync(r => r.RoleID == id);
            
            if (role != null)
            {
                if (role.IsSystemRole)
                {
                    TempData["Error"] = "System roles cannot be deleted.";
                    return RedirectToAction(nameof(Index));
                }

                if (role.UserRoles.Any(ur => ur.IsActive))
                {
                    TempData["Error"] = "Cannot delete role that is assigned to active users.";
                    return RedirectToAction(nameof(Index));
                }

                _context.Roles.Remove(role);
                await _context.SaveChangesAsync();
                
                TempData["Success"] = "Role deleted successfully.";
            }

            return RedirectToAction(nameof(Index));
        }

        private bool RoleExists(int id)
        {
            return _context.Roles.Any(e => e.RoleID == id);
        }
    }
}