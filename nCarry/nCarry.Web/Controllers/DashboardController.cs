using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using nCarry.Web.Data;

namespace nCarry.Web.Controllers
{
    [Authorize]
    public class DashboardController : Controller
    {
        private readonly ApplicationDbContext _context;

        public DashboardController(ApplicationDbContext context)
        {
            _context = context;
        }

        public async Task<IActionResult> Index()
        {
            // Basic statistics
            ViewBag.CustomerCount = await _context.Customers.CountAsync();
            ViewBag.SupplierCount = await _context.Suppliers.CountAsync();
            ViewBag.ProductCount = await _context.Products.CountAsync();
            ViewBag.SalesOrderCount = await _context.SalesOrders.CountAsync();

            ViewBag.TodayOrderCount = await _context.SalesOrders
                .Where(x => x.OrderDate.Date == DateTime.Today)
                .CountAsync();

            ViewBag.MonthlyRevenue = await _context.SalesInvoices
                .Where(x => x.InvoiceDate.Month == DateTime.Now.Month && x.InvoiceDate.Year == DateTime.Now.Year)
                .SumAsync(x => (decimal?)x.TotalAmount) ?? 0;

            // Get today's statistics
            var today = DateTime.Today;
            ViewBag.TodaySales = await _context.SalesOrders
                .Where(o => o.OrderDate.Date == today)
                .SumAsync(o => (decimal?)o.TotalAmount) ?? 0;

            // Get recent orders
            var recentOrders = await _context.SalesOrders
                .Include(o => o.Customer)
                .OrderByDescending(o => o.OrderDate)
                .Take(10)
                .ToListAsync();

            ViewBag.RecentOrders = recentOrders;

            // Get low stock products
            var lowStockProducts = await _context.Inventory
                .Include(i => i.Product)
                .Where(i => i.Product.MinStockLevel != null && i.QuantityOnHand < i.Product.MinStockLevel)
                .OrderBy(i => i.QuantityOnHand)
                .Take(10)
                .ToListAsync();

            ViewBag.LowStockProducts = lowStockProducts;

            // Get active KPIs for dashboard
            var activeKPIs = await _context.KPIs
                .Where(k => k.IsActive)
                .ToListAsync();
            
            // Filter by DashboardWidget after loading from database since it's a NotMapped property
            activeKPIs = activeKPIs.Where(k => !string.IsNullOrEmpty(k.DashboardWidget)).ToList();

            ViewBag.ActiveKPIs = activeKPIs;

            // Get performance metrics
            ViewBag.MonthlyGrowth = await CalculateMonthlyGrowth();
            ViewBag.TopSellingProducts = await GetTopSellingProducts();
            ViewBag.RevenueByCategory = await GetRevenueByCategory();
            ViewBag.OrderStatusBreakdown = await GetOrderStatusBreakdown();

            // Get pending shipments
            ViewBag.PendingShipments = await _context.Shipments
                .Where(s => s.ShipmentStatus == "Pending" || s.ShipmentStatus == "InTransit")
                .CountAsync();

            // Get active delivery routes
            ViewBag.ActiveRoutes = await _context.DeliveryRoutes
                .Where(r => r.IsActive)
                .CountAsync();

            return View();
        }

        // GET: Dashboard/GetKPIData
        public async Task<IActionResult> GetKPIData(int kpiId)
        {
            var kpi = await _context.KPIs.FindAsync(kpiId);
            if (kpi == null)
            {
                return Json(new { success = false });
            }

            // Simulate KPI calculation
            var random = new Random();
            var value = random.Next(100, 1000);
            var trend = random.Next(-10, 10);

            return Json(new 
            { 
                success = true,
                value = value,
                trend = trend,
                formattedValue = FormatValue(value, kpi.DataFormat, kpi.Unit),
                status = GetStatus(value, kpi)
            });
        }

        // GET: Dashboard/GetChartData
        public async Task<IActionResult> GetChartData(string chartType, string period = "week")
        {
            var data = chartType switch
            {
                "sales" => await GetSalesChartData(period),
                "orders" => await GetOrdersChartData(period),
                "inventory" => await GetInventoryChartData(period),
                "customers" => await GetCustomersChartData(period),
                _ => null
            };

            return Json(data);
        }

        private async Task<decimal> CalculateMonthlyGrowth()
        {
            var currentMonth = DateTime.Now;
            var previousMonth = currentMonth.AddMonths(-1);

            var currentMonthSales = await _context.SalesOrders
                .Where(o => o.OrderDate.Month == currentMonth.Month && o.OrderDate.Year == currentMonth.Year)
                .SumAsync(o => (decimal?)o.TotalAmount) ?? 0;

            var previousMonthSales = await _context.SalesOrders
                .Where(o => o.OrderDate.Month == previousMonth.Month && o.OrderDate.Year == previousMonth.Year)
                .SumAsync(o => (decimal?)o.TotalAmount) ?? 0;

            if (previousMonthSales == 0) return 0;

            return ((currentMonthSales - previousMonthSales) / previousMonthSales) * 100;
        }

        private async Task<List<object>> GetTopSellingProducts()
        {
            return await _context.SalesOrderItems
                .Include(i => i.Product)
                .GroupBy(i => new { i.ProductID, i.Product.ProductName })
                .Select(g => new
                {
                    ProductName = g.Key.ProductName,
                    Quantity = g.Sum(i => i.OrderedQuantity),
                    Revenue = g.Sum(i => i.OrderedQuantity * i.UnitPrice)
                })
                .OrderByDescending(p => p.Revenue)
                .Take(5)
                .ToListAsync<object>();
        }

        private async Task<object> GetRevenueByCategory()
        {
            var data = await _context.SalesOrderItems
                .Include(i => i.Product)
                    .ThenInclude(p => p.Category)
                .Where(i => i.Product != null && i.Product.Category != null)
                .GroupBy(i => i.Product.Category.CategoryName)
                .Select(g => new
                {
                    Category = g.Key ?? "Uncategorized",
                    Revenue = g.Sum(i => i.OrderedQuantity * i.UnitPrice)
                })
                .ToListAsync();

            return new
            {
                labels = data.Select(d => d.Category).ToList(),
                data = data.Select(d => d.Revenue).ToList()
            };
        }

        private async Task<object> GetOrderStatusBreakdown()
        {
            var statusCounts = await _context.SalesOrders
                .GroupBy(o => o.OrderStatus)
                .Select(g => new
                {
                    Status = g.Key,
                    Count = g.Count()
                })
                .ToListAsync();

            return new
            {
                labels = statusCounts.Select(s => s.Status).ToList(),
                data = statusCounts.Select(s => s.Count).ToList()
            };
        }

        private async Task<object> GetSalesChartData(string period)
        {
            var endDate = DateTime.Now;
            var startDate = period switch
            {
                "day" => endDate.AddDays(-1),
                "week" => endDate.AddDays(-7),
                "month" => endDate.AddMonths(-1),
                "year" => endDate.AddYears(-1),
                _ => endDate.AddDays(-7)
            };

            var salesData = await _context.SalesOrders
                .Where(o => o.OrderDate >= startDate && o.OrderDate <= endDate)
                .GroupBy(o => o.OrderDate.Date)
                .Select(g => new
                {
                    Date = g.Key,
                    Total = g.Sum(o => o.TotalAmount)
                })
                .OrderBy(d => d.Date)
                .ToListAsync();

            return new
            {
                labels = salesData.Select(d => d.Date.ToString("MMM dd")).ToList(),
                datasets = new[]
                {
                    new
                    {
                        label = "Sales",
                        data = salesData.Select(d => d.Total).ToList(),
                        borderColor = "rgb(75, 192, 192)",
                        backgroundColor = "rgba(75, 192, 192, 0.2)"
                    }
                }
            };
        }

        private async Task<object> GetOrdersChartData(string period)
        {
            var endDate = DateTime.Now;
            var startDate = period switch
            {
                "day" => endDate.AddDays(-1),
                "week" => endDate.AddDays(-7),
                "month" => endDate.AddMonths(-1),
                "year" => endDate.AddYears(-1),
                _ => endDate.AddDays(-7)
            };

            var orderData = await _context.SalesOrders
                .Where(o => o.OrderDate >= startDate && o.OrderDate <= endDate)
                .GroupBy(o => o.OrderDate.Date)
                .Select(g => new
                {
                    Date = g.Key,
                    Count = g.Count()
                })
                .OrderBy(d => d.Date)
                .ToListAsync();

            return new
            {
                labels = orderData.Select(d => d.Date.ToString("MMM dd")).ToList(),
                datasets = new[]
                {
                    new
                    {
                        label = "Orders",
                        data = orderData.Select(d => d.Count).ToList(),
                        borderColor = "rgb(54, 162, 235)",
                        backgroundColor = "rgba(54, 162, 235, 0.2)"
                    }
                }
            };
        }

        private async Task<object> GetInventoryChartData(string period)
        {
            var inventoryLevels = await _context.Inventory
                .Include(i => i.Product)
                .ThenInclude(p => p.Category)
                .GroupBy(i => i.Product.Category.CategoryName)
                .Select(g => new
                {
                    Category = g.Key,
                    TotalStock = g.Sum(i => i.QuantityOnHand),
                    TotalValue = g.Sum(i => i.QuantityOnHand * i.Product.UnitPrice)
                })
                .ToListAsync();

            return new
            {
                labels = inventoryLevels.Select(i => i.Category).ToList(),
                datasets = new[]
                {
                    new
                    {
                        label = "Stock Quantity",
                        data = inventoryLevels.Select(i => i.TotalStock).ToList(),
                        borderColor = "rgb(255, 205, 86)",
                        backgroundColor = "rgba(255, 205, 86, 0.2)"
                    }
                }
            };
        }

        private async Task<object> GetCustomersChartData(string period)
        {
            var endDate = DateTime.Now;
            var startDate = period switch
            {
                "day" => endDate.AddDays(-1),
                "week" => endDate.AddDays(-7),
                "month" => endDate.AddMonths(-1),
                "year" => endDate.AddYears(-1),
                _ => endDate.AddDays(-7)
            };

            var customerData = await _context.Customers
                .Where(c => c.CreatedDate >= startDate && c.CreatedDate <= endDate)
                .GroupBy(c => c.CreatedDate.Date)
                .Select(g => new
                {
                    Date = g.Key,
                    Count = g.Count()
                })
                .OrderBy(d => d.Date)
                .ToListAsync();

            return new
            {
                labels = customerData.Select(d => d.Date.ToString("MMM dd")).ToList(),
                datasets = new[]
                {
                    new
                    {
                        label = "New Customers",
                        data = customerData.Select(d => d.Count).ToList(),
                        borderColor = "rgb(255, 99, 132)",
                        backgroundColor = "rgba(255, 99, 132, 0.2)"
                    }
                }
            };
        }

        private string FormatValue(decimal value, string? format, string? unit)
        {
            var formatted = format switch
            {
                "Currency" => $"${value:N2}",
                "Percentage" => $"{value:N1}%",
                "Number" => value.ToString("N0"),
                "Decimal" => value.ToString("N2"),
                _ => value.ToString()
            };

            if (!string.IsNullOrEmpty(unit) && format != "Currency" && format != "Percentage")
            {
                formatted += $" {unit}";
            }

            return formatted;
        }

        private string GetStatus(decimal value, Models.System.KPI kpi)
        {
            if (kpi.TargetValue.HasValue)
            {
                var percentage = (value / kpi.TargetValue.Value) * 100;
                if (percentage >= 95) return "success";
                if (percentage >= 80) return "warning";
                return "danger";
            }

            return "info";
        }
    }
}