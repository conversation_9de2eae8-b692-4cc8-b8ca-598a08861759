using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Rendering;
using Microsoft.EntityFrameworkCore;
using nCarry.Web.Data;
using nCarry.Web.Models.System;

namespace nCarry.Web.Controllers
{
    [Authorize]
    public class SystemConfigurationController : Controller
    {
        private readonly ApplicationDbContext _context;

        public SystemConfigurationController(ApplicationDbContext context)
        {
            _context = context;
        }

        // GET: SystemConfiguration
        public async Task<IActionResult> Index(string? category, string? searchTerm)
        {
            var configurationsQuery = _context.SystemConfigurations.AsQueryable();

            if (!string.IsNullOrEmpty(category))
            {
                configurationsQuery = configurationsQuery.Where(sc => sc.Category == category);
                ViewBag.SelectedCategory = category;
            }

            if (!string.IsNullOrEmpty(searchTerm))
            {
                configurationsQuery = configurationsQuery.Where(sc => 
                    sc.ConfigKey.Contains(searchTerm) || 
                    sc.ConfigValue.Contains(searchTerm) ||
                    (sc.Description != null && sc.Description.Contains(searchTerm)));
                ViewBag.SearchTerm = searchTerm;
            }

            var configurations = await configurationsQuery
                .OrderBy(sc => sc.Category)
                .ThenBy(sc => sc.ConfigKey)
                .ToListAsync();

            // Get distinct categories for filter
            var categories = await _context.SystemConfigurations
                .Select(sc => sc.Category)
                .Distinct()
                .OrderBy(c => c)
                .ToListAsync();

            ViewBag.Categories = new SelectList(categories.Select(c => new { Value = c, Text = c }), "Value", "Text", category);

            return View(configurations);
        }

        // GET: SystemConfiguration/Details/5
        public async Task<IActionResult> Details(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var systemConfiguration = await _context.SystemConfigurations
                .FirstOrDefaultAsync(m => m.ConfigID == id);

            if (systemConfiguration == null)
            {
                return NotFound();
            }

            return View(systemConfiguration);
        }

        // GET: SystemConfiguration/Create
        public IActionResult Create()
        {
            PopulateDropdowns();
            return View();
        }

        // POST: SystemConfiguration/Create
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Create([Bind("Category,ConfigKey,ConfigValue,DataType,Description,IsUserConfigurable,ValidationRule,DefaultValue")] SystemConfiguration systemConfiguration)
        {
            if (ModelState.IsValid)
            {
                // Check if config key already exists in the same category
                if (await _context.SystemConfigurations.AnyAsync(sc => sc.Category == systemConfiguration.Category && sc.ConfigKey == systemConfiguration.ConfigKey))
                {
                    ModelState.AddModelError("ConfigKey", "Configuration key already exists in this category.");
                    PopulateDropdowns();
                    return View(systemConfiguration);
                }

                systemConfiguration.CreatedDate = DateTime.Now;
                systemConfiguration.UpdatedDate = DateTime.Now;

                _context.Add(systemConfiguration);
                await _context.SaveChangesAsync();
                
                TempData["Success"] = "System configuration created successfully.";
                return RedirectToAction(nameof(Index));
            }

            PopulateDropdowns();
            return View(systemConfiguration);
        }

        // GET: SystemConfiguration/Edit/5
        public async Task<IActionResult> Edit(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var systemConfiguration = await _context.SystemConfigurations.FindAsync(id);
            if (systemConfiguration == null)
            {
                return NotFound();
            }

            PopulateDropdowns();
            return View(systemConfiguration);
        }

        // POST: SystemConfiguration/Edit/5
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Edit(int id, [Bind("ConfigID,Category,ConfigKey,ConfigValue,DataType,Description,IsUserConfigurable,ValidationRule,DefaultValue,CreatedDate,CreatedByUserID")] SystemConfiguration systemConfiguration)
        {
            if (id != systemConfiguration.ConfigID)
            {
                return NotFound();
            }

            if (ModelState.IsValid)
            {
                try
                {
                    // Check if config key already exists in the same category (excluding current record)
                    if (await _context.SystemConfigurations.AnyAsync(sc => sc.Category == systemConfiguration.Category && sc.ConfigKey == systemConfiguration.ConfigKey && sc.ConfigID != id))
                    {
                        ModelState.AddModelError("ConfigKey", "Configuration key already exists in this category.");
                        PopulateDropdowns();
                        return View(systemConfiguration);
                    }

                    systemConfiguration.UpdatedDate = DateTime.Now;
                    _context.Update(systemConfiguration);
                    await _context.SaveChangesAsync();
                    
                    TempData["Success"] = "System configuration updated successfully.";
                }
                catch (DbUpdateConcurrencyException)
                {
                    if (!SystemConfigurationExists(systemConfiguration.ConfigID))
                    {
                        return NotFound();
                    }
                    else
                    {
                        throw;
                    }
                }
                return RedirectToAction(nameof(Index));
            }

            PopulateDropdowns();
            return View(systemConfiguration);
        }

        // GET: SystemConfiguration/Delete/5
        public async Task<IActionResult> Delete(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var systemConfiguration = await _context.SystemConfigurations
                .FirstOrDefaultAsync(m => m.ConfigID == id);
                
            if (systemConfiguration == null)
            {
                return NotFound();
            }

            return View(systemConfiguration);
        }

        // POST: SystemConfiguration/Delete/5
        [HttpPost, ActionName("Delete")]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> DeleteConfirmed(int id)
        {
            var systemConfiguration = await _context.SystemConfigurations.FindAsync(id);
            if (systemConfiguration != null)
            {
                _context.SystemConfigurations.Remove(systemConfiguration);
                await _context.SaveChangesAsync();
                
                TempData["Success"] = "System configuration deleted successfully.";
            }

            return RedirectToAction(nameof(Index));
        }

        // GET: SystemConfiguration/Categories
        public async Task<IActionResult> Categories()
        {
            var categoryStats = await _context.SystemConfigurations
                .GroupBy(sc => sc.Category)
                .Select(g => new
                {
                    Category = g.Key,
                    Count = g.Count(),
                    UserConfigurable = g.Count(c => c.IsUserConfigurable),
                    LastUpdated = g.Max(c => c.UpdatedDate)
                })
                .OrderBy(cs => cs.Category)
                .ToListAsync();

            return View(categoryStats);
        }

        // GET: SystemConfiguration/BulkEdit
        public async Task<IActionResult> BulkEdit(string category)
        {
            if (string.IsNullOrEmpty(category))
            {
                return RedirectToAction(nameof(Categories));
            }

            var configurations = await _context.SystemConfigurations
                .Where(sc => sc.Category == category)
                .OrderBy(sc => sc.ConfigKey)
                .ToListAsync();

            ViewBag.Category = category;
            return View(configurations);
        }

        // POST: SystemConfiguration/BulkEdit
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> BulkEdit(string category, Dictionary<int, string> configValues)
        {
            if (string.IsNullOrEmpty(category) || configValues == null)
            {
                TempData["Error"] = "Invalid bulk edit request.";
                return RedirectToAction(nameof(Categories));
            }

            var configurations = await _context.SystemConfigurations
                .Where(sc => sc.Category == category && configValues.Keys.Contains(sc.ConfigID))
                .ToListAsync();

            var updateCount = 0;
            foreach (var config in configurations)
            {
                if (configValues.TryGetValue(config.ConfigID, out string? newValue) && newValue != null && config.ConfigValue != newValue)
                {
                    // Validate the new value based on data type and validation rule
                    if (ValidateConfigValue(config, newValue))
                    {
                        config.ConfigValue = newValue;
                        config.UpdatedDate = DateTime.Now;
                        updateCount++;
                    }
                }
            }

            if (updateCount > 0)
            {
                await _context.SaveChangesAsync();
                TempData["Success"] = $"{updateCount} configurations updated successfully.";
            }
            else
            {
                TempData["Info"] = "No changes were made.";
            }

            return RedirectToAction(nameof(BulkEdit), new { category });
        }

        // GET: SystemConfiguration/Export
        public async Task<IActionResult> Export(string category)
        {
            var query = _context.SystemConfigurations.AsQueryable();
            
            if (!string.IsNullOrEmpty(category))
            {
                query = query.Where(sc => sc.Category == category);
            }

            var configurations = await query
                .OrderBy(sc => sc.Category)
                .ThenBy(sc => sc.ConfigKey)
                .ToListAsync();

            var csv = "Category,ConfigKey,ConfigValue,DataType,Description,IsUserConfigurable,ValidationRule,DefaultValue\n";
            
            foreach (var config in configurations)
            {
                csv += $"\"{config.Category}\",\"{config.ConfigKey}\",\"{config.ConfigValue}\",\"{config.DataType}\",\"{config.Description}\",\"{config.IsUserConfigurable}\",\"{config.ValidationRule}\",\"{config.DefaultValue}\"\n";
            }

            var fileName = string.IsNullOrEmpty(category) ? "system_configurations.csv" : $"system_configurations_{category}.csv";
            return File(System.Text.Encoding.UTF8.GetBytes(csv), "text/csv", fileName);
        }

        // POST: SystemConfiguration/Import
        [HttpPost]
        public async Task<IActionResult> Import(IFormFile file)
        {
            if (file == null || file.Length == 0)
            {
                TempData["Error"] = "Please select a valid CSV file.";
                return RedirectToAction(nameof(Index));
            }

            try
            {
                using var reader = new StreamReader(file.OpenReadStream());
                var content = await reader.ReadToEndAsync();
                var lines = content.Split('\n', StringSplitOptions.RemoveEmptyEntries);

                if (lines.Length < 2)
                {
                    TempData["Error"] = "CSV file must contain at least a header and one data row.";
                    return RedirectToAction(nameof(Index));
                }

                var importCount = 0;
                var skipCount = 0;

                // Skip header line
                for (int i = 1; i < lines.Length; i++)
                {
                    var fields = ParseCsvLine(lines[i]);
                    if (fields.Length >= 8)
                    {
                        var category = fields[0];
                        var configKey = fields[1];

                        // Check if configuration already exists
                        var existing = await _context.SystemConfigurations
                            .FirstOrDefaultAsync(sc => sc.Category == category && sc.ConfigKey == configKey);

                        if (existing == null)
                        {
                            var config = new SystemConfiguration
                            {
                                Category = category,
                                ConfigKey = configKey,
                                ConfigValue = fields[2],
                                DataType = fields[3],
                                Description = fields[4],
                                IsUserConfigurable = bool.Parse(fields[5]),
                                ValidationRule = fields[6],
                                DefaultValue = fields[7],
                                CreatedDate = DateTime.Now,
                                UpdatedDate = DateTime.Now
                            };

                            _context.SystemConfigurations.Add(config);
                            importCount++;
                        }
                        else
                        {
                            skipCount++;
                        }
                    }
                }

                if (importCount > 0)
                {
                    await _context.SaveChangesAsync();
                }

                TempData["Success"] = $"Import completed: {importCount} configurations imported, {skipCount} skipped (already exist).";
            }
            catch (Exception ex)
            {
                TempData["Error"] = $"Import failed: {ex.Message}";
            }

            return RedirectToAction(nameof(Index));
        }

        // GET: SystemConfiguration/Reset
        public async Task<IActionResult> Reset()
        {
            await LoadDefaultConfigurations();
            TempData["Success"] = "System configurations reset to default values.";
            return RedirectToAction(nameof(Index));
        }

        private bool ValidateConfigValue(SystemConfiguration config, string value)
        {
            // Basic validation based on data type
            switch (config.DataType.ToLower())
            {
                case "integer":
                    return int.TryParse(value, out _);
                case "decimal":
                    return decimal.TryParse(value, out _);
                case "boolean":
                    return bool.TryParse(value, out _);
                case "email":
                    return IsValidEmail(value);
                case "url":
                    return Uri.TryCreate(value, UriKind.Absolute, out _);
                default:
                    return true; // String values are always valid
            }
        }

        private bool IsValidEmail(string email)
        {
            try
            {
                var addr = new System.Net.Mail.MailAddress(email);
                return addr.Address == email;
            }
            catch
            {
                return false;
            }
        }

        private string[] ParseCsvLine(string line)
        {
            var result = new List<string>();
            var inQuotes = false;
            var currentField = "";

            for (int i = 0; i < line.Length; i++)
            {
                var c = line[i];
                
                if (c == '"')
                {
                    inQuotes = !inQuotes;
                }
                else if (c == ',' && !inQuotes)
                {
                    result.Add(currentField);
                    currentField = "";
                }
                else
                {
                    currentField += c;
                }
            }
            
            result.Add(currentField);
            return result.ToArray();
        }

        private async Task LoadDefaultConfigurations()
        {
            var defaultConfigs = new List<SystemConfiguration>
            {
                // Company Settings
                new SystemConfiguration { Category = "Company", ConfigKey = "CompanyName", ConfigValue = "nCarry Wholesale", DataType = "String", Description = "Company name displayed throughout the system", IsUserConfigurable = true, DefaultValue = "nCarry Wholesale" },
                new SystemConfiguration { Category = "Company", ConfigKey = "CompanyAddress", ConfigValue = "", DataType = "String", Description = "Company address for invoices and documents", IsUserConfigurable = true, DefaultValue = "" },
                new SystemConfiguration { Category = "Company", ConfigKey = "CompanyPhone", ConfigValue = "", DataType = "String", Description = "Company phone number", IsUserConfigurable = true, DefaultValue = "" },
                new SystemConfiguration { Category = "Company", ConfigKey = "CompanyEmail", ConfigValue = "", DataType = "Email", Description = "Company email address", IsUserConfigurable = true, DefaultValue = "" },
                
                // System Settings
                new SystemConfiguration { Category = "System", ConfigKey = "DefaultLanguage", ConfigValue = "en-US", DataType = "String", Description = "Default system language", IsUserConfigurable = true, DefaultValue = "en-US" },
                new SystemConfiguration { Category = "System", ConfigKey = "DefaultTimezone", ConfigValue = "UTC", DataType = "String", Description = "Default system timezone", IsUserConfigurable = true, DefaultValue = "UTC" },
                new SystemConfiguration { Category = "System", ConfigKey = "DefaultCurrency", ConfigValue = "USD", DataType = "String", Description = "Default system currency", IsUserConfigurable = true, DefaultValue = "USD" },
                new SystemConfiguration { Category = "System", ConfigKey = "SessionTimeout", ConfigValue = "480", DataType = "Integer", Description = "Session timeout in minutes", IsUserConfigurable = true, DefaultValue = "480" },
                
                // Email Settings
                new SystemConfiguration { Category = "Email", ConfigKey = "SMTPServer", ConfigValue = "", DataType = "String", Description = "SMTP server address", IsUserConfigurable = true, DefaultValue = "" },
                new SystemConfiguration { Category = "Email", ConfigKey = "SMTPPort", ConfigValue = "587", DataType = "Integer", Description = "SMTP server port", IsUserConfigurable = true, DefaultValue = "587" },
                new SystemConfiguration { Category = "Email", ConfigKey = "SMTPUsername", ConfigValue = "", DataType = "String", Description = "SMTP username", IsUserConfigurable = true, DefaultValue = "" },
                new SystemConfiguration { Category = "Email", ConfigKey = "SMTPPassword", ConfigValue = "", DataType = "String", Description = "SMTP password", IsUserConfigurable = false, DefaultValue = "" },
                new SystemConfiguration { Category = "Email", ConfigKey = "EnableSSL", ConfigValue = "true", DataType = "Boolean", Description = "Enable SSL for SMTP", IsUserConfigurable = true, DefaultValue = "true" },
                
                // Inventory Settings
                new SystemConfiguration { Category = "Inventory", ConfigKey = "LowStockThreshold", ConfigValue = "10", DataType = "Integer", Description = "Low stock threshold for alerts", IsUserConfigurable = true, DefaultValue = "10" },
                new SystemConfiguration { Category = "Inventory", ConfigKey = "EnableNegativeStock", ConfigValue = "false", DataType = "Boolean", Description = "Allow negative stock quantities", IsUserConfigurable = true, DefaultValue = "false" },
                new SystemConfiguration { Category = "Inventory", ConfigKey = "AutoReorderPoint", ConfigValue = "5", DataType = "Integer", Description = "Automatic reorder point", IsUserConfigurable = true, DefaultValue = "5" },
                
                // Sales Settings
                new SystemConfiguration { Category = "Sales", ConfigKey = "DefaultPaymentTerms", ConfigValue = "Net 30", DataType = "String", Description = "Default payment terms for sales", IsUserConfigurable = true, DefaultValue = "Net 30" },
                new SystemConfiguration { Category = "Sales", ConfigKey = "AutoApproveOrders", ConfigValue = "false", DataType = "Boolean", Description = "Automatically approve sales orders", IsUserConfigurable = true, DefaultValue = "false" },
                new SystemConfiguration { Category = "Sales", ConfigKey = "RequireShippingAddress", ConfigValue = "true", DataType = "Boolean", Description = "Require shipping address for orders", IsUserConfigurable = true, DefaultValue = "true" },
                
                // Shipping Settings
                new SystemConfiguration { Category = "Shipping", ConfigKey = "DefaultShippingMethod", ConfigValue = "Standard", DataType = "String", Description = "Default shipping method", IsUserConfigurable = true, DefaultValue = "Standard" },
                new SystemConfiguration { Category = "Shipping", ConfigKey = "FreeShippingThreshold", ConfigValue = "100.00", DataType = "Decimal", Description = "Free shipping threshold amount", IsUserConfigurable = true, DefaultValue = "100.00" },
                new SystemConfiguration { Category = "Shipping", ConfigKey = "MaxPackageWeight", ConfigValue = "25.0", DataType = "Decimal", Description = "Maximum package weight in kg", IsUserConfigurable = true, DefaultValue = "25.0" }
            };

            // Remove existing default configurations
            var existingConfigs = await _context.SystemConfigurations.ToListAsync();
            _context.SystemConfigurations.RemoveRange(existingConfigs);

            // Add new default configurations
            foreach (var config in defaultConfigs)
            {
                config.CreatedDate = DateTime.Now;
                config.UpdatedDate = DateTime.Now;
            }

            _context.SystemConfigurations.AddRange(defaultConfigs);
            await _context.SaveChangesAsync();
        }

        private bool SystemConfigurationExists(int id)
        {
            return _context.SystemConfigurations.Any(e => e.ConfigID == id);
        }

        private void PopulateDropdowns()
        {
            ViewBag.Categories = new List<SelectListItem>
            {
                new SelectListItem { Value = "Company", Text = "Company" },
                new SelectListItem { Value = "System", Text = "System" },
                new SelectListItem { Value = "Email", Text = "Email" },
                new SelectListItem { Value = "Inventory", Text = "Inventory" },
                new SelectListItem { Value = "Sales", Text = "Sales" },
                new SelectListItem { Value = "Shipping", Text = "Shipping" },
                new SelectListItem { Value = "Security", Text = "Security" },
                new SelectListItem { Value = "Integration", Text = "Integration" }
            };

            ViewBag.DataTypes = new List<SelectListItem>
            {
                new SelectListItem { Value = "String", Text = "String" },
                new SelectListItem { Value = "Integer", Text = "Integer" },
                new SelectListItem { Value = "Decimal", Text = "Decimal" },
                new SelectListItem { Value = "Boolean", Text = "Boolean" },
                new SelectListItem { Value = "Email", Text = "Email" },
                new SelectListItem { Value = "URL", Text = "URL" },
                new SelectListItem { Value = "Password", Text = "Password" }
            };
        }
    }
}