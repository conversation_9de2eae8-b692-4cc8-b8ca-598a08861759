using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Rendering;
using Microsoft.EntityFrameworkCore;
using nCarry.Web.Data;
using nCarry.Web.Models.Logistics;

namespace nCarry.Web.Controllers
{
    [Authorize]
    public class FreightRateController : Controller
    {
        private readonly ApplicationDbContext _context;

        public FreightRateController(ApplicationDbContext context)
        {
            _context = context;
        }

        // GET: FreightRate
        public async Task<IActionResult> Index(int? carrierId, int? serviceId)
        {
            var ratesQuery = _context.FreightRates
                .Include(fr => fr.Carrier)
                .Include(fr => fr.Service)
                .AsQueryable();

            if (carrierId.HasValue)
            {
                ratesQuery = ratesQuery.Where(fr => fr.CarrierID == carrierId.Value);
                ViewBag.SelectedCarrierId = carrierId.Value;
            }

            if (serviceId.HasValue)
            {
                ratesQuery = ratesQuery.Where(fr => fr.ServiceID == serviceId.Value);
                ViewBag.SelectedServiceId = serviceId.Value;
            }

            var rates = await ratesQuery
                .Where(fr => fr.IsActive)
                .OrderBy(fr => fr.Carrier.CarrierName)
                .ThenBy(fr => fr.Service!.ServiceName)
                .ThenBy(fr => fr.FromZone)
                .ToListAsync();

            // Populate filter dropdowns
            ViewBag.Carriers = new SelectList(
                await _context.Carriers.Where(c => c.IsActive).OrderBy(c => c.CarrierName).ToListAsync(),
                "CarrierID", "CarrierName", carrierId);

            ViewBag.Services = new SelectList(
                await _context.CarrierServices.Where(s => s.IsActive && (!carrierId.HasValue || s.CarrierID == carrierId.Value))
                    .Include(s => s.Carrier)
                    .OrderBy(s => s.ServiceName).ToListAsync(),
                "ServiceID", "ServiceName", serviceId);

            return View(rates);
        }

        // GET: FreightRate/Details/5
        public async Task<IActionResult> Details(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var freightRate = await _context.FreightRates
                .Include(fr => fr.Carrier)
                .Include(fr => fr.Service)
                .FirstOrDefaultAsync(m => m.RateID == id);

            if (freightRate == null)
            {
                return NotFound();
            }

            return View(freightRate);
        }

        // GET: FreightRate/Create
        public async Task<IActionResult> Create(int? carrierId, int? serviceId)
        {
            await PopulateDropdowns(carrierId, serviceId);
            
            var rate = new FreightRate();
            if (carrierId.HasValue)
            {
                rate.CarrierID = carrierId.Value;
            }
            if (serviceId.HasValue)
            {
                rate.ServiceID = serviceId.Value;
            }

            return View(rate);
        }

        // POST: FreightRate/Create
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Create([Bind("CarrierID,ServiceID,RateType,FromZone,ToZone,MinWeight,MaxWeight,RatePerKg,FlatRate,MinCharge,FuelSurchargePercent,EffectiveFrom,EffectiveTo,IsActive")] FreightRate freightRate)
        {
            if (ModelState.IsValid)
            {
                freightRate.CreatedDate = DateTime.Now;
                freightRate.UpdatedDate = DateTime.Now;

                _context.Add(freightRate);
                await _context.SaveChangesAsync();
                
                TempData["Success"] = "Freight rate created successfully.";
                return RedirectToAction(nameof(Index), new { carrierId = freightRate.CarrierID });
            }

            await PopulateDropdowns(freightRate.CarrierID, freightRate.ServiceID);
            return View(freightRate);
        }

        // GET: FreightRate/Edit/5
        public async Task<IActionResult> Edit(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var freightRate = await _context.FreightRates.FindAsync(id);
            if (freightRate == null)
            {
                return NotFound();
            }

            await PopulateDropdowns(freightRate.CarrierID, freightRate.ServiceID);
            return View(freightRate);
        }

        // POST: FreightRate/Edit/5
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Edit(int id, [Bind("RateID,CarrierID,ServiceID,RateType,FromZone,ToZone,MinWeight,MaxWeight,RatePerKg,FlatRate,MinCharge,FuelSurchargePercent,EffectiveFrom,EffectiveTo,IsActive,CreatedDate,CreatedByUserID")] FreightRate freightRate)
        {
            if (id != freightRate.RateID)
            {
                return NotFound();
            }

            if (ModelState.IsValid)
            {
                try
                {
                    freightRate.UpdatedDate = DateTime.Now;

                    _context.Update(freightRate);
                    await _context.SaveChangesAsync();
                    
                    TempData["Success"] = "Freight rate updated successfully.";
                }
                catch (DbUpdateConcurrencyException)
                {
                    if (!FreightRateExists(freightRate.RateID))
                    {
                        return NotFound();
                    }
                    else
                    {
                        throw;
                    }
                }
                return RedirectToAction(nameof(Index), new { carrierId = freightRate.CarrierID });
            }

            await PopulateDropdowns(freightRate.CarrierID, freightRate.ServiceID);
            return View(freightRate);
        }

        // GET: FreightRate/Delete/5
        public async Task<IActionResult> Delete(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var freightRate = await _context.FreightRates
                .Include(fr => fr.Carrier)
                .Include(fr => fr.Service)
                .FirstOrDefaultAsync(m => m.RateID == id);
                
            if (freightRate == null)
            {
                return NotFound();
            }

            return View(freightRate);
        }

        // POST: FreightRate/Delete/5
        [HttpPost, ActionName("Delete")]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> DeleteConfirmed(int id)
        {
            var freightRate = await _context.FreightRates.FindAsync(id);
            if (freightRate != null)
            {
                var carrierId = freightRate.CarrierID;
                _context.FreightRates.Remove(freightRate);
                await _context.SaveChangesAsync();
                
                TempData["Success"] = "Freight rate deleted successfully.";
                return RedirectToAction(nameof(Index), new { carrierId });
            }

            return RedirectToAction(nameof(Index));
        }

        // GET: FreightRate/Calculator
        public async Task<IActionResult> Calculator()
        {
            await PopulateDropdowns();
            return View();
        }

        // POST: FreightRate/Calculator
        [HttpPost]
        public async Task<IActionResult> Calculator(int carrierId, int? serviceId, string? fromZone, string? toZone, decimal weight)
        {
            var query = _context.FreightRates
                .Include(fr => fr.Carrier)
                .Include(fr => fr.Service)
                .Where(fr => fr.CarrierID == carrierId && fr.IsActive);

            if (serviceId.HasValue)
            {
                query = query.Where(fr => fr.ServiceID == serviceId.Value);
            }

            if (!string.IsNullOrEmpty(fromZone))
            {
                query = query.Where(fr => fr.FromZone == fromZone || fr.FromZone == null);
            }

            if (!string.IsNullOrEmpty(toZone))
            {
                query = query.Where(fr => fr.ToZone == toZone || fr.ToZone == null);
            }

            // Filter by weight range
            query = query.Where(fr => 
                (fr.MinWeight == null || weight >= fr.MinWeight) &&
                (fr.MaxWeight == null || weight <= fr.MaxWeight));

            var applicableRates = await query.ToListAsync();

            // Calculate costs for each applicable rate
            var calculations = applicableRates.Select(rate => new
            {
                Rate = rate,
                BaseRate = rate.RateType == "Flat" ? rate.FlatRate : rate.RatePerKg * weight,
                FuelSurcharge = (rate.RateType == "Flat" ? rate.FlatRate : rate.RatePerKg * weight) * (rate.FuelSurchargePercent / 100),
                MinCharge = rate.MinCharge,
                TotalCost = Math.Max(
                    (rate.RateType == "Flat" ? rate.FlatRate : rate.RatePerKg * weight) + 
                    ((rate.RateType == "Flat" ? rate.FlatRate : rate.RatePerKg * weight) * (rate.FuelSurchargePercent / 100)),
                    rate.MinCharge)
            }).OrderBy(c => c.TotalCost).ToList();

            ViewBag.Calculations = calculations;
            ViewBag.Weight = weight;
            ViewBag.FromZone = fromZone;
            ViewBag.ToZone = toZone;
            ViewBag.CarrierId = carrierId;
            ViewBag.ServiceId = serviceId;

            await PopulateDropdowns(carrierId, serviceId);
            return View();
        }

        private bool FreightRateExists(int id)
        {
            return _context.FreightRates.Any(e => e.RateID == id);
        }

        private async Task PopulateDropdowns(int? carrierId = null, int? serviceId = null)
        {
            ViewBag.Carriers = new SelectList(
                await _context.Carriers.Where(c => c.IsActive).OrderBy(c => c.CarrierName).ToListAsync(),
                "CarrierID", "CarrierName", carrierId);

            ViewBag.Services = new SelectList(
                await _context.CarrierServices
                    .Where(s => s.IsActive && (!carrierId.HasValue || s.CarrierID == carrierId.Value))
                    .OrderBy(s => s.ServiceName).ToListAsync(),
                "ServiceID", "ServiceName", serviceId);

            ViewBag.RateTypes = new List<SelectListItem>
            {
                new SelectListItem { Value = "Weight", Text = "Per Kg" },
                new SelectListItem { Value = "Flat", Text = "Flat Rate" },
                new SelectListItem { Value = "Zone", Text = "Zone Based" },
                new SelectListItem { Value = "Volume", Text = "Per m³" }
            };
        }
    }
}