using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Rendering;
using Microsoft.EntityFrameworkCore;
using nCarry.Web.Data;
using nCarry.Web.Models.Purchase;
using nCarry.Web.Models.Warehouse;
using System.Security.Claims;

namespace nCarry.Web.Controllers
{
    [Authorize]
    public class GoodsReceiptController : Controller
    {
        private readonly ApplicationDbContext _context;

        public GoodsReceiptController(ApplicationDbContext context)
        {
            _context = context;
        }

        // GET: GoodsReceipt
        public async Task<IActionResult> Index(string? status, DateTime? fromDate, DateTime? toDate)
        {
            var query = _context.GoodsReceipts
                .Include(g => g.PurchaseOrder)
                    .ThenInclude(p => p!.Supplier)
                .Include(g => g.ReceivedByUser)
                .Include(g => g.Warehouse)
                .AsQueryable();

            if (!string.IsNullOrEmpty(status))
            {
                query = query.Where(g => g.Status == status);
                ViewBag.SelectedStatus = status;
            }

            if (fromDate.HasValue)
            {
                query = query.Where(g => g.ReceiptDate >= fromDate.Value);
                ViewBag.FromDate = fromDate.Value;
            }

            if (toDate.HasValue)
            {
                query = query.Where(g => g.ReceiptDate <= toDate.Value);
                ViewBag.ToDate = toDate.Value;
            }

            var receipts = await query
                .OrderByDescending(g => g.ReceiptDate)
                .ToListAsync();

            PopulateStatusDropdown();
            return View(receipts);
        }

        // GET: GoodsReceipt/Details/5
        public async Task<IActionResult> Details(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var goodsReceipt = await _context.GoodsReceipts
                .Include(g => g.PurchaseOrder)
                    .ThenInclude(p => p!.Supplier)
                .Include(g => g.ReceivedByUser)
                .Include(g => g.Warehouse)
                .Include(g => g.Items)
                    .ThenInclude(i => i.Product)
                .Include(g => g.Items)
                    .ThenInclude(i => i.UOM)
                .FirstOrDefaultAsync(m => m.GoodsReceiptID == id);

            if (goodsReceipt == null)
            {
                return NotFound();
            }

            return View(goodsReceipt);
        }

        // GET: GoodsReceipt/Create
        public async Task<IActionResult> Create(int? purchaseOrderId)
        {
            var model = new GoodsReceipt
            {
                ReceiptDate = DateTime.Today,
                ReceiptNumber = await GenerateReceiptNumber(),
                Status = "Draft"
            };

            if (purchaseOrderId.HasValue)
            {
                var po = await _context.PurchaseOrders
                    .Include(p => p.Supplier)
                    .Include(p => p.PurchaseOrderItems)
                        .ThenInclude(i => i.Product)
                    .FirstOrDefaultAsync(p => p.PurchaseOrderID == purchaseOrderId.Value);

                if (po != null)
                {
                    model.PurchaseOrderID = po.PurchaseOrderID;
                    model.SupplierID = po.SupplierID;
                    model.SupplierDeliveryNote = string.Empty;
                    
                    // Pre-populate items from PO
                    model.Items = po.PurchaseOrderItems.Where(i => i.OrderedQuantity > i.ReceivedQuantity).Select(poi => new GoodsReceiptItem
                    {
                        PurchaseOrderItemID = poi.PurchaseOrderItemID,
                        ProductID = poi.ProductID,
                        ProductName = poi.ProductName,
                        ReceivedQuantity = poi.OrderedQuantity - poi.ReceivedQuantity,
                        UOMID = poi.UOMID,
                        UnitCost = poi.UnitPrice,
                        TaxRate = poi.TaxRate
                    }).ToList();

                    ViewBag.PurchaseOrder = po;
                }
            }

            await PopulateDropdowns();
            return View(model);
        }

        // POST: GoodsReceipt/Create
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Create(GoodsReceipt goodsReceipt, List<GoodsReceiptItem> items)
        {
            // Remove navigation property validation errors
            ModelState.Remove("Supplier");
            ModelState.Remove("Warehouse");
            ModelState.Remove("PurchaseOrder");
            ModelState.Remove("ReceivedByUser");
            ModelState.Remove("CreatedByUser");
            
            // Remove navigation property validation errors for items
            for (int i = 0; i < (items?.Count ?? 0); i++)
            {
                ModelState.Remove($"Items[{i}].GoodsReceipt");
                ModelState.Remove($"Items[{i}].Product");
                ModelState.Remove($"Items[{i}].UOM");
                ModelState.Remove($"Items[{i}].Location");
                ModelState.Remove($"items[{i}].GoodsReceipt");
                ModelState.Remove($"items[{i}].Product");
                ModelState.Remove($"items[{i}].UOM");
                ModelState.Remove($"items[{i}].Location");
            }
            
            // Debug: Log ModelState errors
            if (!ModelState.IsValid)
            {
                var errors = ModelState
                    .Where(x => x.Value.Errors.Count > 0)
                    .Select(x => new { Field = x.Key, Errors = x.Value.Errors.Select(e => e.ErrorMessage) })
                    .ToList();
                
                Console.WriteLine($"ModelState validation failed. Errors: {string.Join("; ", errors.Select(e => $"{e.Field}: {string.Join(", ", e.Errors)}"))}");
                Console.WriteLine($"Items count received: {items?.Count ?? 0}");
                Console.WriteLine($"GoodsReceipt data: PO={goodsReceipt?.PurchaseOrderID}, Warehouse={goodsReceipt?.WarehouseID}");
            }
            
            if (ModelState.IsValid)
            {
                using var transaction = await _context.Database.BeginTransactionAsync();
                try
                {
                    // Debug: Log item counts
                    Console.WriteLine($"Items parameter count: {items?.Count ?? 0}");
                    Console.WriteLine($"GoodsReceipt.Items count: {goodsReceipt.Items?.Count ?? 0}");
                    
                    // Ensure Items collection is initialized
                    if (goodsReceipt.Items == null)
                    {
                        goodsReceipt.Items = new List<GoodsReceiptItem>();
                    }
                    else
                    {
                        // Clear any items that might have been bound to goodsReceipt.Items
                        goodsReceipt.Items.Clear();
                    }
                    
                    // Debug: Log to ensure Items is empty
                    Console.WriteLine($"After clearing, goodsReceipt.Items count: {goodsReceipt.Items.Count}");
                    // Set SupplierID from PurchaseOrder
                    if (goodsReceipt.PurchaseOrderID.HasValue)
                    {
                        var purchaseOrder = await _context.PurchaseOrders
                            .FirstOrDefaultAsync(po => po.PurchaseOrderID == goodsReceipt.PurchaseOrderID.Value);
                        if (purchaseOrder != null)
                        {
                            goodsReceipt.SupplierID = purchaseOrder.SupplierID;
                        }
                    }
                    
                    // Set audit fields
                    var userId = int.Parse(User.FindFirst(ClaimTypes.NameIdentifier)?.Value ?? "0");
                    goodsReceipt.ReceivedByUserID = userId;
                    goodsReceipt.CreatedByUserID = userId;
                    goodsReceipt.Status = "Posted";
                    
                    // Generate receipt number if not provided
                    if (string.IsNullOrEmpty(goodsReceipt.ReceiptNumber))
                    {
                        goodsReceipt.ReceiptNumber = await GenerateReceiptNumber();
                    }

                    // Calculate totals
                    decimal subTotal = 0;
                    decimal taxTotal = 0;

                    int itemLineNumber = 0;
                    var itemsToProcess = items?.Where(i => i != null && i.ReceivedQuantity > 0) ?? Enumerable.Empty<GoodsReceiptItem>();
                    foreach (var item in itemsToProcess)
                    {
                        itemLineNumber++;
                        item.LineNumber = itemLineNumber;  // Ensure unique line numbers
                        item.LineTotal = item.ReceivedQuantity * item.UnitCost;
                        item.TaxAmount = item.LineTotal * item.TaxRate / 100;
                        
                        Console.WriteLine($"Processing item: LineNumber={item.LineNumber}, Product={item.ProductName}, LineTotal={item.LineTotal}, TaxAmount={item.TaxAmount}");
                        
                        subTotal += item.LineTotal;
                        taxTotal += item.TaxAmount;

                        goodsReceipt.Items.Add(item);
                    }
                    
                    Console.WriteLine($"Total items added: {goodsReceipt.Items.Count}");

                    goodsReceipt.SubTotal = subTotal;
                    goodsReceipt.TaxAmount = taxTotal;
                    goodsReceipt.TotalAmount = subTotal + taxTotal;
                    
                    Console.WriteLine($"Final totals: SubTotal={subTotal}, TaxAmount={taxTotal}, TotalAmount={goodsReceipt.TotalAmount}");

                    _context.Add(goodsReceipt);
                    await _context.SaveChangesAsync();

                    // Update Purchase Order received quantities
                    if (goodsReceipt.PurchaseOrderID.HasValue)
                    {
                        // Generate base transaction number for inventory transactions
                        var baseTransactionNumber = await GenerateTransactionNumber();
                        int transactionSequence = 0;
                        
                        foreach (var item in goodsReceipt.Items)
                        {
                            if (item.PurchaseOrderItemID.HasValue)
                            {
                                var poItem = await _context.PurchaseOrderItems
                                    .FirstOrDefaultAsync(i => i.PurchaseOrderItemID == item.PurchaseOrderItemID.Value);
                                
                                if (poItem != null)
                                {
                                    poItem.ReceivedQuantity += item.ReceivedQuantity;
                                    // LastReceiptDate is stored in the PurchaseOrderItem table
                                    
                                    if (poItem.ReceivedQuantity >= poItem.OrderedQuantity)
                                    {
                                        poItem.ItemStatus = "Received";
                                    }
                                    else if (poItem.ReceivedQuantity > 0)
                                    {
                                        poItem.ItemStatus = "PartiallyReceived";
                                    }
                                }
                            }

                            // Create inventory transaction
                            transactionSequence++;
                            var invTransaction = new InventoryTransaction
                            {
                                TransactionNumber = baseTransactionNumber + "-" + transactionSequence.ToString("D3"),
                                ReferenceNumber = $"GR-{goodsReceipt.ReceiptNumber}",
                                TransactionType = "Receipt",
                                TransactionDate = goodsReceipt.ReceiptDate,
                                WarehouseID = goodsReceipt.WarehouseID,
                                ProductID = item.ProductID,
                                Quantity = item.ReceivedQuantity,
                                UnitCost = item.UnitCost,
                                TotalCost = item.LineTotal,
                                ReferenceType = "GoodsReceipt",
                                ReferenceID = goodsReceipt.GoodsReceiptID,
                                Notes = $"Goods Receipt from {goodsReceipt.ReceiptNumber}",
                                CreatedByUserID = userId
                            };

                            _context.InventoryTransactions.Add(invTransaction);

                            // Update or create inventory record
                            var inventory = await _context.Inventories
                                .FirstOrDefaultAsync(i => i.WarehouseID == goodsReceipt.WarehouseID && 
                                                         i.ProductID == item.ProductID);

                            if (inventory != null)
                            {
                                inventory.QuantityOnHand += item.ReceivedQuantity;
                                inventory.QuantityAvailable += item.ReceivedQuantity;
                                // LastReceiptDate is not available in Inventory table
                            }
                            else
                            {
                                inventory = new Inventory
                                {
                                    WarehouseID = goodsReceipt.WarehouseID,
                                    ProductID = item.ProductID,
                                    QuantityOnHand = item.ReceivedQuantity,
                                    QuantityAvailable = item.ReceivedQuantity,
                                    QuantityReserved = 0,
                                    // LastReceiptDate is not available in Inventory table,
                                    LastCountDate = goodsReceipt.ReceiptDate
                                };
                                _context.Inventories.Add(inventory);
                            }
                        }

                        // Update PO status
                        var po = await _context.PurchaseOrders
                            .Include(p => p.PurchaseOrderItems)
                            .FirstOrDefaultAsync(p => p.PurchaseOrderID == goodsReceipt.PurchaseOrderID.Value);

                        if (po != null)
                        {
                            var allReceived = po.PurchaseOrderItems.All(i => i.ReceivedQuantity >= i.OrderedQuantity);
                            var partiallyReceived = po.PurchaseOrderItems.Any(i => i.ReceivedQuantity > 0);

                            if (allReceived)
                            {
                                po.OrderStatus = "Received";
                            }
                            else if (partiallyReceived)
                            {
                                po.OrderStatus = "PartiallyReceived";
                            }
                        }
                    }

                    await _context.SaveChangesAsync();
                    await transaction.CommitAsync();

                    TempData["Success"] = "Goods Receipt created successfully!";
                    return RedirectToAction(nameof(Details), new { id = goodsReceipt.GoodsReceiptID });
                }
                catch (Exception ex)
                {
                    await transaction.RollbackAsync();
                    
                    // Log the full exception details
                    Console.WriteLine($"Error creating goods receipt: {ex}");
                    Console.WriteLine($"Stack trace: {ex.StackTrace}");
                    
                    // Add inner exception details if available
                    if (ex.InnerException != null)
                    {
                        Console.WriteLine($"Inner exception: {ex.InnerException.Message}");
                        Console.WriteLine($"Inner stack trace: {ex.InnerException.StackTrace}");
                    }
                    
                    ModelState.AddModelError("", "Error creating goods receipt: " + ex.Message);
                }
            }

            await PopulateDropdowns();
            if (goodsReceipt.PurchaseOrderID.HasValue)
            {
                ViewBag.PurchaseOrder = await _context.PurchaseOrders
                    .Include(p => p.Supplier)
                    .FirstOrDefaultAsync(p => p.PurchaseOrderID == goodsReceipt.PurchaseOrderID.Value);
            }
            return View(goodsReceipt);
        }

        // GET: GoodsReceipt/CreateFromPO/5
        public async Task<IActionResult> CreateFromPO(int id)
        {
            var po = await _context.PurchaseOrders
                .Include(p => p.Supplier)
                .Include(p => p.PurchaseOrderItems.Where(i => i.OrderedQuantity > i.ReceivedQuantity))
                    .ThenInclude(i => i.Product)
                .Include(p => p.PurchaseOrderItems)
                    .ThenInclude(i => i.UnitOfMeasure)
                .FirstOrDefaultAsync(p => p.PurchaseOrderID == id);

            if (po == null)
            {
                return NotFound();
            }

            if (!po.PurchaseOrderItems.Any(i => i.OrderedQuantity > i.ReceivedQuantity))
            {
                TempData["Warning"] = "All items in this Purchase Order have been fully received.";
                return RedirectToAction("Details", "PurchaseOrder", new { id });
            }

            return RedirectToAction(nameof(Create), new { purchaseOrderId = id });
        }

        // GET: GoodsReceipt/Print/5
        public async Task<IActionResult> Print(int id)
        {
            var goodsReceipt = await _context.GoodsReceipts
                .Include(g => g.PurchaseOrder)
                    .ThenInclude(p => p!.Supplier)
                .Include(g => g.ReceivedByUser)
                .Include(g => g.Warehouse)
                .Include(g => g.Items)
                    .ThenInclude(i => i.Product)
                .Include(g => g.Items)
                    .ThenInclude(i => i.UOM)
                .FirstOrDefaultAsync(m => m.GoodsReceiptID == id);

            if (goodsReceipt == null)
            {
                return NotFound();
            }

            return View(goodsReceipt);
        }

        // Helper methods
        private async Task<string> GenerateReceiptNumber()
        {
            var prefix = "GR";
            var date = DateTime.Now.ToString("yyyyMMdd");
            
            var lastNumber = await _context.GoodsReceipts
                .Where(g => g.ReceiptNumber.StartsWith(prefix + date))
                .OrderByDescending(g => g.ReceiptNumber)
                .Select(g => g.ReceiptNumber)
                .FirstOrDefaultAsync();

            int sequence = 1;
            if (!string.IsNullOrEmpty(lastNumber) && lastNumber.Length > (prefix + date).Length)
            {
                var lastSequence = lastNumber.Substring((prefix + date).Length);
                if (int.TryParse(lastSequence, out int parsed))
                {
                    sequence = parsed + 1;
                }
            }

            return $"{prefix}{date}{sequence:D4}";
        }

        private async Task<string> GenerateTransactionNumber()
        {
            var prefix = "IT";
            var date = DateTime.Now.ToString("yyyyMMdd");
            
            var lastNumber = await _context.InventoryTransactions
                .Where(t => t.TransactionNumber.StartsWith(prefix + date))
                .OrderByDescending(t => t.TransactionNumber)
                .Select(t => t.TransactionNumber)
                .FirstOrDefaultAsync();

            int sequence = 1;
            if (!string.IsNullOrEmpty(lastNumber) && lastNumber.Length > (prefix + date).Length)
            {
                var lastSequence = lastNumber.Substring((prefix + date).Length);
                if (int.TryParse(lastSequence, out int parsed))
                {
                    sequence = parsed + 1;
                }
            }

            return $"{prefix}{date}{sequence:D4}";
        }

        private async Task PopulateDropdowns()
        {
            var purchaseOrders = await _context.PurchaseOrders
                .Include(p => p.Supplier)
                .Where(p => !p.IsDeleted && (p.OrderStatus == "Draft" || p.OrderStatus == "Sent" || p.OrderStatus == "Confirmed" || p.OrderStatus == "PartiallyReceived"))
                .OrderByDescending(p => p.OrderDate)
                .ToListAsync();

            ViewBag.PurchaseOrders = new SelectList(
                purchaseOrders.Select(p => new
                {
                    p.PurchaseOrderID,
                    DisplayText = p.PurchaseOrderNumber + " - " + (p.Supplier?.SupplierName ?? "Unknown Supplier")
                }).ToList(),
                "PurchaseOrderID",
                "DisplayText"
            );

            ViewBag.Warehouses = new SelectList(
                await _context.Warehouses
                    .Where(w => w.IsActive)
                    .OrderBy(w => w.WarehouseName)
                    .ToListAsync(),
                "WarehouseID",
                "WarehouseName"
            );
        }

        private void PopulateStatusDropdown()
        {
            ViewBag.StatusList = new List<SelectListItem>
            {
                new SelectListItem { Value = "", Text = "All Statuses" },
                new SelectListItem { Value = "Draft", Text = "Draft" },
                new SelectListItem { Value = "Posted", Text = "Posted" },
                new SelectListItem { Value = "Cancelled", Text = "Cancelled" }
            };
        }
    }
}