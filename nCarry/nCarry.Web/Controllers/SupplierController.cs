using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using nCarry.Web.Data;
using nCarry.Web.Models;
using nCarry.Web.Models.Suppliers;

namespace nCarry.Web.Controllers
{
    [Authorize]
    public class SupplierController : Controller
    {
        private readonly ApplicationDbContext _context;

        public SupplierController(ApplicationDbContext context)
        {
            _context = context;
        }

        // GET: Supplier
        public async Task<IActionResult> Index()
        {
            var suppliers = await _context.Suppliers
                .Where(s => !s.IsDeleted)
                .OrderBy(s => s.SupplierName)
                .ToListAsync();
            return View(suppliers);
        }

        // GET: Supplier/Details/5
        public async Task<IActionResult> Details(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var supplier = await _context.Suppliers
                .Include(s => s.Addresses)
                .FirstOrDefaultAsync(m => m.SupplierID == id && !m.IsDeleted);
            
            if (supplier == null)
            {
                return NotFound();
            }

            return View(supplier);
        }

        // GET: Supplier/Create
        public IActionResult Create()
        {
            var model = new SupplierCreateViewModel
            {
                SupplierCode = GenerateSupplierCode(),
                PaymentTermDays = 30,
                MinimumOrderValue = 0,
                LeadTimeDays = 7,
                IsActive = true
            };
            return View(model);
        }

        // POST: Supplier/Create
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Create(SupplierCreateViewModel model)
        {
            if (ModelState.IsValid)
            {
                try
                {
                    // Check for duplicate supplier code
                    var existingSupplier = await _context.Suppliers
                        .Where(s => s.SupplierCode == model.SupplierCode && !s.IsDeleted)
                        .FirstOrDefaultAsync();
                    
                    if (existingSupplier != null)
                    {
                        ModelState.AddModelError("SupplierCode", "Supplier code already exists. Please try again.");
                        model.SupplierCode = GenerateSupplierCode(); // Generate new code
                        return View(model);
                    }

                    var supplier = new Supplier
                    {
                        SupplierCode = model.SupplierCode,
                        SupplierName = model.SupplierName,
                        TradingName = model.TradingName,
                        TaxNumber = model.TaxNumber,
                        SupplierType = model.SupplierType,
                        ContactEmail = model.Email,
                        ContactPhone = model.Phone,
                        Website = model.Website,
                        PaymentTermDays = model.PaymentTermDays,
                        MinimumOrderValue = model.MinimumOrderValue,
                        LeadTimeDays = model.LeadTimeDays,
                        Notes = model.Notes,
                        IsActive = model.IsActive,
                        CreatedDate = DateTime.Now,
                        CreatedByUserID = int.Parse(User.FindFirst(System.Security.Claims.ClaimTypes.NameIdentifier)?.Value ?? "0")
                    };

                    _context.Add(supplier);
                    await _context.SaveChangesAsync();

                // Add primary address if provided
                if (!string.IsNullOrEmpty(model.Address1))
                {
                    var address = new SupplierAddress
                    {
                        SupplierID = supplier.SupplierID,
                        AddressType = "Billing",
                        IsDefault = true,
                        Address1 = model.Address1!,
                        Address2 = model.Address2,
                        City = model.City,
                        State = model.StateProvince,
                        PostCode = model.PostalCode,
                        Country = model.Country,
                        IsActive = true,
                        CreatedDate = DateTime.Now,
                        UpdatedDate = DateTime.Now
                    };
                    _context.SupplierAddresses.Add(address);
                    await _context.SaveChangesAsync();
                }

                    TempData["Success"] = "Supplier created successfully.";
                    return RedirectToAction(nameof(Index));
                }
                catch (Exception)
                {
                    ModelState.AddModelError("", "An error occurred while creating the supplier. Please try again.");
                    model.SupplierCode = GenerateSupplierCode(); // Generate new code for retry
                }
            }

            return View(model);
        }

        // GET: Supplier/Edit/5
        public async Task<IActionResult> Edit(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var supplier = await _context.Suppliers
                .Include(s => s.Addresses)
                .FirstOrDefaultAsync(s => s.SupplierID == id && !s.IsDeleted);
            
            if (supplier == null)
            {
                return NotFound();
            }

            var primaryAddress = supplier.Addresses.FirstOrDefault(a => a.IsDefault && a.IsActive);
            
            var model = new SupplierEditViewModel
            {
                SupplierID = supplier.SupplierID,
                SupplierCode = supplier.SupplierCode,
                SupplierName = supplier.SupplierName,
                TradingName = supplier.TradingName,
                TaxNumber = supplier.TaxNumber,
                SupplierType = supplier.SupplierType ?? "Standard",
                Email = supplier.ContactEmail,
                Phone = supplier.ContactPhone,
                Website = supplier.Website,
                PaymentTermDays = supplier.PaymentTermDays ?? 30,
                MinimumOrderValue = supplier.MinimumOrderValue,
                LeadTimeDays = supplier.LeadTimeDays ?? 7,
                Rating = supplier.Rating,
                Notes = supplier.Notes,
                IsActive = supplier.IsActive,
                IsPreferred = supplier.IsPreferred,
                Address1 = primaryAddress?.Address1,
                Address2 = primaryAddress?.Address2,
                City = primaryAddress?.City,
                StateProvince = primaryAddress?.State,
                PostalCode = primaryAddress?.PostCode,
                Country = primaryAddress?.Country ?? "United Kingdom"
            };

            return View(model);
        }

        // POST: Supplier/Edit/5
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Edit(int id, SupplierEditViewModel model)
        {
            if (id != model.SupplierID)
            {
                return NotFound();
            }

            if (ModelState.IsValid)
            {
                try
                {
                    var supplier = await _context.Suppliers
                        .Include(s => s.Addresses)
                        .FirstOrDefaultAsync(s => s.SupplierID == id && !s.IsDeleted);
                    
                    if (supplier == null)
                    {
                        return NotFound();
                    }

                    // Update supplier fields
                    supplier.SupplierName = model.SupplierName;
                    supplier.TradingName = model.TradingName;
                    supplier.TaxNumber = model.TaxNumber;
                    supplier.SupplierType = model.SupplierType;
                    supplier.ContactEmail = model.Email;
                    supplier.ContactPhone = model.Phone;
                    supplier.Website = model.Website;
                    supplier.PaymentTermDays = model.PaymentTermDays;
                    supplier.MinimumOrderValue = model.MinimumOrderValue;
                    supplier.LeadTimeDays = model.LeadTimeDays;
                    supplier.Rating = model.Rating;
                    supplier.Notes = model.Notes;
                    supplier.IsActive = model.IsActive;
                    supplier.IsPreferred = model.IsPreferred;
                    supplier.UpdatedDate = DateTime.Now;
                    supplier.UpdatedByUserID = int.Parse(User.FindFirst(System.Security.Claims.ClaimTypes.NameIdentifier)?.Value ?? "0");

                    // Update primary address
                    var primaryAddress = supplier.Addresses.FirstOrDefault(a => a.IsDefault && a.IsActive);
                    if (primaryAddress != null)
                    {
                        primaryAddress.Address1 = model.Address1!;
                        primaryAddress.Address2 = model.Address2;
                        primaryAddress.City = model.City;
                        primaryAddress.State = model.StateProvince;
                        primaryAddress.PostCode = model.PostalCode;
                        primaryAddress.Country = model.Country;
                        primaryAddress.UpdatedDate = DateTime.Now;
                    }
                    else if (!string.IsNullOrEmpty(model.Address1))
                    {
                        // Create new primary address if none exists
                        var address = new SupplierAddress
                        {
                            SupplierID = supplier.SupplierID,
                            AddressType = "Billing",
                            IsDefault = true,
                            Address1 = model.Address1!,
                            Address2 = model.Address2,
                            City = model.City,
                            State = model.StateProvince,
                            PostCode = model.PostalCode,
                            Country = model.Country,
                            IsActive = true,
                            CreatedDate = DateTime.Now,
                            UpdatedDate = DateTime.Now
                        };
                        _context.SupplierAddresses.Add(address);
                    }

                    _context.Update(supplier);
                    await _context.SaveChangesAsync();
                    
                    TempData["Success"] = "Supplier updated successfully.";
                    return RedirectToAction(nameof(Index));
                }
                catch (DbUpdateConcurrencyException)
                {
                    if (!SupplierExists(model.SupplierID))
                    {
                        return NotFound();
                    }
                    else
                    {
                        throw;
                    }
                }
            }

            return View(model);
        }

        // GET: Supplier/Delete/5
        public async Task<IActionResult> Delete(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var supplier = await _context.Suppliers
                .FirstOrDefaultAsync(m => m.SupplierID == id && !m.IsDeleted);
            
            if (supplier == null)
            {
                return NotFound();
            }

            return View(supplier);
        }

        // POST: Supplier/Delete/5
        [HttpPost, ActionName("Delete")]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> DeleteConfirmed(int id)
        {
            var supplier = await _context.Suppliers.FindAsync(id);
            if (supplier != null)
            {
                // Soft delete
                supplier.IsDeleted = true;
                supplier.IsActive = false;
                supplier.DeletedDate = DateTime.Now;
                supplier.DeletedByUserID = int.Parse(User.FindFirst(System.Security.Claims.ClaimTypes.NameIdentifier)?.Value ?? "0");
                
                _context.Update(supplier);
                await _context.SaveChangesAsync();
                
                TempData["Success"] = "Supplier deleted successfully.";
            }

            return RedirectToAction(nameof(Index));
        }

        private bool SupplierExists(int id)
        {
            return _context.Suppliers.Any(e => e.SupplierID == id);
        }

        private string GenerateSupplierCode()
        {
            try
            {
                var lastSupplier = _context.Suppliers
                    .Where(s => !s.IsDeleted) // Only consider non-deleted suppliers
                    .OrderByDescending(s => s.SupplierCode)
                    .FirstOrDefault();

                if (lastSupplier == null)
                {
                    return "SUPP-0001";
                }

                // Extract number from last code (handle both formats: SUPP-0001 and SUPP0001)
                var codeWithoutPrefix = lastSupplier.SupplierCode.Replace("SUPP-", "").Replace("SUPP", "");
                if (int.TryParse(codeWithoutPrefix, out var lastNumber))
                {
                    return $"SUPP-{(lastNumber + 1):D4}";
                }
                else
                {
                    // Fallback if parsing fails
                    return "SUPP-0001";
                }
            }
            catch (Exception)
            {
                // Fallback in case of any error
                return "SUPP-0001";
            }
        }
    }
}