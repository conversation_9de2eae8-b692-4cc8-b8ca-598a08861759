using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using nCarry.Web.Data;
using nCarry.Web.Models.Warehouse;

namespace nCarry.Web.Controllers
{
    [Authorize]
    public class WarehouseController : Controller
    {
        private readonly ApplicationDbContext _context;

        public WarehouseController(ApplicationDbContext context)
        {
            _context = context;
        }

        // GET: Warehouse
        public async Task<IActionResult> Index()
        {
            return View(await _context.Warehouses
                .OrderBy(w => w.WarehouseName)
                .ToListAsync());
        }

        // GET: Warehouse/Details/5
        public async Task<IActionResult> Details(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var warehouse = await _context.Warehouses
                .Include(w => w.Locations)
                .FirstOrDefaultAsync(m => m.WarehouseID == id);
                
            if (warehouse == null)
            {
                return NotFound();
            }

            // Get inventory summary
            ViewBag.InventoryCount = await _context.Inventories
                .Where(i => i.WarehouseID == id)
                .CountAsync();

            ViewBag.TotalInventoryValue = await _context.Inventories
                .Where(i => i.WarehouseID == id)
                .SumAsync(i => i.QuantityOnHand * (i.AverageCost ?? 0));

            return View(warehouse);
        }

        // GET: Warehouse/Create
        public IActionResult Create()
        {
            return View();
        }

        // POST: Warehouse/Create
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Create([Bind("WarehouseCode,WarehouseName,WarehouseType,Description,Address1,Address2,City,State,PostCode,Country,Phone,Email,ManagerName,IsActive,IsDefault,AllowNegativeStock,OperatingHours,TimeZone,TotalArea,StorageCapacity")] Warehouse warehouse)
        {
            if (ModelState.IsValid)
            {
                // Check if warehouse code already exists
                if (await _context.Warehouses.AnyAsync(w => w.WarehouseCode == warehouse.WarehouseCode))
                {
                    ModelState.AddModelError("WarehouseCode", "Warehouse code already exists.");
                    return View(warehouse);
                }

                // If setting as default, unset other defaults
                if (warehouse.IsDefault)
                {
                    var existingDefaults = await _context.Warehouses
                        .Where(w => w.IsDefault)
                        .ToListAsync();
                    
                    foreach (var w in existingDefaults)
                    {
                        w.IsDefault = false;
                    }
                }

                warehouse.CreatedDate = DateTime.Now;
                warehouse.UpdatedDate = DateTime.Now;
                warehouse.CreatedByUserID = 1; // TODO: Get from current user

                _context.Add(warehouse);
                await _context.SaveChangesAsync();
                
                TempData["Success"] = "Warehouse created successfully.";
                return RedirectToAction(nameof(Index));
            }
            return View(warehouse);
        }

        // GET: Warehouse/Edit/5
        public async Task<IActionResult> Edit(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var warehouse = await _context.Warehouses
                .FirstOrDefaultAsync(w => w.WarehouseID == id);
                
            if (warehouse == null)
            {
                return NotFound();
            }
            return View(warehouse);
        }

        // POST: Warehouse/Edit/5
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Edit(int id, [Bind("WarehouseID,WarehouseCode,WarehouseName,WarehouseType,Description,Address1,Address2,City,State,PostCode,Country,Phone,Email,ManagerName,IsActive,IsDefault,AllowNegativeStock,OperatingHours,TimeZone,TotalArea,StorageCapacity,CreatedDate,CreatedByUserID")] Warehouse warehouse)
        {
            if (id != warehouse.WarehouseID)
            {
                return NotFound();
            }

            if (ModelState.IsValid)
            {
                try
                {
                    // Check if warehouse code already exists (excluding current record)
                    if (await _context.Warehouses.AnyAsync(w => w.WarehouseCode == warehouse.WarehouseCode && w.WarehouseID != id))
                    {
                        ModelState.AddModelError("WarehouseCode", "Warehouse code already exists.");
                        return View(warehouse);
                    }

                    // If setting as default, unset other defaults
                    if (warehouse.IsDefault)
                    {
                        var existingDefaults = await _context.Warehouses
                            .Where(w => w.IsDefault && w.WarehouseID != id)
                            .ToListAsync();
                        
                        foreach (var w in existingDefaults)
                        {
                            w.IsDefault = false;
                        }
                    }

                    warehouse.UpdatedDate = DateTime.Now;
                    warehouse.UpdatedByUserID = 1; // TODO: Get from current user

                    _context.Update(warehouse);
                    await _context.SaveChangesAsync();
                    
                    TempData["Success"] = "Warehouse updated successfully.";
                }
                catch (DbUpdateConcurrencyException)
                {
                    if (!WarehouseExists(warehouse.WarehouseID))
                    {
                        return NotFound();
                    }
                    else
                    {
                        throw;
                    }
                }
                return RedirectToAction(nameof(Index));
            }
            return View(warehouse);
        }

        // GET: Warehouse/Delete/5
        public async Task<IActionResult> Delete(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var warehouse = await _context.Warehouses
                .FirstOrDefaultAsync(m => m.WarehouseID == id);
                
            if (warehouse == null)
            {
                return NotFound();
            }

            // Check if warehouse has inventory
            var hasInventory = await _context.Inventories
                .AnyAsync(i => i.WarehouseID == id && i.QuantityOnHand > 0);
                
            ViewBag.HasInventory = hasInventory;

            return View(warehouse);
        }

        // POST: Warehouse/Delete/5
        [HttpPost, ActionName("Delete")]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> DeleteConfirmed(int id)
        {
            var warehouse = await _context.Warehouses.FindAsync(id);
            if (warehouse != null)
            {
                // Check if warehouse has inventory
                var hasInventory = await _context.Inventories
                    .AnyAsync(i => i.WarehouseID == id && i.QuantityOnHand > 0);
                    
                if (hasInventory)
                {
                    TempData["Error"] = "Cannot delete warehouse with existing inventory.";
                    return RedirectToAction(nameof(Index));
                }

                // Hard delete since table doesn't have soft delete columns
                _context.Warehouses.Remove(warehouse);
                await _context.SaveChangesAsync();
                
                TempData["Success"] = "Warehouse deleted successfully.";
            }
            
            return RedirectToAction(nameof(Index));
        }

        private bool WarehouseExists(int id)
        {
            return _context.Warehouses.Any(e => e.WarehouseID == id);
        }
    }
}