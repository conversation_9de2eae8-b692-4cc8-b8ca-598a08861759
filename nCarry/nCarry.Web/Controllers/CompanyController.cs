using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using nCarry.Web.Data;
using nCarry.Web.Models.System;

namespace nCarry.Web.Controllers
{
    [Authorize]
    public class CompanyController : Controller
    {
        private readonly ApplicationDbContext _context;
        private readonly IWebHostEnvironment _hostEnvironment;

        public CompanyController(ApplicationDbContext context, IWebHostEnvironment hostEnvironment)
        {
            _context = context;
            _hostEnvironment = hostEnvironment;
        }

        // GET: Company
        public async Task<IActionResult> Index()
        {
            var companies = await _context.Companies
                .Include(c => c.BaseCurrency)
                .OrderBy(c => c.CompanyName)
                .ToListAsync();
            return View(companies);
        }

        // GET: Company/Details/5
        public async Task<IActionResult> Details(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var company = await _context.Companies
                .Include(c => c.BaseCurrency)
                .Include(c => c.Branches)
                .FirstOrDefaultAsync(m => m.CompanyID == id);

            if (company == null)
            {
                return NotFound();
            }

            return View(company);
        }

        // GET: Company/Create
        public IActionResult Create()
        {
            ViewBag.Currencies = _context.Currencies.OrderBy(c => c.CurrencyCode).ToList();
            return View();
        }

        // POST: Company/Create
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Create([Bind("CompanyCode,CompanyName,LegalName,TaxNumber,RegistrationNumber,Website,Address1,Address2,City,State,PostCode,Country,Phone,Email,ContactName,BaseCurrencyID,FiscalYearStart,DefaultPaymentTerms,IsActive,IsDefault")] Company company, IFormFile? logoFile)
        {
            if (ModelState.IsValid)
            {
                // Check if company code already exists
                if (await _context.Companies.AnyAsync(c => c.CompanyCode == company.CompanyCode))
                {
                    ModelState.AddModelError("CompanyCode", "Company code already exists.");
                    ViewBag.Currencies = _context.Currencies.OrderBy(c => c.CurrencyCode).ToList();
                    return View(company);
                }

                // Handle logo upload
                if (logoFile != null && logoFile.Length > 0)
                {
                    using (var memoryStream = new MemoryStream())
                    {
                        await logoFile.CopyToAsync(memoryStream);
                        company.Logo = memoryStream.ToArray();
                    }
                }

                // If setting as default, unset other defaults
                if (company.IsDefault)
                {
                    var existingDefaults = await _context.Companies
                        .Where(c => c.IsDefault)
                        .ToListAsync();
                    
                    foreach (var c in existingDefaults)
                    {
                        c.IsDefault = false;
                    }
                }

                company.CreatedDate = DateTime.Now;
                company.UpdatedDate = DateTime.Now;

                _context.Add(company);
                await _context.SaveChangesAsync();
                
                TempData["Success"] = "Company created successfully.";
                return RedirectToAction(nameof(Index));
            }

            ViewBag.Currencies = _context.Currencies.OrderBy(c => c.CurrencyCode).ToList();
            return View(company);
        }

        // GET: Company/Edit/5
        public async Task<IActionResult> Edit(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var company = await _context.Companies.FindAsync(id);
            if (company == null)
            {
                return NotFound();
            }

            ViewBag.Currencies = _context.Currencies.OrderBy(c => c.CurrencyCode).ToList();
            return View(company);
        }

        // POST: Company/Edit/5
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Edit(int id, [Bind("CompanyID,CompanyCode,CompanyName,LegalName,TaxNumber,RegistrationNumber,Website,Address1,Address2,City,State,PostCode,Country,Phone,Email,ContactName,BaseCurrencyID,FiscalYearStart,DefaultPaymentTerms,IsActive,IsDefault,CreatedDate")] Company company, IFormFile? logoFile, bool removeLogo = false)
        {
            if (id != company.CompanyID)
            {
                return NotFound();
            }

            if (ModelState.IsValid)
            {
                try
                {
                    // Check if company code already exists (excluding current record)
                    if (await _context.Companies.AnyAsync(c => c.CompanyCode == company.CompanyCode && c.CompanyID != id))
                    {
                        ModelState.AddModelError("CompanyCode", "Company code already exists.");
                        ViewBag.Currencies = _context.Currencies.OrderBy(c => c.CurrencyCode).ToList();
                        return View(company);
                    }

                    // Get the existing company to preserve logo if not updating
                    var existingCompany = await _context.Companies.AsNoTracking().FirstOrDefaultAsync(c => c.CompanyID == id);
                    if (existingCompany == null)
                    {
                        return NotFound();
                    }

                    // Handle logo
                    if (removeLogo)
                    {
                        company.Logo = null;
                    }
                    else if (logoFile != null && logoFile.Length > 0)
                    {
                        using (var memoryStream = new MemoryStream())
                        {
                            await logoFile.CopyToAsync(memoryStream);
                            company.Logo = memoryStream.ToArray();
                        }
                    }
                    else
                    {
                        company.Logo = existingCompany.Logo;
                    }

                    // If setting as default, unset other defaults
                    if (company.IsDefault)
                    {
                        var existingDefaults = await _context.Companies
                            .Where(c => c.IsDefault && c.CompanyID != id)
                            .ToListAsync();
                        
                        foreach (var c in existingDefaults)
                        {
                            c.IsDefault = false;
                        }
                    }

                    company.UpdatedDate = DateTime.Now;

                    _context.Update(company);
                    await _context.SaveChangesAsync();
                    
                    TempData["Success"] = "Company updated successfully.";
                }
                catch (DbUpdateConcurrencyException)
                {
                    if (!CompanyExists(company.CompanyID))
                    {
                        return NotFound();
                    }
                    else
                    {
                        throw;
                    }
                }
                return RedirectToAction(nameof(Index));
            }

            ViewBag.Currencies = _context.Currencies.OrderBy(c => c.CurrencyCode).ToList();
            return View(company);
        }

        // GET: Company/Delete/5
        public async Task<IActionResult> Delete(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var company = await _context.Companies
                .Include(c => c.BaseCurrency)
                .FirstOrDefaultAsync(m => m.CompanyID == id);
                
            if (company == null)
            {
                return NotFound();
            }

            // Check if company has branches
            var hasBranches = await _context.Branches.AnyAsync(b => b.CompanyID == id);
            ViewBag.HasBranches = hasBranches;

            return View(company);
        }

        // POST: Company/Delete/5
        [HttpPost, ActionName("Delete")]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> DeleteConfirmed(int id)
        {
            var company = await _context.Companies.FindAsync(id);
            if (company != null)
            {
                // Check if company has branches
                var hasBranches = await _context.Branches.AnyAsync(b => b.CompanyID == id);
                if (hasBranches)
                {
                    TempData["Error"] = "Cannot delete company with existing branches.";
                    return RedirectToAction(nameof(Index));
                }

                _context.Companies.Remove(company);
                await _context.SaveChangesAsync();
                
                TempData["Success"] = "Company deleted successfully.";
            }

            return RedirectToAction(nameof(Index));
        }

        // GET: Company/Logo/5
        public async Task<IActionResult> Logo(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var company = await _context.Companies
                .Where(c => c.CompanyID == id && c.Logo != null)
                .Select(c => new { c.Logo })
                .FirstOrDefaultAsync();

            if (company == null || company.Logo == null)
            {
                return NotFound();
            }

            return File(company.Logo, "image/png");
        }

        private bool CompanyExists(int id)
        {
            return _context.Companies.Any(e => e.CompanyID == id);
        }
    }
}