using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using nCarry.Web.Data;
using nCarry.Web.Models.Products;

namespace nCarry.Web.Controllers
{
    [Authorize]
    public class ProductCategoryController : Controller
    {
        private readonly ApplicationDbContext _context;

        public ProductCategoryController(ApplicationDbContext context)
        {
            _context = context;
        }

        // GET: ProductCategory
        public async Task<IActionResult> Index()
        {
            var categories = await _context.ProductCategories
                .Where(c => c.IsActive)
                .OrderBy(c => c.SortOrder)
                .ThenBy(c => c.CategoryName)
                .ToListAsync();
            
            return View(categories);
        }

        // GET: ProductCategory/Create
        public IActionResult Create()
        {
            return View();
        }

        // POST: ProductCategory/Create
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Create([Bind("CategoryCode,CategoryName,Description,SortOrder")] ProductCategory productCategory)
        {
            if (ModelState.IsValid)
            {
                productCategory.CategoryPath = "/" + productCategory.CategoryName;
                productCategory.IsActive = true;
                productCategory.CreatedDate = DateTime.Now;
                productCategory.UpdatedDate = DateTime.Now;
                
                _context.Add(productCategory);
                await _context.SaveChangesAsync();
                
                TempData["Success"] = "Product category created successfully.";
                return RedirectToAction(nameof(Index));
            }
            return View(productCategory);
        }

        // GET: ProductCategory/Edit/5
        public async Task<IActionResult> Edit(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var productCategory = await _context.ProductCategories.FindAsync(id);
            if (productCategory == null)
            {
                return NotFound();
            }
            return View(productCategory);
        }

        // POST: ProductCategory/Edit/5
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Edit(int id, [Bind("CategoryID,CategoryCode,CategoryName,Description,SortOrder,IsActive")] ProductCategory productCategory)
        {
            if (id != productCategory.CategoryID)
            {
                return NotFound();
            }

            if (ModelState.IsValid)
            {
                try
                {
                    var existingCategory = await _context.ProductCategories.FindAsync(id);
                    if (existingCategory == null)
                    {
                        return NotFound();
                    }

                    existingCategory.CategoryCode = productCategory.CategoryCode;
                    existingCategory.CategoryName = productCategory.CategoryName;
                    existingCategory.Description = productCategory.Description;
                    existingCategory.SortOrder = productCategory.SortOrder;
                    existingCategory.IsActive = productCategory.IsActive;
                    existingCategory.CategoryPath = "/" + productCategory.CategoryName;
                    existingCategory.UpdatedDate = DateTime.Now;

                    _context.Update(existingCategory);
                    await _context.SaveChangesAsync();
                    
                    TempData["Success"] = "Product category updated successfully.";
                }
                catch (DbUpdateConcurrencyException)
                {
                    if (!ProductCategoryExists(productCategory.CategoryID))
                    {
                        return NotFound();
                    }
                    else
                    {
                        throw;
                    }
                }
                return RedirectToAction(nameof(Index));
            }
            return View(productCategory);
        }

        // GET: ProductCategory/Delete/5
        public async Task<IActionResult> Delete(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var productCategory = await _context.ProductCategories
                .FirstOrDefaultAsync(m => m.CategoryID == id);
            if (productCategory == null)
            {
                return NotFound();
            }

            return View(productCategory);
        }

        // POST: ProductCategory/Delete/5
        [HttpPost, ActionName("Delete")]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> DeleteConfirmed(int id)
        {
            var productCategory = await _context.ProductCategories.FindAsync(id);
            if (productCategory != null)
            {
                // Check if category has products
                var hasProducts = await _context.Products.AnyAsync(p => p.CategoryID == id && !p.IsDeleted);
                if (hasProducts)
                {
                    TempData["Error"] = "Cannot delete category that contains products.";
                    return RedirectToAction(nameof(Index));
                }

                productCategory.IsActive = false;
                _context.Update(productCategory);
                await _context.SaveChangesAsync();
                
                TempData["Success"] = "Product category deleted successfully.";
            }

            return RedirectToAction(nameof(Index));
        }

        private bool ProductCategoryExists(int id)
        {
            return _context.ProductCategories.Any(e => e.CategoryID == id);
        }
    }
}