using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Rendering;
using Microsoft.EntityFrameworkCore;
using nCarry.Web.Data;
using nCarry.Web.Models.Sales;
using nCarry.Web.Models.ViewModels;
using System.Linq;

namespace nCarry.Web.Controllers
{
    [Authorize]
    public class SalesInvoiceController : Controller
    {
        private readonly ApplicationDbContext _context;

        public SalesInvoiceController(ApplicationDbContext context)
        {
            _context = context;
        }

        // GET: SalesInvoice
        public async Task<IActionResult> Index()
        {
            var invoices = await _context.SalesInvoices
                .Include(i => i.Customer)
                .Where(i => !i.IsDeleted)
                .OrderByDescending(i => i.InvoiceDate)
                .ToListAsync();

            return View(invoices);
        }

        // GET: SalesInvoice/Details/5
        public async Task<IActionResult> Details(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var invoice = await _context.SalesInvoices
                .Include(i => i.Customer)
                .Include(i => i.SalesOrder)
                .Include(i => i.SalesInvoiceItems)
                    .ThenInclude(item => item.Product)
                .Include(i => i.SalesInvoiceItems)
                    .ThenInclude(item => item.UnitOfMeasure)
                .FirstOrDefaultAsync(i => i.InvoiceID == id && !i.IsDeleted);

            if (invoice == null)
            {
                return NotFound();
            }

            return View(invoice);
        }

        // GET: SalesInvoice/CreateFromOrder/5
        public async Task<IActionResult> CreateFromOrder(int? orderId)
        {
            if (orderId == null)
            {
                return NotFound();
            }

            var order = await _context.SalesOrders
                .Include(o => o.Customer)
                .Include(o => o.SalesOrderItems)
                    .ThenInclude(i => i.Product)
                .Include(o => o.SalesOrderItems)
                    .ThenInclude(i => i.UnitOfMeasure)
                .FirstOrDefaultAsync(o => o.OrderID == orderId && !o.IsDeleted);

            if (order == null)
            {
                return NotFound();
            }

            var viewModel = new SalesInvoiceCreateViewModel
            {
                OrderID = order.OrderID,
                CustomerID = order.CustomerID,
                Currency = order.Currency,
                ExchangeRate = order.ExchangeRate,
                PaymentTerms = order.PaymentTerms,
                DueDate = DateTime.Now.AddDays(30),
                InvoiceDate = DateTime.Now,
                InvoiceType = "Sales Invoice",
                InvoiceNumber = await GenerateInvoiceNumber(),
                CustomerName = order.Customer?.CustomerName ?? "",
                CustomerPO = order.CustomerPO,
                ShippingAmount = order.ShippingAmount,
                Items = order.SalesOrderItems.Select(item => new SalesInvoiceItemViewModel
                {
                    ProductID = item.ProductID,
                    ProductCode = item.ProductCode,
                    ProductName = item.ProductName,
                    Quantity = item.OrderedQuantity,
                    UOMID = item.UOMID,
                    UOMCode = item.UnitOfMeasure?.UOMCode ?? "EA",
                    UnitPrice = item.UnitPrice,
                    DiscountPercent = item.DiscountPercent,
                    DiscountAmount = item.DiscountAmount,
                    TaxRate = item.TaxRate,
                    TaxAmount = item.TaxAmount,
                    LineTotal = item.LineTotal
                }).ToList()
            };

            PopulateDropdowns();
            return View("Create", viewModel);
        }

        // GET: SalesInvoice/Create
        public IActionResult Create()
        {
            var viewModel = new SalesInvoiceCreateViewModel
            {
                InvoiceDate = DateTime.Now,
                DueDate = DateTime.Now.AddDays(30),
                Currency = "GBP",
                ExchangeRate = 1.00m,
                InvoiceType = "Sales Invoice"
            };

            PopulateDropdowns();
            return View(viewModel);
        }

        // POST: SalesInvoice/Create
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Create(SalesInvoiceCreateViewModel viewModel)
        {
            if (ModelState.IsValid)
            {
                using var transaction = await _context.Database.BeginTransactionAsync();
                try
                {
                    // Generate invoice number
                    var invoiceNumber = await GenerateInvoiceNumber();

                    var invoice = new SalesInvoice
                    {
                        InvoiceNumber = invoiceNumber,
                        InvoiceDate = viewModel.InvoiceDate,
                        DueDate = viewModel.DueDate,
                        CustomerID = viewModel.CustomerID,
                        OrderID = viewModel.OrderID,
                        InvoiceType = viewModel.InvoiceType,
                        InvoiceStatus = "Draft",
                        Currency = viewModel.Currency,
                        ExchangeRate = viewModel.ExchangeRate,
                        PaymentTerms = viewModel.PaymentTerms,
                        PaymentStatus = "Unpaid",
                        Notes = viewModel.Notes,
                        InternalNotes = viewModel.InternalNotes,
                        FooterText = viewModel.FooterText,
                        ShippingAmount = viewModel.ShippingAmount,
                        CustomerPO = viewModel.CustomerPO,
                        CreatedDate = DateTime.Now,
                        UpdatedDate = DateTime.Now,
                        IsDeleted = false
                    };

                    // Calculate totals
                    decimal subTotal = 0;
                    decimal taxAmount = 0;
                    decimal discountAmount = 0;

                    _context.SalesInvoices.Add(invoice);
                    await _context.SaveChangesAsync();

                    // Add items
                    if (viewModel.Items != null && viewModel.Items.Any())
                    {
                        int lineNumber = 10;
                        foreach (var item in viewModel.Items.Where(i => i.ProductID > 0 && i.Quantity > 0))
                        {
                            var invoiceItem = new SalesInvoiceItem
                            {
                                InvoiceID = invoice.InvoiceID,
                                LineNumber = lineNumber,
                                ProductID = item.ProductID,
                                ProductCode = item.ProductCode,
                                ProductName = item.ProductName,
                                Quantity = item.Quantity,
                                UOMID = item.UOMID,
                                UnitPrice = item.UnitPrice,
                                DiscountPercent = item.DiscountPercent,
                                DiscountAmount = item.DiscountAmount,
                                TaxRate = item.TaxRate,
                                Description = item.ProductName,
                                SortOrder = lineNumber,
                                CreatedDate = DateTime.Now
                            };

                            // Calculate line totals
                            var lineSubtotal = invoiceItem.Quantity * invoiceItem.UnitPrice;
                            invoiceItem.DiscountAmount = invoiceItem.DiscountPercent > 0 
                                ? lineSubtotal * invoiceItem.DiscountPercent / 100 
                                : invoiceItem.DiscountAmount;
                            invoiceItem.TaxAmount = (lineSubtotal - invoiceItem.DiscountAmount) * invoiceItem.TaxRate / 100;
                            invoiceItem.LineTotal = lineSubtotal - invoiceItem.DiscountAmount + invoiceItem.TaxAmount;

                            subTotal += lineSubtotal;
                            discountAmount += invoiceItem.DiscountAmount;
                            taxAmount += invoiceItem.TaxAmount;

                            _context.SalesInvoiceItems.Add(invoiceItem);
                            lineNumber += 10;
                        }
                    }

                    // Update totals
                    invoice.SubTotal = subTotal;
                    invoice.DiscountAmount = discountAmount;
                    invoice.TaxAmount = taxAmount;
                    invoice.TotalAmount = subTotal - discountAmount + taxAmount + invoice.ShippingAmount;
                    invoice.PaidAmount = 0;
                    invoice.BalanceAmount = invoice.TotalAmount - invoice.PaidAmount;

                    await _context.SaveChangesAsync();

                    // Update sales order if linked
                    if (viewModel.OrderID.HasValue)
                    {
                        var order = await _context.SalesOrders.FindAsync(viewModel.OrderID);
                        if (order != null)
                        {
                            order.OrderStatus = "Invoiced";
                            order.UpdatedDate = DateTime.Now;
                            await _context.SaveChangesAsync();
                        }
                    }

                    await transaction.CommitAsync();
                    TempData["Success"] = "Invoice created successfully.";
                    return RedirectToAction(nameof(Details), new { id = invoice.InvoiceID });
                }
                catch (Exception ex)
                {
                    await transaction.RollbackAsync();
                    var innerException = ex.InnerException?.Message ?? "No inner exception";
                    ModelState.AddModelError("", $"An error occurred while creating the invoice: {ex.Message}. Inner: {innerException}");
                }
            }

            PopulateDropdowns();
            return View(viewModel);
        }

        // GET: SalesInvoice/Print/5
        public async Task<IActionResult> Print(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var invoice = await _context.SalesInvoices
                .Include(i => i.Customer)
                    .ThenInclude(c => c.Addresses)
                .Include(i => i.SalesOrder)
                .Include(i => i.SalesInvoiceItems)
                    .ThenInclude(item => item.Product)
                .Include(i => i.SalesInvoiceItems)
                    .ThenInclude(item => item.UnitOfMeasure)
                .FirstOrDefaultAsync(i => i.InvoiceID == id && !i.IsDeleted);

            if (invoice == null)
            {
                return NotFound();
            }

            // Get company information
            var company = await _context.Companies.FirstOrDefaultAsync();
            ViewBag.Company = company;

            return View(invoice);
        }

        private void PopulateDropdowns()
        {
            ViewBag.Customers = new SelectList(_context.Customers.Where(c => c.IsActive && !c.IsDeleted)
                .OrderBy(c => c.CustomerName), "CustomerID", "CustomerName");
            
            ViewBag.SalesOrders = new SelectList(_context.SalesOrders
                .Include(o => o.Customer)
                .Where(o => !o.IsDeleted && o.OrderStatus != "Cancelled")
                .OrderByDescending(o => o.OrderDate)
                .Select(o => new { o.OrderID, DisplayText = o.OrderNumber + " - " + o.Customer.CustomerName }),
                "OrderID", "DisplayText");
        }

        // GET: SalesInvoice/Edit/5
        public async Task<IActionResult> Edit(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var invoice = await _context.SalesInvoices
                .Include(i => i.Customer)
                .Include(i => i.SalesInvoiceItems)
                .FirstOrDefaultAsync(i => i.InvoiceID == id && !i.IsDeleted);

            if (invoice == null)
            {
                return NotFound();
            }

            // Only draft invoices can be edited
            if (invoice.InvoiceStatus != "Draft")
            {
                TempData["Error"] = "Only draft invoices can be edited.";
                return RedirectToAction(nameof(Details), new { id = invoice.InvoiceID });
            }

            var viewModel = new SalesInvoiceEditViewModel
            {
                InvoiceID = invoice.InvoiceID,
                InvoiceNumber = invoice.InvoiceNumber,
                InvoiceDate = invoice.InvoiceDate,
                DueDate = invoice.DueDate,
                CustomerID = invoice.CustomerID,
                CustomerName = invoice.Customer.CustomerName,
                OrderID = invoice.OrderID,
                Currency = invoice.Currency,
                ExchangeRate = invoice.ExchangeRate,
                PaymentTerms = invoice.PaymentTerms,
                CustomerPO = invoice.CustomerPO,
                Notes = invoice.Notes,
                InternalNotes = invoice.InternalNotes,
                FooterText = invoice.FooterText,
                Items = invoice.SalesInvoiceItems.Select(item => new SalesInvoiceItemViewModel
                {
                    ProductID = item.ProductID,
                    ProductCode = item.ProductCode ?? "",
                    ProductName = item.ProductName ?? "",
                    Quantity = item.Quantity,
                    UOMID = item.UOMID,
                    UnitPrice = item.UnitPrice,
                    DiscountPercent = item.DiscountPercent,
                    DiscountAmount = item.DiscountAmount,
                    TaxRate = item.TaxRate,
                    TaxAmount = item.TaxAmount,
                    LineTotal = item.LineTotal
                }).ToList()
            };

            PopulateDropdowns();
            return View(viewModel);
        }

        // POST: SalesInvoice/Edit/5
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Edit(int id, SalesInvoiceEditViewModel viewModel)
        {
            if (id != viewModel.InvoiceID)
            {
                return NotFound();
            }

            if (ModelState.IsValid)
            {
                using var transaction = await _context.Database.BeginTransactionAsync();
                try
                {
                    var invoice = await _context.SalesInvoices
                        .Include(i => i.SalesInvoiceItems)
                        .FirstOrDefaultAsync(i => i.InvoiceID == id && !i.IsDeleted);

                    if (invoice == null)
                    {
                        return NotFound();
                    }

                    // Only draft invoices can be edited
                    if (invoice.InvoiceStatus != "Draft")
                    {
                        ModelState.AddModelError("", "Only draft invoices can be edited.");
                        PopulateDropdowns();
                        return View(viewModel);
                    }

                    // Update invoice properties
                    invoice.InvoiceDate = viewModel.InvoiceDate;
                    invoice.DueDate = viewModel.DueDate;
                    invoice.PaymentTerms = viewModel.PaymentTerms;
                    invoice.CustomerPO = viewModel.CustomerPO;
                    invoice.Notes = viewModel.Notes;
                    invoice.InternalNotes = viewModel.InternalNotes;
                    invoice.FooterText = viewModel.FooterText;
                    invoice.UpdatedDate = DateTime.Now;

                    // Remove existing items
                    _context.SalesInvoiceItems.RemoveRange(invoice.SalesInvoiceItems);

                    // Calculate totals
                    decimal subTotal = 0;
                    decimal taxAmount = 0;
                    decimal discountAmount = 0;

                    // Add updated items
                    if (viewModel.Items != null && viewModel.Items.Any())
                    {
                        int lineNumber = 10;
                        foreach (var item in viewModel.Items.Where(i => i.ProductID > 0 && i.Quantity > 0))
                        {
                            var invoiceItem = new SalesInvoiceItem
                            {
                                InvoiceID = invoice.InvoiceID,
                                LineNumber = lineNumber,
                                ProductID = item.ProductID,
                                ProductCode = item.ProductCode,
                                ProductName = item.ProductName,
                                Quantity = item.Quantity,
                                UOMID = item.UOMID,
                                UnitPrice = item.UnitPrice,
                                DiscountPercent = item.DiscountPercent,
                                DiscountAmount = item.DiscountAmount,
                                TaxRate = item.TaxRate,
                                Description = item.ProductName,
                                SortOrder = lineNumber,
                                CreatedDate = DateTime.Now
                            };

                            // Calculate line totals
                            var lineSubtotal = invoiceItem.Quantity * invoiceItem.UnitPrice;
                            invoiceItem.DiscountAmount = invoiceItem.DiscountPercent > 0 
                                ? lineSubtotal * invoiceItem.DiscountPercent / 100 
                                : invoiceItem.DiscountAmount;
                            invoiceItem.TaxAmount = (lineSubtotal - invoiceItem.DiscountAmount) * invoiceItem.TaxRate / 100;
                            invoiceItem.LineTotal = lineSubtotal - invoiceItem.DiscountAmount + invoiceItem.TaxAmount;

                            subTotal += lineSubtotal;
                            discountAmount += invoiceItem.DiscountAmount;
                            taxAmount += invoiceItem.TaxAmount;

                            _context.SalesInvoiceItems.Add(invoiceItem);
                            lineNumber += 10;
                        }
                    }

                    // Update totals
                    invoice.SubTotal = subTotal;
                    invoice.DiscountAmount = discountAmount;
                    invoice.TaxAmount = taxAmount;
                    invoice.TotalAmount = subTotal - discountAmount + taxAmount + invoice.ShippingAmount;
                    invoice.BalanceAmount = invoice.TotalAmount - invoice.PaidAmount;

                    await _context.SaveChangesAsync();
                    await transaction.CommitAsync();

                    TempData["Success"] = "Invoice updated successfully.";
                    return RedirectToAction(nameof(Details), new { id = invoice.InvoiceID });
                }
                catch (Exception)
                {
                    await transaction.RollbackAsync();
                    ModelState.AddModelError("", "An error occurred while updating the invoice.");
                }
            }

            PopulateDropdowns();
            return View(viewModel);
        }

        // GET: SalesInvoice/Delete/5
        public async Task<IActionResult> Delete(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var invoice = await _context.SalesInvoices
                .Include(i => i.Customer)
                .FirstOrDefaultAsync(i => i.InvoiceID == id && !i.IsDeleted);

            if (invoice == null)
            {
                return NotFound();
            }

            // Only draft invoices can be deleted
            if (invoice.InvoiceStatus != "Draft")
            {
                TempData["Error"] = "Only draft invoices can be deleted.";
                return RedirectToAction(nameof(Index));
            }

            return View(invoice);
        }

        // POST: SalesInvoice/Delete/5
        [HttpPost, ActionName("Delete")]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> DeleteConfirmed(int id)
        {
            var invoice = await _context.SalesInvoices.FindAsync(id);
            if (invoice == null)
            {
                return NotFound();
            }

            // Only draft invoices can be deleted
            if (invoice.InvoiceStatus != "Draft")
            {
                TempData["Error"] = "Only draft invoices can be deleted.";
                return RedirectToAction(nameof(Index));
            }

            // Soft delete
            invoice.IsDeleted = true;
            invoice.DeletedDate = DateTime.Now;
            
            await _context.SaveChangesAsync();
            TempData["Success"] = "Invoice deleted successfully.";
            return RedirectToAction(nameof(Index));
        }

        private async Task<string> GenerateInvoiceNumber()
        {
            var lastInvoice = await _context.SalesInvoices
                .OrderByDescending(i => i.InvoiceID)
                .FirstOrDefaultAsync();

            int nextNumber = lastInvoice != null ? lastInvoice.InvoiceID + 1 : 1;
            return $"INV-{nextNumber:D4}";
        }
    }
}