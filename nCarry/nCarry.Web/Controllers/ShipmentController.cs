using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Rendering;
using Microsoft.EntityFrameworkCore;
using nCarry.Web.Data;
using nCarry.Web.Models.Logistics;

namespace nCarry.Web.Controllers
{
    [Authorize]
    public class ShipmentController : Controller
    {
        private readonly ApplicationDbContext _context;

        public ShipmentController(ApplicationDbContext context)
        {
            _context = context;
        }

        // GET: Shipment
        public async Task<IActionResult> Index(int? branchId, string? status, DateTime? fromDate, DateTime? toDate, int? carrierId)
        {
            var shipmentsQuery = _context.Shipments
                .Include(s => s.Carrier)
                .Include(s => s.Service)
                .Include(s => s.Route)
                .Include(s => s.ShipFromWarehouse)
                .AsQueryable();

            if (branchId.HasValue)
            {
                shipmentsQuery = shipmentsQuery.Where(s => s.BranchID == branchId.Value);
                ViewBag.SelectedBranchId = branchId.Value;
            }

            if (!string.IsNullOrEmpty(status))
            {
                shipmentsQuery = shipmentsQuery.Where(s => s.Status == status);
                ViewBag.SelectedStatus = status;
            }

            if (fromDate.HasValue)
            {
                shipmentsQuery = shipmentsQuery.Where(s => s.ShipmentDate >= fromDate.Value);
                ViewBag.FromDate = fromDate.Value;
            }

            if (toDate.HasValue)
            {
                shipmentsQuery = shipmentsQuery.Where(s => s.ShipmentDate <= toDate.Value);
                ViewBag.ToDate = toDate.Value;
            }

            if (carrierId.HasValue)
            {
                shipmentsQuery = shipmentsQuery.Where(s => s.CarrierID == carrierId.Value);
                ViewBag.SelectedCarrierId = carrierId.Value;
            }

            var shipments = await shipmentsQuery
                .OrderByDescending(s => s.ShipmentDate)
                .ThenBy(s => s.ShipmentNumber)
                .ToListAsync();

            // Populate filter dropdowns
            ViewBag.Branches = new SelectList(
                await _context.Branches.Where(b => b.IsActive).OrderBy(b => b.BranchName).ToListAsync(),
                "BranchID", "BranchName", branchId);

            ViewBag.Carriers = new SelectList(
                await _context.Carriers.Where(c => c.IsActive).OrderBy(c => c.CarrierName).ToListAsync(),
                "CarrierID", "CarrierName", carrierId);

            ViewBag.StatusList = new List<SelectListItem>
            {
                new SelectListItem { Value = "", Text = "All Statuses" },
                new SelectListItem { Value = "Pending", Text = "Pending", Selected = status == "Pending" },
                new SelectListItem { Value = "Picked", Text = "Picked", Selected = status == "Picked" },
                new SelectListItem { Value = "InTransit", Text = "In Transit", Selected = status == "InTransit" },
                new SelectListItem { Value = "OutForDelivery", Text = "Out for Delivery", Selected = status == "OutForDelivery" },
                new SelectListItem { Value = "Delivered", Text = "Delivered", Selected = status == "Delivered" },
                new SelectListItem { Value = "Failed", Text = "Failed", Selected = status == "Failed" },
                new SelectListItem { Value = "Returned", Text = "Returned", Selected = status == "Returned" }
            };

            return View(shipments);
        }

        // GET: Shipment/Details/5
        public async Task<IActionResult> Details(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var shipment = await _context.Shipments
                .Include(s => s.Carrier)
                .Include(s => s.Service)
                .Include(s => s.Route)
                .Include(s => s.ShipFromWarehouse)
                .Include(s => s.TrackingHistory)
                .FirstOrDefaultAsync(m => m.ShipmentID == id);

            if (shipment == null)
            {
                return NotFound();
            }

            return View(shipment);
        }

        // GET: Shipment/Create
        public async Task<IActionResult> Create(int? branchId, int? customerId)
        {
            await PopulateDropdowns(branchId, customerId);
            
            var shipment = new Shipment
            {
                ShipmentDate = DateTime.Today,
                Status = "Pending",
                Priority = "Medium"
            };
            
            if (branchId.HasValue)
            {
                shipment.BranchID = branchId.Value;
            }
            if (customerId.HasValue)
            {
                shipment.CustomerID = customerId.Value;
            }

            // Generate shipment number
            shipment.ShipmentNumber = await GenerateShipmentNumber();

            return View(shipment);
        }

        // POST: Shipment/Create
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Create([Bind("BranchID,CustomerID,CarrierID,ServiceID,ShipmentNumber,ShipmentDate,DeliveryZoneID,RouteID,RecipientName,RecipientPhone,RecipientEmail,DeliveryAddress,DeliveryPostalCode,DeliveryCity,DeliveryCountry,Weight,Volume,DeclaredValue,InsuranceValue,CODAmount,DeliveryInstructions,Priority,SpecialHandling,Status")] Shipment shipment)
        {
            if (ModelState.IsValid)
            {
                // Check if shipment number already exists
                if (await _context.Shipments.AnyAsync(s => s.ShipmentNumber == shipment.ShipmentNumber))
                {
                    shipment.ShipmentNumber = await GenerateShipmentNumber();
                }

                shipment.CreatedDate = DateTime.Now;
                shipment.UpdatedDate = DateTime.Now;

                _context.Add(shipment);
                await _context.SaveChangesAsync();

                // Create initial tracking event
                var trackingEvent = new ShipmentTracking
                {
                    ShipmentID = shipment.ShipmentID,
                    EventDate = DateTime.Now,
                    Status = shipment.Status,
                    Location = shipment.Branch?.BranchName ?? "Origin",
                    Description = "Shipment created",
                    CreatedDate = DateTime.Now
                };

                _context.ShipmentTrackings.Add(trackingEvent);
                await _context.SaveChangesAsync();
                
                TempData["Success"] = "Shipment created successfully.";
                return RedirectToAction(nameof(Details), new { id = shipment.ShipmentID });
            }

            await PopulateDropdowns(shipment.BranchID, shipment.CustomerID);
            return View(shipment);
        }

        // GET: Shipment/Edit/5
        public async Task<IActionResult> Edit(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var shipment = await _context.Shipments.FindAsync(id);
            if (shipment == null)
            {
                return NotFound();
            }

            await PopulateDropdowns(shipment.BranchID, shipment.CustomerID);
            return View(shipment);
        }

        // POST: Shipment/Edit/5
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Edit(int id, [Bind("ShipmentID,BranchID,CustomerID,CarrierID,ServiceID,ShipmentNumber,ShipmentDate,DeliveryZoneID,RouteID,RecipientName,RecipientPhone,RecipientEmail,DeliveryAddress,DeliveryPostalCode,DeliveryCity,DeliveryCountry,Weight,Volume,DeclaredValue,InsuranceValue,CODAmount,DeliveryInstructions,Priority,SpecialHandling,Status,CreatedDate")] Shipment shipment)
        {
            if (id != shipment.ShipmentID)
            {
                return NotFound();
            }

            if (ModelState.IsValid)
            {
                try
                {
                    var originalShipment = await _context.Shipments.AsNoTracking().FirstOrDefaultAsync(s => s.ShipmentID == id);
                    
                    shipment.UpdatedDate = DateTime.Now;
                    _context.Update(shipment);
                    await _context.SaveChangesAsync();

                    // Create tracking event if status changed
                    if (originalShipment != null && originalShipment.Status != shipment.Status)
                    {
                        var trackingEvent = new ShipmentTracking
                        {
                            ShipmentID = shipment.ShipmentID,
                            EventDate = DateTime.Now,
                            Status = shipment.Status,
                            Location = "Updated",
                            Description = $"Status changed from {originalShipment.Status} to {shipment.Status}",
                            CreatedDate = DateTime.Now,
                            UpdatedDate = DateTime.Now
                        };

                        _context.ShipmentTrackings.Add(trackingEvent);
                        await _context.SaveChangesAsync();
                    }
                    
                    TempData["Success"] = "Shipment updated successfully.";
                }
                catch (DbUpdateConcurrencyException)
                {
                    if (!ShipmentExists(shipment.ShipmentID))
                    {
                        return NotFound();
                    }
                    else
                    {
                        throw;
                    }
                }
                return RedirectToAction(nameof(Details), new { id = shipment.ShipmentID });
            }

            await PopulateDropdowns(shipment.BranchID, shipment.CustomerID);
            return View(shipment);
        }

        // GET: Shipment/Track
        public IActionResult Track()
        {
            return View();
        }

        // POST: Shipment/Track
        [HttpPost]
        public async Task<IActionResult> Track(string trackingNumber)
        {
            if (string.IsNullOrEmpty(trackingNumber))
            {
                ViewBag.Error = "Please enter a tracking number.";
                return View();
            }

            var shipment = await _context.Shipments
                .Include(s => s.Carrier)
                .Include(s => s.Service)
                .Include(s => s.ShipFromWarehouse)
                .Include(s => s.TrackingHistory)
                .FirstOrDefaultAsync(s => s.ShipmentNumber == trackingNumber || s.TrackingNumber == trackingNumber);

            if (shipment == null)
            {
                ViewBag.Error = "Shipment not found. Please check your tracking number.";
                return View();
            }

            return View("TrackingResult", shipment);
        }

        // POST: Shipment/UpdateStatus
        [HttpPost]
        public async Task<IActionResult> UpdateStatus(int shipmentId, string status, string location, string description)
        {
            var shipment = await _context.Shipments.FindAsync(shipmentId);
            if (shipment == null)
            {
                return Json(new { success = false, message = "Shipment not found." });
            }

            shipment.Status = status;
            shipment.UpdatedDate = DateTime.Now;

            // Create tracking event
            var trackingEvent = new ShipmentTracking
            {
                ShipmentID = shipmentId,
                EventDate = DateTime.Now,
                Status = status,
                Location = location ?? "Unknown",
                Description = description ?? $"Status updated to {status}",
                CreatedDate = DateTime.Now
            };

            _context.Update(shipment);
            _context.ShipmentTrackings.Add(trackingEvent);
            await _context.SaveChangesAsync();

            return Json(new { success = true, message = "Status updated successfully." });
        }

        // GET: Shipment/BulkUpdate
        public IActionResult BulkUpdate()
        {
            ViewBag.StatusList = new List<SelectListItem>
            {
                new SelectListItem { Value = "Picked", Text = "Picked" },
                new SelectListItem { Value = "InTransit", Text = "In Transit" },
                new SelectListItem { Value = "OutForDelivery", Text = "Out for Delivery" },
                new SelectListItem { Value = "Delivered", Text = "Delivered" },
                new SelectListItem { Value = "Failed", Text = "Failed" },
                new SelectListItem { Value = "Returned", Text = "Returned" }
            };

            return View();
        }

        // POST: Shipment/BulkUpdate
        [HttpPost]
        public async Task<IActionResult> BulkUpdate(string shipmentNumbers, string status, string location, string description)
        {
            if (string.IsNullOrEmpty(shipmentNumbers) || string.IsNullOrEmpty(status))
            {
                TempData["Error"] = "Please provide shipment numbers and status.";
                return RedirectToAction(nameof(BulkUpdate));
            }

            var numbers = shipmentNumbers.Split('\n', StringSplitOptions.RemoveEmptyEntries)
                .Select(n => n.Trim()).ToList();

            var shipments = await _context.Shipments
                .Where(s => numbers.Contains(s.ShipmentNumber) || (s.TrackingNumber != null && numbers.Contains(s.TrackingNumber)))
                .ToListAsync();

            var trackingEvents = new List<ShipmentTracking>();
            var updateCount = 0;

            foreach (var shipment in shipments)
            {
                shipment.Status = status;
                shipment.UpdatedDate = DateTime.Now;

                trackingEvents.Add(new ShipmentTracking
                {
                    ShipmentID = shipment.ShipmentID,
                    EventDate = DateTime.Now,
                    Status = status,
                    Location = location ?? "Bulk Update",
                    Description = description ?? $"Bulk status update to {status}",
                    CreatedDate = DateTime.Now
                });

                updateCount++;
            }

            _context.UpdateRange(shipments);
            _context.ShipmentTrackings.AddRange(trackingEvents);
            await _context.SaveChangesAsync();

            TempData["Success"] = $"{updateCount} shipments updated successfully.";
            return RedirectToAction(nameof(Index));
        }

        private async Task<string> GenerateShipmentNumber()
        {
            var prefix = "SH";
            var date = DateTime.Now.ToString("yyyyMMdd");
            
            var lastNumber = await _context.Shipments
                .Where(s => s.ShipmentNumber.StartsWith(prefix + date))
                .OrderByDescending(s => s.ShipmentNumber)
                .Select(s => s.ShipmentNumber)
                .FirstOrDefaultAsync();

            int sequence = 1;
            if (!string.IsNullOrEmpty(lastNumber))
            {
                var lastSequence = lastNumber.Substring((prefix + date).Length);
                if (int.TryParse(lastSequence, out int parsed))
                {
                    sequence = parsed + 1;
                }
            }

            return $"{prefix}{date}{sequence:D4}";
        }

        private bool ShipmentExists(int id)
        {
            return _context.Shipments.Any(e => e.ShipmentID == id);
        }

        private async Task PopulateDropdowns(int? branchId = null, int? customerId = null)
        {
            ViewBag.Branches = new SelectList(
                await _context.Branches.Where(b => b.IsActive).OrderBy(b => b.BranchName).ToListAsync(),
                "BranchID", "BranchName", branchId);

            ViewBag.Customers = new SelectList(
                await _context.Customers.Where(c => c.IsActive).OrderBy(c => c.CustomerName).ToListAsync(),
                "CustomerID", "CustomerName", customerId);

            ViewBag.Warehouses = new SelectList(
                await _context.Warehouses.OrderBy(w => w.WarehouseName).ToListAsync(),
                "WarehouseID", "WarehouseName");

            ViewBag.Carriers = new SelectList(
                await _context.Carriers.Where(c => c.IsActive).OrderBy(c => c.CarrierName).ToListAsync(),
                "CarrierID", "CarrierName");

            ViewBag.Services = new SelectList(
                await _context.CarrierServices.Where(s => s.IsActive).Include(s => s.Carrier)
                    .OrderBy(s => s.Carrier.CarrierName).ThenBy(s => s.ServiceName).ToListAsync(),
                "ServiceID", "ServiceName");

            ViewBag.DeliveryZones = new SelectList(
                await _context.DeliveryZones.Where(z => z.IsActive && (!branchId.HasValue || z.BranchID == branchId.Value))
                    .OrderBy(z => z.ZoneName).ToListAsync(),
                "ZoneID", "ZoneName");

            ViewBag.Routes = new SelectList(
                await _context.DeliveryRoutes.Where(r => r.IsActive && (!branchId.HasValue || r.BranchID == branchId.Value))
                    .OrderBy(r => r.RouteName).ToListAsync(),
                "RouteID", "RouteName");

            ViewBag.StatusList = new List<SelectListItem>
            {
                new SelectListItem { Value = "Pending", Text = "Pending" },
                new SelectListItem { Value = "Picked", Text = "Picked" },
                new SelectListItem { Value = "InTransit", Text = "In Transit" },
                new SelectListItem { Value = "OutForDelivery", Text = "Out for Delivery" },
                new SelectListItem { Value = "Delivered", Text = "Delivered" },
                new SelectListItem { Value = "Failed", Text = "Failed" },
                new SelectListItem { Value = "Returned", Text = "Returned" }
            };

            ViewBag.PriorityList = new List<SelectListItem>
            {
                new SelectListItem { Value = "Low", Text = "Low" },
                new SelectListItem { Value = "Medium", Text = "Medium" },
                new SelectListItem { Value = "High", Text = "High" },
                new SelectListItem { Value = "Urgent", Text = "Urgent" }
            };
        }
    }
}