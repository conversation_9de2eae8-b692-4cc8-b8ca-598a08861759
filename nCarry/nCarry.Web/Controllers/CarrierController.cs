using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using nCarry.Web.Data;
using nCarry.Web.Models.Logistics;

namespace nCarry.Web.Controllers
{
    [Authorize]
    public class CarrierController : Controller
    {
        private readonly ApplicationDbContext _context;

        public CarrierController(ApplicationDbContext context)
        {
            _context = context;
        }

        // GET: Carrier
        public async Task<IActionResult> Index()
        {
            var carriers = await _context.Carriers
                .Include(c => c.Services)
                .OrderBy(c => c.CarrierName)
                .ToListAsync();
            return View(carriers);
        }

        // GET: Carrier/Details/5
        public async Task<IActionResult> Details(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var carrier = await _context.Carriers
                .Include(c => c.Services)
                .Include(c => c.FreightRates)
                .FirstOrDefaultAsync(m => m.CarrierID == id);

            if (carrier == null)
            {
                return NotFound();
            }

            return View(carrier);
        }

        // GET: Carrier/Create
        public IActionResult Create()
        {
            return View();
        }

        // POST: Carrier/Create
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Create([Bind("CarrierCode,CarrierName,CarrierType,ContactName,Phone,Email,Website,AccountNumber,TrackingURL,APIEndpoint,APIKey,IsActive,IsDefault,Notes")] Carrier carrier)
        {
            if (ModelState.IsValid)
            {
                // Check if carrier code already exists
                if (await _context.Carriers.AnyAsync(c => c.CarrierCode == carrier.CarrierCode))
                {
                    ModelState.AddModelError("CarrierCode", "Carrier code already exists.");
                    return View(carrier);
                }

                // If setting as default, unset other defaults
                if (carrier.IsDefault)
                {
                    var existingDefaults = await _context.Carriers
                        .Where(c => c.IsDefault)
                        .ToListAsync();
                    
                    foreach (var c in existingDefaults)
                    {
                        c.IsDefault = false;
                    }
                }

                carrier.CreatedDate = DateTime.Now;
                carrier.UpdatedDate = DateTime.Now;

                _context.Add(carrier);
                await _context.SaveChangesAsync();
                
                TempData["Success"] = "Carrier created successfully.";
                return RedirectToAction(nameof(Index));
            }
            return View(carrier);
        }

        // GET: Carrier/Edit/5
        public async Task<IActionResult> Edit(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var carrier = await _context.Carriers.FindAsync(id);
            if (carrier == null)
            {
                return NotFound();
            }
            return View(carrier);
        }

        // POST: Carrier/Edit/5
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Edit(int id, [Bind("CarrierID,CarrierCode,CarrierName,CarrierType,ContactName,Phone,Email,Website,AccountNumber,TrackingURL,APIEndpoint,APIKey,IsActive,IsDefault,Notes,CreatedDate,CreatedByUserID")] Carrier carrier)
        {
            if (id != carrier.CarrierID)
            {
                return NotFound();
            }

            if (ModelState.IsValid)
            {
                try
                {
                    // Check if carrier code already exists (excluding current record)
                    if (await _context.Carriers.AnyAsync(c => c.CarrierCode == carrier.CarrierCode && c.CarrierID != id))
                    {
                        ModelState.AddModelError("CarrierCode", "Carrier code already exists.");
                        return View(carrier);
                    }

                    // If setting as default, unset other defaults
                    if (carrier.IsDefault)
                    {
                        var existingDefaults = await _context.Carriers
                            .Where(c => c.IsDefault && c.CarrierID != id)
                            .ToListAsync();
                        
                        foreach (var c in existingDefaults)
                        {
                            c.IsDefault = false;
                        }
                    }

                    carrier.UpdatedDate = DateTime.Now;

                    _context.Update(carrier);
                    await _context.SaveChangesAsync();
                    
                    TempData["Success"] = "Carrier updated successfully.";
                }
                catch (DbUpdateConcurrencyException)
                {
                    if (!CarrierExists(carrier.CarrierID))
                    {
                        return NotFound();
                    }
                    else
                    {
                        throw;
                    }
                }
                return RedirectToAction(nameof(Index));
            }
            return View(carrier);
        }

        // GET: Carrier/Delete/5
        public async Task<IActionResult> Delete(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var carrier = await _context.Carriers
                .Include(c => c.Services)
                .FirstOrDefaultAsync(m => m.CarrierID == id);
                
            if (carrier == null)
            {
                return NotFound();
            }

            // Check if carrier has services or shipments
            var hasServices = carrier.Services.Any();
            var hasShipments = await _context.Shipments.AnyAsync(s => s.CarrierID == id);
            
            ViewBag.HasServices = hasServices;
            ViewBag.HasShipments = hasShipments;

            return View(carrier);
        }

        // POST: Carrier/Delete/5
        [HttpPost, ActionName("Delete")]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> DeleteConfirmed(int id)
        {
            var carrier = await _context.Carriers.FindAsync(id);
            if (carrier != null)
            {
                // Check if carrier has services or shipments
                var hasServices = await _context.CarrierServices.AnyAsync(s => s.CarrierID == id);
                var hasShipments = await _context.Shipments.AnyAsync(s => s.CarrierID == id);
                
                if (hasServices || hasShipments)
                {
                    TempData["Error"] = "Cannot delete carrier with existing services or shipments.";
                    return RedirectToAction(nameof(Index));
                }

                _context.Carriers.Remove(carrier);
                await _context.SaveChangesAsync();
                
                TempData["Success"] = "Carrier deleted successfully.";
            }

            return RedirectToAction(nameof(Index));
        }

        private bool CarrierExists(int id)
        {
            return _context.Carriers.Any(e => e.CarrierID == id);
        }
    }
}