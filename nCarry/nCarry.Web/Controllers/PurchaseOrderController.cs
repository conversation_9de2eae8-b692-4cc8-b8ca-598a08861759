using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Rendering;
using Microsoft.EntityFrameworkCore;
using nCarry.Web.Data;
using nCarry.Web.Models.Purchase;
using nCarry.Web.Models.ViewModels;

namespace nCarry.Web.Controllers
{
    [Authorize]
    public class PurchaseOrderController : Controller
    {
        private readonly ApplicationDbContext _context;

        public PurchaseOrderController(ApplicationDbContext context)
        {
            _context = context;
        }

        // GET: PurchaseOrder
        public async Task<IActionResult> Index()
        {
            var purchaseOrders = await _context.PurchaseOrders
                .Include(p => p.Supplier)
                .Include(p => p.DeliveryWarehouse)
                .Where(p => !p.IsDeleted)
                .OrderByDescending(p => p.CreatedDate)
                .ToListAsync();

            return View(purchaseOrders);
        }

        // GET: PurchaseOrder/Details/5
        public async Task<IActionResult> Details(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var purchaseOrder = await _context.PurchaseOrders
                .Include(p => p.Supplier)
                .Include(p => p.DeliveryWarehouse)
                .Include(p => p.Buyer)
                .Include(p => p.ApprovedByUser)
                .Include(p => p.PurchaseOrderItems)
                    .ThenInclude(poi => poi.Product)
                .Include(p => p.PurchaseOrderItems)
                    .ThenInclude(poi => poi.UnitOfMeasure)
                .FirstOrDefaultAsync(m => m.PurchaseOrderID == id && !m.IsDeleted);

            if (purchaseOrder == null)
            {
                return NotFound();
            }

            return View(purchaseOrder);
        }

        // GET: PurchaseOrder/Create
        public async Task<IActionResult> Create()
        {
            var viewModel = new PurchaseOrderCreateViewModel();
            await PopulateDropdownsAsync(viewModel);
            
            // Also populate ViewBag for JavaScript access
            ViewBag.Products = new SelectList(
                await _context.Products
                    .Where(p => p.IsActive && !p.IsDeleted)
                    .OrderBy(p => p.ProductName)
                    .ToListAsync(),
                "ProductID", "ProductName");

            ViewBag.UnitOfMeasures = new SelectList(
                await _context.UnitOfMeasures
                    .Where(u => u.IsActive)
                    .OrderBy(u => u.UOMName)
                    .ToListAsync(),
                "UOMID", "UOMName");
                
            return View(viewModel);
        }

        // POST: PurchaseOrder/Create
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Create(PurchaseOrderCreateViewModel viewModel)
        {
            if (ModelState.IsValid)
            {
                using var transaction = await _context.Database.BeginTransactionAsync();
                try
                {
                    // Generate PO number
                    var poNumber = await GeneratePurchaseOrderNumberAsync();

                    var purchaseOrder = new PurchaseOrder
                    {
                        PurchaseOrderNumber = poNumber,
                        OrderDate = DateTime.Now,
                        SupplierID = viewModel.SupplierID,
                        SupplierReference = viewModel.SupplierReference,
                        BuyerID = viewModel.BuyerID,
                        OrderType = viewModel.OrderType,
                        RequiredDate = viewModel.RequiredDate,
                        ExpectedDate = viewModel.ExpectedDate,
                        DeliveryWarehouseID = viewModel.DeliveryWarehouseID,
                        DeliveryInstructions = viewModel.DeliveryInstructions,
                        ShippingMethod = viewModel.ShippingMethod,
                        FreightTerms = viewModel.FreightTerms,
                        PaymentTerms = viewModel.PaymentTerms,
                        PaymentMethod = viewModel.PaymentMethod,
                        Currency = viewModel.Currency,
                        ExchangeRate = viewModel.ExchangeRate,
                        Notes = viewModel.Notes,
                        InternalNotes = viewModel.InternalNotes,
                        TermsAndConditions = viewModel.TermsAndConditions,
                        CreatedDate = DateTime.Now,
                        UpdatedDate = DateTime.Now
                    };

                    _context.Add(purchaseOrder);
                    await _context.SaveChangesAsync();
                    
                    // Add items if any
                    if (viewModel.Items != null && viewModel.Items.Any())
                    {
                        int lineNumber = 10;
                        foreach (var itemViewModel in viewModel.Items.Where(i => i.ProductID > 0 && i.OrderedQuantity > 0))
                        {
                            var product = await _context.Products.FindAsync(itemViewModel.ProductID);
                            if (product != null)
                            {
                                var item = new PurchaseOrderItem
                                {
                                    PurchaseOrderID = purchaseOrder.PurchaseOrderID,
                                    LineNumber = lineNumber,
                                    ProductID = itemViewModel.ProductID,
                                    ProductName = product.ProductName,
                                    SupplierProductCode = itemViewModel.SupplierProductCode,
                                    Description = itemViewModel.Description ?? product.Description,
                                    OrderedQuantity = itemViewModel.OrderedQuantity,
                                    ReceivedQuantity = 0,
                                    UOMID = itemViewModel.UOMID,
                                    UnitPrice = itemViewModel.UnitPrice,
                                    DiscountPercent = itemViewModel.DiscountPercent,
                                    DiscountAmount = itemViewModel.DiscountAmount,
                                    TaxRate = itemViewModel.TaxRate,
                                    ItemStatus = "Open",
                                    RequiredDate = itemViewModel.RequiredDate,
                                    Notes = itemViewModel.Notes
                                };

                                // Calculate line totals
                                var subtotal = item.OrderedQuantity * item.UnitPrice;
                                item.DiscountAmount = item.DiscountPercent > 0 
                                    ? subtotal * item.DiscountPercent / 100 
                                    : item.DiscountAmount;
                                item.TaxAmount = (subtotal - item.DiscountAmount) * item.TaxRate / 100;
                                item.LineTotal = subtotal - item.DiscountAmount + item.TaxAmount;

                                _context.PurchaseOrderItems.Add(item);
                                lineNumber += 10;
                            }
                        }
                        
                        await _context.SaveChangesAsync();
                        
                        // Update purchase order totals
                        await UpdatePurchaseOrderTotalsAsync(purchaseOrder.PurchaseOrderID);
                    }
                    
                    await transaction.CommitAsync();
                    TempData["Success"] = "Purchase order created successfully.";
                    return RedirectToAction(nameof(Details), new { id = purchaseOrder.PurchaseOrderID });
                }
                catch (Exception)
                {
                    await transaction.RollbackAsync();
                    ModelState.AddModelError("", "An error occurred while creating the purchase order.");
                }
            }

            await PopulateDropdownsAsync(viewModel);
            
            // Also populate ViewBag for JavaScript access
            ViewBag.Products = new SelectList(
                await _context.Products
                    .Where(p => p.IsActive && !p.IsDeleted)
                    .OrderBy(p => p.ProductName)
                    .ToListAsync(),
                "ProductID", "ProductName");

            ViewBag.UnitOfMeasures = new SelectList(
                await _context.UnitOfMeasures
                    .Where(u => u.IsActive)
                    .OrderBy(u => u.UOMName)
                    .ToListAsync(),
                "UOMID", "UOMName");
                
            return View(viewModel);
        }

        // GET: PurchaseOrder/Edit/5
        public async Task<IActionResult> Edit(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var purchaseOrder = await _context.PurchaseOrders
                .FirstOrDefaultAsync(p => p.PurchaseOrderID == id && !p.IsDeleted);

            if (purchaseOrder == null)
            {
                return NotFound();
            }

            var viewModel = new PurchaseOrderEditViewModel
            {
                PurchaseOrderID = purchaseOrder.PurchaseOrderID,
                PurchaseOrderNumber = purchaseOrder.PurchaseOrderNumber,
                SupplierID = purchaseOrder.SupplierID,
                SupplierReference = purchaseOrder.SupplierReference,
                BuyerID = purchaseOrder.BuyerID,
                OrderStatus = purchaseOrder.OrderStatus,
                OrderType = purchaseOrder.OrderType,
                RequiredDate = purchaseOrder.RequiredDate,
                PromisedDate = purchaseOrder.PromisedDate,
                ExpectedDate = purchaseOrder.ExpectedDate,
                DeliveryWarehouseID = purchaseOrder.DeliveryWarehouseID,
                DeliveryInstructions = purchaseOrder.DeliveryInstructions,
                ShippingMethod = purchaseOrder.ShippingMethod,
                FreightTerms = purchaseOrder.FreightTerms,
                PaymentTerms = purchaseOrder.PaymentTerms,
                PaymentMethod = purchaseOrder.PaymentMethod,
                Currency = purchaseOrder.Currency,
                ExchangeRate = purchaseOrder.ExchangeRate,
                ApprovalStatus = purchaseOrder.ApprovalStatus,
                ApprovalNotes = purchaseOrder.ApprovalNotes,
                Notes = purchaseOrder.Notes,
                InternalNotes = purchaseOrder.InternalNotes,
                TermsAndConditions = purchaseOrder.TermsAndConditions
            };

            await PopulateDropdownsAsync(viewModel);
            return View(viewModel);
        }

        // POST: PurchaseOrder/Edit/5
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Edit(int id, PurchaseOrderEditViewModel viewModel)
        {
            if (id != viewModel.PurchaseOrderID)
            {
                return NotFound();
            }

            if (ModelState.IsValid)
            {
                try
                {
                    var purchaseOrder = await _context.PurchaseOrders
                        .FirstOrDefaultAsync(p => p.PurchaseOrderID == id && !p.IsDeleted);

                    if (purchaseOrder == null)
                    {
                        return NotFound();
                    }

                    purchaseOrder.SupplierID = viewModel.SupplierID;
                    purchaseOrder.SupplierReference = viewModel.SupplierReference;
                    purchaseOrder.BuyerID = viewModel.BuyerID;
                    purchaseOrder.OrderStatus = viewModel.OrderStatus;
                    purchaseOrder.OrderType = viewModel.OrderType;
                    purchaseOrder.RequiredDate = viewModel.RequiredDate;
                    purchaseOrder.PromisedDate = viewModel.PromisedDate;
                    purchaseOrder.ExpectedDate = viewModel.ExpectedDate;
                    purchaseOrder.DeliveryWarehouseID = viewModel.DeliveryWarehouseID;
                    purchaseOrder.DeliveryInstructions = viewModel.DeliveryInstructions;
                    purchaseOrder.ShippingMethod = viewModel.ShippingMethod;
                    purchaseOrder.FreightTerms = viewModel.FreightTerms;
                    purchaseOrder.PaymentTerms = viewModel.PaymentTerms;
                    purchaseOrder.PaymentMethod = viewModel.PaymentMethod;
                    purchaseOrder.Currency = viewModel.Currency;
                    purchaseOrder.ExchangeRate = viewModel.ExchangeRate;
                    purchaseOrder.ApprovalStatus = viewModel.ApprovalStatus;
                    purchaseOrder.ApprovalNotes = viewModel.ApprovalNotes;
                    purchaseOrder.Notes = viewModel.Notes;
                    purchaseOrder.InternalNotes = viewModel.InternalNotes;
                    purchaseOrder.TermsAndConditions = viewModel.TermsAndConditions;
                    purchaseOrder.UpdatedDate = DateTime.Now;

                    _context.Update(purchaseOrder);
                    await _context.SaveChangesAsync();
                    
                    TempData["Success"] = "Purchase order updated successfully.";
                }
                catch (DbUpdateConcurrencyException)
                {
                    if (!await PurchaseOrderExistsAsync(viewModel.PurchaseOrderID))
                    {
                        return NotFound();
                    }
                    else
                    {
                        throw;
                    }
                }
                return RedirectToAction(nameof(Index));
            }

            await PopulateDropdownsAsync(viewModel);
            return View(viewModel);
        }

        // GET: PurchaseOrder/Delete/5
        public async Task<IActionResult> Delete(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var purchaseOrder = await _context.PurchaseOrders
                .Include(p => p.Supplier)
                .Include(p => p.DeliveryWarehouse)
                .FirstOrDefaultAsync(m => m.PurchaseOrderID == id && !m.IsDeleted);

            if (purchaseOrder == null)
            {
                return NotFound();
            }

            return View(purchaseOrder);
        }

        // POST: PurchaseOrder/Delete/5
        [HttpPost, ActionName("Delete")]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> DeleteConfirmed(int id)
        {
            var purchaseOrder = await _context.PurchaseOrders
                .FirstOrDefaultAsync(p => p.PurchaseOrderID == id && !p.IsDeleted);

            if (purchaseOrder != null)
            {
                purchaseOrder.IsDeleted = true;
                purchaseOrder.DeletedDate = DateTime.Now;
                _context.Update(purchaseOrder);
                await _context.SaveChangesAsync();
                
                TempData["Success"] = "Purchase order deleted successfully.";
            }

            return RedirectToAction(nameof(Index));
        }

        // GET: PurchaseOrder/Approve/5
        [HttpPost]
        public async Task<IActionResult> Approve(int id, string approvalNotes)
        {
            var purchaseOrder = await _context.PurchaseOrders
                .FirstOrDefaultAsync(p => p.PurchaseOrderID == id && !p.IsDeleted);

            if (purchaseOrder == null)
            {
                return NotFound();
            }

            purchaseOrder.ApprovalStatus = "Approved";
            purchaseOrder.ApprovedByUserID = int.Parse(User.FindFirst(System.Security.Claims.ClaimTypes.NameIdentifier)?.Value ?? "0");
            purchaseOrder.ApprovedDate = DateTime.Now;
            purchaseOrder.ApprovalNotes = approvalNotes;
            purchaseOrder.OrderStatus = "Sent";
            purchaseOrder.UpdatedDate = DateTime.Now;

            _context.Update(purchaseOrder);
            await _context.SaveChangesAsync();

            TempData["Success"] = "Purchase order approved successfully.";
            return RedirectToAction(nameof(Details), new { id });
        }

        // GET: PurchaseOrder/Reject/5
        [HttpPost]
        public async Task<IActionResult> Reject(int id, string approvalNotes)
        {
            var purchaseOrder = await _context.PurchaseOrders
                .FirstOrDefaultAsync(p => p.PurchaseOrderID == id && !p.IsDeleted);

            if (purchaseOrder == null)
            {
                return NotFound();
            }

            purchaseOrder.ApprovalStatus = "Rejected";
            purchaseOrder.ApprovalNotes = approvalNotes;
            purchaseOrder.UpdatedDate = DateTime.Now;

            _context.Update(purchaseOrder);
            await _context.SaveChangesAsync();

            TempData["Error"] = "Purchase order rejected.";
            return RedirectToAction(nameof(Details), new { id });
        }

        private async Task<bool> PurchaseOrderExistsAsync(int id)
        {
            return await _context.PurchaseOrders.AnyAsync(e => e.PurchaseOrderID == id && !e.IsDeleted);
        }

        private async Task<string> GeneratePurchaseOrderNumberAsync()
        {
            var today = DateTime.Today;
            var prefix = $"PO{today:yyyyMMdd}";
            
            var lastOrder = await _context.PurchaseOrders
                .Where(p => p.PurchaseOrderNumber.StartsWith(prefix))
                .OrderByDescending(p => p.PurchaseOrderNumber)
                .FirstOrDefaultAsync();

            int nextNumber = 1;
            if (lastOrder != null && lastOrder.PurchaseOrderNumber.Length > prefix.Length)
            {
                var lastNumberStr = lastOrder.PurchaseOrderNumber.Substring(prefix.Length);
                if (int.TryParse(lastNumberStr, out int lastNumber))
                {
                    nextNumber = lastNumber + 1;
                }
            }

            return $"{prefix}{nextNumber:D3}";
        }

        private async Task PopulateDropdownsAsync(PurchaseOrderCreateViewModel viewModel)
        {
            viewModel.Suppliers = new SelectList(
                await _context.Suppliers
                    .Where(s => s.IsActive && !s.IsDeleted)
                    .OrderBy(s => s.SupplierName)
                    .ToListAsync(),
                "SupplierID", "SupplierName");

            viewModel.Buyers = new SelectList(
                await _context.Users
                    .Where(u => u.IsActive)
                    .OrderBy(u => u.FirstName)
                    .ThenBy(u => u.LastName)
                    .ToListAsync(),
                "UserID", "FullName");

            viewModel.Warehouses = new SelectList(
                await _context.Warehouses
                    .Where(w => w.IsActive)
                    .OrderBy(w => w.WarehouseName)
                    .ToListAsync(),
                "WarehouseID", "WarehouseName");
                
            viewModel.Products = new SelectList(
                await _context.Products
                    .Where(p => p.IsActive && !p.IsDeleted)
                    .OrderBy(p => p.ProductName)
                    .ToListAsync(),
                "ProductID", "ProductName");

            viewModel.UnitOfMeasures = new SelectList(
                await _context.UnitOfMeasures
                    .Where(u => u.IsActive)
                    .OrderBy(u => u.UOMName)
                    .ToListAsync(),
                "UOMID", "UOMName");
        }

        private async Task PopulateDropdownsAsync(PurchaseOrderEditViewModel viewModel)
        {
            viewModel.Suppliers = new SelectList(
                await _context.Suppliers
                    .Where(s => s.IsActive && !s.IsDeleted)
                    .OrderBy(s => s.SupplierName)
                    .ToListAsync(),
                "SupplierID", "SupplierName");

            viewModel.Buyers = new SelectList(
                await _context.Users
                    .Where(u => u.IsActive)
                    .OrderBy(u => u.FirstName)
                    .ThenBy(u => u.LastName)
                    .ToListAsync(),
                "UserID", "FullName");

            viewModel.Warehouses = new SelectList(
                await _context.Warehouses
                    .Where(w => w.IsActive)
                    .OrderBy(w => w.WarehouseName)
                    .ToListAsync(),
                "WarehouseID", "WarehouseName");
        }

        // GET: PurchaseOrder/AddItem/5
        public async Task<IActionResult> AddItem(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var purchaseOrder = await _context.PurchaseOrders
                .Include(p => p.Supplier)
                .FirstOrDefaultAsync(p => p.PurchaseOrderID == id && !p.IsDeleted);

            if (purchaseOrder == null)
            {
                return NotFound();
            }

            if (purchaseOrder.OrderStatus != "Draft" && purchaseOrder.OrderStatus != "Pending")
            {
                TempData["Error"] = "Items can only be added to Draft or Pending orders.";
                return RedirectToAction(nameof(Details), new { id });
            }

            var viewModel = new PurchaseOrderItemViewModel
            {
                PurchaseOrderID = purchaseOrder.PurchaseOrderID,
                LineNumber = await GetNextLineNumberAsync(purchaseOrder.PurchaseOrderID),
                OrderedQuantity = 1,
                UnitPrice = 0,
                DiscountPercent = 0,
                TaxRate = 0
            };

            ViewBag.PurchaseOrder = purchaseOrder;
            await PopulateItemDropdownsAsync();

            return View(viewModel);
        }

        // POST: PurchaseOrder/AddItem
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> AddItem(PurchaseOrderItemViewModel viewModel)
        {
            if (ModelState.IsValid)
            {
                var purchaseOrder = await _context.PurchaseOrders
                    .FirstOrDefaultAsync(p => p.PurchaseOrderID == viewModel.PurchaseOrderID && !p.IsDeleted);

                if (purchaseOrder == null)
                {
                    return NotFound();
                }

                var product = await _context.Products
                    .FirstOrDefaultAsync(p => p.ProductID == viewModel.ProductID);

                if (product == null)
                {
                    ModelState.AddModelError("ProductID", "Selected product not found.");
                    await PopulateItemDropdownsAsync();
                    ViewBag.PurchaseOrder = purchaseOrder;
                    return View(viewModel);
                }

                var item = new PurchaseOrderItem
                {
                    PurchaseOrderID = viewModel.PurchaseOrderID,
                    LineNumber = viewModel.LineNumber,
                    ProductID = viewModel.ProductID,
                    ProductName = product.ProductName,
                    SupplierProductCode = viewModel.SupplierProductCode,
                    Description = viewModel.Description ?? product.Description,
                    OrderedQuantity = viewModel.OrderedQuantity,
                    ReceivedQuantity = 0,
                    UOMID = viewModel.UOMID,
                    UnitPrice = viewModel.UnitPrice,
                    DiscountPercent = viewModel.DiscountPercent,
                    DiscountAmount = viewModel.DiscountAmount,
                    TaxRate = viewModel.TaxRate,
                    ItemStatus = "Open",
                    RequiredDate = viewModel.RequiredDate,
                    Notes = viewModel.Notes
                };

                // Calculate line totals
                var subtotal = item.OrderedQuantity * item.UnitPrice;
                item.DiscountAmount = item.DiscountPercent > 0 
                    ? subtotal * item.DiscountPercent / 100 
                    : item.DiscountAmount;
                item.TaxAmount = (subtotal - item.DiscountAmount) * item.TaxRate / 100;
                item.LineTotal = subtotal - item.DiscountAmount + item.TaxAmount;

                _context.PurchaseOrderItems.Add(item);
                await _context.SaveChangesAsync();

                // Update purchase order totals
                await UpdatePurchaseOrderTotalsAsync(purchaseOrder.PurchaseOrderID);

                TempData["Success"] = "Item added successfully.";
                return RedirectToAction(nameof(Details), new { id = purchaseOrder.PurchaseOrderID });
            }

            var po = await _context.PurchaseOrders
                .Include(p => p.Supplier)
                .FirstOrDefaultAsync(p => p.PurchaseOrderID == viewModel.PurchaseOrderID);
            
            ViewBag.PurchaseOrder = po;
            await PopulateItemDropdownsAsync();
            return View(viewModel);
        }

        // GET: PurchaseOrder/RemoveItem/5
        public async Task<IActionResult> RemoveItem(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var item = await _context.PurchaseOrderItems
                .Include(i => i.PurchaseOrder)
                .FirstOrDefaultAsync(i => i.PurchaseOrderItemID == id);

            if (item == null)
            {
                return NotFound();
            }

            if (item.PurchaseOrder.OrderStatus != "Draft" && item.PurchaseOrder.OrderStatus != "Pending")
            {
                TempData["Error"] = "Items can only be removed from Draft or Pending orders.";
                return RedirectToAction(nameof(Details), new { id = item.PurchaseOrderID });
            }

            if (item.ReceivedQuantity > 0)
            {
                TempData["Error"] = "Cannot remove items that have been partially or fully received.";
                return RedirectToAction(nameof(Details), new { id = item.PurchaseOrderID });
            }

            _context.PurchaseOrderItems.Remove(item);
            await _context.SaveChangesAsync();

            // Update purchase order totals
            await UpdatePurchaseOrderTotalsAsync(item.PurchaseOrderID);

            // Resequence line numbers
            await ResequenceLineNumbersAsync(item.PurchaseOrderID);

            TempData["Success"] = "Item removed successfully.";
            return RedirectToAction(nameof(Details), new { id = item.PurchaseOrderID });
        }

        private async Task<int> GetNextLineNumberAsync(int purchaseOrderId)
        {
            var maxLineNumber = await _context.PurchaseOrderItems
                .Where(i => i.PurchaseOrderID == purchaseOrderId)
                .MaxAsync(i => (int?)i.LineNumber) ?? 0;

            return maxLineNumber + 10;
        }

        private async Task UpdatePurchaseOrderTotalsAsync(int purchaseOrderId)
        {
            var purchaseOrder = await _context.PurchaseOrders
                .Include(p => p.PurchaseOrderItems)
                .FirstOrDefaultAsync(p => p.PurchaseOrderID == purchaseOrderId);

            if (purchaseOrder != null)
            {
                // Calculate subtotal from line totals
                decimal subtotal = 0;
                foreach (var item in purchaseOrder.PurchaseOrderItems)
                {
                    var itemSubtotal = item.OrderedQuantity * item.UnitPrice;
                    subtotal += itemSubtotal;
                }
                
                purchaseOrder.SubTotal = subtotal;
                purchaseOrder.DiscountAmount = purchaseOrder.PurchaseOrderItems.Sum(i => i.DiscountAmount);
                purchaseOrder.TaxAmount = purchaseOrder.PurchaseOrderItems.Sum(i => i.TaxAmount);
                purchaseOrder.TotalAmount = purchaseOrder.SubTotal - purchaseOrder.DiscountAmount + purchaseOrder.TaxAmount + purchaseOrder.FreightAmount;
                purchaseOrder.UpdatedDate = DateTime.Now;

                _context.Update(purchaseOrder);
                await _context.SaveChangesAsync();
            }
        }

        private async Task ResequenceLineNumbersAsync(int purchaseOrderId)
        {
            var items = await _context.PurchaseOrderItems
                .Where(i => i.PurchaseOrderID == purchaseOrderId)
                .OrderBy(i => i.LineNumber)
                .ToListAsync();

            int lineNumber = 10;
            foreach (var item in items)
            {
                item.LineNumber = lineNumber;
                lineNumber += 10;
            }

            await _context.SaveChangesAsync();
        }

        private async Task PopulateItemDropdownsAsync()
        {
            ViewBag.Products = new SelectList(
                await _context.Products
                    .Where(p => p.IsActive && !p.IsDeleted)
                    .OrderBy(p => p.ProductName)
                    .ToListAsync(),
                "ProductID", "ProductName");

            ViewBag.UnitOfMeasures = new SelectList(
                await _context.UnitOfMeasures
                    .Where(u => u.IsActive)
                    .OrderBy(u => u.UOMName)
                    .ToListAsync(),
                "UOMID", "UOMName");
        }
    }
}