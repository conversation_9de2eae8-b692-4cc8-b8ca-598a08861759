using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Rendering;
using Microsoft.EntityFrameworkCore;
using nCarry.Web.Data;
using nCarry.Web.Models.Sales;
using nCarry.Web.Models.ViewModels;
using nCarry.Web.Services;

namespace nCarry.Web.Controllers
{
    public class SalesOrderController : Controller
    {
        private readonly ApplicationDbContext _context;
        private readonly IPricingService _pricingService;

        public SalesOrderController(ApplicationDbContext context, IPricingService pricingService)
        {
            _context = context;
            _pricingService = pricingService;
        }

        // GET: SalesOrder
        public async Task<IActionResult> Index()
        {
            var salesOrders = await _context.SalesOrders
                .Include(s => s.Customer)
                .Where(s => !s.IsDeleted)
                .OrderByDescending(s => s.CreatedDate)
                .ToListAsync();

            return View(salesOrders);
        }

        // GET: SalesOrder/Details/5
        public async Task<IActionResult> Details(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            // Quote include disabled until manual schema fix
            var salesOrder = await _context.SalesOrders
                .Include(s => s.Customer)
                .Include(s => s.BillingAddress)
                .Include(s => s.ShippingAddress)
                .Include(s => s.SalesOrderItems)
                    .ThenInclude(si => si.Product)
                .Include(s => s.SalesOrderItems)
                    .ThenInclude(si => si.UnitOfMeasure)
                .Include(s => s.SalesOrderItems)
                    .ThenInclude(si => si.ProductVariant)
                .FirstOrDefaultAsync(m => m.OrderID == id && !m.IsDeleted);

            if (salesOrder == null)
            {
                return NotFound();
            }

            return View(salesOrder);
        }

        // GET: SalesOrder/Create
        public async Task<IActionResult> Create()
        {
            var viewModel = new SalesOrderCreateViewModel();
            await PopulateDropdownsAsync(viewModel);
            return View(viewModel);
        }

        // POST: SalesOrder/Create
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Create(SalesOrderCreateViewModel viewModel)
        {
            if (ModelState.IsValid)
            {
                // Generate order number
                var orderNumber = await GenerateOrderNumberAsync();

                var salesOrder = new SalesOrder
                {
                    OrderNumber = orderNumber,
                    OrderDate = DateTime.Now,
                    CustomerID = viewModel.CustomerID,
                    CustomerPO = viewModel.CustomerPO,
                    QuoteID = viewModel.QuoteID,
                    SalesRepID = viewModel.SalesRepID,
                    OrderType = viewModel.OrderType,
                    RequestedDate = viewModel.RequestedDate,
                    PromisedDate = viewModel.PromisedDate,
                    BillingAddressID = viewModel.BillingAddressID,
                    ShippingAddressID = viewModel.ShippingAddressID,
                    DeliveryInstructions = viewModel.DeliveryInstructions,
                    ShippingMethod = viewModel.ShippingMethod,
                    FreightTerms = viewModel.FreightTerms,
                    PaymentTerms = viewModel.PaymentTerms,
                    Currency = viewModel.Currency,
                    ExchangeRate = viewModel.ExchangeRate,
                    WarehouseID = viewModel.WarehouseID,
                    PickingPriority = viewModel.PickingPriority,
                    Notes = viewModel.Notes,
                    InternalNotes = viewModel.InternalNotes,
                    CreatedDate = DateTime.Now,
                    UpdatedDate = DateTime.Now
                };

                _context.Add(salesOrder);
                await _context.SaveChangesAsync();
                return RedirectToAction(nameof(Index));
            }

            await PopulateDropdownsAsync(viewModel);
            return View(viewModel);
        }

        // GET: SalesOrder/Edit/5
        public async Task<IActionResult> Edit(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var salesOrder = await _context.SalesOrders
                .Include(s => s.SalesOrderItems)
                    .ThenInclude(i => i.Product)
                .Include(s => s.SalesOrderItems)
                    .ThenInclude(i => i.UnitOfMeasure)
                .FirstOrDefaultAsync(s => s.OrderID == id && !s.IsDeleted);

            if (salesOrder == null)
            {
                return NotFound();
            }

            var viewModel = new SalesOrderEditViewModel
            {
                OrderID = salesOrder.OrderID,
                OrderNumber = salesOrder.OrderNumber,
                CustomerID = salesOrder.CustomerID,
                CustomerPO = salesOrder.CustomerPO,
                QuoteID = salesOrder.QuoteID,
                SalesRepID = salesOrder.SalesRepID,
                OrderStatus = salesOrder.OrderStatus,
                OrderType = salesOrder.OrderType,
                RequestedDate = salesOrder.RequestedDate,
                PromisedDate = salesOrder.PromisedDate,
                BillingAddressID = salesOrder.BillingAddressID,
                ShippingAddressID = salesOrder.ShippingAddressID,
                DeliveryInstructions = salesOrder.DeliveryInstructions,
                ShippingMethod = salesOrder.ShippingMethod,
                TrackingNumber = salesOrder.TrackingNumber,
                FreightTerms = salesOrder.FreightTerms,
                PaymentTerms = salesOrder.PaymentTerms,
                PaymentDueDate = salesOrder.PaymentDueDate,
                Currency = salesOrder.Currency,
                ExchangeRate = salesOrder.ExchangeRate,
                WarehouseID = salesOrder.WarehouseID,
                PickingPriority = salesOrder.PickingPriority ?? 100,
                Notes = salesOrder.Notes,
                InternalNotes = salesOrder.InternalNotes
            };

            // Load existing order items
            viewModel.Items = salesOrder.SalesOrderItems.Select(i => new SalesOrderItemViewModel
            {
                OrderItemID = i.OrderItemID,
                OrderID = i.OrderID,
                LineNumber = i.LineNumber,
                ProductID = i.ProductID,
                VariantID = i.VariantID,
                Description = i.Description,
                OrderedQuantity = i.OrderedQuantity,
                UOMID = i.UOMID,
                UnitPrice = i.UnitPrice,
                DiscountPercent = i.DiscountPercent,
                DiscountAmount = i.DiscountAmount,
                TaxRate = i.TaxRate,
                WarehouseID = i.WarehouseID,
                Notes = i.Notes,
                TaxAmount = i.TaxAmount,
                LineTotal = i.LineTotal,
                ProductCode = i.Product?.ProductCode,
                ProductName = i.Product?.ProductName,
                UOMName = i.UnitOfMeasure?.UOMName
            }).OrderBy(i => i.LineNumber).ToList();

            await PopulateDropdownsAsync(viewModel, salesOrder.CustomerID);
            return View(viewModel);
        }

        // POST: SalesOrder/Edit/5
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Edit(int id, SalesOrderEditViewModel viewModel)
        {
            if (id != viewModel.OrderID)
            {
                return NotFound();
            }

            if (ModelState.IsValid)
            {
                try
                {
                    var salesOrder = await _context.SalesOrders
                        .Include(s => s.SalesOrderItems)
                        .FirstOrDefaultAsync(s => s.OrderID == id && !s.IsDeleted);

                    if (salesOrder == null)
                    {
                        return NotFound();
                    }

                    // Update order header fields
                    salesOrder.CustomerID = viewModel.CustomerID;
                    salesOrder.CustomerPO = viewModel.CustomerPO;
                    salesOrder.QuoteID = viewModel.QuoteID;
                    salesOrder.SalesRepID = viewModel.SalesRepID;
                    salesOrder.OrderStatus = viewModel.OrderStatus;
                    salesOrder.OrderType = viewModel.OrderType;
                    salesOrder.RequestedDate = viewModel.RequestedDate;
                    salesOrder.PromisedDate = viewModel.PromisedDate;
                    salesOrder.BillingAddressID = viewModel.BillingAddressID;
                    salesOrder.ShippingAddressID = viewModel.ShippingAddressID;
                    salesOrder.DeliveryInstructions = viewModel.DeliveryInstructions;
                    salesOrder.ShippingMethod = viewModel.ShippingMethod;
                    salesOrder.TrackingNumber = viewModel.TrackingNumber;
                    salesOrder.FreightTerms = viewModel.FreightTerms;
                    salesOrder.PaymentTerms = viewModel.PaymentTerms;
                    salesOrder.PaymentDueDate = viewModel.PaymentDueDate;
                    salesOrder.Currency = viewModel.Currency;
                    salesOrder.ExchangeRate = viewModel.ExchangeRate;
                    salesOrder.WarehouseID = viewModel.WarehouseID;
                    salesOrder.PickingPriority = viewModel.PickingPriority;
                    salesOrder.Notes = viewModel.Notes;
                    salesOrder.InternalNotes = viewModel.InternalNotes;
                    salesOrder.UpdatedDate = DateTime.Now;

                    // Update line items
                    if (viewModel.Items != null && viewModel.Items.Any())
                    {
                        // Remove existing items that are not in the updated list
                        var itemIdsToKeep = viewModel.Items
                            .Where(i => i.OrderItemID > 0)
                            .Select(i => i.OrderItemID)
                            .ToList();
                        
                        var itemsToRemove = salesOrder.SalesOrderItems
                            .Where(i => !itemIdsToKeep.Contains(i.OrderItemID))
                            .ToList();
                        
                        foreach (var item in itemsToRemove)
                        {
                            _context.SalesOrderItems.Remove(item);
                        }

                        // Update or add items
                        foreach (var itemViewModel in viewModel.Items)
                        {
                            // Skip items with no product selected
                            if (itemViewModel.ProductID <= 0) continue;

                            SalesOrderItem item;
                            
                            if (itemViewModel.OrderItemID > 0)
                            {
                                // Update existing item
                                item = salesOrder.SalesOrderItems
                                    .FirstOrDefault(i => i.OrderItemID == itemViewModel.OrderItemID);
                                
                                if (item == null) continue;
                            }
                            else
                            {
                                // Add new item
                                item = new SalesOrderItem
                                {
                                    OrderID = salesOrder.OrderID
                                };
                                salesOrder.SalesOrderItems.Add(item);
                            }

                            // Get product info for additional fields
                            var product = await _context.Products
                                .FirstOrDefaultAsync(p => p.ProductID == itemViewModel.ProductID);

                            // Update item properties
                            item.ProductID = itemViewModel.ProductID;
                            item.VariantID = itemViewModel.VariantID;
                            item.Description = itemViewModel.Description ?? product?.ProductName ?? "";
                            item.OrderedQuantity = itemViewModel.OrderedQuantity;
                            item.UOMID = itemViewModel.UOMID;
                            item.UnitPrice = itemViewModel.UnitPrice;
                            item.DiscountPercent = itemViewModel.DiscountPercent;
                            item.DiscountAmount = itemViewModel.DiscountAmount;
                            item.TaxRate = itemViewModel.TaxRate;
                            item.WarehouseID = itemViewModel.WarehouseID;
                            item.Notes = itemViewModel.Notes;
                            item.LineNumber = itemViewModel.LineNumber;

                            // Set product code and name from the actual product
                            if (product != null)
                            {
                                item.ProductCode = product.ProductCode;
                                item.ProductName = product.ProductName;
                            }

                            // Calculate line totals
                            var lineSubtotal = item.OrderedQuantity * item.UnitPrice;
                            var discountAmount = lineSubtotal * (item.DiscountPercent / 100) + item.DiscountAmount;
                            var netAmount = lineSubtotal - discountAmount;
                            item.TaxAmount = netAmount * (item.TaxRate / 100);
                            item.LineTotal = netAmount + item.TaxAmount;
                        }
                    }

                    // Update order totals
                    salesOrder.SubTotal = salesOrder.SalesOrderItems.Sum(i => i.OrderedQuantity * i.UnitPrice);
                    var totalDiscounts = salesOrder.SalesOrderItems.Sum(i => (i.OrderedQuantity * i.UnitPrice * i.DiscountPercent / 100) + i.DiscountAmount);
                    salesOrder.DiscountAmount = totalDiscounts;
                    salesOrder.TaxAmount = salesOrder.SalesOrderItems.Sum(i => i.TaxAmount);
                    salesOrder.TotalAmount = salesOrder.SubTotal - salesOrder.DiscountAmount + salesOrder.TaxAmount + salesOrder.ShippingAmount;
                    salesOrder.BalanceAmount = salesOrder.TotalAmount - salesOrder.PaidAmount;

                    _context.Update(salesOrder);
                    await _context.SaveChangesAsync();
                }
                catch (DbUpdateConcurrencyException)
                {
                    if (!await SalesOrderExistsAsync(viewModel.OrderID))
                    {
                        return NotFound();
                    }
                    else
                    {
                        throw;
                    }
                }
                return RedirectToAction(nameof(Index));
            }

            await PopulateDropdownsAsync(viewModel, viewModel.CustomerID);
            return View(viewModel);
        }

        // GET: SalesOrder/Delete/5
        public async Task<IActionResult> Delete(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            // Quote include disabled until manual schema fix
            var salesOrder = await _context.SalesOrders
                .Include(s => s.Customer)
                .FirstOrDefaultAsync(m => m.OrderID == id && !m.IsDeleted);

            if (salesOrder == null)
            {
                return NotFound();
            }

            return View(salesOrder);
        }

        // POST: SalesOrder/Delete/5
        [HttpPost, ActionName("Delete")]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> DeleteConfirmed(int id)
        {
            var salesOrder = await _context.SalesOrders
                .FirstOrDefaultAsync(s => s.OrderID == id && !s.IsDeleted);

            if (salesOrder != null)
            {
                salesOrder.IsDeleted = true;
                salesOrder.DeletedDate = DateTime.Now;
                _context.Update(salesOrder);
                await _context.SaveChangesAsync();
            }

            return RedirectToAction(nameof(Index));
        }

        private async Task<bool> SalesOrderExistsAsync(int id)
        {
            return await _context.SalesOrders.AnyAsync(e => e.OrderID == id && !e.IsDeleted);
        }

        private async Task<string> GenerateOrderNumberAsync()
        {
            var today = DateTime.Today;
            var prefix = $"SO{today:yyyyMMdd}";
            
            SalesOrder? lastOrder = null;
            try
            {
                // Try to get last order with Quote include
                lastOrder = await _context.SalesOrders
                    .Where(s => s.OrderNumber.StartsWith(prefix))
                    .OrderByDescending(s => s.OrderNumber)
                    .FirstOrDefaultAsync();
            }
            catch (Exception ex) when (ex.Message.Contains("Invalid column name"))
            {
                // Quote schema issues - use basic query
                Console.WriteLine($"Schema mismatch in GenerateOrderNumberAsync: {ex.Message}");
                lastOrder = await _context.SalesOrders
                    .Where(s => s.OrderNumber.StartsWith(prefix))
                    .OrderByDescending(s => s.OrderNumber)
                    .FirstOrDefaultAsync();
            }

            int nextNumber = 1;
            if (lastOrder != null && lastOrder.OrderNumber.Length > prefix.Length)
            {
                var lastNumberStr = lastOrder.OrderNumber.Substring(prefix.Length);
                if (int.TryParse(lastNumberStr, out int lastNumber))
                {
                    nextNumber = lastNumber + 1;
                }
            }

            return $"{prefix}{nextNumber:D3}";
        }

        private async Task PopulateDropdownsAsync(SalesOrderCreateViewModel viewModel)
        {
            viewModel.Customers = new SelectList(
                await _context.Customers
                    .Where(c => c.IsActive && !c.IsDeleted)
                    .OrderBy(c => c.CustomerName)
                    .ToListAsync(),
                "CustomerID", "CustomerName");

            // Quote loading disabled until manual schema fix
            Console.WriteLine("Quote functionality temporarily disabled - manual SQL schema fix required.");
            viewModel.Quotes = new SelectList(new List<object>(), "QuoteID", "QuoteNumber");

            // Safe User loading with error handling
            try
            {
                viewModel.SalesReps = new SelectList(
                    await _context.Users
                        .Where(u => u.IsActive)
                        .OrderBy(u => u.FirstName)
                        .ThenBy(u => u.LastName)
                        .ToListAsync(),
                    "UserID", "FullName");
            }
            catch (Exception ex)
            {
                // Log error and provide empty list
                Console.WriteLine($"User loading error: {ex.Message}");
                viewModel.SalesReps = new SelectList(new List<object>(), "UserID", "FullName");
            }

            viewModel.Warehouses = new SelectList(
                await _context.Warehouses
                    .Where(w => w.IsActive)
                    .OrderBy(w => w.WarehouseName)
                    .ToListAsync(),
                "WarehouseID", "WarehouseName");
        }

        private async Task PopulateDropdownsAsync(SalesOrderEditViewModel viewModel, int? customerID = null)
        {
            viewModel.Customers = new SelectList(
                await _context.Customers
                    .Where(c => c.IsActive && !c.IsDeleted)
                    .OrderBy(c => c.CustomerName)
                    .ToListAsync(),
                "CustomerID", "CustomerName", customerID);

            // Quote loading disabled until manual schema fix
            viewModel.Quotes = new SelectList(new List<object>(), "QuoteID", "QuoteNumber");

            // Safe User loading with error handling
            try
            {
                viewModel.SalesReps = new SelectList(
                    await _context.Users
                        .Where(u => u.IsActive)
                        .OrderBy(u => u.FirstName)
                        .ThenBy(u => u.LastName)
                        .ToListAsync(),
                    "UserID", "FullName");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"User loading error: {ex.Message}");
                viewModel.SalesReps = new SelectList(new List<object>(), "UserID", "FullName");
            }

            if (customerID.HasValue)
            {
                viewModel.CustomerAddresses = new SelectList(
                    await _context.CustomerAddresses
                        .Where(ca => ca.CustomerID == customerID.Value && ca.IsActive)
                        .OrderBy(ca => ca.AddressType)
                        .ToListAsync(),
                    "AddressID", "FullAddress");
            }

            viewModel.Warehouses = new SelectList(
                await _context.Warehouses
                    .Where(w => w.IsActive)
                    .OrderBy(w => w.WarehouseName)
                    .ToListAsync(),
                "WarehouseID", "WarehouseName");

            // Load Products for line items
            viewModel.Products = new SelectList(
                await _context.Products
                    .Where(p => p.IsActive && !p.IsDeleted)
                    .OrderBy(p => p.ProductName)
                    .ToListAsync(),
                "ProductID", "ProductName");

            // Load Unit of Measures for line items
            viewModel.UnitOfMeasures = new SelectList(
                await _context.UnitOfMeasures
                    .Where(u => u.IsActive)
                    .OrderBy(u => u.UOMName)
                    .ToListAsync(),
                "UOMID", "UOMName");
        }

        // GET: SalesOrder/GetProductPriceForCustomer
        [HttpGet]
        public async Task<IActionResult> GetProductPriceForCustomer(int customerId, int productId, decimal quantity = 1)
        {
            try
            {
                var pricingDetail = await _pricingService.GetPricingDetailAsync(customerId, productId, quantity);
                
                return Json(new
                {
                    success = true,
                    basePrice = pricingDetail.BasePrice,
                    customerSpecificPrice = pricingDetail.CustomerSpecificPrice,
                    customerDiscountPercent = pricingDetail.CustomerDiscountPercent,
                    priceListDiscountPercent = pricingDetail.PriceListDiscountPercent,
                    finalUnitPrice = pricingDetail.FinalUnitPrice,
                    priceListName = pricingDetail.PriceListName,
                    isCustomerPriceApplied = pricingDetail.IsCustomerPriceApplied
                });
            }
            catch (Exception ex)
            {
                return Json(new
                {
                    success = false,
                    error = ex.Message
                });
            }
        }

        // POST: SalesOrder/AddItem
        [HttpPost]
        public async Task<IActionResult> AddItem(int orderId, int productId, decimal quantity)
        {
            try
            {
                var order = await _context.SalesOrders
                    .FirstOrDefaultAsync(o => o.OrderID == orderId && !o.IsDeleted);

                if (order == null)
                {
                    return Json(new { success = false, error = "Order not found" });
                }

                var product = await _context.Products
                    .Include(p => p.SalesUOM)
                    .FirstOrDefaultAsync(p => p.ProductID == productId && p.Status == "Active");

                if (product == null)
                {
                    return Json(new { success = false, error = "Product not found" });
                }

                // Get customer-specific price
                var unitPrice = await _pricingService.GetProductPriceAsync(order.CustomerID, productId, quantity);

                var orderItem = new SalesOrderItem
                {
                    OrderID = orderId,
                    ProductID = productId,
                    ProductCode = product.ProductCode,
                    ProductName = product.ProductName,
                    OrderedQuantity = quantity,
                    UOMID = product.SalesUOMID ?? product.InventoryUOMID,
                    UnitPrice = unitPrice,
                    UnitCost = product.StandardCost,
                    TaxRate = 20, // Default VAT rate
                    ItemStatus = "Pending",
                    LineNumber = await GetNextLineNumberAsync(orderId)
                };

                // Calculate line total
                var lineSubtotal = orderItem.OrderedQuantity * orderItem.UnitPrice;
                orderItem.TaxAmount = lineSubtotal * orderItem.TaxRate / 100;
                orderItem.LineTotal = lineSubtotal + orderItem.TaxAmount;

                _context.SalesOrderItems.Add(orderItem);
                await _context.SaveChangesAsync();

                // Update order totals
                await UpdateOrderTotalsAsync(orderId);

                return Json(new { success = true, orderItemId = orderItem.OrderItemID });
            }
            catch (Exception ex)
            {
                return Json(new { success = false, error = ex.Message });
            }
        }

        private async Task<int> GetNextLineNumberAsync(int orderId)
        {
            var maxLineNumber = await _context.SalesOrderItems
                .Where(i => i.OrderID == orderId)
                .MaxAsync(i => (int?)i.LineNumber) ?? 0;

            return maxLineNumber + 1;
        }

        private async Task UpdateOrderTotalsAsync(int orderId)
        {
            var order = await _context.SalesOrders
                .Include(o => o.SalesOrderItems)
                .FirstOrDefaultAsync(o => o.OrderID == orderId);

            if (order != null)
            {
                order.SubTotal = order.SalesOrderItems.Sum(i => i.OrderedQuantity * i.UnitPrice);
                order.TaxAmount = order.SalesOrderItems.Sum(i => i.TaxAmount);
                order.TotalAmount = order.SubTotal + order.TaxAmount + order.ShippingAmount - order.DiscountAmount;
                order.BalanceAmount = order.TotalAmount - order.PaidAmount;
                order.UpdatedDate = DateTime.Now;

                _context.Update(order);
                await _context.SaveChangesAsync();
            }
        }
    }
}