using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Rendering;
using Microsoft.EntityFrameworkCore;
using nCarry.Web.Data;
using nCarry.Web.Models.Customers;
using nCarry.Web.Models.ViewModels;

namespace nCarry.Web.Controllers
{
    [Authorize]
    public class CustomerPriceListController : Controller
    {
        private readonly ApplicationDbContext _context;
        private readonly ILogger<CustomerPriceListController> _logger;

        public CustomerPriceListController(ApplicationDbContext context, ILogger<CustomerPriceListController> logger)
        {
            _context = context;
            _logger = logger;
        }

        // GET: CustomerPriceList
        public async Task<IActionResult> Index()
        {
            var priceLists = await _context.CustomerPriceLists
                .Include(p => p.Customer)
                // .Include(p => p.CustomerGroup) // Commented out - CustomerGroup relationship not working
                .OrderByDescending(p => p.CreatedDate)
                .ToListAsync();

            return View(priceLists);
        }

        // GET: CustomerPriceList/Details/5
        public async Task<IActionResult> Details(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var priceList = await _context.CustomerPriceLists
                .Include(p => p.Customer)
                // .Include(p => p.CustomerGroup) // Commented out - CustomerGroup relationship not working
                .Include(p => p.PriceListItems)
                    .ThenInclude(i => i.Product)
                .FirstOrDefaultAsync(p => p.PriceListID == id);

            if (priceList == null)
            {
                return NotFound();
            }

            return View(priceList);
        }

        // GET: CustomerPriceList/Create
        public IActionResult Create()
        {
            PopulateDropdowns();
            return View();
        }

        // POST: CustomerPriceList/Create
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Create(CustomerPriceList priceList)
        {
            // Log incoming data
            _logger.LogInformation($"Creating price list - Code: {priceList.PriceListCode}, CustomerID: {priceList.CustomerID}, Priority: {priceList.Priority}");
            
            // Check ModelState validation errors
            if (!ModelState.IsValid)
            {
                _logger.LogWarning("ModelState is invalid");
                foreach (var modelError in ModelState.Values.SelectMany(v => v.Errors))
                {
                    _logger.LogWarning($"Validation error: {modelError.ErrorMessage}");
                }
            }
            
            if (ModelState.IsValid)
            {
                try
                {
                    // Check for duplicate code
                    var existingCode = await _context.CustomerPriceLists
                        .AnyAsync(p => p.PriceListCode == priceList.PriceListCode);
                    
                    if (existingCode)
                    {
                        ModelState.AddModelError("PriceListCode", "A price list with this code already exists.");
                        PopulateDropdowns(priceList);
                        return View(priceList);
                    }
                    
                    var userId = HttpContext.Session.GetInt32("UserID") ?? 0;
                    priceList.CreatedByUserID = userId;
                    priceList.UpdatedByUserID = userId;
                    priceList.CreatedDate = DateTime.Now;
                    priceList.UpdatedDate = DateTime.Now;

                    _context.Add(priceList);
                    await _context.SaveChangesAsync();
                    TempData["SuccessMessage"] = "Price list created successfully.";
                    return RedirectToAction(nameof(Details), new { id = priceList.PriceListID });
                }
                catch (DbUpdateException dbEx)
                {
                    _logger.LogError(dbEx, "Database error creating price list");
                    
                    if (dbEx.InnerException != null)
                    {
                        _logger.LogError(dbEx.InnerException, "Inner database exception");
                        
                        if (dbEx.InnerException.Message.Contains("FK_"))
                        {
                            ModelState.AddModelError("", "Foreign key constraint error. Please ensure the selected customer/group exists.");
                        }
                        else if (dbEx.InnerException.Message.Contains("CHK_"))
                        {
                            ModelState.AddModelError("", "Check constraint error. Please ensure discount percent is between 0 and 100.");
                        }
                        else
                        {
                            ModelState.AddModelError("", $"Database error: {dbEx.InnerException.Message}");
                        }
                    }
                    else
                    {
                        ModelState.AddModelError("", $"Database update error: {dbEx.Message}");
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error creating price list");
                    ModelState.AddModelError("", $"Error: {ex.Message}");
                    
                    if (ex.InnerException != null)
                    {
                        _logger.LogError(ex.InnerException, "Inner exception");
                        ModelState.AddModelError("", $"Inner error: {ex.InnerException.Message}");
                    }
                }
            }

            PopulateDropdowns(priceList);
            return View(priceList);
        }

        // GET: CustomerPriceList/Edit/5
        public async Task<IActionResult> Edit(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var priceList = await _context.CustomerPriceLists
                .FirstOrDefaultAsync(p => p.PriceListID == id);

            if (priceList == null)
            {
                return NotFound();
            }

            PopulateDropdowns(priceList);
            return View(priceList);
        }

        // POST: CustomerPriceList/Edit/5
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Edit(int id, CustomerPriceList priceList)
        {
            if (id != priceList.PriceListID)
            {
                return NotFound();
            }

            if (ModelState.IsValid)
            {
                try
                {
                    var existingPriceList = await _context.CustomerPriceLists
                        .FirstOrDefaultAsync(p => p.PriceListID == id);

                    if (existingPriceList == null)
                    {
                        return NotFound();
                    }

                    // Update properties
                    existingPriceList.PriceListCode = priceList.PriceListCode;
                    existingPriceList.PriceListName = priceList.PriceListName;
                    existingPriceList.Description = priceList.Description;
                    existingPriceList.CustomerID = priceList.CustomerID;
                    existingPriceList.Currency = priceList.Currency;
                    existingPriceList.EffectiveFrom = priceList.EffectiveFrom;
                    existingPriceList.EffectiveTo = priceList.EffectiveTo;
                    existingPriceList.DiscountPercent = priceList.DiscountPercent;
                    existingPriceList.MarkupPercent = priceList.MarkupPercent;
                    existingPriceList.Priority = priceList.Priority;
                    existingPriceList.IsActive = priceList.IsActive;
                    existingPriceList.UpdatedDate = DateTime.Now;
                    existingPriceList.UpdatedByUserID = HttpContext.Session.GetInt32("UserID") ?? 0;

                    await _context.SaveChangesAsync();
                    TempData["SuccessMessage"] = "Price list updated successfully.";
                    return RedirectToAction(nameof(Details), new { id = priceList.PriceListID });
                }
                catch (DbUpdateConcurrencyException)
                {
                    if (!PriceListExists(priceList.PriceListID))
                    {
                        return NotFound();
                    }
                    else
                    {
                        throw;
                    }
                }
                catch (Exception)
                {
                    ModelState.AddModelError("", "An error occurred while updating the price list.");
                }
            }

            PopulateDropdowns(priceList);
            return View(priceList);
        }

        // GET: CustomerPriceList/Delete/5
        public async Task<IActionResult> Delete(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var priceList = await _context.CustomerPriceLists
                .Include(p => p.Customer)
                // .Include(p => p.CustomerGroup) // Commented out - CustomerGroup relationship not working
                .FirstOrDefaultAsync(p => p.PriceListID == id);

            if (priceList == null)
            {
                return NotFound();
            }

            return View(priceList);
        }

        // POST: CustomerPriceList/Delete/5
        [HttpPost, ActionName("Delete")]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> DeleteConfirmed(int id)
        {
            var priceList = await _context.CustomerPriceLists
                .FirstOrDefaultAsync(p => p.PriceListID == id);

            if (priceList == null)
            {
                return NotFound();
            }

            // Hard delete
            _context.CustomerPriceLists.Remove(priceList);

            await _context.SaveChangesAsync();
            TempData["SuccessMessage"] = "Price list deleted successfully.";
            return RedirectToAction(nameof(Index));
        }

        // Price List Items Management

        // GET: CustomerPriceList/AddItem/5
        public async Task<IActionResult> AddItem(int? priceListId)
        {
            if (priceListId == null)
            {
                return NotFound();
            }

            var priceList = await _context.CustomerPriceLists
                .FirstOrDefaultAsync(p => p.PriceListID == priceListId);

            if (priceList == null)
            {
                return NotFound();
            }

            ViewBag.PriceListID = priceListId;
            ViewBag.PriceListName = priceList.PriceListName;
            ViewBag.Products = new SelectList(
                await _context.Products
                    .Where(p => p.Status == "Active")
                    .Select(p => new { p.ProductID, DisplayName = p.ProductCode + " - " + p.ProductName })
                    .ToListAsync(),
                "ProductID", "DisplayName");

            return View(new CustomerPriceListItem { PriceListID = priceListId.Value });
        }

        // POST: CustomerPriceList/AddItem
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> AddItem(CustomerPriceListItem item)
        {
            if (ModelState.IsValid)
            {
                item.CreatedDate = DateTime.Now;
                item.UpdatedDate = DateTime.Now;

                _context.Add(item);
                await _context.SaveChangesAsync();
                TempData["SuccessMessage"] = "Price list item added successfully.";
                return RedirectToAction(nameof(Details), new { id = item.PriceListID });
            }

            var priceList = await _context.CustomerPriceLists
                .FirstOrDefaultAsync(p => p.PriceListID == item.PriceListID);

            ViewBag.PriceListID = item.PriceListID;
            ViewBag.PriceListName = priceList?.PriceListName;
            ViewBag.Products = new SelectList(
                await _context.Products
                    .Where(p => p.Status == "Active")
                    .Select(p => new { p.ProductID, DisplayName = p.ProductCode + " - " + p.ProductName })
                    .ToListAsync(),
                "ProductID", "DisplayName", item.ProductID);

            return View(item);
        }

        // GET: CustomerPriceList/EditItem/5
        public async Task<IActionResult> EditItem(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var item = await _context.CustomerPriceListItems
                .Include(i => i.PriceList)
                .FirstOrDefaultAsync(i => i.PriceListItemID == id);

            if (item == null)
            {
                return NotFound();
            }

            ViewBag.PriceListName = item.PriceList.PriceListName;
            ViewBag.Products = new SelectList(
                await _context.Products
                    .Where(p => p.Status == "Active")
                    .Select(p => new { p.ProductID, DisplayName = p.ProductCode + " - " + p.ProductName })
                    .ToListAsync(),
                "ProductID", "DisplayName", item.ProductID);

            return View(item);
        }

        // POST: CustomerPriceList/EditItem/5
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> EditItem(int id, CustomerPriceListItem item)
        {
            if (id != item.PriceListItemID)
            {
                return NotFound();
            }

            if (ModelState.IsValid)
            {
                try
                {
                    var existingItem = await _context.CustomerPriceListItems
                        .FirstOrDefaultAsync(i => i.PriceListItemID == id);

                    if (existingItem == null)
                    {
                        return NotFound();
                    }

                    existingItem.ProductID = item.ProductID;
                    existingItem.UnitPrice = item.UnitPrice;
                    existingItem.MinQuantity = item.MinQuantity;
                    existingItem.MaxQuantity = item.MaxQuantity;
                    existingItem.DiscountPercent = item.DiscountPercent;
                    existingItem.FixedDiscount = item.FixedDiscount;
                    existingItem.Notes = item.Notes;
                    existingItem.IsActive = item.IsActive;
                    existingItem.UpdatedDate = DateTime.Now;

                    await _context.SaveChangesAsync();
                    TempData["SuccessMessage"] = "Price list item updated successfully.";
                    return RedirectToAction(nameof(Details), new { id = existingItem.PriceListID });
                }
                catch (DbUpdateConcurrencyException)
                {
                    if (!PriceListItemExists(item.PriceListItemID))
                    {
                        return NotFound();
                    }
                    else
                    {
                        throw;
                    }
                }
            }

            return View(item);
        }

        // POST: CustomerPriceList/DeleteItem/5
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> DeleteItem(int id)
        {
            var item = await _context.CustomerPriceListItems
                .FirstOrDefaultAsync(i => i.PriceListItemID == id);

            if (item == null)
            {
                return Json(new { success = false, error = "Item not found." });
            }

            _context.CustomerPriceListItems.Remove(item);
            await _context.SaveChangesAsync();

            return Json(new { success = true });
        }

        private void PopulateDropdowns(CustomerPriceList? priceList = null)
        {
            ViewBag.Customers = new SelectList(
                _context.Customers
                    .Where(c => c.IsActive && !c.IsDeleted)
                    .Select(c => new { c.CustomerID, DisplayName = c.CustomerCode + " - " + c.CustomerName })
                    .ToList(),
                "CustomerID", "DisplayName", priceList?.CustomerID);


            ViewBag.Currencies = new SelectList(new[]
            {
                new { Code = "GBP", Name = "British Pound (GBP)" },
                new { Code = "USD", Name = "US Dollar (USD)" },
                new { Code = "EUR", Name = "Euro (EUR)" }
            }, "Code", "Name", priceList?.Currency ?? "GBP");
        }

        private bool PriceListExists(int id)
        {
            return _context.CustomerPriceLists.Any(e => e.PriceListID == id);
        }

        private bool PriceListItemExists(int id)
        {
            return _context.CustomerPriceListItems.Any(e => e.PriceListItemID == id);
        }
    }
}