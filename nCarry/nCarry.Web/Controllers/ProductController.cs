using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Rendering;
using Microsoft.EntityFrameworkCore;
using nCarry.Web.Data;
using nCarry.Web.Models;
using nCarry.Web.Models.Products;

namespace nCarry.Web.Controllers
{
    [Authorize]
    public class ProductController : Controller
    {
        private readonly ApplicationDbContext _context;

        public ProductController(ApplicationDbContext context)
        {
            _context = context;
        }

        // GET: Product
        public async Task<IActionResult> Index()
        {
            var products = await _context.Products
                .Include(p => p.Category)
                .Include(p => p.InventoryUOM)
                .Where(p => !p.IsDeleted)
                .OrderBy(p => p.ProductName)
                .ToListAsync();
            return View(products);
        }

        // GET: Product/Details/5
        public async Task<IActionResult> Details(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            // Query without problematic Variants and Barcodes includes
            var product = await _context.Products
                .Include(p => p.Category)
                .Include(p => p.InventoryUOM)
                .Include(p => p.PurchaseUOM)
                .Include(p => p.SalesUOM)
                .Include(p => p.WeightUOM)
                .Include(p => p.DimensionUOM)
                .Include(p => p.VolumeUOM)
                .FirstOrDefaultAsync(m => m.ProductID == id && !m.IsDeleted);
            
            if (product == null)
            {
                return NotFound();
            }

            return View(product);
        }

        // GET: Product/Create
        public async Task<IActionResult> Create()
        {
            await PopulateDropdowns();
            
            var model = new ProductCreateViewModel
            {
                ProductCode = await GenerateProductCodeAsync(),
                ProductType = "Standard",
                StandardCost = 0,
                ListPrice = 0,
                SellPrice = 0,
                MinStockLevel = 0,
                LeadTimeDays = 7,
                TrackInventory = true,
                IsActive = true
            };
            return View(model);
        }

        // POST: Product/Create
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Create(ProductCreateViewModel model)
        {
            if (ModelState.IsValid)
            {
                var product = new Product
                {
                    ProductCode = model.ProductCode,
                    ProductName = model.ProductName,
                    ShortDescription = model.ShortDescription,
                    Description = model.Description,
                    CategoryID = model.CategoryID,
                    ProductType = model.ProductType,
                    Brand = model.Brand,
                    Manufacturer = model.Manufacturer,
                    ManufacturerPartNumber = model.ManufacturerPartNumber,
                    InventoryUOMID = model.InventoryUOMID,
                    PurchaseUOMID = model.PurchaseUOMID,
                    SalesUOMID = model.SalesUOMID,
                    Weight = model.Weight,
                    Length = model.Length,
                    Width = model.Width,
                    Height = model.Height,
                    StandardCost = model.StandardCost,
                    ListPrice = model.ListPrice,
                    SellPrice = model.SellPrice,
                    MinStockLevel = model.MinStockLevel,
                    ReorderPoint = model.ReorderPoint,
                    ReorderQuantity = model.ReorderQuantity,
                    LeadTimeDays = model.LeadTimeDays,
                    TrackInventory = model.TrackInventory,
                    AllowBackorder = model.AllowBackorder,
                    Notes = model.Notes,
                    IsActive = model.IsActive,
                    CreatedDate = DateTime.Now,
                    CreatedByUserID = int.Parse(User.FindFirst(System.Security.Claims.ClaimTypes.NameIdentifier)?.Value ?? "0")
                };

                _context.Add(product);
                await _context.SaveChangesAsync();

                TempData["Success"] = "Product created successfully.";
                return RedirectToAction(nameof(Index));
            }

            await PopulateDropdowns(model.CategoryID, model.InventoryUOMID, model.PurchaseUOMID, model.SalesUOMID);
            return View(model);
        }

        // GET: Product/Edit/5
        public async Task<IActionResult> Edit(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var product = await _context.Products
                .FirstOrDefaultAsync(p => p.ProductID == id && !p.IsDeleted);
            
            if (product == null)
            {
                return NotFound();
            }

            var model = new ProductEditViewModel
            {
                ProductID = product.ProductID,
                ProductCode = product.ProductCode,
                ProductName = product.ProductName,
                ShortDescription = product.ShortDescription,
                Description = product.Description,
                CategoryID = product.CategoryID,
                ProductType = product.ProductType,
                Status = product.Status,
                Brand = product.Brand,
                Manufacturer = product.Manufacturer,
                ManufacturerPartNumber = product.ManufacturerPartNumber,
                InventoryUOMID = product.InventoryUOMID,
                PurchaseUOMID = product.PurchaseUOMID,
                SalesUOMID = product.SalesUOMID,
                Weight = product.Weight,
                Length = product.Length,
                Width = product.Width,
                Height = product.Height,
                StandardCost = product.StandardCost,
                ListPrice = product.ListPrice,
                SellPrice = product.SellPrice,
                MinStockLevel = product.MinStockLevel,
                ReorderPoint = product.ReorderPoint,
                ReorderQuantity = product.ReorderQuantity,
                LeadTimeDays = product.LeadTimeDays,
                TrackInventory = product.TrackInventory,
                AllowBackorder = product.AllowBackorder,
                HasVariants = product.HasVariants,
                Notes = product.Notes,
                IsActive = product.IsActive
            };

            await PopulateDropdowns(model.CategoryID, model.InventoryUOMID, model.PurchaseUOMID, model.SalesUOMID);
            return View(model);
        }

        // POST: Product/Edit/5
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Edit(int id, ProductEditViewModel model)
        {
            if (id != model.ProductID)
            {
                return NotFound();
            }

            if (ModelState.IsValid)
            {
                try
                {
                    var product = await _context.Products
                        .FirstOrDefaultAsync(p => p.ProductID == id && !p.IsDeleted);
                    
                    if (product == null)
                    {
                        return NotFound();
                    }

                    // Update product fields
                    product.ProductName = model.ProductName;
                    product.ShortDescription = model.ShortDescription;
                    product.Description = model.Description;
                    product.CategoryID = model.CategoryID;
                    product.ProductType = model.ProductType;
                    product.Status = model.Status;
                    product.Brand = model.Brand;
                    product.Manufacturer = model.Manufacturer;
                    product.ManufacturerPartNumber = model.ManufacturerPartNumber;
                    product.InventoryUOMID = model.InventoryUOMID;
                    product.PurchaseUOMID = model.PurchaseUOMID;
                    product.SalesUOMID = model.SalesUOMID;
                    product.Weight = model.Weight;
                    product.Length = model.Length;
                    product.Width = model.Width;
                    product.Height = model.Height;
                    product.StandardCost = model.StandardCost;
                    product.ListPrice = model.ListPrice;
                    product.SellPrice = model.SellPrice;
                    product.MinStockLevel = model.MinStockLevel;
                    product.ReorderPoint = model.ReorderPoint;
                    product.ReorderQuantity = model.ReorderQuantity;
                    product.LeadTimeDays = model.LeadTimeDays;
                    product.TrackInventory = model.TrackInventory;
                    product.AllowBackorder = model.AllowBackorder;
                    product.HasVariants = model.HasVariants;
                    product.Notes = model.Notes;
                    product.IsActive = model.IsActive;
                    product.UpdatedDate = DateTime.Now;
                    product.UpdatedByUserID = int.Parse(User.FindFirst(System.Security.Claims.ClaimTypes.NameIdentifier)?.Value ?? "0");

                    _context.Update(product);
                    await _context.SaveChangesAsync();
                    
                    TempData["Success"] = "Product updated successfully.";
                    return RedirectToAction(nameof(Index));
                }
                catch (DbUpdateConcurrencyException)
                {
                    if (!ProductExists(model.ProductID))
                    {
                        return NotFound();
                    }
                    else
                    {
                        throw;
                    }
                }
            }

            await PopulateDropdowns(model.CategoryID, model.InventoryUOMID, model.PurchaseUOMID, model.SalesUOMID);
            return View(model);
        }

        // GET: Product/Delete/5
        public async Task<IActionResult> Delete(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var product = await _context.Products
                .Include(p => p.Category)
                .Include(p => p.InventoryUOM)
                .FirstOrDefaultAsync(m => m.ProductID == id && !m.IsDeleted);
            
            if (product == null)
            {
                return NotFound();
            }

            return View(product);
        }

        // POST: Product/Delete/5
        [HttpPost, ActionName("Delete")]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> DeleteConfirmed(int id)
        {
            var product = await _context.Products.FindAsync(id);
            if (product != null)
            {
                // Soft delete
                product.IsDeleted = true;
                product.IsActive = false;
                product.DeletedDate = DateTime.Now;
                product.DeletedByUserID = int.Parse(User.FindFirst(System.Security.Claims.ClaimTypes.NameIdentifier)?.Value ?? "0");
                
                _context.Update(product);
                await _context.SaveChangesAsync();
                
                TempData["Success"] = "Product deleted successfully.";
            }

            return RedirectToAction(nameof(Index));
        }

        private bool ProductExists(int id)
        {
            return _context.Products.Any(e => e.ProductID == id);
        }

        private async Task<string> GenerateProductCodeAsync()
        {
            var lastProduct = await _context.Products
                .OrderByDescending(p => p.ProductCode)
                .FirstOrDefaultAsync();

            if (lastProduct == null)
            {
                return "PRD0001";
            }

            // Extract number from last code
            var codePrefix = "PRD";
            if (lastProduct.ProductCode.StartsWith(codePrefix))
            {
                var numberPart = lastProduct.ProductCode.Substring(codePrefix.Length);
                if (int.TryParse(numberPart, out int lastNumber))
                {
                    return $"{codePrefix}{(lastNumber + 1):D4}";
                }
            }

            return "PRD0001";
        }

        private async Task PopulateDropdowns(int? selectedCategoryId = null, int? selectedInventoryUomId = null, int? selectedPurchaseUomId = null, int? selectedSalesUomId = null)
        {
            ViewData["CategoryID"] = new SelectList(
                await _context.ProductCategories.Where(c => c.IsActive).OrderBy(c => c.CategoryName).ToListAsync(),
                "CategoryID", "CategoryName", selectedCategoryId);

            ViewData["InventoryUOMID"] = new SelectList(
                await _context.UnitOfMeasures.Where(u => u.IsActive).OrderBy(u => u.UOMName).ToListAsync(),
                "UOMID", "UOMName", selectedInventoryUomId);

            ViewData["PurchaseUOMID"] = new SelectList(
                await _context.UnitOfMeasures.Where(u => u.IsActive).OrderBy(u => u.UOMName).ToListAsync(),
                "UOMID", "UOMName", selectedPurchaseUomId);

            ViewData["SalesUOMID"] = new SelectList(
                await _context.UnitOfMeasures.Where(u => u.IsActive).OrderBy(u => u.UOMName).ToListAsync(),
                "UOMID", "UOMName", selectedSalesUomId);
        }
    }
}