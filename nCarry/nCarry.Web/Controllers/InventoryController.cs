using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Rendering;
using Microsoft.EntityFrameworkCore;
using nCarry.Web.Data;
using nCarry.Web.Models.Warehouse;
using nCarry.Web.Models.ViewModels;

namespace nCarry.Web.Controllers
{
    [Authorize]
    public class InventoryController : Controller
    {
        private readonly ApplicationDbContext _context;

        public InventoryController(ApplicationDbContext context)
        {
            _context = context;
        }

        // GET: Inventory
        public async Task<IActionResult> Index(int? warehouseId, int? productId)
        {
            var inventoriesQuery = _context.Inventories
                .Include(i => i.Warehouse)
                .Include(i => i.Location)
                .Include(i => i.Product)
                    .ThenInclude(p => p.InventoryUOM)
                .Include(i => i.Product)
                    .ThenInclude(p => p.Category)
                .AsQueryable();

            if (warehouseId.HasValue)
            {
                inventoriesQuery = inventoriesQuery.Where(i => i.WarehouseID == warehouseId.Value);
            }

            if (productId.HasValue)
            {
                inventoriesQuery = inventoriesQuery.Where(i => i.ProductID == productId.Value);
            }

            var inventories = await inventoriesQuery
                .OrderBy(i => i.Warehouse.WarehouseName)
                .ThenBy(i => i.Product.ProductName)
                .ToListAsync();

            // Populate filter dropdowns
            ViewBag.Warehouses = new SelectList(
                await _context.Warehouses.OrderBy(w => w.WarehouseName).ToListAsync(),
                "WarehouseID", "WarehouseName", warehouseId);
            
            ViewBag.Products = new SelectList(
                await _context.Products.Where(p => p.IsActive && !p.IsDeleted).OrderBy(p => p.ProductName).ToListAsync(),
                "ProductID", "ProductName", productId);

            ViewBag.SelectedWarehouseId = warehouseId;
            ViewBag.SelectedProductId = productId;

            return View(inventories);
        }

        // GET: Inventory/Details/5
        public async Task<IActionResult> Details(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var inventory = await _context.Inventories
                .Include(i => i.Warehouse)
                .Include(i => i.Location)
                .Include(i => i.Product)
                    .ThenInclude(p => p.InventoryUOM)
                .Include(i => i.Product)
                    .ThenInclude(p => p.Category)
                .Include(i => i.Variant)
                .FirstOrDefaultAsync(m => m.InventoryID == id);

            if (inventory == null)
            {
                return NotFound();
            }

            // Get recent transactions
            var recentTransactions = await _context.InventoryTransactions
                .Where(t => t.WarehouseID == inventory.WarehouseID && 
                           t.ProductID == inventory.ProductID &&
                           (inventory.BatchNumber == null || t.BatchNumber == inventory.BatchNumber) &&
                           (inventory.SerialNumber == null || t.SerialNumber == inventory.SerialNumber))
                .OrderByDescending(t => t.TransactionDate)
                .Take(20)
                .ToListAsync();

            ViewBag.RecentTransactions = recentTransactions;

            return View(inventory);
        }

        // GET: Inventory/StockAdjustment
        public async Task<IActionResult> StockAdjustment()
        {
            var viewModel = new StockAdjustmentViewModel();
            await PopulateStockAdjustmentDropdowns(viewModel);
            return View(viewModel);
        }

        // POST: Inventory/StockAdjustment
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> StockAdjustment(StockAdjustmentViewModel viewModel)
        {
            if (ModelState.IsValid)
            {
                // Find or create inventory record
                var inventory = await _context.Inventories
                    .FirstOrDefaultAsync(i => i.WarehouseID == viewModel.WarehouseID &&
                                            i.ProductID == viewModel.ProductID &&
                                            i.LocationID == viewModel.LocationID &&
                                            (viewModel.BatchNumber == null || i.BatchNumber == viewModel.BatchNumber) &&
                                            (viewModel.SerialNumber == null || i.SerialNumber == viewModel.SerialNumber));

                if (inventory == null)
                {
                    inventory = new Inventory
                    {
                        WarehouseID = viewModel.WarehouseID,
                        LocationID = viewModel.LocationID,
                        ProductID = viewModel.ProductID,
                        BatchNumber = viewModel.BatchNumber,
                        SerialNumber = viewModel.SerialNumber,
                        ExpiryDate = viewModel.ExpiryDate,
                        QuantityOnHand = 0
                    };
                    _context.Inventories.Add(inventory);
                }

                // Apply adjustment
                var adjustmentQuantity = viewModel.AdjustmentQuantity;
                if (viewModel.AdjustmentType == "Decrease")
                {
                    adjustmentQuantity = -adjustmentQuantity;
                }

                inventory.QuantityOnHand += adjustmentQuantity;
                inventory.UpdatedDate = DateTime.Now;

                // Set quantity for "Set" type
                if (viewModel.AdjustmentType == "Set")
                {
                    inventory.QuantityOnHand = viewModel.AdjustmentQuantity;
                }

                // Create transaction record
                var transaction = new InventoryTransaction
                {
                    TransactionNumber = await GenerateTransactionNumber(),
                    TransactionDate = DateTime.Now,
                    TransactionType = viewModel.AdjustmentType == "Increase" ? "Adjustment In" : 
                                    viewModel.AdjustmentType == "Decrease" ? "Adjustment Out" : "Stock Count",
                    WarehouseID = viewModel.WarehouseID,
                    LocationID = viewModel.LocationID,
                    ProductID = viewModel.ProductID,
                    BatchNumber = viewModel.BatchNumber,
                    SerialNumber = viewModel.SerialNumber,
                    Quantity = Math.Abs(adjustmentQuantity),
                    UnitCost = viewModel.UnitCost,
                    TotalCost = viewModel.UnitCost * Math.Abs(adjustmentQuantity),
                    ReferenceType = "Manual Adjustment",
                    Notes = viewModel.Reason,
                    CreatedByUserID = int.Parse(User.FindFirst(System.Security.Claims.ClaimTypes.NameIdentifier)?.Value ?? "0")
                };

                _context.InventoryTransactions.Add(transaction);
                await _context.SaveChangesAsync();

                TempData["Success"] = "Stock adjustment completed successfully.";
                return RedirectToAction(nameof(Index));
            }

            await PopulateStockAdjustmentDropdowns(viewModel);
            return View(viewModel);
        }

        // GET: Inventory/Transfer
        public async Task<IActionResult> Transfer()
        {
            var viewModel = new InventoryTransferViewModel();
            await PopulateTransferDropdowns(viewModel);
            return View(viewModel);
        }

        // POST: Inventory/Transfer
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Transfer(InventoryTransferViewModel viewModel)
        {
            if (ModelState.IsValid)
            {
                // Get source inventory
                var sourceInventory = await _context.Inventories
                    .FirstOrDefaultAsync(i => i.WarehouseID == viewModel.FromWarehouseID &&
                                            i.ProductID == viewModel.ProductID &&
                                            (viewModel.FromLocationID == null || i.LocationID == viewModel.FromLocationID));

                var availableQuantity = sourceInventory?.QuantityOnHand - sourceInventory?.QuantityReserved ?? 0;
                if (sourceInventory == null || availableQuantity < viewModel.TransferQuantity)
                {
                    ModelState.AddModelError("", "Insufficient stock available for transfer.");
                    await PopulateTransferDropdowns(viewModel);
                    return View(viewModel);
                }

                // Find or create destination inventory
                var destInventory = await _context.Inventories
                    .FirstOrDefaultAsync(i => i.WarehouseID == viewModel.ToWarehouseID &&
                                            i.ProductID == viewModel.ProductID &&
                                            (viewModel.ToLocationID == null || i.LocationID == viewModel.ToLocationID) &&
                                            i.BatchNumber == sourceInventory.BatchNumber &&
                                            i.SerialNumber == sourceInventory.SerialNumber);

                if (destInventory == null)
                {
                    destInventory = new Inventory
                    {
                        WarehouseID = viewModel.ToWarehouseID,
                        LocationID = viewModel.ToLocationID,
                        ProductID = viewModel.ProductID,
                        BatchNumber = sourceInventory.BatchNumber,
                        SerialNumber = sourceInventory.SerialNumber,
                        ExpiryDate = sourceInventory.ExpiryDate,
                        QuantityOnHand = 0,
                        AverageCost = sourceInventory.AverageCost,
                        LastCost = sourceInventory.LastCost
                    };
                    _context.Inventories.Add(destInventory);
                }

                // Update quantities
                sourceInventory.QuantityOnHand -= viewModel.TransferQuantity;
                sourceInventory.QuantityAvailable = sourceInventory.QuantityOnHand - sourceInventory.QuantityReserved;
                sourceInventory.UpdatedDate = DateTime.Now;

                destInventory.QuantityOnHand += viewModel.TransferQuantity;
                destInventory.QuantityAvailable = destInventory.QuantityOnHand - destInventory.QuantityReserved;
                destInventory.UpdatedDate = DateTime.Now;

                // Create transaction records
                var transferNumber = await GenerateTransferNumberAsync();
                var userId = int.Parse(User.FindFirst(System.Security.Claims.ClaimTypes.NameIdentifier)?.Value ?? "0");

                // Out transaction
                var outTransaction = new InventoryTransaction
                {
                    TransactionNumber = await GenerateTransactionNumber(),
                    TransactionDate = DateTime.Now,
                    TransactionType = "Transfer Out",
                    WarehouseID = viewModel.FromWarehouseID,
                    LocationID = viewModel.FromLocationID,
                    ProductID = viewModel.ProductID,
                    BatchNumber = sourceInventory.BatchNumber,
                    SerialNumber = sourceInventory.SerialNumber,
                    Quantity = viewModel.TransferQuantity,
                    ReferenceType = "Transfer",
                    ReferenceNumber = transferNumber,
                    Notes = viewModel.Notes,
                    CreatedByUserID = userId
                };

                // In transaction
                var inTransaction = new InventoryTransaction
                {
                    TransactionNumber = await GenerateTransactionNumber(),
                    TransactionDate = DateTime.Now,
                    TransactionType = "Transfer In",
                    WarehouseID = viewModel.ToWarehouseID,
                    LocationID = viewModel.ToLocationID,
                    ProductID = viewModel.ProductID,
                    BatchNumber = sourceInventory.BatchNumber,
                    SerialNumber = sourceInventory.SerialNumber,
                    Quantity = viewModel.TransferQuantity,
                    ReferenceType = "Transfer",
                    ReferenceNumber = transferNumber,
                    Notes = viewModel.Notes,
                    CreatedByUserID = userId
                };

                _context.InventoryTransactions.Add(outTransaction);
                _context.InventoryTransactions.Add(inTransaction);
                await _context.SaveChangesAsync();

                TempData["Success"] = $"Stock transfer completed successfully. Transfer Number: {transferNumber}";
                return RedirectToAction(nameof(Index));
            }

            await PopulateTransferDropdowns(viewModel);
            return View(viewModel);
        }

        // GET: Inventory/Transactions
        public async Task<IActionResult> Transactions(int? warehouseId, int? productId, DateTime? fromDate, DateTime? toDate)
        {
            var transactionsQuery = _context.InventoryTransactions
                .Include(t => t.Warehouse)
                .Include(t => t.Location)
                .Include(t => t.Product)
                .AsQueryable();

            if (warehouseId.HasValue)
            {
                transactionsQuery = transactionsQuery.Where(t => t.WarehouseID == warehouseId.Value);
            }

            if (productId.HasValue)
            {
                transactionsQuery = transactionsQuery.Where(t => t.ProductID == productId.Value);
            }

            if (fromDate.HasValue)
            {
                transactionsQuery = transactionsQuery.Where(t => t.TransactionDate >= fromDate.Value);
            }

            if (toDate.HasValue)
            {
                var endDate = toDate.Value.AddDays(1);
                transactionsQuery = transactionsQuery.Where(t => t.TransactionDate < endDate);
            }

            var transactions = await transactionsQuery
                .OrderByDescending(t => t.TransactionDate)
                .Take(500)
                .ToListAsync();

            // Populate filter dropdowns
            ViewBag.Warehouses = new SelectList(
                await _context.Warehouses.OrderBy(w => w.WarehouseName).ToListAsync(),
                "WarehouseID", "WarehouseName", warehouseId);
            
            ViewBag.Products = new SelectList(
                await _context.Products.Where(p => p.IsActive && !p.IsDeleted).OrderBy(p => p.ProductName).ToListAsync(),
                "ProductID", "ProductName", productId);

            ViewBag.SelectedWarehouseId = warehouseId;
            ViewBag.SelectedProductId = productId;
            ViewBag.FromDate = fromDate;
            ViewBag.ToDate = toDate;

            return View(transactions);
        }

        private async Task<string> GenerateTransferNumberAsync()
        {
            var today = DateTime.Today;
            var prefix = $"TR{today:yyyyMMdd}";
            
            var lastTransfer = await _context.InventoryTransactions
                .Where(t => t.ReferenceType == "Transfer" && t.ReferenceNumber != null && t.ReferenceNumber.StartsWith(prefix))
                .OrderByDescending(t => t.ReferenceNumber)
                .FirstOrDefaultAsync();

            int nextNumber = 1;
            if (lastTransfer != null && lastTransfer.ReferenceNumber!.Length > prefix.Length)
            {
                var lastNumberStr = lastTransfer.ReferenceNumber.Substring(prefix.Length);
                if (int.TryParse(lastNumberStr, out int lastNumber))
                {
                    nextNumber = lastNumber + 1;
                }
            }

            return $"{prefix}{nextNumber:D3}";
        }

        private async Task PopulateStockAdjustmentDropdowns(StockAdjustmentViewModel viewModel)
        {
            viewModel.Warehouses = new SelectList(
                await _context.Warehouses.OrderBy(w => w.WarehouseName).ToListAsync(),
                "WarehouseID", "WarehouseName");

            viewModel.Products = new SelectList(
                await _context.Products.Where(p => p.IsActive && !p.IsDeleted).OrderBy(p => p.ProductName).ToListAsync(),
                "ProductID", "ProductName");

            viewModel.UnitOfMeasures = new SelectList(
                await _context.UnitOfMeasures.Where(u => u.IsActive).OrderBy(u => u.UOMName).ToListAsync(),
                "UOMID", "UOMName");

            if (viewModel.WarehouseID > 0)
            {
                viewModel.Locations = new SelectList(
                    await _context.WarehouseLocations
                        .Where(l => l.WarehouseID == viewModel.WarehouseID && l.IsActive)
                        .OrderBy(l => l.LocationCode)
                        .ToListAsync(),
                    "LocationID", "LocationCode");
            }
            else
            {
                viewModel.Locations = new SelectList(Enumerable.Empty<SelectListItem>());
            }
        }

        private async Task PopulateTransferDropdowns(InventoryTransferViewModel viewModel)
        {
            viewModel.Warehouses = new SelectList(
                await _context.Warehouses.OrderBy(w => w.WarehouseName).ToListAsync(),
                "WarehouseID", "WarehouseName");

            viewModel.Products = new SelectList(
                await _context.Products.Where(p => p.IsActive && !p.IsDeleted).OrderBy(p => p.ProductName).ToListAsync(),
                "ProductID", "ProductName");

            viewModel.UnitOfMeasures = new SelectList(
                await _context.UnitOfMeasures.Where(u => u.IsActive).OrderBy(u => u.UOMName).ToListAsync(),
                "UOMID", "UOMName");

            if (viewModel.FromWarehouseID > 0)
            {
                viewModel.FromLocations = new SelectList(
                    await _context.WarehouseLocations
                        .Where(l => l.WarehouseID == viewModel.FromWarehouseID && l.IsActive)
                        .OrderBy(l => l.LocationCode)
                        .ToListAsync(),
                    "LocationID", "LocationCode");
            }
            else
            {
                viewModel.FromLocations = new SelectList(Enumerable.Empty<SelectListItem>());
            }

            if (viewModel.ToWarehouseID > 0)
            {
                viewModel.ToLocations = new SelectList(
                    await _context.WarehouseLocations
                        .Where(l => l.WarehouseID == viewModel.ToWarehouseID && l.IsActive)
                        .OrderBy(l => l.LocationCode)
                        .ToListAsync(),
                    "LocationID", "LocationCode");
            }
            else
            {
                viewModel.ToLocations = new SelectList(Enumerable.Empty<SelectListItem>());
            }
        }
        private async Task<string> GenerateTransactionNumber()
        {
            var prefix = "IT";
            var date = DateTime.Now.ToString("yyyyMMdd");
            
            var lastNumber = await _context.InventoryTransactions
                .Where(t => t.TransactionNumber.StartsWith(prefix + date))
                .OrderByDescending(t => t.TransactionNumber)
                .Select(t => t.TransactionNumber)
                .FirstOrDefaultAsync();

            int sequence = 1;
            if (!string.IsNullOrEmpty(lastNumber) && lastNumber.Length > (prefix + date).Length)
            {
                var lastSequence = lastNumber.Substring((prefix + date).Length);
                if (int.TryParse(lastSequence, out int parsed))
                {
                    sequence = parsed + 1;
                }
            }

            return $"{prefix}{date}{sequence:D4}";
        }
    }
}