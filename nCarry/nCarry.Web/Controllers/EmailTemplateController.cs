using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Rendering;
using Microsoft.EntityFrameworkCore;
using nCarry.Web.Data;
using nCarry.Web.Models.System;

namespace nCarry.Web.Controllers
{
    [Authorize]
    public class EmailTemplateController : Controller
    {
        private readonly ApplicationDbContext _context;

        public EmailTemplateController(ApplicationDbContext context)
        {
            _context = context;
        }

        // GET: EmailTemplate
        public async Task<IActionResult> Index(string? category, string? language, bool? isActive)
        {
            var templatesQuery = _context.EmailTemplates.AsQueryable();

            if (!string.IsNullOrEmpty(category))
            {
                templatesQuery = templatesQuery.Where(et => et.Category == category);
                ViewBag.SelectedCategory = category;
            }

            if (!string.IsNullOrEmpty(language))
            {
                templatesQuery = templatesQuery.Where(et => et.Language == language);
                ViewBag.SelectedLanguage = language;
            }

            if (isActive.HasValue)
            {
                templatesQuery = templatesQuery.Where(et => et.IsActive == isActive.Value);
                ViewBag.SelectedIsActive = isActive.Value;
            }

            var templates = await templatesQuery
                .OrderBy(et => et.Category)
                .ThenBy(et => et.TemplateName)
                .ToListAsync();

            // Populate filter dropdowns
            var categories = await _context.EmailTemplates
                .Select(et => et.Category)
                .Distinct()
                .OrderBy(c => c)
                .ToListAsync();

            var languages = await _context.EmailTemplates
                .Select(et => et.Language)
                .Distinct()
                .OrderBy(l => l)
                .ToListAsync();

            ViewBag.Categories = new SelectList(categories.Select(c => new { Value = c, Text = c }), "Value", "Text", category);
            ViewBag.Languages = new SelectList(languages.Select(l => new { Value = l, Text = l }), "Value", "Text", language);

            ViewBag.ActiveFilter = new List<SelectListItem>
            {
                new SelectListItem { Value = "", Text = "All" },
                new SelectListItem { Value = "true", Text = "Active", Selected = isActive == true },
                new SelectListItem { Value = "false", Text = "Inactive", Selected = isActive == false }
            };

            return View(templates);
        }

        // GET: EmailTemplate/Details/5
        public async Task<IActionResult> Details(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var emailTemplate = await _context.EmailTemplates
                .FirstOrDefaultAsync(m => m.TemplateID == id);

            if (emailTemplate == null)
            {
                return NotFound();
            }

            return View(emailTemplate);
        }

        // GET: EmailTemplate/Create
        public IActionResult Create()
        {
            PopulateDropdowns();
            
            var emailTemplate = new EmailTemplate
            {
                Language = "en-US",
                IsActive = true
            };

            return View(emailTemplate);
        }

        // POST: EmailTemplate/Create
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Create([Bind("Category,TemplateName,Language,Subject,Body,PlainTextBody,Variables,IsActive,Description")] EmailTemplate emailTemplate)
        {
            if (ModelState.IsValid)
            {
                // Check if template already exists for this category, name, and language
                if (await _context.EmailTemplates.AnyAsync(et => et.Category == emailTemplate.Category && 
                    et.TemplateName == emailTemplate.TemplateName && et.Language == emailTemplate.Language))
                {
                    ModelState.AddModelError("TemplateName", "Template already exists for this category, name, and language combination.");
                    PopulateDropdowns();
                    return View(emailTemplate);
                }

                emailTemplate.CreatedDate = DateTime.Now;
                emailTemplate.UpdatedDate = DateTime.Now;

                _context.Add(emailTemplate);
                await _context.SaveChangesAsync();
                
                TempData["Success"] = "Email template created successfully.";
                return RedirectToAction(nameof(Index));
            }

            PopulateDropdowns();
            return View(emailTemplate);
        }

        // GET: EmailTemplate/Edit/5
        public async Task<IActionResult> Edit(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var emailTemplate = await _context.EmailTemplates.FindAsync(id);
            if (emailTemplate == null)
            {
                return NotFound();
            }

            PopulateDropdowns();
            return View(emailTemplate);
        }

        // POST: EmailTemplate/Edit/5
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Edit(int id, [Bind("TemplateID,Category,TemplateName,Language,Subject,Body,PlainTextBody,Variables,IsActive,Description,CreatedDate,CreatedByUserID")] EmailTemplate emailTemplate)
        {
            if (id != emailTemplate.TemplateID)
            {
                return NotFound();
            }

            if (ModelState.IsValid)
            {
                try
                {
                    // Check if template already exists for this category, name, and language (excluding current record)
                    if (await _context.EmailTemplates.AnyAsync(et => et.Category == emailTemplate.Category && 
                        et.TemplateName == emailTemplate.TemplateName && et.Language == emailTemplate.Language && et.TemplateID != id))
                    {
                        ModelState.AddModelError("TemplateName", "Template already exists for this category, name, and language combination.");
                        PopulateDropdowns();
                        return View(emailTemplate);
                    }

                    emailTemplate.UpdatedDate = DateTime.Now;
                    _context.Update(emailTemplate);
                    await _context.SaveChangesAsync();
                    
                    TempData["Success"] = "Email template updated successfully.";
                }
                catch (DbUpdateConcurrencyException)
                {
                    if (!EmailTemplateExists(emailTemplate.TemplateID))
                    {
                        return NotFound();
                    }
                    else
                    {
                        throw;
                    }
                }
                return RedirectToAction(nameof(Index));
            }

            PopulateDropdowns();
            return View(emailTemplate);
        }

        // GET: EmailTemplate/Delete/5
        public async Task<IActionResult> Delete(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var emailTemplate = await _context.EmailTemplates
                .FirstOrDefaultAsync(m => m.TemplateID == id);
                
            if (emailTemplate == null)
            {
                return NotFound();
            }

            return View(emailTemplate);
        }

        // POST: EmailTemplate/Delete/5
        [HttpPost, ActionName("Delete")]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> DeleteConfirmed(int id)
        {
            var emailTemplate = await _context.EmailTemplates.FindAsync(id);
            if (emailTemplate != null)
            {
                _context.EmailTemplates.Remove(emailTemplate);
                await _context.SaveChangesAsync();
                
                TempData["Success"] = "Email template deleted successfully.";
            }

            return RedirectToAction(nameof(Index));
        }

        // GET: EmailTemplate/Preview/5
        public async Task<IActionResult> Preview(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var emailTemplate = await _context.EmailTemplates.FindAsync(id);
            if (emailTemplate == null)
            {
                return NotFound();
            }

            // Generate sample data for preview
            var sampleData = GenerateSampleData(emailTemplate.Category);
            var previewSubject = ReplaceVariables(emailTemplate.Subject, sampleData);
            var previewBody = ReplaceVariables(emailTemplate.Body, sampleData);

            ViewBag.PreviewSubject = previewSubject;
            ViewBag.PreviewBody = previewBody;
            ViewBag.SampleData = sampleData;

            return View(emailTemplate);
        }

        // POST: EmailTemplate/TestSend
        [HttpPost]
        public async Task<IActionResult> TestSend(int templateId, string testEmail)
        {
            if (string.IsNullOrEmpty(testEmail))
            {
                return Json(new { success = false, message = "Please provide a test email address." });
            }

            var template = await _context.EmailTemplates.FindAsync(templateId);
            if (template == null)
            {
                return Json(new { success = false, message = "Template not found." });
            }

            try
            {
                // Generate sample data and send test email
                var sampleData = GenerateSampleData(template.Category);
                var subject = ReplaceVariables(template.Subject, sampleData);
                var body = ReplaceVariables(template.Body, sampleData);

                // Here you would integrate with your email service
                // await _emailService.SendEmailAsync(testEmail, subject, body);

                return Json(new { success = true, message = "Test email sent successfully." });
            }
            catch (Exception ex)
            {
                return Json(new { success = false, message = $"Failed to send test email: {ex.Message}" });
            }
        }

        // GET: EmailTemplate/LoadDefaults
        public async Task<IActionResult> LoadDefaults()
        {
            await LoadDefaultEmailTemplates();
            TempData["Success"] = "Default email templates loaded successfully.";
            return RedirectToAction(nameof(Index));
        }

        // GET: EmailTemplate/Variables
        public IActionResult Variables(string category)
        {
            var variables = GetAvailableVariables(category);
            return Json(variables);
        }

        private Dictionary<string, string> GenerateSampleData(string category)
        {
            var sampleData = new Dictionary<string, string>
            {
                {"CompanyName", "nCarry Wholesale"},
                {"CompanyEmail", "<EMAIL>"},
                {"CompanyPhone", "******-0123"},
                {"CurrentDate", DateTime.Now.ToString("dd/MM/yyyy")},
                {"CurrentTime", DateTime.Now.ToString("HH:mm")}
            };

            switch (category.ToLower())
            {
                case "sales":
                    foreach (var kvp in new Dictionary<string, string>
                    {
                        {"CustomerName", "John Smith"},
                        {"CustomerEmail", "<EMAIL>"},
                        {"OrderNumber", "SO20240629001"},
                        {"OrderDate", DateTime.Now.ToString("dd/MM/yyyy")},
                        {"OrderTotal", "$1,250.00"},
                        {"DeliveryAddress", "123 Main St, New York, NY 10001"}
                    })
                    {
                        sampleData[kvp.Key] = kvp.Value;
                    }
                    break;

                case "purchase":
                    foreach (var kvp in new Dictionary<string, string>
                    {
                        {"SupplierName", "ABC Supplies Inc."},
                        {"SupplierEmail", "<EMAIL>"},
                        {"PurchaseOrderNumber", "PO20240629001"},
                        {"OrderDate", DateTime.Now.ToString("dd/MM/yyyy")},
                        {"OrderTotal", "$5,750.00"},
                        {"DeliveryDate", DateTime.Now.AddDays(7).ToString("dd/MM/yyyy")}
                    })
                    {
                        sampleData[kvp.Key] = kvp.Value;
                    }
                    break;

                case "shipment":
                    foreach (var kvp in new Dictionary<string, string>
                    {
                        {"TrackingNumber", "SH20240629001"},
                        {"CarrierName", "Express Logistics"},
                        {"ShipmentDate", DateTime.Now.ToString("dd/MM/yyyy")},
                        {"EstimatedDelivery", DateTime.Now.AddDays(3).ToString("dd/MM/yyyy")},
                        {"RecipientName", "Jane Doe"},
                        {"DeliveryAddress", "456 Oak Ave, Los Angeles, CA 90210"}
                    })
                    {
                        sampleData[kvp.Key] = kvp.Value;
                    }
                    break;

                case "system":
                    foreach (var kvp in new Dictionary<string, string>
                    {
                        {"UserName", "admin"},
                        {"UserEmail", "<EMAIL>"},
                        {"LoginTime", DateTime.Now.ToString("dd/MM/yyyy HH:mm")},
                        {"IPAddress", "*************"},
                        {"SystemVersion", "1.0.0"}
                    })
                    {
                        sampleData[kvp.Key] = kvp.Value;
                    }
                    break;
            }

            return sampleData;
        }

        private string ReplaceVariables(string content, Dictionary<string, string> variables)
        {
            if (string.IsNullOrEmpty(content))
                return content;

            foreach (var variable in variables)
            {
                content = content.Replace($"{{{variable.Key}}}", variable.Value);
            }

            return content;
        }

        private Dictionary<string, string> GetAvailableVariables(string category)
        {
            var variables = new Dictionary<string, string>
            {
                {"CompanyName", "Company name"},
                {"CompanyEmail", "Company email address"},
                {"CompanyPhone", "Company phone number"},
                {"CurrentDate", "Current date"},
                {"CurrentTime", "Current time"}
            };

            switch (category?.ToLower())
            {
                case "sales":
                    var salesVars = new Dictionary<string, string>
                    {
                        {"CustomerName", "Customer name"},
                        {"CustomerEmail", "Customer email"},
                        {"OrderNumber", "Sales order number"},
                        {"OrderDate", "Order date"},
                        {"OrderTotal", "Order total amount"},
                        {"DeliveryAddress", "Delivery address"}
                    };
                    foreach (var kvp in salesVars)
                        variables[kvp.Key] = kvp.Value;
                    break;

                case "purchase":
                    var purchaseVars = new Dictionary<string, string>
                    {
                        {"SupplierName", "Supplier name"},
                        {"SupplierEmail", "Supplier email"},
                        {"PurchaseOrderNumber", "Purchase order number"},
                        {"OrderDate", "Order date"},
                        {"OrderTotal", "Order total amount"},
                        {"DeliveryDate", "Expected delivery date"}
                    };
                    foreach (var kvp in purchaseVars)
                        variables[kvp.Key] = kvp.Value;
                    break;

                case "shipment":
                    var shipmentVars = new Dictionary<string, string>
                    {
                        {"TrackingNumber", "Shipment tracking number"},
                        {"CarrierName", "Carrier name"},
                        {"ShipmentDate", "Shipment date"},
                        {"EstimatedDelivery", "Estimated delivery date"},
                        {"RecipientName", "Recipient name"},
                        {"DeliveryAddress", "Delivery address"}
                    };
                    foreach (var kvp in shipmentVars)
                        variables[kvp.Key] = kvp.Value;
                    break;

                case "system":
                    var systemVars = new Dictionary<string, string>
                    {
                        {"UserName", "User name"},
                        {"UserEmail", "User email"},
                        {"LoginTime", "Login time"},
                        {"IPAddress", "IP address"},
                        {"SystemVersion", "System version"}
                    };
                    foreach (var kvp in systemVars)
                        variables[kvp.Key] = kvp.Value;
                    break;
            }

            return variables;
        }

        private async Task LoadDefaultEmailTemplates()
        {
            var defaultTemplates = new List<EmailTemplate>
            {
                // Sales Templates
                new EmailTemplate 
                { 
                    Category = "Sales", 
                    TemplateName = "OrderConfirmation", 
                    Language = "en-US", 
                    Subject = "Order Confirmation - {OrderNumber}", 
                    Body = @"<h2>Order Confirmation</h2>
                            <p>Dear {CustomerName},</p>
                            <p>Thank you for your order! We have received your order <strong>{OrderNumber}</strong> placed on {OrderDate}.</p>
                            <p><strong>Order Details:</strong></p>
                            <ul>
                                <li>Order Number: {OrderNumber}</li>
                                <li>Order Date: {OrderDate}</li>
                                <li>Total Amount: {OrderTotal}</li>
                                <li>Delivery Address: {DeliveryAddress}</li>
                            </ul>
                            <p>We will process your order and notify you when it's ready for shipment.</p>
                            <p>Best regards,<br>{CompanyName}</p>", 
                    Variables = "CustomerName,CustomerEmail,OrderNumber,OrderDate,OrderTotal,DeliveryAddress,CompanyName", 
                    IsActive = true, 
                    Description = "Sent when a sales order is confirmed" 
                },

                // Purchase Templates
                new EmailTemplate 
                { 
                    Category = "Purchase", 
                    TemplateName = "PurchaseOrderSent", 
                    Language = "en-US", 
                    Subject = "Purchase Order {PurchaseOrderNumber}", 
                    Body = @"<h2>Purchase Order</h2>
                            <p>Dear {SupplierName},</p>
                            <p>Please find attached our purchase order <strong>{PurchaseOrderNumber}</strong> dated {OrderDate}.</p>
                            <p><strong>Order Details:</strong></p>
                            <ul>
                                <li>PO Number: {PurchaseOrderNumber}</li>
                                <li>Order Date: {OrderDate}</li>
                                <li>Total Amount: {OrderTotal}</li>
                                <li>Expected Delivery: {DeliveryDate}</li>
                            </ul>
                            <p>Please confirm receipt and provide delivery schedule.</p>
                            <p>Best regards,<br>{CompanyName}</p>", 
                    Variables = "SupplierName,SupplierEmail,PurchaseOrderNumber,OrderDate,OrderTotal,DeliveryDate,CompanyName", 
                    IsActive = true, 
                    Description = "Sent when a purchase order is created" 
                },

                // Shipment Templates
                new EmailTemplate 
                { 
                    Category = "Shipment", 
                    TemplateName = "ShipmentNotification", 
                    Language = "en-US", 
                    Subject = "Your order has been shipped - Tracking: {TrackingNumber}", 
                    Body = @"<h2>Shipment Notification</h2>
                            <p>Dear {RecipientName},</p>
                            <p>Great news! Your order has been shipped and is on its way to you.</p>
                            <p><strong>Shipment Details:</strong></p>
                            <ul>
                                <li>Tracking Number: <strong>{TrackingNumber}</strong></li>
                                <li>Carrier: {CarrierName}</li>
                                <li>Shipment Date: {ShipmentDate}</li>
                                <li>Estimated Delivery: {EstimatedDelivery}</li>
                                <li>Delivery Address: {DeliveryAddress}</li>
                            </ul>
                            <p>You can track your shipment using the tracking number provided.</p>
                            <p>Best regards,<br>{CompanyName}</p>", 
                    Variables = "RecipientName,TrackingNumber,CarrierName,ShipmentDate,EstimatedDelivery,DeliveryAddress,CompanyName", 
                    IsActive = true, 
                    Description = "Sent when an order is shipped" 
                },

                // System Templates
                new EmailTemplate 
                { 
                    Category = "System", 
                    TemplateName = "UserWelcome", 
                    Language = "en-US", 
                    Subject = "Welcome to {CompanyName}", 
                    Body = @"<h2>Welcome to {CompanyName}!</h2>
                            <p>Dear {UserName},</p>
                            <p>Welcome to our system! Your account has been created successfully.</p>
                            <p><strong>Account Details:</strong></p>
                            <ul>
                                <li>Username: {UserName}</li>
                                <li>Email: {UserEmail}</li>
                                <li>Registration Date: {CurrentDate}</li>
                            </ul>
                            <p>You can now log in to the system and start using our services.</p>
                            <p>If you have any questions, please don't hesitate to contact us.</p>
                            <p>Best regards,<br>{CompanyName} Team</p>", 
                    Variables = "UserName,UserEmail,CurrentDate,CompanyName", 
                    IsActive = true, 
                    Description = "Sent when a new user account is created" 
                }
            };

            // Remove existing templates
            var existingTemplates = await _context.EmailTemplates.ToListAsync();
            _context.EmailTemplates.RemoveRange(existingTemplates);

            // Add new templates
            foreach (var template in defaultTemplates)
            {
                template.CreatedDate = DateTime.Now;
                template.UpdatedDate = DateTime.Now;
            }

            _context.EmailTemplates.AddRange(defaultTemplates);
            await _context.SaveChangesAsync();
        }

        private bool EmailTemplateExists(int id)
        {
            return _context.EmailTemplates.Any(e => e.TemplateID == id);
        }

        private void PopulateDropdowns()
        {
            ViewBag.Categories = new List<SelectListItem>
            {
                new SelectListItem { Value = "Sales", Text = "Sales" },
                new SelectListItem { Value = "Purchase", Text = "Purchase" },
                new SelectListItem { Value = "Shipment", Text = "Shipment" },
                new SelectListItem { Value = "Inventory", Text = "Inventory" },
                new SelectListItem { Value = "System", Text = "System" },
                new SelectListItem { Value = "Marketing", Text = "Marketing" },
                new SelectListItem { Value = "Financial", Text = "Financial" }
            };

            ViewBag.Languages = new List<SelectListItem>
            {
                new SelectListItem { Value = "en-US", Text = "English (US)" },
                new SelectListItem { Value = "en-GB", Text = "English (UK)" },
                new SelectListItem { Value = "es-ES", Text = "Spanish" },
                new SelectListItem { Value = "fr-FR", Text = "French" },
                new SelectListItem { Value = "de-DE", Text = "German" },
                new SelectListItem { Value = "tr-TR", Text = "Turkish" }
            };

            ViewBag.TemplateNames = new List<SelectListItem>
            {
                new SelectListItem { Value = "OrderConfirmation", Text = "Order Confirmation" },
                new SelectListItem { Value = "ShipmentNotification", Text = "Shipment Notification" },
                new SelectListItem { Value = "DeliveryConfirmation", Text = "Delivery Confirmation" },
                new SelectListItem { Value = "PurchaseOrderSent", Text = "Purchase Order Sent" },
                new SelectListItem { Value = "LowStockAlert", Text = "Low Stock Alert" },
                new SelectListItem { Value = "UserWelcome", Text = "User Welcome" },
                new SelectListItem { Value = "PasswordReset", Text = "Password Reset" },
                new SelectListItem { Value = "InvoiceGenerated", Text = "Invoice Generated" }
            };
        }
    }
}