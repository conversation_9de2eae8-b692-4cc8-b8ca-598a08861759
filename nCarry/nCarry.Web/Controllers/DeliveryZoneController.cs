using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Rendering;
using Microsoft.EntityFrameworkCore;
using nCarry.Web.Data;
using nCarry.Web.Models.Logistics;

namespace nCarry.Web.Controllers
{
    [Authorize]
    public class DeliveryZoneController : Controller
    {
        private readonly ApplicationDbContext _context;

        public DeliveryZoneController(ApplicationDbContext context)
        {
            _context = context;
        }

        // GET: DeliveryZone
        public async Task<IActionResult> Index(int? branchId)
        {
            var zonesQuery = _context.DeliveryZones
                .Include(dz => dz.Branch)
                .AsQueryable();

            if (branchId.HasValue)
            {
                zonesQuery = zonesQuery.Where(dz => dz.BranchID == branchId.Value);
                ViewBag.SelectedBranchId = branchId.Value;
                ViewBag.SelectedBranchName = await _context.Branches
                    .Where(b => b.BranchID == branchId.Value)
                    .Select(b => b.BranchName)
                    .FirstOrDefaultAsync();
            }

            var zones = await zonesQuery
                .OrderBy(dz => dz.Branch != null ? dz.Branch.BranchName : string.Empty)
                .ThenBy(dz => dz.ZoneCode)
                .ToListAsync();

            // Populate branch filter dropdown
            ViewBag.Branches = new SelectList(
                await _context.Branches.Where(b => b.IsActive).OrderBy(b => b.BranchName).ToListAsync(),
                "BranchID", "BranchName", branchId);

            return View(zones);
        }

        // GET: DeliveryZone/Details/5
        public async Task<IActionResult> Details(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var deliveryZone = await _context.DeliveryZones
                .Include(dz => dz.Branch)
                .Include(dz => dz.Routes)
                .FirstOrDefaultAsync(m => m.ZoneID == id);

            if (deliveryZone == null)
            {
                return NotFound();
            }

            return View(deliveryZone);
        }

        // GET: DeliveryZone/Create
        public async Task<IActionResult> Create(int? branchId)
        {
            await PopulateDropdowns(branchId);
            
            var zone = new DeliveryZone();
            if (branchId.HasValue)
            {
                zone.BranchID = branchId.Value;
            }

            return View(zone);
        }

        // POST: DeliveryZone/Create
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Create([Bind("BranchID,ZoneCode,ZoneName,Description,PostalCodes,MinDeliveryValue,StandardDeliveryFee,ExpressDeliveryFee,StandardDeliveryDays,ExpressDeliveryDays,IsActive")] DeliveryZone deliveryZone)
        {
            if (ModelState.IsValid)
            {
                // Check if zone code already exists for this branch
                if (await _context.DeliveryZones.AnyAsync(dz => dz.BranchID == deliveryZone.BranchID && dz.ZoneCode == deliveryZone.ZoneCode))
                {
                    ModelState.AddModelError("ZoneCode", "Zone code already exists for this branch.");
                    await PopulateDropdowns(deliveryZone.BranchID);
                    return View(deliveryZone);
                }

                deliveryZone.CreatedDate = DateTime.Now;
                deliveryZone.UpdatedDate = DateTime.Now;

                _context.Add(deliveryZone);
                await _context.SaveChangesAsync();
                
                TempData["Success"] = "Delivery zone created successfully.";
                return RedirectToAction(nameof(Index), new { branchId = deliveryZone.BranchID });
            }

            await PopulateDropdowns(deliveryZone.BranchID);
            return View(deliveryZone);
        }

        // GET: DeliveryZone/Edit/5
        public async Task<IActionResult> Edit(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var deliveryZone = await _context.DeliveryZones.FindAsync(id);
            if (deliveryZone == null)
            {
                return NotFound();
            }

            await PopulateDropdowns(deliveryZone.BranchID);
            return View(deliveryZone);
        }

        // POST: DeliveryZone/Edit/5
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Edit(int id, [Bind("ZoneID,BranchID,ZoneCode,ZoneName,Description,PostalCodes,MinDeliveryValue,StandardDeliveryFee,ExpressDeliveryFee,StandardDeliveryDays,ExpressDeliveryDays,IsActive,CreatedDate,CreatedByUserID")] DeliveryZone deliveryZone)
        {
            if (id != deliveryZone.ZoneID)
            {
                return NotFound();
            }

            if (ModelState.IsValid)
            {
                try
                {
                    // Check if zone code already exists for this branch (excluding current record)
                    if (await _context.DeliveryZones.AnyAsync(dz => dz.BranchID == deliveryZone.BranchID && dz.ZoneCode == deliveryZone.ZoneCode && dz.ZoneID != id))
                    {
                        ModelState.AddModelError("ZoneCode", "Zone code already exists for this branch.");
                        await PopulateDropdowns(deliveryZone.BranchID);
                        return View(deliveryZone);
                    }

                    deliveryZone.UpdatedDate = DateTime.Now;

                    _context.Update(deliveryZone);
                    await _context.SaveChangesAsync();
                    
                    TempData["Success"] = "Delivery zone updated successfully.";
                }
                catch (DbUpdateConcurrencyException)
                {
                    if (!DeliveryZoneExists(deliveryZone.ZoneID))
                    {
                        return NotFound();
                    }
                    else
                    {
                        throw;
                    }
                }
                return RedirectToAction(nameof(Index), new { branchId = deliveryZone.BranchID });
            }

            await PopulateDropdowns(deliveryZone.BranchID);
            return View(deliveryZone);
        }

        // GET: DeliveryZone/Delete/5
        public async Task<IActionResult> Delete(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var deliveryZone = await _context.DeliveryZones
                .Include(dz => dz.Branch)
                .FirstOrDefaultAsync(m => m.ZoneID == id);
                
            if (deliveryZone == null)
            {
                return NotFound();
            }

            // Check if zone has routes or shipments
            var hasRoutes = await _context.DeliveryRoutes.AnyAsync(r => r.ZoneID == id);
            var hasShipments = await _context.Shipments.AnyAsync(s => s.DeliveryZoneID == id);
            
            ViewBag.HasRoutes = hasRoutes;
            ViewBag.HasShipments = hasShipments;

            return View(deliveryZone);
        }

        // POST: DeliveryZone/Delete/5
        [HttpPost, ActionName("Delete")]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> DeleteConfirmed(int id)
        {
            var deliveryZone = await _context.DeliveryZones.FindAsync(id);
            if (deliveryZone != null)
            {
                // Check if zone has routes or shipments
                var hasRoutes = await _context.DeliveryRoutes.AnyAsync(r => r.ZoneID == id);
                var hasShipments = await _context.Shipments.AnyAsync(s => s.DeliveryZoneID == id);
                
                if (hasRoutes || hasShipments)
                {
                    TempData["Error"] = "Cannot delete delivery zone with existing routes or shipments.";
                    return RedirectToAction(nameof(Index), new { branchId = deliveryZone.BranchID });
                }

                var branchId = deliveryZone.BranchID;
                _context.DeliveryZones.Remove(deliveryZone);
                await _context.SaveChangesAsync();
                
                TempData["Success"] = "Delivery zone deleted successfully.";
                return RedirectToAction(nameof(Index), new { branchId });
            }

            return RedirectToAction(nameof(Index));
        }

        // GET: DeliveryZone/CheckPostalCode
        [HttpGet]
        public async Task<IActionResult> CheckPostalCode(string postalCode)
        {
            if (string.IsNullOrEmpty(postalCode))
            {
                return Json(new { found = false });
            }

            var zone = await _context.DeliveryZones
                .Where(dz => dz.IsActive && dz.PostalCodes != null && dz.PostalCodes.Contains(postalCode))
                .Include(dz => dz.Branch)
                .FirstOrDefaultAsync();

            if (zone != null)
            {
                return Json(new 
                { 
                    found = true,
                    zoneId = zone.ZoneID,
                    zoneName = zone.ZoneName,
                    branchName = zone.Branch?.BranchName ?? string.Empty,
                    standardFee = zone.StandardDeliveryFee,
                    expressFee = zone.ExpressDeliveryFee,
                    standardDays = zone.StandardDeliveryDays,
                    expressDays = zone.ExpressDeliveryDays
                });
            }

            return Json(new { found = false });
        }

        private bool DeliveryZoneExists(int id)
        {
            return _context.DeliveryZones.Any(e => e.ZoneID == id);
        }

        private async Task PopulateDropdowns(int? branchId = null)
        {
            ViewBag.Branches = new SelectList(
                await _context.Branches.Where(b => b.IsActive).OrderBy(b => b.BranchName).ToListAsync(),
                "BranchID", "BranchName", branchId);
        }
    }
}