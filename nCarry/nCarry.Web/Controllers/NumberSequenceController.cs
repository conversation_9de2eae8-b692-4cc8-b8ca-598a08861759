using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Rendering;
using Microsoft.EntityFrameworkCore;
using nCarry.Web.Data;
using nCarry.Web.Models.System;

namespace nCarry.Web.Controllers
{
    [Authorize]
    public class NumberSequenceController : Controller
    {
        private readonly ApplicationDbContext _context;

        public NumberSequenceController(ApplicationDbContext context)
        {
            _context = context;
        }

        // GET: NumberSequence
        public async Task<IActionResult> Index(string? module, bool? isActive)
        {
            var numberSequencesQuery = _context.NumberSequences.AsQueryable();

            if (!string.IsNullOrEmpty(module))
            {
                numberSequencesQuery = numberSequencesQuery.Where(ns => ns.Module == module);
                ViewBag.SelectedModule = module;
            }

            if (isActive.HasValue)
            {
                numberSequencesQuery = numberSequencesQuery.Where(ns => ns.IsActive == isActive.Value);
                ViewBag.SelectedIsActive = isActive.Value;
            }

            var numberSequences = await numberSequencesQuery
                .OrderBy(ns => ns.Module)
                .ThenBy(ns => ns.SequenceType)
                .ToListAsync();

            // Get distinct modules for filter
            var modules = await _context.NumberSequences
                .Select(ns => ns.Module)
                .Distinct()
                .OrderBy(m => m)
                .ToListAsync();

            ViewBag.Modules = new SelectList(modules.Select(m => new { Value = m, Text = m }), "Value", "Text", module);

            ViewBag.ActiveFilter = new List<SelectListItem>
            {
                new SelectListItem { Value = "", Text = "All" },
                new SelectListItem { Value = "true", Text = "Active", Selected = isActive == true },
                new SelectListItem { Value = "false", Text = "Inactive", Selected = isActive == false }
            };

            return View(numberSequences);
        }

        // GET: NumberSequence/Details/5
        public async Task<IActionResult> Details(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var numberSequence = await _context.NumberSequences
                .FirstOrDefaultAsync(m => m.SequenceID == id);

            if (numberSequence == null)
            {
                return NotFound();
            }

            // Generate sample numbers
            ViewBag.SampleNumbers = GenerateSampleNumbers(numberSequence, 5);

            return View(numberSequence);
        }

        // GET: NumberSequence/Create
        public IActionResult Create()
        {
            PopulateDropdowns();
            
            var numberSequence = new NumberSequence
            {
                StartNumber = 1,
                CurrentNumber = 1,
                Increment = 1,
                IsActive = true
            };

            return View(numberSequence);
        }

        // POST: NumberSequence/Create
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Create([Bind("Module,SequenceType,Prefix,Suffix,StartNumber,CurrentNumber,Increment,PadLength,DateFormat,IsActive,Description")] NumberSequence numberSequence)
        {
            if (ModelState.IsValid)
            {
                // Check if sequence already exists for this module and type
                if (await _context.NumberSequences.AnyAsync(ns => ns.Module == numberSequence.Module && ns.SequenceType == numberSequence.SequenceType))
                {
                    ModelState.AddModelError("SequenceType", "Number sequence already exists for this module and type.");
                    PopulateDropdowns();
                    return View(numberSequence);
                }

                numberSequence.CreatedDate = DateTime.Now;
                numberSequence.UpdatedDate = DateTime.Now;

                _context.Add(numberSequence);
                await _context.SaveChangesAsync();
                
                TempData["Success"] = "Number sequence created successfully.";
                return RedirectToAction(nameof(Index));
            }

            PopulateDropdowns();
            return View(numberSequence);
        }

        // GET: NumberSequence/Edit/5
        public async Task<IActionResult> Edit(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var numberSequence = await _context.NumberSequences.FindAsync(id);
            if (numberSequence == null)
            {
                return NotFound();
            }

            PopulateDropdowns();
            return View(numberSequence);
        }

        // POST: NumberSequence/Edit/5
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Edit(int id, [Bind("SequenceID,Module,SequenceType,Prefix,Suffix,StartNumber,CurrentNumber,Increment,PadLength,DateFormat,IsActive,Description,CreatedDate,CreatedByUserID")] NumberSequence numberSequence)
        {
            if (id != numberSequence.SequenceID)
            {
                return NotFound();
            }

            if (ModelState.IsValid)
            {
                try
                {
                    // Check if sequence already exists for this module and type (excluding current record)
                    if (await _context.NumberSequences.AnyAsync(ns => ns.Module == numberSequence.Module && ns.SequenceType == numberSequence.SequenceType && ns.SequenceID != id))
                    {
                        ModelState.AddModelError("SequenceType", "Number sequence already exists for this module and type.");
                        PopulateDropdowns();
                        return View(numberSequence);
                    }

                    numberSequence.UpdatedDate = DateTime.Now;
                    _context.Update(numberSequence);
                    await _context.SaveChangesAsync();
                    
                    TempData["Success"] = "Number sequence updated successfully.";
                }
                catch (DbUpdateConcurrencyException)
                {
                    if (!NumberSequenceExists(numberSequence.SequenceID))
                    {
                        return NotFound();
                    }
                    else
                    {
                        throw;
                    }
                }
                return RedirectToAction(nameof(Index));
            }

            PopulateDropdowns();
            return View(numberSequence);
        }

        // GET: NumberSequence/Delete/5
        public async Task<IActionResult> Delete(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var numberSequence = await _context.NumberSequences
                .FirstOrDefaultAsync(m => m.SequenceID == id);
                
            if (numberSequence == null)
            {
                return NotFound();
            }

            return View(numberSequence);
        }

        // POST: NumberSequence/Delete/5
        [HttpPost, ActionName("Delete")]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> DeleteConfirmed(int id)
        {
            var numberSequence = await _context.NumberSequences.FindAsync(id);
            if (numberSequence != null)
            {
                _context.NumberSequences.Remove(numberSequence);
                await _context.SaveChangesAsync();
                
                TempData["Success"] = "Number sequence deleted successfully.";
            }

            return RedirectToAction(nameof(Index));
        }

        // POST: NumberSequence/Reset/5
        [HttpPost]
        public async Task<IActionResult> Reset(int id)
        {
            var numberSequence = await _context.NumberSequences.FindAsync(id);
            if (numberSequence == null)
            {
                return Json(new { success = false, message = "Number sequence not found." });
            }

            numberSequence.CurrentNumber = numberSequence.StartNumber;
            numberSequence.UpdatedDate = DateTime.Now;

            _context.Update(numberSequence);
            await _context.SaveChangesAsync();

            return Json(new { success = true, message = "Number sequence reset successfully." });
        }

        // POST: NumberSequence/GenerateNext
        [HttpPost]
        public async Task<IActionResult> GenerateNext(int id)
        {
            var numberSequence = await _context.NumberSequences.FindAsync(id);
            if (numberSequence == null)
            {
                return Json(new { success = false, message = "Number sequence not found." });
            }

            if (!numberSequence.IsActive)
            {
                return Json(new { success = false, message = "Number sequence is not active." });
            }

            var nextNumber = GenerateNextNumber(numberSequence);
            
            // Update current number
            numberSequence.CurrentNumber += numberSequence.Increment;
            numberSequence.UpdatedDate = DateTime.Now;
            _context.Update(numberSequence);
            await _context.SaveChangesAsync();

            return Json(new { success = true, nextNumber = nextNumber, currentNumber = numberSequence.CurrentNumber });
        }

        // GET: NumberSequence/Preview
        [HttpGet]
        public IActionResult Preview(string module, string sequenceType, string prefix, string suffix, 
            int startNumber, int increment, int? padLength, string dateFormat)
        {
            var tempSequence = new NumberSequence
            {
                Module = module,
                SequenceType = sequenceType,
                Prefix = prefix,
                Suffix = suffix,
                StartNumber = startNumber,
                CurrentNumber = startNumber,
                Increment = increment,
                PadLength = padLength ?? 0,
                DateFormat = dateFormat
            };

            var samples = GenerateSampleNumbers(tempSequence, 5);
            return Json(new { success = true, samples = samples });
        }

        // GET: NumberSequence/LoadDefaults
        public async Task<IActionResult> LoadDefaults()
        {
            await LoadDefaultNumberSequences();
            TempData["Success"] = "Default number sequences loaded successfully.";
            return RedirectToAction(nameof(Index));
        }

        private string GenerateNextNumber(NumberSequence sequence)
        {
            var number = "";

            // Add prefix
            if (!string.IsNullOrEmpty(sequence.Prefix))
            {
                number += sequence.Prefix;
            }

            // Add date format if specified
            if (!string.IsNullOrEmpty(sequence.DateFormat))
            {
                number += DateTime.Now.ToString(sequence.DateFormat);
            }

            // Add padded number
            var numberPart = sequence.CurrentNumber.ToString();
            if (sequence.PadLength > 0)
            {
                numberPart = numberPart.PadLeft(sequence.PadLength, '0');
            }
            number += numberPart;

            // Add suffix
            if (!string.IsNullOrEmpty(sequence.Suffix))
            {
                number += sequence.Suffix;
            }

            return number;
        }

        private List<string> GenerateSampleNumbers(NumberSequence sequence, int count)
        {
            var samples = new List<string>();
            var tempCurrentNumber = sequence.CurrentNumber;

            for (int i = 0; i < count; i++)
            {
                var tempSequence = new NumberSequence
                {
                    Prefix = sequence.Prefix,
                    Suffix = sequence.Suffix,
                    CurrentNumber = tempCurrentNumber,
                    PadLength = sequence.PadLength,
                    DateFormat = sequence.DateFormat
                };

                samples.Add(GenerateNextNumber(tempSequence));
                tempCurrentNumber += sequence.Increment;
            }

            return samples;
        }

        private async Task LoadDefaultNumberSequences()
        {
            var defaultSequences = new List<NumberSequence>
            {
                // Sales Module
                new NumberSequence { Module = "Sales", SequenceType = "SalesOrder", Prefix = "SO", DateFormat = "yyyyMMdd", StartNumber = 1, CurrentNumber = 1, Increment = 1, PadLength = 3, IsActive = true, Description = "Sales Order Number" },
                new NumberSequence { Module = "Sales", SequenceType = "SalesInvoice", Prefix = "SI", DateFormat = "yyyyMMdd", StartNumber = 1, CurrentNumber = 1, Increment = 1, PadLength = 3, IsActive = true, Description = "Sales Invoice Number" },
                new NumberSequence { Module = "Sales", SequenceType = "Quote", Prefix = "QT", DateFormat = "yyyyMMdd", StartNumber = 1, CurrentNumber = 1, Increment = 1, PadLength = 3, IsActive = true, Description = "Quote Number" },
                
                // Purchase Module
                new NumberSequence { Module = "Purchase", SequenceType = "PurchaseOrder", Prefix = "PO", DateFormat = "yyyyMMdd", StartNumber = 1, CurrentNumber = 1, Increment = 1, PadLength = 3, IsActive = true, Description = "Purchase Order Number" },
                new NumberSequence { Module = "Purchase", SequenceType = "GoodsReceipt", Prefix = "GR", DateFormat = "yyyyMMdd", StartNumber = 1, CurrentNumber = 1, Increment = 1, PadLength = 3, IsActive = true, Description = "Goods Receipt Number" },
                
                // Warehouse Module
                new NumberSequence { Module = "Warehouse", SequenceType = "PickingList", Prefix = "PL", DateFormat = "yyyyMMdd", StartNumber = 1, CurrentNumber = 1, Increment = 1, PadLength = 3, IsActive = true, Description = "Picking List Number" },
                new NumberSequence { Module = "Warehouse", SequenceType = "PackingList", Prefix = "PK", DateFormat = "yyyyMMdd", StartNumber = 1, CurrentNumber = 1, Increment = 1, PadLength = 3, IsActive = true, Description = "Packing List Number" },
                new NumberSequence { Module = "Warehouse", SequenceType = "InventoryAdjustment", Prefix = "IA", DateFormat = "yyyyMMdd", StartNumber = 1, CurrentNumber = 1, Increment = 1, PadLength = 3, IsActive = true, Description = "Inventory Adjustment Number" },
                
                // Shipment Module
                new NumberSequence { Module = "Shipment", SequenceType = "Shipment", Prefix = "SH", DateFormat = "yyyyMMdd", StartNumber = 1, CurrentNumber = 1, Increment = 1, PadLength = 4, IsActive = true, Description = "Shipment Number" },
                
                // Customer Module
                new NumberSequence { Module = "Customer", SequenceType = "Customer", Prefix = "C", StartNumber = 1000, CurrentNumber = 1000, Increment = 1, PadLength = 6, IsActive = true, Description = "Customer Number" },
                
                // Supplier Module
                new NumberSequence { Module = "Supplier", SequenceType = "Supplier", Prefix = "S", StartNumber = 1000, CurrentNumber = 1000, Increment = 1, PadLength = 6, IsActive = true, Description = "Supplier Number" },
                
                // Product Module
                new NumberSequence { Module = "Product", SequenceType = "Product", Prefix = "P", StartNumber = 1000, CurrentNumber = 1000, Increment = 1, PadLength = 6, IsActive = true, Description = "Product Number" },
                
                // Financial Module
                new NumberSequence { Module = "Financial", SequenceType = "Payment", Prefix = "PAY", DateFormat = "yyyyMMdd", StartNumber = 1, CurrentNumber = 1, Increment = 1, PadLength = 4, IsActive = true, Description = "Payment Number" }
            };

            // Remove existing sequences
            var existingSequences = await _context.NumberSequences.ToListAsync();
            _context.NumberSequences.RemoveRange(existingSequences);

            // Add new sequences
            foreach (var sequence in defaultSequences)
            {
                sequence.CreatedDate = DateTime.Now;
                sequence.UpdatedDate = DateTime.Now;
            }

            _context.NumberSequences.AddRange(defaultSequences);
            await _context.SaveChangesAsync();
        }

        private bool NumberSequenceExists(int id)
        {
            return _context.NumberSequences.Any(e => e.SequenceID == id);
        }

        private void PopulateDropdowns()
        {
            ViewBag.Modules = new List<SelectListItem>
            {
                new SelectListItem { Value = "Sales", Text = "Sales" },
                new SelectListItem { Value = "Purchase", Text = "Purchase" },
                new SelectListItem { Value = "Warehouse", Text = "Warehouse" },
                new SelectListItem { Value = "Shipment", Text = "Shipment" },
                new SelectListItem { Value = "Customer", Text = "Customer" },
                new SelectListItem { Value = "Supplier", Text = "Supplier" },
                new SelectListItem { Value = "Product", Text = "Product" },
                new SelectListItem { Value = "Financial", Text = "Financial" },
                new SelectListItem { Value = "Inventory", Text = "Inventory" }
            };

            ViewBag.SequenceTypes = new List<SelectListItem>
            {
                new SelectListItem { Value = "SalesOrder", Text = "Sales Order" },
                new SelectListItem { Value = "SalesInvoice", Text = "Sales Invoice" },
                new SelectListItem { Value = "Quote", Text = "Quote" },
                new SelectListItem { Value = "PurchaseOrder", Text = "Purchase Order" },
                new SelectListItem { Value = "GoodsReceipt", Text = "Goods Receipt" },
                new SelectListItem { Value = "PickingList", Text = "Picking List" },
                new SelectListItem { Value = "PackingList", Text = "Packing List" },
                new SelectListItem { Value = "Shipment", Text = "Shipment" },
                new SelectListItem { Value = "Customer", Text = "Customer" },
                new SelectListItem { Value = "Supplier", Text = "Supplier" },
                new SelectListItem { Value = "Product", Text = "Product" },
                new SelectListItem { Value = "Payment", Text = "Payment" }
            };
        }
    }
}