# Entity Framework Models - Database Table Summary

This document provides a comprehensive list of all database tables with their key columns for creating Entity Framework models in the nCarry.Web project.

## Core Business Tables

### 1. Customer Management

#### Customer
- **Primary Key**: CustomerID (int, identity)
- **Foreign Keys**: 
  - PaymentMethodID → PaymentMethod
  - SalesRepID → User
  - TerritoryID → (not defined in current schema)
  - PriceListID → CustomerPriceList
  - BlockedByUserID → User
  - CreatedByUserID → User
  - UpdatedByUserID → User
  - DeletedByUserID → User
- **Important Columns**:
  - CustomerCode (nvarchar(20), unique)
  - CustomerName (nvarchar(255))
  - TaxNumber (nvarchar(50))
  - CustomerType (nvarchar(50))
  - CustomerStatus (nvarchar(20))
  - CreditLimit (decimal(18,2))
  - OutstandingBalance (decimal(18,2))
  - IsActive (bit)
  - IsDeleted (bit)

#### CustomerAddress
- **Primary Key**: AddressID (int, identity)
- **Foreign Keys**:
  - CustomerID → Customer
- **Important Columns**:
  - AddressType (nvarchar(20))
  - Address1, Address2 (nvarchar(500))
  - City, State, PostCode, Country
  - IsDefault (bit)
  - IsActive (bit)

#### CustomerContact
- **Primary Key**: ContactID (int, identity)
- **Foreign Keys**:
  - CustomerID → Customer
  - CreatedByUserID → User
  - UpdatedByUserID → User
- **Important Columns**:
  - ContactName (nvarchar(255))
  - Email (nvarchar(255))
  - Phone, Mobile (nvarchar(50))
  - IsPrimary (bit)
  - ContactType (nvarchar(50))

#### CustomerGroup
- **Primary Key**: CustomerGroupID (int, identity)
- **Foreign Keys**:
  - ParentGroupID → CustomerGroup (self-referencing)
- **Important Columns**:
  - GroupCode (nvarchar(20), unique)
  - GroupName (nvarchar(100))
  - DiscountPercent (decimal(5,2))
  - CreditLimit (decimal(18,2))

#### CustomerGroupMember
- **Primary Key**: MemberID (int, identity)
- **Foreign Keys**:
  - CustomerGroupID → CustomerGroup
  - CustomerID → Customer
- **Important Columns**:
  - JoinDate (datetime2)
  - IsActive (bit)

#### CustomerPriceList
- **Primary Key**: PriceListID (int, identity)
- **Foreign Keys**:
  - CustomerID → Customer (nullable)
  - CustomerGroupID → CustomerGroup (nullable)
  - CreatedByUserID → User
  - UpdatedByUserID → User
- **Important Columns**:
  - PriceListCode (nvarchar(20), unique)
  - PriceListName (nvarchar(100))
  - Currency (nvarchar(3))
  - EffectiveFrom, EffectiveTo (datetime2)
  - DiscountPercent (decimal(5,2))

#### CustomerPriceListItem
- **Primary Key**: PriceListItemID (int, identity)
- **Foreign Keys**:
  - PriceListID → CustomerPriceList
  - ProductID → Product
- **Important Columns**:
  - UnitPrice (decimal(18,4))
  - MinQuantity, MaxQuantity (decimal(18,3))
  - DiscountPercent (decimal(5,2))

#### CustomerCreditHistory
- **Primary Key**: CreditHistoryID (int, identity)
- **Foreign Keys**:
  - CustomerID → Customer
  - AuthorizedByUserID → User
  - CreatedByUserID → User
- **Important Columns**:
  - ChangeType (nvarchar(50))
  - OldCreditLimit, NewCreditLimit (decimal(18,2))
  - OldCreditStatus, NewCreditStatus (nvarchar(20))

### 2. Supplier Management

#### Supplier
- **Primary Key**: SupplierID (int, identity)
- **Foreign Keys**:
  - PaymentMethodID → PaymentMethod
  - BlockedByUserID → User
  - CreatedByUserID → User
  - UpdatedByUserID → User
  - DeletedByUserID → User
- **Important Columns**:
  - SupplierCode (nvarchar(20), unique)
  - SupplierName (nvarchar(255))
  - TaxNumber (nvarchar(50))
  - SupplierType (nvarchar(50))
  - SupplierStatus (nvarchar(20))
  - Rating (int)
  - IsActive (bit)
  - IsDeleted (bit)

#### SupplierAddress
- **Primary Key**: AddressID (int, identity)
- **Foreign Keys**:
  - SupplierID → Supplier
- **Important Columns**:
  - AddressType (nvarchar(20))
  - Address1, Address2 (nvarchar(500))
  - IsDefault (bit)
  - IsActive (bit)

#### SupplierContact
- **Primary Key**: ContactID (int, identity)
- **Foreign Keys**:
  - SupplierID → Supplier
  - CreatedByUserID → User
  - UpdatedByUserID → User
- **Important Columns**:
  - ContactName (nvarchar(255))
  - Email (nvarchar(255))
  - IsPrimary (bit)
  - ContactType (nvarchar(50))

#### SupplierPriceList
- **Primary Key**: PriceListID (int, identity)
- **Foreign Keys**:
  - SupplierID → Supplier
  - CreatedByUserID → User
  - UpdatedByUserID → User
- **Important Columns**:
  - PriceListCode (nvarchar(20), unique)
  - Currency (nvarchar(3))
  - EffectiveFrom, EffectiveTo (datetime2)
  - MinOrderValue (decimal(18,2))

#### SupplierPriceListItem
- **Primary Key**: PriceListItemID (int, identity)
- **Foreign Keys**:
  - PriceListID → SupplierPriceList
  - ProductID → Product (nullable)
- **Important Columns**:
  - SupplierProductCode (nvarchar(100))
  - UnitCost (decimal(18,4))
  - MinOrderQuantity (decimal(18,3))
  - LeadTimeDays (int)

#### SupplierProduct
- **Primary Key**: SupplierProductID (int, identity)
- **Foreign Keys**:
  - SupplierID → Supplier
  - ProductID → Product
- **Important Columns**:
  - SupplierProductCode (nvarchar(100))
  - IsPreferredSupplier (bit)
  - MinOrderQuantity (decimal(18,3))
  - StandardLeadTime (int)

#### SupplierPerformance
- **Primary Key**: PerformanceID (int, identity)
- **Foreign Keys**:
  - SupplierID → Supplier
- **Important Columns**:
  - PeriodYear, PeriodMonth (int)
  - OnTimeDeliveryRate (decimal(5,2))
  - QualityRate (decimal(5,2))
  - OverallScore (decimal(5,2))

#### SupplierDocument
- **Primary Key**: DocumentID (int, identity)
- **Foreign Keys**:
  - SupplierID → Supplier
  - UploadedByUserID → User
- **Important Columns**:
  - DocumentType (nvarchar(50))
  - DocumentName (nvarchar(255))
  - ExpiryDate (datetime2)

#### SupplierAudit
- **Primary Key**: AuditID (int, identity)
- **Foreign Keys**:
  - SupplierID → Supplier
  - DocumentID → SupplierDocument
  - CreatedByUserID → User
- **Important Columns**:
  - AuditType (nvarchar(50))
  - AuditScore (decimal(5,2))
  - AuditStatus (nvarchar(20))

### 3. User Management

#### User
- **Primary Key**: UserID (int, identity)
- **Foreign Keys**:
  - CreatedByUserID → User (self-referencing)
  - UpdatedByUserID → User (self-referencing)
  - DeletedByUserID → User (self-referencing)
- **Important Columns**:
  - Username (nvarchar(50), unique)
  - Email (nvarchar(255), unique)
  - PasswordHash, PasswordSalt (nvarchar(255))
  - FirstName, LastName (nvarchar(100))
  - IsActive, IsLocked (bit)
  - TwoFactorEnabled (bit)

#### Role
- **Primary Key**: RoleID (int, identity)
- **Foreign Keys**:
  - CreatedByUserID → User
  - UpdatedByUserID → User
- **Important Columns**:
  - RoleName (nvarchar(50), unique)
  - IsSystemRole (bit)
  - IsActive (bit)

#### Permission
- **Primary Key**: PermissionID (int, identity)
- **Important Columns**:
  - PermissionCode (nvarchar(100), unique)
  - PermissionName (nvarchar(200))
  - Module (nvarchar(50))
  - Category (nvarchar(50))

#### UserRole
- **Primary Key**: UserRoleID (int, identity)
- **Foreign Keys**:
  - UserID → User
  - RoleID → Role
  - AssignedByUserID → User
- **Important Columns**:
  - AssignedDate (datetime2)
  - ExpiryDate (datetime2)
  - IsActive (bit)

#### RolePermission
- **Primary Key**: RolePermissionID (int, identity)
- **Foreign Keys**:
  - RoleID → Role
  - PermissionID → Permission
  - GrantedByUserID → User

#### UserSession
- **Primary Key**: SessionID (int, identity)
- **Foreign Keys**:
  - UserID → User
- **Important Columns**:
  - SessionToken (nvarchar(255), unique)
  - LoginDate, LogoutDate (datetime2)
  - IsActive (bit)

#### PasswordResetToken
- **Primary Key**: TokenID (int, identity)
- **Foreign Keys**:
  - UserID → User
- **Important Columns**:
  - Token (nvarchar(255), unique)
  - ExpiryDate (datetime2)
  - IsUsed (bit)

### 4. Product Management

#### UnitOfMeasure
- **Primary Key**: UOMID (int, identity)
- **Important Columns**:
  - UOMCode (nvarchar(20), unique)
  - UOMName (nvarchar(50))
  - UOMType (nvarchar(20))
  - BaseUnit (bit)
  - ConversionFactor (decimal(18,6))

#### ProductCategory
- **Primary Key**: CategoryID (int, identity)
- **Foreign Keys**:
  - ParentCategoryID → ProductCategory (self-referencing)
  - CreatedByUserID → User
  - UpdatedByUserID → User
- **Important Columns**:
  - CategoryCode (nvarchar(20), unique)
  - CategoryName (nvarchar(100))
  - CategoryPath (nvarchar(500))
  - Level (int)

#### Product
- **Primary Key**: ProductID (int, identity)
- **Foreign Keys**:
  - CategoryID → ProductCategory
  - WeightUOMID → UnitOfMeasure
  - DimensionUOMID → UnitOfMeasure
  - VolumeUOMID → UnitOfMeasure
  - InventoryUOMID → UnitOfMeasure
  - PurchaseUOMID → UnitOfMeasure
  - SalesUOMID → UnitOfMeasure
  - TaxCodeID → TaxCode
  - CreatedByUserID → User
  - UpdatedByUserID → User
  - DeletedByUserID → User
- **Important Columns**:
  - ProductCode (nvarchar(50), unique)
  - ProductName (nvarchar(255))
  - ProductType (nvarchar(20))
  - Status (nvarchar(20))
  - StandardCost, ListPrice, SellPrice (decimal(18,4))
  - MinStockLevel, MaxStockLevel (decimal(18,3))
  - HasVariants, IsKit (bit)

#### ProductVariant
- **Primary Key**: VariantID (int, identity)
- **Foreign Keys**:
  - ProductID → Product
- **Important Columns**:
  - VariantCode (nvarchar(50), unique)
  - VariantName (nvarchar(255))
  - VariantType, VariantValue (nvarchar(50/100))

#### Barcode
- **Primary Key**: BarcodeID (int, identity)
- **Foreign Keys**:
  - ProductID → Product
  - VariantID → ProductVariant
- **Important Columns**:
  - BarcodeNumber (nvarchar(100), unique)
  - BarcodeType (nvarchar(20))
  - IsPrimary (bit)

#### ProductImage
- **Primary Key**: ImageID (int, identity)
- **Foreign Keys**:
  - ProductID → Product
  - VariantID → ProductVariant
  - CreatedByUserID → User
- **Important Columns**:
  - ImageName (nvarchar(255))
  - ImageType (nvarchar(20))
  - IsPrimary (bit)

#### ProductPricing
- **Primary Key**: PricingID (int, identity)
- **Foreign Keys**:
  - ProductID → Product
  - VariantID → ProductVariant
  - CustomerGroupID → CustomerGroup
  - CreatedByUserID → User
  - UpdatedByUserID → User
- **Important Columns**:
  - PriceType (nvarchar(50))
  - MinQuantity, MaxQuantity (decimal(18,3))
  - UnitPrice (decimal(18,4))
  - EffectiveFrom, EffectiveTo (datetime2)

#### ProductPriceHistory
- **Primary Key**: HistoryID (int, identity)
- **Foreign Keys**:
  - ProductID → Product
  - ApprovedByUserID → User
  - CreatedByUserID → User
- **Important Columns**:
  - ChangeDate (datetime2)
  - OldPrice, NewPrice (decimal(18,4))

#### ProductKitComponent
- **Primary Key**: ComponentID (int, identity)
- **Foreign Keys**:
  - KitProductID → Product
  - ComponentProductID → Product
- **Important Columns**:
  - Quantity (decimal(18,3))
  - IsOptional (bit)

#### ProductUOMConversion
- **Primary Key**: ConversionID (int, identity)
- **Foreign Keys**:
  - ProductID → Product
  - FromUOMID → UnitOfMeasure
  - ToUOMID → UnitOfMeasure
- **Important Columns**:
  - ConversionFactor (decimal(18,6))

### 5. Warehouse & Inventory Management

#### Warehouse
- **Primary Key**: WarehouseID (int, identity)
- **Foreign Keys**:
  - CreatedByUserID → User
  - UpdatedByUserID → User
- **Important Columns**:
  - WarehouseCode (nvarchar(20), unique)
  - WarehouseName (nvarchar(100))
  - WarehouseType (nvarchar(20))
  - IsActive, IsDefault (bit)

#### WarehouseLocation
- **Primary Key**: LocationID (int, identity)
- **Foreign Keys**:
  - WarehouseID → Warehouse
- **Important Columns**:
  - LocationCode (nvarchar(50))
  - LocationType (nvarchar(20))
  - Capacity (decimal(18,3))
  - IsActive, IsDefault (bit)

#### Inventory
- **Primary Key**: InventoryID (int, identity)
- **Foreign Keys**:
  - ProductID → Product
  - VariantID → ProductVariant
  - WarehouseID → Warehouse
  - LocationID → WarehouseLocation
- **Important Columns**:
  - BatchNumber (nvarchar(50))
  - SerialNumber (nvarchar(100))
  - QuantityOnHand, QuantityAvailable, QuantityReserved (decimal(18,3))
  - AverageCost (decimal(18,4))

#### InventoryTransaction
- **Primary Key**: TransactionID (bigint, identity)
- **Foreign Keys**:
  - ProductID → Product
  - VariantID → ProductVariant
  - WarehouseID → Warehouse
  - LocationID → WarehouseLocation
  - CreatedByUserID → User
- **Important Columns**:
  - TransactionNumber (nvarchar(50), unique)
  - TransactionType (nvarchar(50))
  - Quantity (decimal(18,3))
  - UnitCost (decimal(18,4))

#### StockTransfer
- **Primary Key**: TransferID (int, identity)
- **Foreign Keys**:
  - FromWarehouseID → Warehouse
  - ToWarehouseID → Warehouse
  - CreatedByUserID → User
  - UpdatedByUserID → User
- **Important Columns**:
  - TransferNumber (nvarchar(50), unique)
  - TransferStatus (nvarchar(20))
  - TransferDate (datetime2)

#### StockTransferDetail
- **Primary Key**: TransferDetailID (int, identity)
- **Foreign Keys**:
  - TransferID → StockTransfer
  - ProductID → Product
  - VariantID → ProductVariant
  - FromLocationID → WarehouseLocation
  - ToLocationID → WarehouseLocation
- **Important Columns**:
  - RequestedQuantity, ShippedQuantity, ReceivedQuantity (decimal(18,3))

#### StockAdjustment
- **Primary Key**: AdjustmentID (int, identity)
- **Foreign Keys**:
  - WarehouseID → Warehouse
  - ApprovedByUserID → User
  - CreatedByUserID → User
  - UpdatedByUserID → User
- **Important Columns**:
  - AdjustmentNumber (nvarchar(50), unique)
  - AdjustmentType (nvarchar(50))
  - Status (nvarchar(20))

#### StockAdjustmentDetail
- **Primary Key**: AdjustmentDetailID (int, identity)
- **Foreign Keys**:
  - AdjustmentID → StockAdjustment
  - ProductID → Product
  - VariantID → ProductVariant
  - LocationID → WarehouseLocation
- **Important Columns**:
  - CurrentQuantity, AdjustedQuantity, VarianceQuantity (decimal(18,3))

#### StockReservation
- **Primary Key**: ReservationID (int, identity)
- **Foreign Keys**:
  - ProductID → Product
  - VariantID → ProductVariant
  - WarehouseID → Warehouse
  - LocationID → WarehouseLocation
  - CreatedByUserID → User
- **Important Columns**:
  - ReservationNumber (nvarchar(50), unique)
  - Quantity (decimal(18,3))
  - ReservationType (nvarchar(50))
  - Status (nvarchar(20))

#### CycleCount
- **Primary Key**: CycleCountID (int, identity)
- **Foreign Keys**:
  - WarehouseID → Warehouse
  - CreatedByUserID → User
- **Important Columns**:
  - CycleCountNumber (nvarchar(50), unique)
  - CountType (nvarchar(50))
  - Status (nvarchar(20))

#### CycleCountDetail
- **Primary Key**: CountDetailID (int, identity)
- **Foreign Keys**:
  - CycleCountID → CycleCount
  - ProductID → Product
  - VariantID → ProductVariant
  - LocationID → WarehouseLocation
  - CountedByUserID → User
- **Important Columns**:
  - SystemQuantity, CountedQuantity, VarianceQuantity (decimal(18,3))

#### ProductBatch
- **Primary Key**: BatchID (int, identity)
- **Foreign Keys**:
  - ProductID → Product
  - SupplierID → Supplier
  - CreatedByUserID → User
- **Important Columns**:
  - BatchNumber (nvarchar(50), unique)
  - ExpiryDate (date)
  - InitialQuantity, RemainingQuantity (decimal(18,3))
  - Status (nvarchar(20))

#### SerialNumber
- **Primary Key**: SerialID (int, identity)
- **Foreign Keys**:
  - ProductID → Product
  - VariantID → ProductVariant
  - BatchID → ProductBatch
  - WarehouseID → Warehouse
  - LocationID → WarehouseLocation
  - CustomerID → Customer
- **Important Columns**:
  - SerialNumber (nvarchar(100), unique)
  - Status (nvarchar(20))
  - WarrantyEndDate (date)

### 6. Sales Management

#### Quote
- **Primary Key**: QuoteID (int, identity)
- **Foreign Keys**:
  - CustomerID → Customer
  - SalesRepID → User
  - BillingAddressID → CustomerAddress
  - ShippingAddressID → CustomerAddress
  - ConvertedToOrderID → SalesOrder
  - CreatedByUserID → User
  - UpdatedByUserID → User
- **Important Columns**:
  - QuoteNumber (nvarchar(50), unique)
  - QuoteDate (datetime2)
  - QuoteStatus (nvarchar(20))
  - ValidFrom, ValidTo (datetime2)
  - TotalAmount (decimal(18,2))

#### QuoteItem
- **Primary Key**: QuoteItemID (int, identity)
- **Foreign Keys**:
  - QuoteID → Quote
  - ProductID → Product
  - VariantID → ProductVariant
  - UOMID → UnitOfMeasure
- **Important Columns**:
  - LineNumber (int)
  - Quantity (decimal(18,3))
  - UnitPrice (decimal(18,4))
  - LineTotal (decimal(18,2))

#### SalesOrder
- **Primary Key**: OrderID (int, identity)
- **Foreign Keys**:
  - CustomerID → Customer
  - QuoteID → Quote
  - SalesRepID → User
  - BillingAddressID → CustomerAddress
  - ShippingAddressID → CustomerAddress
  - WarehouseID → Warehouse
  - CarrierID → Carrier
  - CreatedByUserID → User
  - UpdatedByUserID → User
  - DeletedByUserID → User
- **Important Columns**:
  - OrderNumber (nvarchar(50), unique)
  - OrderDate (datetime2)
  - OrderStatus (nvarchar(20))
  - OrderType (nvarchar(20))
  - TotalAmount (decimal(18,2))
  - PaidAmount, BalanceAmount (decimal(18,2))

#### SalesOrderItem
- **Primary Key**: OrderItemID (int, identity)
- **Foreign Keys**:
  - OrderID → SalesOrder
  - ProductID → Product
  - VariantID → ProductVariant
  - UOMID → UnitOfMeasure
  - WarehouseID → Warehouse
  - LocationID → WarehouseLocation
- **Important Columns**:
  - LineNumber (int)
  - OrderedQuantity, ShippedQuantity (decimal(18,3))
  - UnitPrice (decimal(18,4))
  - LineTotal (decimal(18,2))
  - ItemStatus (nvarchar(20))

#### SalesInvoice
- **Primary Key**: InvoiceID (int, identity)
- **Foreign Keys**:
  - CustomerID → Customer
  - OrderID → SalesOrder
  - BillingAddressID → CustomerAddress
  - ShippingAddressID → CustomerAddress
  - PostedByUserID → User
  - CreatedByUserID → User
  - UpdatedByUserID → User
  - DeletedByUserID → User
- **Important Columns**:
  - InvoiceNumber (nvarchar(50), unique)
  - InvoiceDate (datetime2)
  - InvoiceType (nvarchar(20))
  - InvoiceStatus (nvarchar(20))
  - TotalAmount (decimal(18,2))
  - PaidAmount, BalanceAmount (decimal(18,2))
  - DueDate (datetime2)

#### SalesInvoiceItem
- **Primary Key**: InvoiceItemID (int, identity)
- **Foreign Keys**:
  - InvoiceID → SalesInvoice
  - OrderItemID → SalesOrderItem
  - ProductID → Product
  - VariantID → ProductVariant
  - UOMID → UnitOfMeasure
- **Important Columns**:
  - LineNumber (int)
  - Quantity (decimal(18,3))
  - UnitPrice (decimal(18,4))
  - LineTotal (decimal(18,2))

#### CreditNote
- **Primary Key**: CreditNoteID (int, identity)
- **Foreign Keys**:
  - CustomerID → Customer
  - InvoiceID → SalesInvoice
  - ApprovedByUserID → User
  - CreatedByUserID → User
  - UpdatedByUserID → User
- **Important Columns**:
  - CreditNoteNumber (nvarchar(50), unique)
  - CreditNoteDate (datetime2)
  - CreditNoteStatus (nvarchar(20))
  - CreditReason (nvarchar(100))
  - TotalAmount (decimal(18,2))

#### CreditNoteItem
- **Primary Key**: CreditNoteItemID (int, identity)
- **Foreign Keys**:
  - CreditNoteID → CreditNote
  - InvoiceItemID → SalesInvoiceItem
  - ProductID → Product
  - VariantID → ProductVariant
  - UOMID → UnitOfMeasure
- **Important Columns**:
  - LineNumber (int)
  - Quantity (decimal(18,3))
  - UnitPrice (decimal(18,4))
  - LineTotal (decimal(18,2))

#### Commission
- **Primary Key**: CommissionID (int, identity)
- **Foreign Keys**:
  - SalesRepID → User
  - OrderID → SalesOrder
  - InvoiceID → SalesInvoice
  - CreatedByUserID → User
- **Important Columns**:
  - PeriodYear, PeriodMonth (int)
  - CommissionType (nvarchar(20))
  - CommissionRate (decimal(5,2))
  - CommissionAmount (decimal(18,2))
  - Status (nvarchar(20))

#### SalesTarget
- **Primary Key**: TargetID (int, identity)
- **Foreign Keys**:
  - SalesRepID → User
  - TerritoryID → (not defined in current schema)
  - CreatedByUserID → User
- **Important Columns**:
  - TargetYear, TargetMonth (int)
  - TargetType (nvarchar(20))
  - TargetAmount (decimal(18,2))
  - AchievedAmount (decimal(18,2))

### 7. Purchase Management

#### PurchaseRequisition
- **Primary Key**: RequisitionID (int, identity)
- **Foreign Keys**:
  - RequestedByUserID → User
  - ApprovedByUserID → User
  - CreatedByUserID → User
  - UpdatedByUserID → User
- **Important Columns**:
  - RequisitionNumber (nvarchar(50), unique)
  - RequisitionDate (datetime2)
  - RequisitionStatus (nvarchar(20))
  - Priority (nvarchar(20))
  - TotalAmount (decimal(18,2))

#### PurchaseRequisitionItem
- **Primary Key**: RequisitionItemID (int, identity)
- **Foreign Keys**:
  - RequisitionID → PurchaseRequisition
  - ProductID → Product
  - UOMID → UnitOfMeasure
  - SuggestedSupplierID → Supplier
- **Important Columns**:
  - LineNumber (int)
  - Quantity (decimal(18,3))
  - EstimatedUnitPrice (decimal(18,4))

#### PurchaseOrder
- **Primary Key**: PurchaseOrderID (int, identity)
- **Foreign Keys**:
  - SupplierID → Supplier
  - RequisitionID → PurchaseRequisition
  - BuyerID → User
  - DeliveryWarehouseID → Warehouse
  - DeliveryAddressID → SupplierAddress
  - ApprovedByUserID → User
  - CreatedByUserID → User
  - UpdatedByUserID → User
  - DeletedByUserID → User
- **Important Columns**:
  - PurchaseOrderNumber (nvarchar(50), unique)
  - OrderDate (datetime2)
  - OrderStatus (nvarchar(20))
  - OrderType (nvarchar(20))
  - TotalAmount (decimal(18,2))
  - ApprovalStatus (nvarchar(20))

#### PurchaseOrderItem
- **Primary Key**: PurchaseOrderItemID (int, identity)
- **Foreign Keys**:
  - PurchaseOrderID → PurchaseOrder
  - ProductID → Product
  - UOMID → UnitOfMeasure
- **Important Columns**:
  - LineNumber (int)
  - OrderedQuantity, ReceivedQuantity, InvoicedQuantity (decimal(18,3))
  - UnitPrice (decimal(18,4))
  - LineTotal (decimal(18,2))
  - ItemStatus (nvarchar(20))

#### GoodsReceipt
- **Primary Key**: ReceiptID (int, identity)
- **Foreign Keys**:
  - SupplierID → Supplier
  - PurchaseOrderID → PurchaseOrder
  - WarehouseID → Warehouse
  - ReceivedByUserID → User
  - QualityCheckByUserID → User
  - PostedByUserID → User
  - CreatedByUserID → User
  - UpdatedByUserID → User
- **Important Columns**:
  - ReceiptNumber (nvarchar(50), unique)
  - ReceiptDate (datetime2)
  - ReceiptType (nvarchar(20))
  - ReceiptStatus (nvarchar(20))
  - QualityCheckStatus (nvarchar(20))

#### GoodsReceiptItem
- **Primary Key**: ReceiptItemID (int, identity)
- **Foreign Keys**:
  - ReceiptID → GoodsReceipt
  - PurchaseOrderItemID → PurchaseOrderItem
  - ProductID → Product
  - UOMID → UnitOfMeasure
  - LocationID → WarehouseLocation
- **Important Columns**:
  - LineNumber (int)
  - ReceivedQuantity, AcceptedQuantity, RejectedQuantity (decimal(18,3))
  - UnitCost (decimal(18,4))
  - QualityStatus (nvarchar(20))

#### PurchaseInvoice
- **Primary Key**: InvoiceID (int, identity)
- **Foreign Keys**:
  - SupplierID → Supplier
  - PurchaseOrderID → PurchaseOrder
  - ApprovedByUserID → User
  - PostedByUserID → User
  - CreatedByUserID → User
  - UpdatedByUserID → User
  - DeletedByUserID → User
- **Important Columns**:
  - InvoiceNumber (nvarchar(50), unique)
  - SupplierInvoiceNumber (nvarchar(100))
  - InvoiceDate (datetime2)
  - InvoiceType (nvarchar(20))
  - InvoiceStatus (nvarchar(20))
  - TotalAmount (decimal(18,2))
  - DueDate (datetime2)

#### PurchaseInvoiceItem
- **Primary Key**: InvoiceItemID (int, identity)
- **Foreign Keys**:
  - InvoiceID → PurchaseInvoice
  - PurchaseOrderItemID → PurchaseOrderItem
  - GoodsReceiptItemID → GoodsReceiptItem
  - ProductID → Product
  - UOMID → UnitOfMeasure
- **Important Columns**:
  - LineNumber (int)
  - Quantity (decimal(18,3))
  - UnitPrice (decimal(18,4))
  - LineTotal (decimal(18,2))

#### PurchaseReturn
- **Primary Key**: ReturnID (int, identity)
- **Foreign Keys**:
  - SupplierID → Supplier
  - GoodsReceiptID → GoodsReceipt
  - WarehouseID → Warehouse
  - CreatedByUserID → User
  - UpdatedByUserID → User
- **Important Columns**:
  - ReturnNumber (nvarchar(50), unique)
  - ReturnDate (datetime2)
  - ReturnStatus (nvarchar(20))
  - ReturnReason (nvarchar(100))
  - TotalAmount (decimal(18,2))

#### PurchaseReturnItem
- **Primary Key**: ReturnItemID (int, identity)
- **Foreign Keys**:
  - ReturnID → PurchaseReturn
  - GoodsReceiptItemID → GoodsReceiptItem
  - ProductID → Product
  - UOMID → UnitOfMeasure
- **Important Columns**:
  - LineNumber (int)
  - ReturnQuantity (decimal(18,3))
  - UnitCost (decimal(18,4))
  - LineTotal (decimal(18,2))

### 8. Financial Management

#### Currency
- **Primary Key**: CurrencyID (int, identity)
- **Important Columns**:
  - CurrencyCode (nvarchar(3), unique)
  - CurrencyName (nvarchar(50))
  - IsBaseCurrency (bit)

#### ExchangeRate
- **Primary Key**: ExchangeRateID (int, identity)
- **Foreign Keys**:
  - FromCurrencyID → Currency
  - ToCurrencyID → Currency
  - CreatedByUserID → User
- **Important Columns**:
  - RateDate (date)
  - ExchangeRate (decimal(18,6))

#### PaymentMethod
- **Primary Key**: PaymentMethodID (int, identity)
- **Important Columns**:
  - MethodCode (nvarchar(20), unique)
  - MethodName (nvarchar(50))
  - MethodType (nvarchar(20))

#### Bank
- **Primary Key**: BankID (int, identity)
- **Foreign Keys**:
  - CurrencyID → Currency
  - CreatedByUserID → User
  - UpdatedByUserID → User
- **Important Columns**:
  - BankCode (nvarchar(20), unique)
  - BankName (nvarchar(100))
  - AccountNumber (nvarchar(50))
  - CurrentBalance (decimal(18,2))

#### Payment
- **Primary Key**: PaymentID (int, identity)
- **Foreign Keys**:
  - PaymentMethodID → PaymentMethod
  - CustomerID → Customer
  - SupplierID → Supplier
  - CurrencyID → Currency
  - BankID → Bank
  - CreatedByUserID → User
  - UpdatedByUserID → User
  - DeletedByUserID → User
- **Important Columns**:
  - PaymentNumber (nvarchar(50), unique)
  - PaymentDate (datetime2)
  - PaymentType (nvarchar(20))
  - PaymentStatus (nvarchar(20))
  - Amount (decimal(18,2))

#### PaymentAllocation
- **Primary Key**: AllocationID (int, identity)
- **Foreign Keys**:
  - PaymentID → Payment
  - CreatedByUserID → User
- **Important Columns**:
  - InvoiceType (nvarchar(20))
  - InvoiceID (int)
  - AllocatedAmount (decimal(18,2))

#### BankTransaction
- **Primary Key**: TransactionID (int, identity)
- **Foreign Keys**:
  - BankID → Bank
  - PaymentID → Payment
  - ReconcileByUserID → User
  - CreatedByUserID → User
- **Important Columns**:
  - TransactionDate (datetime2)
  - TransactionType (nvarchar(20))
  - DebitAmount, CreditAmount (decimal(18,2))
  - ReconcileStatus (nvarchar(20))

#### TaxCode
- **Primary Key**: TaxCodeID (int, identity)
- **Important Columns**:
  - TaxCode (nvarchar(20), unique)
  - TaxName (nvarchar(100))
  - TaxRate (decimal(5,2))

#### TaxTransaction
- **Primary Key**: TaxTransactionID (int, identity)
- **Foreign Keys**:
  - TaxCodeID → TaxCode
- **Important Columns**:
  - TransactionDate (datetime2)
  - TransactionType (nvarchar(50))
  - TaxableAmount, TaxAmount (decimal(18,2))

#### ChartOfAccounts
- **Primary Key**: AccountID (int, identity)
- **Foreign Keys**:
  - ParentAccountID → ChartOfAccounts (self-referencing)
  - CurrencyID → Currency
  - CreatedByUserID → User
  - UpdatedByUserID → User
- **Important Columns**:
  - AccountCode (nvarchar(20), unique)
  - AccountName (nvarchar(100))
  - AccountType (nvarchar(20))

#### GeneralLedger
- **Primary Key**: GLID (bigint, identity)
- **Foreign Keys**:
  - AccountID → ChartOfAccounts
  - CurrencyID → Currency
  - CostCenterID → CostCenter
  - ProjectID → (not defined in current schema)
  - ReversalGLID → GeneralLedger (self-referencing)
  - CreatedByUserID → User
- **Important Columns**:
  - PostingDate (datetime2)
  - TransactionType (nvarchar(50))
  - DebitAmount, CreditAmount (decimal(18,2))

#### CostCenter
- **Primary Key**: CostCenterID (int, identity)
- **Foreign Keys**:
  - ParentCostCenterID → CostCenter (self-referencing)
- **Important Columns**:
  - CostCenterCode (nvarchar(20), unique)
  - CostCenterName (nvarchar(100))

#### Budget
- **Primary Key**: BudgetID (int, identity)
- **Foreign Keys**:
  - AccountID → ChartOfAccounts
  - CostCenterID → CostCenter
  - CreatedByUserID → User
  - UpdatedByUserID → User
- **Important Columns**:
  - BudgetYear (int)
  - BudgetType (nvarchar(20))
  - Period1-Period12 (decimal(18,2))

#### Expense
- **Primary Key**: ExpenseID (int, identity)
- **Foreign Keys**:
  - EmployeeID → User
  - SupplierID → Supplier
  - CurrencyID → Currency
  - PaymentMethodID → PaymentMethod
  - PaymentID → Payment
  - AccountID → ChartOfAccounts
  - CostCenterID → CostCenter
  - ProjectID → (not defined in current schema)
  - ApprovedByUserID → User
  - CreatedByUserID → User
  - UpdatedByUserID → User
- **Important Columns**:
  - ExpenseNumber (nvarchar(50), unique)
  - ExpenseDate (datetime2)
  - ExpenseType (nvarchar(50))
  - Amount, TotalAmount (decimal(18,2))

#### CashRegister
- **Primary Key**: RegisterID (int, identity)
- **Foreign Keys**:
  - CurrencyID → Currency
  - LastClosedByUserID → User
- **Important Columns**:
  - RegisterCode (nvarchar(20), unique)
  - RegisterName (nvarchar(100))
  - CurrentBalance (decimal(18,2))

#### Receipt
- **Primary Key**: ReceiptID (int, identity)
- **Foreign Keys**:
  - RegisterID → CashRegister
  - CustomerID → Customer
  - PaymentID → Payment
  - CreatedByUserID → User
- **Important Columns**:
  - ReceiptNumber (nvarchar(50), unique)
  - ReceiptDate (datetime2)
  - Amount (decimal(18,2))

### 9. Logistics & Delivery Management

#### Carrier
- **Primary Key**: CarrierID (int, identity)
- **Important Columns**:
  - CarrierCode (nvarchar(20), unique)
  - CarrierName (nvarchar(100))
  - CarrierType (nvarchar(20))

#### CarrierService
- **Primary Key**: ServiceID (int, identity)
- **Foreign Keys**:
  - CarrierID → Carrier
- **Important Columns**:
  - ServiceCode (nvarchar(50))
  - ServiceName (nvarchar(100))
  - ServiceType (nvarchar(20))
  - TransitDays (int)

#### FreightRate
- **Primary Key**: RateID (int, identity)
- **Foreign Keys**:
  - CarrierID → Carrier
  - ServiceID → CarrierService
- **Important Columns**:
  - RateType (nvarchar(20))
  - RatePerKg (decimal(18,4))
  - FlatRate (decimal(18,2))

#### DeliveryZone
- **Primary Key**: ZoneID (int, identity)
- **Important Columns**:
  - ZoneCode (nvarchar(20), unique)
  - ZoneName (nvarchar(100))
  - DeliveryDays (int)

#### DeliveryRoute
- **Primary Key**: RouteID (int, identity)
- **Important Columns**:
  - RouteCode (nvarchar(20), unique)
  - RouteName (nvarchar(100))
  - RouteType (nvarchar(20))

#### RouteZone
- **Primary Key**: RouteZoneID (int, identity)
- **Foreign Keys**:
  - RouteID → DeliveryRoute
  - ZoneID → DeliveryZone
- **Important Columns**:
  - SequenceOrder (int)

#### PickingList
- **Primary Key**: PickingListID (int, identity)
- **Foreign Keys**:
  - WarehouseID → Warehouse
  - AssignedToUserID → User
  - CreatedByUserID → User
  - UpdatedByUserID → User
- **Important Columns**:
  - PickingListNumber (nvarchar(50), unique)
  - PickingType (nvarchar(20))
  - Status (nvarchar(20))

#### PickingListItem
- **Primary Key**: PickingItemID (int, identity)
- **Foreign Keys**:
  - PickingListID → PickingList
  - OrderID → SalesOrder
  - OrderItemID → SalesOrderItem
  - ProductID → Product
  - VariantID → ProductVariant
  - LocationID → WarehouseLocation
  - PickedByUserID → User
- **Important Columns**:
  - QuantityToPick, QuantityPicked (decimal(18,3))
  - Status (nvarchar(20))

#### PackingSlip
- **Primary Key**: PackingSlipID (int, identity)
- **Foreign Keys**:
  - OrderID → SalesOrder
  - PickingListID → PickingList
  - WarehouseID → Warehouse
  - PackedByUserID → User
  - CreatedByUserID → User
- **Important Columns**:
  - PackingSlipNumber (nvarchar(50), unique)
  - PackingDate (datetime2)
  - TotalBoxes (int)
  - Status (nvarchar(20))

#### PackingSlipItem
- **Primary Key**: PackingItemID (int, identity)
- **Foreign Keys**:
  - PackingSlipID → PackingSlip
  - ProductID → Product
  - VariantID → ProductVariant
  - OrderItemID → SalesOrderItem
- **Important Columns**:
  - BoxNumber (int)
  - Quantity (decimal(18,3))

#### Shipment
- **Primary Key**: ShipmentID (int, identity)
- **Foreign Keys**:
  - CarrierID → Carrier
  - ServiceID → CarrierService
  - RouteID → DeliveryRoute
  - ShipFromWarehouseID → Warehouse
  - CreatedByUserID → User
  - UpdatedByUserID → User
- **Important Columns**:
  - ShipmentNumber (nvarchar(50), unique)
  - ShipmentDate (datetime2)
  - ShipmentType (nvarchar(20))
  - ShipmentStatus (nvarchar(20))
  - TrackingNumber (nvarchar(100))

#### ShipmentItem
- **Primary Key**: ShipmentItemID (int, identity)
- **Foreign Keys**:
  - ShipmentID → Shipment
  - OrderID → SalesOrder
  - PackingSlipID → PackingSlip
  - TransferID → StockTransfer
- **Important Columns**:
  - PackageNumber (nvarchar(50))
  - PackageWeight (decimal(18,3))

#### ShipmentTracking
- **Primary Key**: TrackingID (int, identity)
- **Foreign Keys**:
  - ShipmentID → Shipment
- **Important Columns**:
  - TrackingDate (datetime2)
  - Status (nvarchar(100))
  - Location (nvarchar(255))

#### Document
- **Primary Key**: DocumentID (int, identity)
- **Foreign Keys**:
  - CreatedByUserID → User
- **Important Columns**:
  - DocumentType (nvarchar(50))
  - DocumentNumber (nvarchar(50))
  - EntityType (nvarchar(50))
  - EntityID (int)

### 10. System Configuration

#### Company
- **Primary Key**: CompanyID (int, identity)
- **Foreign Keys**:
  - BaseCurrencyID → Currency
- **Important Columns**:
  - CompanyCode (nvarchar(20), unique)
  - CompanyName (nvarchar(255))
  - TaxNumber (nvarchar(50))

#### Branch
- **Primary Key**: BranchID (int, identity)
- **Foreign Keys**:
  - CompanyID → Company
  - WarehouseID → Warehouse
- **Important Columns**:
  - BranchCode (nvarchar(20))
  - BranchName (nvarchar(100))
  - BranchType (nvarchar(20))

#### SystemConfiguration
- **Primary Key**: ConfigID (int, identity)
- **Foreign Keys**:
  - CompanyID → Company
  - CreatedByUserID → User
  - UpdatedByUserID → User
- **Important Columns**:
  - Module (nvarchar(50))
  - ConfigKey (nvarchar(100))
  - ConfigValue (nvarchar(max))

#### NumberSequence
- **Primary Key**: SequenceID (int, identity)
- **Foreign Keys**:
  - CompanyID → Company
- **Important Columns**:
  - SequenceType (nvarchar(50))
  - SequenceFormat (nvarchar(100))
  - CurrentNumber (int)

#### EmailTemplate
- **Primary Key**: TemplateID (int, identity)
- **Foreign Keys**:
  - CompanyID → Company
  - CreatedByUserID → User
  - UpdatedByUserID → User
- **Important Columns**:
  - TemplateCode (nvarchar(50))
  - TemplateName (nvarchar(100))
  - TemplateType (nvarchar(50))

#### NotificationTemplate
- **Primary Key**: NotificationID (int, identity)
- **Important Columns**:
  - NotificationCode (nvarchar(50), unique)
  - NotificationType (nvarchar(50))
  - EventType (nvarchar(100))

#### ReportTemplate
- **Primary Key**: ReportID (int, identity)
- **Foreign Keys**:
  - CreatedByUserID → User
  - UpdatedByUserID → User
- **Important Columns**:
  - ReportCode (nvarchar(50), unique)
  - ReportName (nvarchar(100))
  - ReportCategory (nvarchar(50))

#### Dashboard
- **Primary Key**: DashboardID (int, identity)
- **Foreign Keys**:
  - UserID → User
  - CreatedByUserID → User
- **Important Columns**:
  - DashboardName (nvarchar(100))
  - IsDefault (bit)

#### KPIDefinition
- **Primary Key**: KPIID (int, identity)
- **Important Columns**:
  - KPICode (nvarchar(50), unique)
  - KPIName (nvarchar(100))
  - Category (nvarchar(50))

#### APIKey
- **Primary Key**: APIKeyID (int, identity)
- **Foreign Keys**:
  - CreatedByUserID → User
- **Important Columns**:
  - APIKey (nvarchar(255), unique)
  - ClientName (nvarchar(100))

#### IntegrationLog
- **Primary Key**: LogID (bigint, identity)
- **Foreign Keys**:
  - APIKeyID → APIKey
- **Important Columns**:
  - IntegrationType (nvarchar(50))
  - Direction (nvarchar(10))
  - Status (nvarchar(20))

#### WebhookConfiguration
- **Primary Key**: WebhookID (int, identity)
- **Foreign Keys**:
  - CreatedByUserID → User
- **Important Columns**:
  - WebhookName (nvarchar(100))
  - EventType (nvarchar(100))
  - URL (nvarchar(500))

#### EDIConfiguration
- **Primary Key**: EDIConfigID (int, identity)
- **Important Columns**:
  - PartnerType (nvarchar(20))
  - PartnerID (int)
  - DocumentType (nvarchar(50))

#### DataExport
- **Primary Key**: ExportID (int, identity)
- **Foreign Keys**:
  - RequestedByUserID → User
- **Important Columns**:
  - ExportName (nvarchar(100))
  - ExportType (nvarchar(50))
  - Status (nvarchar(20))

#### SavedSearch
- **Primary Key**: SearchID (int, identity)
- **Foreign Keys**:
  - UserID → User
- **Important Columns**:
  - SearchName (nvarchar(100))
  - EntityType (nvarchar(50))

## Additional Notes

### Audit/Log Tables (Excluded from main business entities)
- AuditLog: System-wide audit trail

### Views (Not tables, but useful for reporting)
- CustomerSalesHistory: Aggregated customer sales data

### Missing Foreign Key References
Some foreign key references point to tables not defined in the current schema:
- TerritoryID (referenced in Customer and SalesTarget tables)
- ProjectID (referenced in GeneralLedger and Expense tables)

### Common Patterns
- Most tables include IsActive, CreatedDate, UpdatedDate fields
- Many tables have soft delete support (IsDeleted, DeletedDate, DeletedByUserID)
- User tracking is common (CreatedByUserID, UpdatedByUserID)
- Unique codes/numbers are used for business entities
- Status fields are common for workflow management

### Recommended Entity Framework Considerations
1. Use navigation properties for all foreign key relationships
2. Configure cascade delete behavior carefully
3. Consider using views for complex reporting scenarios
4. Implement soft delete globally using query filters
5. Use value converters for enum-like string fields (Status, Type fields)
6. Configure decimal precision for financial fields
7. Set up proper indexes as defined in the SQL scripts
8. Consider using table-per-hierarchy (TPH) for similar entities (e.g., different address types)