# 🔍 ASP.NET Core Module Testing Prompt Template

## 📋 Görev Tanımı:
**[MODULE_NAME]** modülü için sistematik test süreci gerçekleştir. Aşağıdaki adımları takip ederek modülü tamamen test et ve checklist oluştur.

## 📌 Test Kuralları:
1. **Onay Al:** Her işlem öncesi "devam edeyim mi?" diye sor
2. **Sistematik İlerle:** Hiçbir adımı atlama, sırasıyla ilerle
3. **Checklist Güncellemeleri:** Her test sonrası markdown checklist'i güncelle
4. **Hata Raporla:** Kritik hata bulursan işlemi durdur ve bildir
5. **Detaylı Dokümante Et:** Her adımın sonucunu açıkça raporla

## 🎯 Test Süreci Adımları:

### **Adım 1: Modül Analizi**
1. `C:\source\nCarry\nCarry.Web\Controllers\[MODULE_NAME]Controller.cs` dosyasını incele
2. `C:\source\nCarry\nCarry.Web\Models\` altında ilgili model dosyalarını kontrol et
3. `C:\source\nCarry\nCarry.Web\Views\[MODULE_NAME]\` view dosyalarını listele
4. Navigation properties ve business logic'i analiz et

### **Adım 2: Checklist Oluşturma**
Şu kategorilerde detaylı checklist oluştur:
- 🏗️ Proje Yapısı ve Dosya Analizi
- 🗄️ Database ve Data Model  
- 🌐 Web Uygulaması Çalıştırma
- 📝 Index Sayfası Testleri
- ➕ Create Functionality
- ✏️ Edit Functionality  
- 👁️ Details Functionality
- 🗑️ Delete Functionality
- 🎨 UI/UX Kontrolleri
- 🔗 Integration Testleri
- 🐛 Error Handling ve Edge Cases
- 📊 Business Logic Testleri
- 🔐 Security Testing
- 📝 Son Değerlendirme

### **Adım 3: Tarayıcı Testleri**
1. `http://localhost:5298/[MODULE_NAME]` adresine git
2. Index sayfasını test et (data loading, UI, navigation)
3. Create functionality'yi test et (form validation, submission, success)
4. Edit functionality'yi test et (data population, update, validation)
5. Details functionality'yi test et (data display, navigation)
6. Delete functionality'yi test et (confirmation, business rules, soft delete)

### **Adım 4: Test Scenarios**
Her functionality için test scenarios:
- **Create:** Minimum data, full data, validation errors, special cases
- **Edit:** Data population, field updates, validation, concurrency
- **Delete:** Empty entity, entity with dependencies, confirmation flow

### **Adım 5: Checklist Güncellemeleri**
Her test sonrası:
- ✅ Başarılı test sonuçlarını işaretle
- ❌ Hataları dokümante et  
- ⚠️ Geliştirme alanlarını not et
- Checklist'i `C:\source\nCarry\nCarry.Web\Documentation\[MODULE_NAME]_Test_Checklist.md` olarak kaydet

### **Adım 6: Final Rapor**
Test sonunda:
- Genel başarı oranı
- Kritik hatalar listesi
- Geliştirme önerileri
- Production readiness durumu
- Önceki modüllerle karşılaştırma

## 📋 Checklist Template:

```markdown
# 🔍 [MODULE_NAME] Module Test Checklist

## 📋 Test Durumu Legend
- ✅ **Başarılı** - Test tamamlandı, çalışıyor
- ❌ **Hata** - Test başarısız, hata var
- ⚠️ **Geliştirilebilir** - Çalışıyor ama iyileştirme gerekiyor
- ⏳ **Beklemede** - Test edilmedi

---

## 🏗️ **1. Proje Yapısı ve Dosya Analizi**
- [ ] ⏳ Controller dosyası kontrolü ([MODULE_NAME]Controller.cs)
- [ ] ⏳ Model dosyaları kontrolü
- [ ] ⏳ View dosyaları kontrolü (Index, Create, Edit, Details, Delete)
- [ ] ⏳ ViewModel dosyaları kontrolü
- [ ] ⏳ Navigation properties kontrolü

## 🗄️ **2. Database ve Data Model**
- [ ] ⏳ [MODULE_NAME] tablosu yapısı kontrolü
- [ ] ⏳ Foreign key ilişkileri kontrolü
- [ ] ⏳ Validation kuralları kontrolü
- [ ] ⏳ Sample data kontrolü

## 🌐 **3. Web Uygulaması Çalıştırma**
- [ ] ⏳ Uygulama erişim kontrolü (http://localhost:5298/[MODULE_NAME])
- [ ] ⏳ Authentication sistemi kontrolü
- [ ] ⏳ Navigation menü erişim kontrolü

## 📝 **4. [MODULE_NAME] Index Sayfası Testleri**
- [ ] ⏳ Data listesi yüklenme kontrolü
- [ ] ⏳ Table/Grid functionality
- [ ] ⏳ "Create New" butonu kontrolü
- [ ] ⏳ Action butonları kontrolü (Details, Edit, Delete)
- [ ] ⏳ Responsive tasarım kontrolü

## ➕ **5. [MODULE_NAME] Create Functionality**
- [ ] ⏳ Create sayfasına erişim
- [ ] ⏳ Form alanları render kontrolü
- [ ] ⏳ Required field validation
- [ ] ⏳ Form submission işlemi
- [ ] ⏳ Success message ve redirect
- [ ] ⏳ Error handling

## ✏️ **6. [MODULE_NAME] Edit Functionality**
- [ ] ⏳ Edit sayfasına erişim
- [ ] ⏳ Data population kontrolü
- [ ] ⏳ Form validation
- [ ] ⏳ Update işlemi
- [ ] ⏳ Success handling

## 👁️ **7. [MODULE_NAME] Details Functionality**
- [ ] ⏳ Details sayfasına erişim
- [ ] ⏳ Data display kontrolü
- [ ] ⏳ Navigation butonları

## 🗑️ **8. [MODULE_NAME] Delete Functionality**
- [ ] ⏳ Delete confirmation
- [ ] ⏳ Business rules kontrolü
- [ ] ⏳ Delete işlemi
- [ ] ⏳ Success handling
```

## 🎯 Kullanım:
1. `[MODULE_NAME]` placeholder'ını gerçek modül adıyla değiştir (örn: Product, Customer, SalesOrder)
2. Bu prompt'u yapay zekaya ver
3. Sistematik test sürecini başlat
4. Her adımda onay al ve ilerle
5. Detaylı sonuçları dokümante et

## 📊 Beklenen Çıktılar:
- Detaylı test checklist'i (markdown)
- Test sonuçları raporu
- Hata ve geliştirme önerileri listesi
- Production readiness değerlendirmesi
- Kod kalitesi analizi

---

**Bu prompt ile tutarlı, sistematik ve kapsamlı modül testleri gerçekleştirebilirsin!** 🚀