# 🛒 PurchaseOrder Modülü Test Template

## 📌 Görev
**PurchaseOrder** mod<PERSON><PERSON>ü için sistematik test süreci gerçekleştir. Aşağıdaki checklist'i takip ederek modülü tamamen test et.

## 📋 Test Kuralları
1. **Onay Al:** Her işlem öncesi "devam edeyim mi?" diye sor
2. **Sistematik İlerle:** Hiçbir adımı atlama, sırasıyla ilerle
3. **Checklist Güncellemeleri:** Her test sonrası bu dosyayı güncelle
4. **Hata Raporla:** Kritik hata bulursan işlemi durdur ve bildir
5. **Detaylı Dokümante Et:** Her adımın sonucunu açıkça raporla

## 🎯 PurchaseOrder Modülü Bilgileri
- **Test URL:** http://localhost:5298/PurchaseOrder
- **Controller:** PurchaseOrderController.cs
- **Models:** PurchaseOrder.cs, PurchaseOrderItem.cs
- **Views:** Index, Create, Edit, Details, Delete

---

# 🔍 PurchaseOrder Module Test Checklist

## 📋 Test Durumu Legend
- ✅ **Başarılı** - Test tamamlandı, çalışıyor
- ❌ **Hata** - Test başarısız, hata var
- ⚠️ **Geliştirilebilir** - Çalışıyor ama iyileştirme gerekiyor
- ⏳ **Beklemede** - Test edilmedi

---

## 🏗️ **1. Proje Yapısı ve Dosya Analizi**
- [ ] ⏳ PurchaseOrderController.cs dosyası kontrolü
- [ ] ⏳ PurchaseOrder.cs model kontrolü
- [ ] ⏳ PurchaseOrderItem.cs model kontrolü
- [ ] ⏳ PurchaseOrder Views klasörü kontrolü
- [ ] ⏳ PurchaseOrderViewModel.cs kontrolü

## 🗄️ **2. Database ve Data Model**
- [ ] ⏳ PurchaseOrder tablosu yapısı kontrolü
- [ ] ⏳ PurchaseOrderItem tablosu yapısı kontrolü
- [ ] ⏳ Supplier foreign key ilişkisi
- [ ] ⏳ Product foreign key ilişkisi (PurchaseOrderItem)
- [ ] ⏳ PO number uniqueness constraint

## 🌐 **3. Web Uygulaması Çalıştırma**
- [ ] ⏳ http://localhost:5298/PurchaseOrder erişim kontrolü
- [ ] ⏳ Management > Purchase Orders menü erişimi

## 📝 **4. PurchaseOrder Index Sayfası Testleri**
- [ ] ⏳ Purchase order listesi yüklenme kontrolü
- [ ] ⏳ PO number, supplier, date, amount display
- [ ] ⏳ PO status display
- [ ] ⏳ Action butonları kontrolü

## ➕ **5. PurchaseOrder Create Functionality**
- [ ] ⏳ Create sayfasına erişim
- [ ] ⏳ PO number auto-generation
- [ ] ⏳ Supplier selection dropdown
- [ ] ⏳ Order date picker
- [ ] ⏳ Expected delivery date
- [ ] ⏳ Line items management
- [ ] ⏳ Product selection for line items
- [ ] ⏳ Quantity and pricing fields
- [ ] ⏳ Calculations (subtotal, tax, freight, total)

## ✏️ **6. PurchaseOrder Edit Functionality**
- [ ] ⏳ Edit sayfasına erişim
- [ ] ⏳ Data population kontrolü
- [ ] ⏳ Status workflow restrictions
- [ ] ⏳ Line items editing
- [ ] ⏳ Approval process

## 👁️ **7. PurchaseOrder Details Functionality**
- [ ] ⏳ Details sayfasına erişim
- [ ] ⏳ PO header information display
- [ ] ⏳ Supplier information display
- [ ] ⏳ Line items display
- [ ] ⏳ Status and approval history

## 🗑️ **8. PurchaseOrder Delete Functionality**
- [ ] ⏳ Delete confirmation
- [ ] ⏳ Business rules (goods receipts, invoices)
- [ ] ⏳ Status restrictions for deletion

## 🎨 **9. UI/UX Kontrolleri**
- [ ] ⏳ Responsive design
- [ ] ⏳ Line items add/remove functionality
- [ ] ⏳ Real-time calculations
- [ ] ⏳ Status workflow indicators

## 🔗 **10. Integration Testleri**
- [ ] ⏳ Supplier integration
- [ ] ⏳ Product integration
- [ ] ⏳ Goods receipt generation
- [ ] ⏳ Invoice matching
- [ ] ⏳ Inventory impact

## 🐛 **11. Error Handling ve Edge Cases**
- [ ] ⏳ Invalid supplier selection
- [ ] ⏳ Negative quantities
- [ ] ⏳ Zero amounts
- [ ] ⏳ Future delivery dates

## 📊 **12. Business Logic Testleri**
- [ ] ⏳ PO approval workflow
- [ ] ⏳ Status transitions
- [ ] ⏳ Pricing calculations
- [ ] ⏳ Freight calculations
- [ ] ⏳ Receipt tracking

## 🔐 **13. Security Testing**
- [ ] ⏳ Authentication requirement
- [ ] ⏳ Authorization levels
- [ ] ⏳ Approval permissions

## 📝 **14. Son Değerlendirme**
- [ ] ⏳ Genel functionality rating
- [ ] ⏳ Performance rating
- [ ] ⏳ Production readiness
- [ ] ⏳ Kritik hatalar listesi

---

**Son Güncelleme:** [Tarih ve Saat]