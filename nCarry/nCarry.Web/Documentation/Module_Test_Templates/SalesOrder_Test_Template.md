# 📋 SalesOrder Modülü Test Template

## 📌 Görev
**SalesOrder** modülü için sistematik test süreci gerçekleştir. Aşağıdaki checklist'i takip ederek modülü tamamen test et.

## 📋 Test Kuralları
1. **Onay Al:** Her işlem öncesi "devam edeyim mi?" diye sor
2. **Sistematik İlerle:** Hiçbir adımı atlama, sıras<PERSON><PERSON> ilerle
3. **Checklist Güncellemeleri:** Her test sonrası bu dosyayı güncelle
4. **Hata Raporla:** Kritik hata bulursan işlemi durdur ve bildir
5. **Detaylı Dokümante Et:** Her adımın sonucunu açıkça raporla

## 🎯 SalesOrder Modülü Bilgileri
- **Test URL:** http://localhost:5298/SalesOrder
- **Controller:** SalesOrderController.cs
- **Models:** SalesOrder.cs, SalesOrderItem.cs
- **Views:** Index, Create, Edit, Details, Delete

---

# 🔍 SalesOrder Module Test Checklist

## 📋 Test Durumu Legend
- ✅ **Başarılı** - Test tamamlandı, çalışıyor
- ❌ **Hata** - Test başarısız, hata var
- ⚠️ **Geliştirilebilir** - Çalışıyor ama iyileştirme gerekiyor
- ⏳ **Beklemede** - Test edilmedi

---

## 🏗️ **1. Proje Yapısı ve Dosya Analizi**
- [ ] ⏳ SalesOrderController.cs dosyası kontrolü
- [ ] ⏳ SalesOrder.cs model kontrolü
- [ ] ⏳ SalesOrderItem.cs model kontrolü
- [ ] ⏳ SalesOrder Views klasörü kontrolü
- [ ] ⏳ SalesOrderViewModel.cs kontrolü
- [ ] ⏳ Navigation properties kontrolü

## 🗄️ **2. Database ve Data Model**
- [ ] ⏳ SalesOrder tablosu yapısı kontrolü
- [ ] ⏳ SalesOrderItem tablosu yapısı kontrolü
- [ ] ⏳ Customer foreign key ilişkisi
- [ ] ⏳ Product foreign key ilişkisi (SalesOrderItem)
- [ ] ⏳ Order number uniqueness constraint
- [ ] ⏳ Decimal precision ayarları (amounts, quantities)

## 🌐 **3. Web Uygulaması Çalıştırma**
- [ ] ⏳ http://localhost:5298/SalesOrder erişim kontrolü
- [ ] ⏳ Authentication sistemi kontrolü
- [ ] ⏳ Management > Sales Orders menü erişimi

## 📝 **4. SalesOrder Index Sayfası Testleri**
- [ ] ⏳ Sales order listesi yüklenme kontrolü
- [ ] ⏳ Order number, customer, date, amount display
- [ ] ⏳ Order status display
- [ ] ⏳ Action butonları kontrolü
- [ ] ⏳ Create New Order butonu

## ➕ **5. SalesOrder Create Functionality**
- [ ] ⏳ Create sayfasına erişim
- [ ] ⏳ Order number auto-generation
- [ ] ⏳ Customer selection dropdown
- [ ] ⏳ Order date picker
- [ ] ⏳ Line items section
- [ ] ⏳ Product selection for line items
- [ ] ⏳ Quantity and pricing fields
- [ ] ⏳ Calculations (subtotal, tax, total)
- [ ] ⏳ Form validation
- [ ] ⏳ Save functionality

## ✏️ **6. SalesOrder Edit Functionality**
- [ ] ⏳ Edit sayfasına erişim
- [ ] ⏳ Data population kontrolü
- [ ] ⏳ Line items editing
- [ ] ⏳ Recalculations on changes
- [ ] ⏳ Update işlemi

## 👁️ **7. SalesOrder Details Functionality**
- [ ] ⏳ Details sayfasına erişim
- [ ] ⏳ Order header information display
- [ ] ⏳ Line items display
- [ ] ⏳ Totals display
- [ ] ⏳ Customer information display

## 🗑️ **8. SalesOrder Delete Functionality**
- [ ] ⏳ Delete confirmation
- [ ] ⏳ Business rules (shipments, payments)
- [ ] ⏳ Cascade delete line items

## 🎨 **9. UI/UX Kontrolleri**
- [ ] ⏳ Responsive design
- [ ] ⏳ Line items add/remove functionality
- [ ] ⏳ Real-time calculations
- [ ] ⏳ Form layout

## 🔗 **10. Integration Testleri**
- [ ] ⏳ Customer integration
- [ ] ⏳ Product integration
- [ ] ⏳ Inventory impact
- [ ] ⏳ Payment integration
- [ ] ⏳ Shipment integration

## 🐛 **11. Error Handling ve Edge Cases**
- [ ] ⏳ Invalid customer selection
- [ ] ⏳ Out of stock products
- [ ] ⏳ Negative quantities
- [ ] ⏳ Zero amounts

## 📊 **12. Business Logic Testleri**
- [ ] ⏳ Order workflow states
- [ ] ⏳ Pricing calculations
- [ ] ⏳ Tax calculations
- [ ] ⏳ Discount applications
- [ ] ⏳ Payment tracking

## 🔐 **13. Security Testing**
- [ ] ⏳ Authentication requirement
- [ ] ⏳ Authorization levels
- [ ] ⏳ Input validation

## 📝 **14. Son Değerlendirme**
- [ ] ⏳ Genel functionality rating
- [ ] ⏳ Performance rating
- [ ] ⏳ Production readiness
- [ ] ⏳ Kritik hatalar listesi

---

**Son Güncelleme:** [Tarih ve Saat]