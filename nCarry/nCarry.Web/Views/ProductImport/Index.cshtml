@{
    ViewData["Title"] = "Product Import";
}

<div class="container-fluid">
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">@ViewData["Title"]</h1>
    </div>

    <div class="row">
        <div class="col-lg-6">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Import Frozen Seafood Products</h6>
                </div>
                <div class="card-body">
                    <p>This will import 63 frozen seafood products from the CSV file.</p>
                    <p class="text-muted">Products include: Fish, Prawns, Squid, Lobster, Clams, Mussels, and more.</p>
                    
                    <form asp-action="ImportFrozenSeafood" method="post">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-upload"></i> Import Seafood Products
                        </button>
                    </form>
                </div>
            </div>
        </div>

        <div class="col-lg-6">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Create Test Purchase Order</h6>
                </div>
                <div class="card-body">
                    <p>Create a purchase order with imported seafood products.</p>
                    <p class="text-muted">This will select the first available supplier and add seafood products.</p>
                    
                    <form asp-action="CreateTestPurchaseOrder" method="post">
                        <button type="submit" class="btn btn-success">
                            <i class="fas fa-shopping-cart"></i> Create Purchase Order
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">Import Information</h6>
        </div>
        <div class="card-body">
            <h5>Products to Import:</h5>
            <ul>
                <li><strong>Category:</strong> Frozen Seafood (SEAFOOD)</li>
                <li><strong>Total Products:</strong> 63 items</li>
                <li><strong>Units:</strong> BOX, PALLET, EA (Each)</li>
                <li><strong>Pricing:</strong> Automatically generated based on unit type</li>
            </ul>
            
            <h5 class="mt-4">Sample Products:</h5>
            <ul>
                <li>Black Cod - 1 Pallet</li>
                <li>Baby Squid 10-20 - 8 Box</li>
                <li>Salmon Fillet - 160 Box</li>
                <li>Lobster Meat - 21 Box</li>
                <li>Vacuum Mussels - 2.5 Pallet</li>
            </ul>

            <h5 class="mt-4">Next Steps:</h5>
            <ol>
                <li>Import the products using the button above</li>
                <li>Create a purchase order to receive inventory</li>
                <li>Process goods receipt when products arrive</li>
                <li>Create sales orders to sell the products</li>
            </ol>
        </div>
    </div>
</div>