@model string

@{
    ViewData["Title"] = "Import Result";
}

<div class="container-fluid">
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">@ViewData["Title"]</h1>
    </div>

    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">Import Log</h6>
        </div>
        <div class="card-body">
            <pre class="bg-light p-3">@Model</pre>
        </div>
    </div>

    <div class="form-group">
        <a asp-action="Index" class="btn btn-secondary">Back to Import</a>
        <a asp-controller="Product" asp-action="Index" class="btn btn-primary">View Products</a>
        <a asp-controller="PurchaseOrder" asp-action="Create" class="btn btn-success">Create Purchase Order</a>
    </div>
</div>