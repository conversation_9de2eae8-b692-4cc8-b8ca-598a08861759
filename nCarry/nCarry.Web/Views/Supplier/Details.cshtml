@model nCarry.Web.Models.Suppliers.Supplier

@{
    ViewData["Title"] = "Supplier Details";
}

<div class="container-fluid">
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">@ViewData["Title"]</h1>
        <div>
            <a asp-action="Edit" asp-route-id="@Model.SupplierID" class="btn btn-sm btn-primary shadow-sm">
                <i class="fas fa-edit fa-sm text-white-50"></i> Edit
            </a>
            <a asp-action="Index" class="btn btn-sm btn-secondary shadow-sm">
                <i class="fas fa-arrow-left fa-sm text-white-50"></i> Back to List
            </a>
        </div>
    </div>

    <div class="row">
        <!-- Basic Information Card -->
        <div class="col-lg-6">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Basic Information</h6>
                </div>
                <div class="card-body">
                    <dl class="row">
                        <dt class="col-sm-4">Supplier Code</dt>
                        <dd class="col-sm-8">@Html.DisplayFor(model => model.SupplierCode)</dd>
                        
                        <dt class="col-sm-4">Supplier Name</dt>
                        <dd class="col-sm-8">
                            @Html.DisplayFor(model => model.SupplierName)
                            @if (Model.IsPreferred)
                            {
                                <span class="badge bg-info text-white ms-2">Preferred</span>
                            }
                        </dd>
                        
                        <dt class="col-sm-4">Supplier Type</dt>
                        <dd class="col-sm-8">@Html.DisplayFor(model => model.SupplierType)</dd>
                        
                        @if (!string.IsNullOrEmpty(Model.TradingName))
                        {
                            <dt class="col-sm-4">Trading Name</dt>
                            <dd class="col-sm-8">@Html.DisplayFor(model => model.TradingName)</dd>
                        }
                        
                        @if (!string.IsNullOrEmpty(Model.TaxNumber))
                        {
                            <dt class="col-sm-4">Tax Number</dt>
                            <dd class="col-sm-8">@Html.DisplayFor(model => model.TaxNumber)</dd>
                        }
                        
                        <dt class="col-sm-4">Status</dt>
                        <dd class="col-sm-8">
                            @if (Model.IsActive)
                            {
                                <span class="badge bg-success">Active</span>
                            }
                            else
                            {
                                <span class="badge bg-danger">Inactive</span>
                            }
                        </dd>
                        
                        @if (Model.Rating.HasValue)
                        {
                            <dt class="col-sm-4">Rating</dt>
                            <dd class="col-sm-8">
                                <div class="text-warning">
                                    @for (int i = 1; i <= 5; i++)
                                    {
                                        if (i <= Model.Rating.Value)
                                        {
                                            <i class="fas fa-star"></i>
                                        }
                                        else
                                        {
                                            <i class="far fa-star"></i>
                                        }
                                    }
                                </div>
                            </dd>
                        }
                    </dl>
                </div>
            </div>
        </div>

        <!-- Contact & Purchase Information Card -->
        <div class="col-lg-6">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Contact & Purchase Information</h6>
                </div>
                <div class="card-body">
                    <dl class="row">
                        @if (!string.IsNullOrEmpty(Model.ContactEmail))
                        {
                            <dt class="col-sm-4">Email</dt>
                            <dd class="col-sm-8"><a href="mailto:@Model.ContactEmail">@Model.ContactEmail</a></dd>
                        }
                        
                        @if (!string.IsNullOrEmpty(Model.ContactPhone))
                        {
                            <dt class="col-sm-4">Phone</dt>
                            <dd class="col-sm-8">@Html.DisplayFor(model => model.ContactPhone)</dd>
                        }
                        
                        @if (!string.IsNullOrEmpty(Model.Website))
                        {
                            <dt class="col-sm-4">Website</dt>
                            <dd class="col-sm-8"><a href="@Model.Website" target="_blank">@Model.Website</a></dd>
                        }
                        
                        <dt class="col-sm-4">Payment Terms</dt>
                        <dd class="col-sm-8">@((Model.PaymentTermDays ?? 30) + " days")</dd>
                        
                        <dt class="col-sm-4">Min Order Value</dt>
                        <dd class="col-sm-8">@Model.MinimumOrderValue.ToString("C")</dd>
                        
                        <dt class="col-sm-4">Lead Time</dt>
                        <dd class="col-sm-8">@((Model.LeadTimeDays ?? 0) + " days")</dd>
                        
                        <dt class="col-sm-4">Outstanding Balance</dt>
                        <dd class="col-sm-8">@Model.OutstandingBalance.ToString("C")</dd>
                        
                        <dt class="col-sm-4">YTD Purchases</dt>
                        <dd class="col-sm-8">@Model.YTDPurchases.ToString("C")</dd>
                    </dl>
                </div>
            </div>
        </div>
    </div>

    <!-- Performance Metrics Card -->
    @if (Model.QualityScore.HasValue || Model.DeliveryScore.HasValue || Model.PriceScore.HasValue)
    {
        <div class="card shadow mb-4">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">Performance Metrics</h6>
            </div>
            <div class="card-body">
                <div class="row">
                    @if (Model.QualityScore.HasValue)
                    {
                        <div class="col-md-4">
                            <h6>Quality Score</h6>
                            <div class="progress mb-3">
                                <div class="progress-bar bg-success" role="progressbar" style="width: @Model.QualityScore%;" aria-valuenow="@Model.QualityScore" aria-valuemin="0" aria-valuemax="100">@Model.QualityScore%</div>
                            </div>
                        </div>
                    }
                    @if (Model.DeliveryScore.HasValue)
                    {
                        <div class="col-md-4">
                            <h6>Delivery Score</h6>
                            <div class="progress mb-3">
                                <div class="progress-bar bg-info" role="progressbar" style="width: @Model.DeliveryScore%;" aria-valuenow="@Model.DeliveryScore" aria-valuemin="0" aria-valuemax="100">@Model.DeliveryScore%</div>
                            </div>
                        </div>
                    }
                    @if (Model.PriceScore.HasValue)
                    {
                        <div class="col-md-4">
                            <h6>Price Score</h6>
                            <div class="progress mb-3">
                                <div class="progress-bar bg-warning" role="progressbar" style="width: @Model.PriceScore%;" aria-valuenow="@Model.PriceScore" aria-valuemin="0" aria-valuemax="100">@Model.PriceScore%</div>
                            </div>
                        </div>
                    }
                </div>
            </div>
        </div>
    }

    <!-- Addresses Card -->
    @if (Model.Addresses != null && Model.Addresses.Any())
    {
        <div class="card shadow mb-4">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">Addresses</h6>
            </div>
            <div class="card-body">
                <div class="row">
                    @foreach (var address in Model.Addresses.Where(a => a.IsActive))
                    {
                        <div class="col-md-6 mb-3">
                            <div class="card">
                                <div class="card-header py-2">
                                    <strong>@address.AddressType</strong>
                                    @if (address.IsDefault)
                                    {
                                        <span class="badge bg-primary float-right">Default</span>
                                    }
                                </div>
                                <div class="card-body">
                                    <address>
                                        @address.Address1<br />
                                        @if (!string.IsNullOrEmpty(address.Address2))
                                        {
                                            @address.Address2<br />
                                        }
                                        @if (!string.IsNullOrEmpty(address.City))
                                        {
                                            @address.City<text>,</text>
                                        }
                                        @if (!string.IsNullOrEmpty(address.State))
                                        {
                                            @address.State
                                        }
                                        @if (!string.IsNullOrEmpty(address.PostCode))
                                        {
                                            @address.PostCode<br />
                                        }
                                        @if (!string.IsNullOrEmpty(address.Country))
                                        {
                                            @address.Country
                                        }
                                    </address>
                                </div>
                            </div>
                        </div>
                    }
                </div>
            </div>
        </div>
    }

    <!-- Notes Card -->
    @if (!string.IsNullOrEmpty(Model.Notes))
    {
        <div class="card shadow mb-4">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">Notes</h6>
            </div>
            <div class="card-body">
                <p>@Html.DisplayFor(model => model.Notes)</p>
            </div>
        </div>
    }

    <!-- System Information Card -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">System Information</h6>
        </div>
        <div class="card-body">
            <dl class="row">
                <dt class="col-sm-2">Created Date</dt>
                <dd class="col-sm-4">@Model.CreatedDate.ToString("yyyy-MM-dd HH:mm")</dd>
                
                <dt class="col-sm-2">Updated Date</dt>
                <dd class="col-sm-4">@Model.UpdatedDate.ToString("yyyy-MM-dd HH:mm")</dd>
            </dl>
        </div>
    </div>
</div>