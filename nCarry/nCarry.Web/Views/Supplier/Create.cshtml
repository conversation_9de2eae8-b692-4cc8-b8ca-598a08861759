@model nCarry.Web.Models.SupplierCreateViewModel

@{
    ViewData["Title"] = "Create Supplier";
}

<div class="container-fluid">
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">@ViewData["Title"]</h1>
    </div>

    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">Supplier Information</h6>
        </div>
        <div class="card-body">
            <form asp-action="Create">
                <div asp-validation-summary="ModelOnly" class="alert alert-danger"></div>
                
                <div class="row">
                    <!-- Basic Information -->
                    <div class="col-md-6">
                        <h5 class="mb-3">Basic Information</h5>
                        
                        <div class="form-group">
                            <label asp-for="SupplierCode" class="control-label"></label>
                            <input asp-for="SupplierCode" class="form-control" readonly />
                            <span asp-validation-for="SupplierCode" class="text-danger"></span>
                        </div>
                        
                        <div class="form-group">
                            <label asp-for="SupplierName" class="control-label"></label>
                            <input asp-for="SupplierName" class="form-control" />
                            <span asp-validation-for="SupplierName" class="text-danger"></span>
                        </div>
                        
                        <div class="form-group">
                            <label asp-for="SupplierType" class="control-label"></label>
                            <select asp-for="SupplierType" class="form-control">
                                <option value="Standard">Standard</option>
                                <option value="Manufacturer">Manufacturer</option>
                                <option value="Distributor">Distributor</option>
                                <option value="Importer">Importer</option>
                            </select>
                            <span asp-validation-for="SupplierType" class="text-danger"></span>
                        </div>
                        
                        <div class="form-group">
                            <label asp-for="TradingName" class="control-label"></label>
                            <input asp-for="TradingName" class="form-control" />
                            <span asp-validation-for="TradingName" class="text-danger"></span>
                        </div>
                        
                        <div class="form-group">
                            <label asp-for="TaxNumber" class="control-label"></label>
                            <input asp-for="TaxNumber" class="form-control" />
                            <span asp-validation-for="TaxNumber" class="text-danger"></span>
                        </div>
                    </div>
                    
                    <!-- Contact Information -->
                    <div class="col-md-6">
                        <h5 class="mb-3">Contact Information</h5>
                        
                        <div class="form-group">
                            <label asp-for="Email" class="control-label"></label>
                            <input asp-for="Email" class="form-control" />
                            <span asp-validation-for="Email" class="text-danger"></span>
                        </div>
                        
                        <div class="form-group">
                            <label asp-for="Phone" class="control-label"></label>
                            <input asp-for="Phone" class="form-control" />
                            <span asp-validation-for="Phone" class="text-danger"></span>
                        </div>
                        
                        <div class="form-group">
                            <label asp-for="Website" class="control-label"></label>
                            <input asp-for="Website" class="form-control" />
                            <span asp-validation-for="Website" class="text-danger"></span>
                        </div>
                        
                        <h5 class="mb-3 mt-4">Purchase Information</h5>
                        
                        <div class="form-group">
                            <label asp-for="PaymentTermDays" class="control-label"></label>
                            <input asp-for="PaymentTermDays" class="form-control" type="number" />
                            <span asp-validation-for="PaymentTermDays" class="text-danger"></span>
                        </div>
                        
                        <div class="form-group">
                            <label asp-for="MinimumOrderValue" class="control-label"></label>
                            <input asp-for="MinimumOrderValue" class="form-control" type="number" step="0.01" />
                            <span asp-validation-for="MinimumOrderValue" class="text-danger"></span>
                        </div>
                        
                        <div class="form-group">
                            <label asp-for="LeadTimeDays" class="control-label"></label>
                            <input asp-for="LeadTimeDays" class="form-control" type="number" />
                            <span asp-validation-for="LeadTimeDays" class="text-danger"></span>
                        </div>
                    </div>
                </div>
                
                <hr />
                
                <!-- Address Information -->
                <div class="row">
                    <div class="col-md-12">
                        <h5 class="mb-3">Primary Address</h5>
                    </div>
                    
                    <div class="col-md-6">
                        <div class="form-group">
                            <label asp-for="Address1" class="control-label"></label>
                            <input asp-for="Address1" class="form-control" />
                            <span asp-validation-for="Address1" class="text-danger"></span>
                        </div>
                        
                        <div class="form-group">
                            <label asp-for="Address2" class="control-label"></label>
                            <input asp-for="Address2" class="form-control" />
                            <span asp-validation-for="Address2" class="text-danger"></span>
                        </div>
                        
                        <div class="form-group">
                            <label asp-for="City" class="control-label"></label>
                            <input asp-for="City" class="form-control" />
                            <span asp-validation-for="City" class="text-danger"></span>
                        </div>
                    </div>
                    
                    <div class="col-md-6">
                        <div class="form-group">
                            <label asp-for="StateProvince" class="control-label"></label>
                            <input asp-for="StateProvince" class="form-control" />
                            <span asp-validation-for="StateProvince" class="text-danger"></span>
                        </div>
                        
                        <div class="form-group">
                            <label asp-for="PostalCode" class="control-label"></label>
                            <input asp-for="PostalCode" class="form-control" />
                            <span asp-validation-for="PostalCode" class="text-danger"></span>
                        </div>
                        
                        <div class="form-group">
                            <label asp-for="Country" class="control-label"></label>
                            <input asp-for="Country" class="form-control" />
                            <span asp-validation-for="Country" class="text-danger"></span>
                        </div>
                    </div>
                </div>
                
                <hr />
                
                <!-- Additional Information -->
                <div class="row">
                    <div class="col-md-12">
                        <div class="form-group">
                            <label asp-for="Notes" class="control-label"></label>
                            <textarea asp-for="Notes" class="form-control" rows="3"></textarea>
                            <span asp-validation-for="Notes" class="text-danger"></span>
                        </div>
                        
                        <div class="form-group">
                            <div class="form-check">
                                <input asp-for="IsActive" class="form-check-input" />
                                <label asp-for="IsActive" class="form-check-label"></label>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="form-group">
                    <input type="submit" value="Create" class="btn btn-primary" />
                    <a asp-action="Index" class="btn btn-secondary">Cancel</a>
                </div>
            </form>
        </div>
    </div>
</div>

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}
}