@model IEnumerable<nCarry.Web.Models.Suppliers.Supplier>

@{
    ViewData["Title"] = "Suppliers";
}

<div class="container-fluid">
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">@ViewData["Title"]</h1>
        <a asp-action="Create" class="d-none d-sm-inline-block btn btn-sm btn-primary shadow-sm">
            <i class="fas fa-plus fa-sm text-white-50"></i> Create New Supplier
        </a>
    </div>

    @if (TempData["Success"] != null)
    {
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            @TempData["Success"]
            <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                <span aria-hidden="true">&times;</span>
            </button>
        </div>
    }

    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">Supplier List</h6>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-bordered" id="dataTable" width="100%" cellspacing="0">
                    <thead>
                        <tr>
                            <th>Code</th>
                            <th>Supplier Name</th>
                            <th>Type</th>
                            <th>Email</th>
                            <th>Phone</th>
                            <th>Min Order</th>
                            <th>Lead Time</th>
                            <th>Rating</th>
                            <th>Status</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach (var item in Model)
                        {
                            <tr>
                                <td>@Html.DisplayFor(modelItem => item.SupplierCode)</td>
                                <td>
                                    @Html.DisplayFor(modelItem => item.SupplierName)
                                    @if (!string.IsNullOrEmpty(item.TradingName))
                                    {
                                        <br />
                                        <small class="text-muted">Trading as: @item.TradingName</small>
                                    }
                                    @if (item.IsPreferred)
                                    {
                                        <span class="badge bg-info text-white ms-2">Preferred</span>
                                    }
                                </td>
                                <td>@Html.DisplayFor(modelItem => item.SupplierType)</td>
                                <td>
                                    @if (!string.IsNullOrEmpty(item.ContactEmail))
                                    {
                                        <a href="mailto:@item.ContactEmail">@item.ContactEmail</a>
                                    }
                                    else
                                    {
                                        <span class="text-muted">-</span>
                                    }
                                </td>
                                <td>@(item.ContactPhone ?? "-")</td>
                                <td>@item.MinimumOrderValue.ToString("C")</td>
                                <td>@((item.LeadTimeDays ?? 0) + " days")</td>
                                <td>
                                    @if (item.Rating.HasValue)
                                    {
                                        <div class="text-warning">
                                            @for (int i = 1; i <= 5; i++)
                                            {
                                                if (i <= item.Rating.Value)
                                                {
                                                    <i class="fas fa-star"></i>
                                                }
                                                else
                                                {
                                                    <i class="far fa-star"></i>
                                                }
                                            }
                                        </div>
                                    }
                                    else
                                    {
                                        <span class="text-muted">-</span>
                                    }
                                </td>
                                <td>
                                    @if (item.IsActive)
                                    {
                                        <span class="badge bg-success">Active</span>
                                    }
                                    else
                                    {
                                        <span class="badge bg-danger">Inactive</span>
                                    }
                                </td>
                                <td>
                                    <div class="btn-group" role="group">
                                        <a asp-action="Details" asp-route-id="@item.SupplierID" class="btn btn-sm btn-info" title="Details">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <a asp-action="Edit" asp-route-id="@item.SupplierID" class="btn btn-sm btn-primary" title="Edit">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <a asp-action="Delete" asp-route-id="@item.SupplierID" class="btn btn-sm btn-danger" title="Delete">
                                            <i class="fas fa-trash"></i>
                                        </a>
                                    </div>
                                </td>
                            </tr>
                        }
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <link rel="stylesheet" type="text/css" href="https://cdn.datatables.net/1.10.25/css/dataTables.bootstrap4.min.css">
    <script type="text/javascript" src="https://cdn.datatables.net/1.10.25/js/jquery.dataTables.min.js"></script>
    <script type="text/javascript" src="https://cdn.datatables.net/1.10.25/js/dataTables.bootstrap4.min.js"></script>
    
    <script>
        $(document).ready(function() {
            $('#dataTable').DataTable({
                "order": [[ 1, "asc" ]],
                "pageLength": 25,
                "columnDefs": [
                    { "width": "10%", "targets": 0 },
                    { "width": "20%", "targets": 1 },
                    { "width": "10%", "targets": 2 },
                    { "width": "15%", "targets": 3 },
                    { "width": "10%", "targets": 4 },
                    { "width": "10%", "targets": 5 },
                    { "width": "8%", "targets": 6 },
                    { "width": "7%", "targets": 7 },
                    { "width": "5%", "targets": 8 },
                    { "width": "5%", "targets": 9 }
                ]
            });
        });
    </script>
}