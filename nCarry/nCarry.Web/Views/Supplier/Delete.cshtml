@model nCarry.Web.Models.Suppliers.Supplier

@{
    ViewData["Title"] = "Delete Supplier";
}

<div class="container-fluid">
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">@ViewData["Title"]</h1>
    </div>

    <div class="card shadow mb-4 border-danger">
        <div class="card-header py-3 bg-danger text-white">
            <h6 class="m-0 font-weight-bold">Delete Confirmation</h6>
        </div>
        <div class="card-body">
            <div class="alert alert-danger" role="alert">
                <h5 class="alert-heading">Are you sure you want to delete this supplier?</h5>
                <p>This action cannot be undone. The supplier will be marked as deleted and will not appear in the supplier list.</p>
            </div>

            <dl class="row">
                <dt class="col-sm-3">Supplier Code</dt>
                <dd class="col-sm-9">@Html.DisplayFor(model => model.SupplierCode)</dd>
                
                <dt class="col-sm-3">Supplier Name</dt>
                <dd class="col-sm-9">@Html.DisplayFor(model => model.SupplierName)</dd>
                
                <dt class="col-sm-3">Type</dt>
                <dd class="col-sm-9">@Html.DisplayFor(model => model.SupplierType)</dd>
                
                @if (!string.IsNullOrEmpty(Model.ContactEmail))
                {
                    <dt class="col-sm-3">Email</dt>
                    <dd class="col-sm-9">@Html.DisplayFor(model => model.ContactEmail)</dd>
                }
                
                @if (!string.IsNullOrEmpty(Model.ContactPhone))
                {
                    <dt class="col-sm-3">Phone</dt>
                    <dd class="col-sm-9">@Html.DisplayFor(model => model.ContactPhone)</dd>
                }
                
                <dt class="col-sm-3">Min Order Value</dt>
                <dd class="col-sm-9">@Model.MinimumOrderValue.ToString("C")</dd>
                
                <dt class="col-sm-3">Outstanding Balance</dt>
                <dd class="col-sm-9">@Model.OutstandingBalance.ToString("C")</dd>
                
                <dt class="col-sm-3">Status</dt>
                <dd class="col-sm-9">
                    @if (Model.IsActive)
                    {
                        <span class="badge bg-success">Active</span>
                    }
                    else
                    {
                        <span class="badge bg-danger">Inactive</span>
                    }
                    @if (Model.IsPreferred)
                    {
                        <span class="badge bg-info text-white">Preferred</span>
                    }
                </dd>
            </dl>

            <form asp-action="Delete">
                <input type="hidden" asp-for="SupplierID" />
                <div class="form-group mt-4">
                    <input type="submit" value="Delete" class="btn btn-danger" />
                    <a asp-action="Index" class="btn btn-secondary">Cancel</a>
                </div>
            </form>
        </div>
    </div>
</div>