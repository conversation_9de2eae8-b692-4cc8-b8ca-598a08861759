@model nCarry.Web.Models.Financial.Payment

@{
    ViewData["Title"] = "Create Payment";
    var invoice = ViewBag.Invoice as nCarry.Web.Models.Purchase.SupplierInvoice;
}

<div class="row">
    <div class="col-12">
        <form asp-controller="Payment" asp-action="Create" method="post">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">@ViewData["Title"] for Invoice @invoice?.InvoiceNumber</h3>
                </div>
                <div class="card-body">
                    <div asp-validation-summary="ModelOnly" class="text-danger"></div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <h5>Invoice Information</h5>
                            <dl class="row">
                                <dt class="col-sm-4">Invoice Number</dt>
                                <dd class="col-sm-8">@invoice?.InvoiceNumber</dd>
                                
                                <dt class="col-sm-4">Supplier</dt>
                                <dd class="col-sm-8">@invoice?.Supplier?.SupplierName</dd>
                                
                                <dt class="col-sm-4">Invoice Amount</dt>
                                <dd class="col-sm-8">@invoice?.TotalAmount.ToString("C")</dd>
                                
                                <dt class="col-sm-4">Outstanding</dt>
                                <dd class="col-sm-8"><strong class="text-danger">@invoice?.OutstandingAmount.ToString("C")</strong></dd>
                            </dl>
                        </div>
                        
                        <div class="col-md-6">
                            <h5>Payment Details</h5>
                            
                            <input type="hidden" asp-for="PaymentType" />
                            <input type="hidden" asp-for="SupplierID" value="@invoice?.SupplierID" />
                            <input type="hidden" asp-for="CurrencyID" value="1" />
                            <input type="hidden" name="allocations[0].DocumentType" value="SupplierInvoice" />
                            <input type="hidden" name="allocations[0].DocumentID" value="@invoice?.SupplierInvoiceID" />
                            
                            <div class="form-group">
                                <label asp-for="PaymentDate" class="control-label"></label>
                                <input asp-for="PaymentDate" type="date" class="form-control" />
                                <span asp-validation-for="PaymentDate" class="text-danger"></span>
                            </div>
                            
                            <div class="form-group">
                                <label asp-for="PaymentMethodID" class="control-label">Payment Method</label>
                                <select asp-for="PaymentMethodID" class="form-control" asp-items="ViewBag.PaymentMethods" required>
                                    <option value="">-- Select Payment Method --</option>
                                </select>
                                <span asp-validation-for="PaymentMethodID" class="text-danger"></span>
                            </div>
                            
                            <div class="form-group">
                                <label asp-for="Amount" class="control-label">Payment Amount</label>
                                <input asp-for="Amount" type="number" class="form-control" step="0.01" min="0.01" max="@invoice?.OutstandingAmount" />
                                <span asp-validation-for="Amount" class="text-danger"></span>
                                <small class="form-text text-muted">Maximum: @invoice?.OutstandingAmount.ToString("C")</small>
                            </div>
                            
                            <div class="form-group">
                                <label asp-for="CheckNumber" class="control-label">Check/Reference Number</label>
                                <input asp-for="CheckNumber" class="form-control" />
                                <span asp-validation-for="CheckNumber" class="text-danger"></span>
                            </div>
                            
                            <div class="form-group">
                                <label asp-for="Notes" class="control-label">Description</label>
                                <textarea asp-for="Notes" class="form-control" rows="2"></textarea>
                                <span asp-validation-for="Notes" class="text-danger"></span>
                            </div>
                            
                            <input type="hidden" name="allocations[0].AllocatedAmount" id="allocatedAmount" value="@invoice?.OutstandingAmount" />
                        </div>
                    </div>
                </div>
                <div class="card-footer">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save"></i> Create Payment
                    </button>
                    <a asp-controller="SupplierInvoice" asp-action="Details" asp-route-id="@invoice?.SupplierInvoiceID" class="btn btn-default">Cancel</a>
                </div>
            </div>
        </form>
    </div>
</div>

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}
    <script>
        $(document).ready(function() {
            // Update allocated amount when payment amount changes
            $('#Amount').on('input', function() {
                $('#allocatedAmount').val($(this).val());
            });
        });
    </script>
}