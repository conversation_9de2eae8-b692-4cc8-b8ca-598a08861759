@model nCarry.Web.Models.Purchase.SupplierInvoice

@{
    ViewData["Title"] = "New Supplier Invoice";
    var purchaseOrder = ViewBag.PurchaseOrder as nCarry.Web.Models.Purchase.PurchaseOrder;
    var goodsReceipt = ViewBag.GoodsReceipt as nCarry.Web.Models.Purchase.GoodsReceipt;
}

<div class="row">
    <div class="col-12">
        <form asp-action="Create" method="post">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">@ViewData["Title"]</h3>
                    <div class="card-tools">
                        <a asp-action="Index" class="btn btn-default btn-sm">
                            <i class="fas fa-arrow-left"></i> Back to List
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <div asp-validation-summary="ModelOnly" class="text-danger"></div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label asp-for="InvoiceNumber" class="control-label"></label>
                                <input asp-for="InvoiceNumber" class="form-control" readonly />
                                <span asp-validation-for="InvoiceNumber" class="text-danger"></span>
                            </div>
                            
                            <div class="form-group">
                                <label asp-for="SupplierInvoiceNumber" class="control-label">Supplier Invoice Number</label>
                                <input asp-for="SupplierInvoiceNumber" class="form-control" required />
                                <span asp-validation-for="SupplierInvoiceNumber" class="text-danger"></span>
                            </div>
                            
                            <div class="form-group">
                                <label asp-for="InvoiceDate" class="control-label"></label>
                                <input asp-for="InvoiceDate" type="date" class="form-control" />
                                <span asp-validation-for="InvoiceDate" class="text-danger"></span>
                            </div>
                            
                            <div class="form-group">
                                <label asp-for="DueDate" class="control-label"></label>
                                <input asp-for="DueDate" type="date" class="form-control" />
                                <span asp-validation-for="DueDate" class="text-danger"></span>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="form-group">
                                <label asp-for="SupplierID" class="control-label">Supplier</label>
                                @if (Model.SupplierID > 0)
                                {
                                    <input type="hidden" asp-for="SupplierID" />
                                    <input type="text" class="form-control" value="@(purchaseOrder?.Supplier?.SupplierName ?? goodsReceipt?.PurchaseOrder?.Supplier?.SupplierName)" readonly />
                                }
                                else
                                {
                                    <select asp-for="SupplierID" class="form-control" asp-items="ViewBag.Suppliers" required>
                                        <option value="">-- Select Supplier --</option>
                                    </select>
                                }
                                <span asp-validation-for="SupplierID" class="text-danger"></span>
                            </div>
                            
                            @if (purchaseOrder != null)
                            {
                                <div class="form-group">
                                    <label>Purchase Order</label>
                                    <input type="hidden" asp-for="PurchaseOrderID" />
                                    <input type="text" class="form-control" value="@purchaseOrder.PurchaseOrderNumber" readonly />
                                </div>
                            }
                            
                            @if (goodsReceipt != null)
                            {
                                <div class="form-group">
                                    <label>Goods Receipt</label>
                                    <input type="hidden" asp-for="GoodsReceiptID" />
                                    <input type="text" class="form-control" value="@goodsReceipt.ReceiptNumber" readonly />
                                </div>
                            }
                            
                            <div class="form-group">
                                <label asp-for="Currency" class="control-label"></label>
                                <select asp-for="Currency" class="form-control">
                                    <option value="GBP">GBP - British Pound</option>
                                    <option value="USD">USD - US Dollar</option>
                                    <option value="EUR">EUR - Euro</option>
                                </select>
                                <span asp-validation-for="Currency" class="text-danger"></span>
                            </div>
                            
                            <div class="form-group">
                                <label asp-for="Notes" class="control-label"></label>
                                <textarea asp-for="Notes" class="form-control" rows="2"></textarea>
                                <span asp-validation-for="Notes" class="text-danger"></span>
                            </div>
                        </div>
                    </div>
                    
                    <hr />
                    
                    <h5>Invoice Items</h5>
                    <div class="table-responsive">
                        <table class="table table-bordered" id="itemsTable">
                            <thead>
                                <tr>
                                    <th style="width: 25%">Product</th>
                                    <th style="width: 10%">Quantity</th>
                                    <th style="width: 10%">UOM</th>
                                    <th style="width: 12%">Unit Price</th>
                                    <th style="width: 10%">Discount %</th>
                                    <th style="width: 10%">Tax %</th>
                                    <th style="width: 13%">Line Total</th>
                                    <th style="width: 10%">Action</th>
                                </tr>
                            </thead>
                            <tbody>
                                @if (Model.Items != null && Model.Items.Any())
                                {
                                    var itemsList = Model.Items.ToList();
                                    @for (int i = 0; i < itemsList.Count; i++)
                                    {
                                        var item = itemsList[i];
                                        <tr class="item-row">
                                            <td>
                                                <input type="hidden" name="items[@i].PurchaseOrderItemID" value="@item.PurchaseOrderItemID" />
                                                <input type="hidden" name="items[@i].GoodsReceiptItemID" value="@item.GoodsReceiptItemID" />
                                                <input type="hidden" name="items[@i].ProductID" value="@item.ProductID" />
                                                <input type="hidden" name="items[@i].ProductName" value="@item.ProductName" />
                                                <input type="hidden" name="items[@i].UOMID" value="@item.UOMID" />
                                                <input type="hidden" name="items[@i].LineNumber" value="@(i + 1)" />
                                                @item.ProductName
                                            </td>
                                            <td>
                                                <input type="number" name="items[@i].Quantity" value="@item.Quantity" 
                                                       class="form-control text-right quantity" step="0.01" min="0" />
                                            </td>
                                            <td>@item.UOM?.UOMName</td>
                                            <td>
                                                <input type="number" name="items[@i].UnitPrice" value="@item.UnitPrice" 
                                                       class="form-control text-right unit-price" step="0.01" min="0" />
                                            </td>
                                            <td>
                                                <input type="number" name="items[@i].DiscountPercent" value="@item.DiscountPercent" 
                                                       class="form-control text-right discount-percent" step="0.01" min="0" max="100" />
                                            </td>
                                            <td>
                                                <input type="number" name="items[@i].TaxRate" value="@item.TaxRate" 
                                                       class="form-control text-right tax-rate" step="0.01" min="0" />
                                            </td>
                                            <td class="text-right line-total">0.00</td>
                                            <td>
                                                <button type="button" class="btn btn-danger btn-sm remove-item">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </td>
                                        </tr>
                                    }
                                }
                                else
                                {
                                    <tr id="no-items-row">
                                        <td colspan="8" class="text-center">
                                            <em>No items added. Please select a Purchase Order or Goods Receipt.</em>
                                        </td>
                                    </tr>
                                }
                            </tbody>
                            <tfoot>
                                <tr>
                                    <th colspan="6" class="text-right">Subtotal:</th>
                                    <th class="text-right" id="subtotal">0.00</th>
                                    <th></th>
                                </tr>
                                <tr>
                                    <th colspan="6" class="text-right">Tax:</th>
                                    <th class="text-right" id="tax-total">0.00</th>
                                    <th></th>
                                </tr>
                                <tr>
                                    <th colspan="6" class="text-right">Total:</th>
                                    <th class="text-right" id="grand-total">0.00</th>
                                    <th></th>
                                </tr>
                            </tfoot>
                        </table>
                    </div>
                    
                    @if (Model.SupplierID == 0)
                    {
                        <div class="mt-3">
                            <button type="button" class="btn btn-secondary btn-sm" id="add-from-po">
                                <i class="fas fa-file-alt"></i> Add from Purchase Order
                            </button>
                        </div>
                    }
                </div>
                <div class="card-footer">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save"></i> Create Invoice
                    </button>
                    <a asp-action="Index" class="btn btn-default">Cancel</a>
                </div>
            </div>
        </form>
    </div>
</div>

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}
    <script>
        $(document).ready(function() {
            function calculateTotals() {
                var subtotal = 0;
                var taxTotal = 0;
                
                $('.item-row').each(function() {
                    var quantity = parseFloat($(this).find('.quantity').val()) || 0;
                    var unitPrice = parseFloat($(this).find('.unit-price').val()) || 0;
                    var discountPercent = parseFloat($(this).find('.discount-percent').val()) || 0;
                    var taxRate = parseFloat($(this).find('.tax-rate').val()) || 0;
                    
                    var lineTotal = quantity * unitPrice;
                    var discountAmount = lineTotal * discountPercent / 100;
                    lineTotal -= discountAmount;
                    var taxAmount = lineTotal * taxRate / 100;
                    
                    $(this).find('.line-total').text(lineTotal.toFixed(2));
                    
                    subtotal += lineTotal;
                    taxTotal += taxAmount;
                });
                
                $('#subtotal').text(subtotal.toFixed(2));
                $('#tax-total').text(taxTotal.toFixed(2));
                $('#grand-total').text((subtotal + taxTotal).toFixed(2));
            }
            
            // Calculate on input change
            $(document).on('input', '.quantity, .unit-price, .discount-percent, .tax-rate', function() {
                calculateTotals();
            });
            
            // Remove item
            $(document).on('click', '.remove-item', function() {
                $(this).closest('tr').remove();
                calculateTotals();
                reindexItems();
            });
            
            // Reindex items after removal
            function reindexItems() {
                $('.item-row').each(function(index) {
                    $(this).find('input').each(function() {
                        var name = $(this).attr('name');
                        if (name) {
                            name = name.replace(/\[\d+\]/, '[' + index + ']');
                            $(this).attr('name', name);
                        }
                    });
                });
            }
            
            // Initial calculation
            calculateTotals();
            
            // Add from PO button
            $('#add-from-po').click(function() {
                window.location.href = '@Url.Action("Create")?selectPurchaseOrder=true';
            });
        });
    </script>
}