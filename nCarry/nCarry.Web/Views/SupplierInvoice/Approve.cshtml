@model nCarry.Web.Models.Purchase.SupplierInvoice

@{
    ViewData["Title"] = "Approve Supplier Invoice";
}

<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">@ViewData["Title"]</h3>
            </div>
            <form asp-action="Approve" method="post">
                <div class="card-body">
                    <div class="alert alert-warning">
                        <i class="fas fa-exclamation-triangle"></i> 
                        You are about to approve this supplier invoice. Once approved, the invoice cannot be edited.
                    </div>
                    
                    <dl class="row">
                        <dt class="col-sm-3">Invoice Number</dt>
                        <dd class="col-sm-9">@Model.InvoiceNumber</dd>
                        
                        <dt class="col-sm-3">Supplier Invoice Number</dt>
                        <dd class="col-sm-9">@Model.SupplierInvoiceNumber</dd>
                        
                        <dt class="col-sm-3">Supplier</dt>
                        <dd class="col-sm-9">@Model.Supplier?.SupplierName</dd>
                        
                        <dt class="col-sm-3">Invoice Date</dt>
                        <dd class="col-sm-9">@Model.InvoiceDate.ToString("dd/MM/yyyy")</dd>
                        
                        <dt class="col-sm-3">Due Date</dt>
                        <dd class="col-sm-9">@Model.DueDate.ToString("dd/MM/yyyy")</dd>
                        
                        <dt class="col-sm-3">Total Amount</dt>
                        <dd class="col-sm-9"><strong>@Model.TotalAmount.ToString("C")</strong></dd>
                    </dl>
                    
                    <div class="form-check">
                        <input type="checkbox" class="form-check-input" id="confirmApproval" required>
                        <label class="form-check-label" for="confirmApproval">
                            I confirm that I have reviewed this invoice and approve it for payment.
                        </label>
                    </div>
                </div>
                <div class="card-footer">
                    <button type="submit" class="btn btn-success">
                        <i class="fas fa-check"></i> Approve Invoice
                    </button>
                    <a asp-action="Details" asp-route-id="@Model.SupplierInvoiceID" class="btn btn-default">Cancel</a>
                </div>
            </form>
        </div>
    </div>
</div>