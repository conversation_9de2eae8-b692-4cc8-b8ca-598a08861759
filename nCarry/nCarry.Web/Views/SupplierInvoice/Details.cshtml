@model nCarry.Web.Models.Purchase.SupplierInvoice

@{
    ViewData["Title"] = "Supplier Invoice Details";
    var payments = ViewBag.Payments as List<nCarry.Web.Models.Financial.PaymentAllocation>;
}

<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">@ViewData["Title"] - @Model.InvoiceNumber</h3>
                <div class="card-tools">
                    @if (Model.Status == "Draft")
                    {
                        <a asp-action="Approve" asp-route-id="@Model.SupplierInvoiceID" class="btn btn-success btn-sm">
                            <i class="fas fa-check"></i> Approve
                        </a>
                    }
                    @if (Model.Status == "Approved" && Model.OutstandingAmount > 0)
                    {
                        <a asp-action="CreatePayment" asp-route-id="@Model.SupplierInvoiceID" class="btn btn-primary btn-sm">
                            <i class="fas fa-money-bill"></i> Create Payment
                        </a>
                    }
                    <a asp-action="Index" class="btn btn-default btn-sm">
                        <i class="fas fa-arrow-left"></i> Back to List
                    </a>
                </div>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h5>Invoice Information</h5>
                        <dl class="row">
                            <dt class="col-sm-4">Invoice Number</dt>
                            <dd class="col-sm-8">@Model.InvoiceNumber</dd>

                            <dt class="col-sm-4">Supplier Invoice #</dt>
                            <dd class="col-sm-8">@Model.SupplierInvoiceNumber</dd>

                            <dt class="col-sm-4">Invoice Date</dt>
                            <dd class="col-sm-8">@Model.InvoiceDate.ToString("dd/MM/yyyy")</dd>

                            <dt class="col-sm-4">Due Date</dt>
                            <dd class="col-sm-8">
                                @Model.DueDate.ToString("dd/MM/yyyy")
                                @if (Model.DueDate < DateTime.Today && Model.OutstandingAmount > 0)
                                {
                                    <span class="badge badge-danger ml-2">Overdue</span>
                                }
                            </dd>

                            <dt class="col-sm-4">Status</dt>
                            <dd class="col-sm-8">
                                <span class="badge badge-@(Model.Status == "Approved" ? "success" : Model.Status == "Draft" ? "warning" : Model.Status == "Paid" ? "info" : "secondary")">
                                    @Model.Status
                                </span>
                            </dd>

                            <dt class="col-sm-4">Currency</dt>
                            <dd class="col-sm-8">@Model.Currency</dd>
                        </dl>
                    </div>
                    <div class="col-md-6">
                        <h5>Supplier Information</h5>
                        <dl class="row">
                            <dt class="col-sm-4">Supplier</dt>
                            <dd class="col-sm-8">
                                <a asp-controller="Supplier" asp-action="Details" asp-route-id="@Model.SupplierID">
                                    @Model.Supplier?.SupplierName
                                </a>
                            </dd>

                            @if (Model.PurchaseOrder != null)
                            {
                                <dt class="col-sm-4">Purchase Order</dt>
                                <dd class="col-sm-8">
                                    <a asp-controller="PurchaseOrder" asp-action="Details" asp-route-id="@Model.PurchaseOrderID">
                                        @Model.PurchaseOrder.PurchaseOrderNumber
                                    </a>
                                </dd>
                            }

                            @if (Model.GoodsReceipt != null)
                            {
                                <dt class="col-sm-4">Goods Receipt</dt>
                                <dd class="col-sm-8">
                                    <a asp-controller="GoodsReceipt" asp-action="Details" asp-route-id="@Model.GoodsReceiptID">
                                        @Model.GoodsReceipt.ReceiptNumber
                                    </a>
                                </dd>
                            }
                        </dl>
                        
                        <h5 class="mt-4">Financial Summary</h5>
                        <dl class="row">
                            <dt class="col-sm-4">Subtotal</dt>
                            <dd class="col-sm-8">@Model.SubTotal.ToString("C")</dd>

                            <dt class="col-sm-4">Tax Amount</dt>
                            <dd class="col-sm-8">@Model.TaxAmount.ToString("C")</dd>

                            <dt class="col-sm-4"><strong>Total Amount</strong></dt>
                            <dd class="col-sm-8"><strong>@Model.TotalAmount.ToString("C")</strong></dd>

                            <dt class="col-sm-4">Paid Amount</dt>
                            <dd class="col-sm-8">@Model.PaidAmount.ToString("C")</dd>

                            <dt class="col-sm-4"><strong>Outstanding</strong></dt>
                            <dd class="col-sm-8">
                                <strong class="@(Model.OutstandingAmount > 0 ? "text-danger" : "text-success")">
                                    @Model.OutstandingAmount.ToString("C")
                                </strong>
                            </dd>
                        </dl>
                    </div>
                </div>

                @if (!string.IsNullOrWhiteSpace(Model.Notes))
                {
                    <div class="row mt-3">
                        <div class="col-12">
                            <h5>Notes</h5>
                            <p>@Model.Notes</p>
                        </div>
                    </div>
                }

                <div class="row mt-4">
                    <div class="col-12">
                        <h5>Invoice Items</h5>
                        <div class="table-responsive">
                            <table class="table table-bordered table-sm">
                                <thead>
                                    <tr>
                                        <th>Line #</th>
                                        <th>Product</th>
                                        <th>Quantity</th>
                                        <th>UOM</th>
                                        <th>Unit Price</th>
                                        <th>Discount</th>
                                        <th>Tax Rate</th>
                                        <th>Tax Amount</th>
                                        <th>Line Total</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach (var item in Model.Items.OrderBy(i => i.LineNumber))
                                    {
                                        <tr>
                                            <td>@item.LineNumber</td>
                                            <td>
                                                <a asp-controller="Product" asp-action="Details" asp-route-id="@item.ProductID">
                                                    @item.ProductName
                                                </a>
                                            </td>
                                            <td class="text-right">@item.Quantity.ToString("N2")</td>
                                            <td>@item.UOM?.UOMName</td>
                                            <td class="text-right">@item.UnitPrice.ToString("C")</td>
                                            <td class="text-right">
                                                @if (item.DiscountAmount > 0)
                                                {
                                                    @item.DiscountAmount.ToString("C")
                                                }
                                                else
                                                {
                                                    <text>-</text>
                                                }
                                            </td>
                                            <td class="text-right">@item.TaxRate.ToString("N2")%</td>
                                            <td class="text-right">@item.TaxAmount.ToString("C")</td>
                                            <td class="text-right">@item.LineTotal.ToString("C")</td>
                                        </tr>
                                    }
                                </tbody>
                                <tfoot>
                                    <tr>
                                        <th colspan="8" class="text-right">Subtotal:</th>
                                        <th class="text-right">@Model.SubTotal.ToString("C")</th>
                                    </tr>
                                    <tr>
                                        <th colspan="8" class="text-right">Tax:</th>
                                        <th class="text-right">@Model.TaxAmount.ToString("C")</th>
                                    </tr>
                                    <tr>
                                        <th colspan="8" class="text-right">Total:</th>
                                        <th class="text-right">@Model.TotalAmount.ToString("C")</th>
                                    </tr>
                                </tfoot>
                            </table>
                        </div>
                    </div>
                </div>

                @if (payments != null && payments.Any())
                {
                    <div class="row mt-4">
                        <div class="col-12">
                            <h5>Payment History</h5>
                            <div class="table-responsive">
                                <table class="table table-bordered table-sm">
                                    <thead>
                                        <tr>
                                            <th>Payment Date</th>
                                            <th>Payment Number</th>
                                            <th>Amount</th>
                                            <th>Status</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        @foreach (var allocation in payments)
                                        {
                                            <tr>
                                                <td>@allocation.Payment?.PaymentDate.ToString("dd/MM/yyyy")</td>
                                                <td>
                                                    <a asp-controller="Payment" asp-action="Details" asp-route-id="@allocation.PaymentID">
                                                        @allocation.Payment?.PaymentNumber
                                                    </a>
                                                </td>
                                                <td class="text-right">@allocation.AllocatedAmount.ToString("C")</td>
                                                <td>
                                                    <span class="badge badge-@(allocation.Payment?.PaymentStatus == "Completed" ? "success" : "warning")">
                                                        @allocation.Payment?.PaymentStatus
                                                    </span>
                                                </td>
                                            </tr>
                                        }
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                }

                <div class="row mt-4">
                    <div class="col-12">
                        @if (Model.Status == "Draft")
                        {
                            <a asp-action="Approve" asp-route-id="@Model.SupplierInvoiceID" class="btn btn-success">
                                <i class="fas fa-check"></i> Approve Invoice
                            </a>
                        }
                        @if (Model.Status == "Approved" && Model.OutstandingAmount > 0)
                        {
                            <a asp-action="CreatePayment" asp-route-id="@Model.SupplierInvoiceID" class="btn btn-primary">
                                <i class="fas fa-money-bill"></i> Create Payment
                            </a>
                        }
                        <a asp-action="Index" class="btn btn-default">
                            <i class="fas fa-arrow-left"></i> Back to List
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>