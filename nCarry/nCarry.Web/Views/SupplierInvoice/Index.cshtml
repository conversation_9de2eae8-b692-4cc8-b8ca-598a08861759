@model IEnumerable<nCarry.Web.Models.Purchase.SupplierInvoice>

@{
    ViewData["Title"] = "Supplier Invoices";
}

<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">@ViewData["Title"]</h3>
                <div class="card-tools">
                    <a asp-action="Create" class="btn btn-primary btn-sm">
                        <i class="fas fa-plus"></i> New Supplier Invoice
                    </a>
                </div>
            </div>
            <div class="card-body">
                <form method="get" class="mb-3">
                    <div class="row">
                        <div class="col-md-3">
                            <div class="form-group">
                                <label>Supplier</label>
                                <select name="supplierId" class="form-control form-control-sm" asp-items="ViewBag.Suppliers">
                                    <option value="">All Suppliers</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-2">
                            <div class="form-group">
                                <label>Status</label>
                                <select name="status" class="form-control form-control-sm" asp-items="ViewBag.StatusList"></select>
                            </div>
                        </div>
                        <div class="col-md-2">
                            <div class="form-group">
                                <label>From Date</label>
                                <input type="date" name="fromDate" value="@ViewBag.FromDate?.ToString("yyyy-MM-dd")" class="form-control form-control-sm" />
                            </div>
                        </div>
                        <div class="col-md-2">
                            <div class="form-group">
                                <label>To Date</label>
                                <input type="date" name="toDate" value="@ViewBag.ToDate?.ToString("yyyy-MM-dd")" class="form-control form-control-sm" />
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="form-group">
                                <label>&nbsp;</label>
                                <button type="submit" class="btn btn-primary btn-sm btn-block">
                                    <i class="fas fa-search"></i> Search
                                </button>
                            </div>
                        </div>
                    </div>
                </form>

                @if (Model.Any())
                {
                    <div class="table-responsive">
                        <table class="table table-bordered table-striped table-sm">
                            <thead>
                                <tr>
                                    <th>Invoice Number</th>
                                    <th>Supplier Invoice #</th>
                                    <th>Invoice Date</th>
                                    <th>Supplier</th>
                                    <th>Due Date</th>
                                    <th>Status</th>
                                    <th>Total Amount</th>
                                    <th>Outstanding</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach (var item in Model)
                                {
                                    <tr>
                                        <td>@item.InvoiceNumber</td>
                                        <td>@item.SupplierInvoiceNumber</td>
                                        <td>@item.InvoiceDate.ToString("dd/MM/yyyy")</td>
                                        <td>
                                            <a asp-controller="Supplier" asp-action="Details" asp-route-id="@item.SupplierID">
                                                @item.Supplier?.SupplierName
                                            </a>
                                        </td>
                                        <td>@item.DueDate.ToString("dd/MM/yyyy")</td>
                                        <td>
                                            <span class="badge badge-@(item.Status == "Approved" ? "success" : item.Status == "Draft" ? "warning" : item.Status == "Paid" ? "info" : "secondary")">
                                                @item.Status
                                            </span>
                                        </td>
                                        <td class="text-right">@item.TotalAmount.ToString("C")</td>
                                        <td class="text-right">
                                            @if (item.OutstandingAmount > 0)
                                            {
                                                <span class="text-danger">@item.OutstandingAmount.ToString("C")</span>
                                            }
                                            else
                                            {
                                                <span class="text-success">Paid</span>
                                            }
                                        </td>
                                        <td>
                                            <a asp-action="Details" asp-route-id="@item.SupplierInvoiceID" class="btn btn-info btn-xs">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            @if (item.Status == "Draft")
                                            {
                                                <a asp-action="Approve" asp-route-id="@item.SupplierInvoiceID" class="btn btn-success btn-xs">
                                                    <i class="fas fa-check"></i>
                                                </a>
                                            }
                                            @if (item.Status == "Approved" && item.OutstandingAmount > 0)
                                            {
                                                <a asp-action="CreatePayment" asp-route-id="@item.SupplierInvoiceID" class="btn btn-primary btn-xs">
                                                    <i class="fas fa-money-bill"></i>
                                                </a>
                                            }
                                        </td>
                                    </tr>
                                }
                            </tbody>
                        </table>
                    </div>
                }
                else
                {
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle"></i> No supplier invoices found.
                    </div>
                }
            </div>
        </div>
    </div>
</div>