@{
    ViewData["Title"] = "Seed Sample Data";
}

<div class="container">
    <div class="row">
        <div class="col-md-8 offset-md-2">
            <h2 class="text-center mb-4">Seed Sample Data</h2>
            
            <div class="alert alert-info">
                <h5>Current Database Status</h5>
                <ul class="mb-0">
                    <li>Companies: <strong>@ViewBag.Stats.Companies</strong></li>
                    <li>Branches: <strong>@ViewBag.Stats.Branches</strong></li>
                    <li>Customers: <strong>@ViewBag.Stats.Customers</strong></li>
                    <li>Suppliers: <strong>@ViewBag.Stats.Suppliers</strong></li>
                    <li>Products: <strong>@ViewBag.Stats.Products</strong></li>
                    <li>Categories: <strong>@ViewBag.Stats.Categories</strong></li>
                    <li>Sales Orders: <strong>@ViewBag.Stats.SalesOrders</strong></li>
                    <li>Warehouses: <strong>@ViewBag.Stats.Warehouses</strong></li>
                </ul>
            </div>

            @if (TempData["Success"] != null)
            {
                <div class="alert alert-success">
                    @TempData["Success"]
                </div>
            }

            @if (TempData["Error"] != null)
            {
                <div class="alert alert-danger">
                    @TempData["Error"]
                </div>
            }

            <div class="card">
                <div class="card-body">
                    <h5 class="card-title">Sample Data</h5>
                    <p class="card-text">
                        This will insert sample data into the database including:
                    </p>
                    <ul>
                        <li>5 Companies and Branches</li>
                        <li>5 Customers with addresses</li>
                        <li>3 Suppliers</li>
                        <li>5 Products in different categories</li>
                        <li>Initial inventory in warehouse</li>
                        <li>3 Sample sales orders</li>
                        <li>Supporting data (currencies, tax codes, payment methods, etc.)</li>
                    </ul>
                    
                    <div class="alert alert-warning">
                        <strong>Note:</strong> This operation will only insert data if the tables are empty. 
                        Existing data will not be affected.
                    </div>

                    <form asp-action="SeedData" method="post" class="text-center">
                        <button type="submit" class="btn btn-primary" 
                                onclick="return confirm('Are you sure you want to seed sample data?')">
                            <i class="fas fa-database"></i> Seed Sample Data
                        </button>
                        <a asp-controller="Dashboard" asp-action="Index" class="btn btn-secondary">
                            <i class="fas fa-tachometer-alt"></i> Back to Dashboard
                        </a>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>