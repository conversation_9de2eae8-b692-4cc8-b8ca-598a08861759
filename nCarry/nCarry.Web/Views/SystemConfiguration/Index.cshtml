@model IEnumerable<nCarry.Web.Models.System.SystemConfiguration>

@{
    ViewData["Title"] = "System Configuration";
}

<div class="container-fluid">
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">@ViewData["Title"]</h1>
        <div>
            <a asp-action="Create" class="btn btn-sm btn-primary shadow-sm">
                <i class="fas fa-plus fa-sm text-white-50"></i> New Configuration
            </a>
            <a asp-action="Categories" class="btn btn-sm btn-info shadow-sm">
                <i class="fas fa-layer-group fa-sm text-white-50"></i> Categories
            </a>
            <div class="dropdown d-inline">
                <button class="btn btn-sm btn-success shadow-sm dropdown-toggle" type="button" id="toolsDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                    <i class="fas fa-tools fa-sm text-white-50"></i> Tools
                </button>
                <ul class="dropdown-menu" aria-labelledby="toolsDropdown">
                    <li><a class="dropdown-item" asp-action="Export">Export All</a></li>
                    <li><a class="dropdown-item" href="#" onclick="$('#importModal').modal('show')">Import</a></li>
                    <li><hr class="dropdown-divider"></li>
                    <li><a class="dropdown-item" asp-action="Reset" onclick="return confirm('This will reset all configurations to default values. Continue?')">Reset to Defaults</a></li>
                </ul>
            </div>
        </div>
    </div>

    @if (TempData["Success"] != null)
    {
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            @TempData["Success"]
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    }

    @if (TempData["Error"] != null)
    {
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            @TempData["Error"]
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    }

    @if (TempData["Info"] != null)
    {
        <div class="alert alert-info alert-dismissible fade show" role="alert">
            @TempData["Info"]
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    }

    <!-- Filter Card -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">Filter Configurations</h6>
        </div>
        <div class="card-body">
            <form method="get">
                <div class="row">
                    <div class="col-md-3">
                        <div class="form-group">
                            <label for="category">Category</label>
                            <select name="category" class="form-control">
                                <option value="">All Categories</option>
                                @foreach (var category in (ViewBag.Categories as SelectList)!)
                                {
                                    @if (category.Selected)
                                    {
                                        <option value="@category.Value" selected>@category.Text</option>
                                    }
                                    else
                                    {
                                        <option value="@category.Value">@category.Text</option>
                                    }
                                }
                            </select>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="searchTerm">Search</label>
                            <input type="text" name="searchTerm" class="form-control" placeholder="Search key, value, or description..." value="@ViewBag.SearchTerm" />
                        </div>
                    </div>
                    <div class="col-md-2">
                        <label>&nbsp;</label><br>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-search"></i> Search
                        </button>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- Configuration Groups -->
    @if (Model.Any())
    {
        var groupedConfigs = Model.GroupBy(c => c.Category).OrderBy(g => g.Key);
        
        foreach (var group in groupedConfigs)
        {
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <div class="row align-items-center">
                        <div class="col">
                            <h6 class="m-0 font-weight-bold text-primary">@group.Key Settings</h6>
                        </div>
                        <div class="col-auto">
                            <span class="badge bg-secondary">@group.Count() configurations</span>
                            <a asp-action="BulkEdit" asp-route-category="@group.Key" class="btn btn-sm btn-outline-primary">
                                <i class="fas fa-edit"></i> Bulk Edit
                            </a>
                            <a asp-action="Export" asp-route-category="@group.Key" class="btn btn-sm btn-outline-success">
                                <i class="fas fa-download"></i> Export
                            </a>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-bordered">
                            <thead>
                                <tr>
                                    <th style="width: 20%;">Configuration Key</th>
                                    <th style="width: 25%;">Current Value</th>
                                    <th style="width: 15%;">Data Type</th>
                                    <th style="width: 30%;">Description</th>
                                    <th style="width: 10%;">Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach (var config in group.OrderBy(c => c.ConfigKey))
                                {
                                    <tr>
                                        <td>
                                            <strong>@config.ConfigKey</strong>
                                            @if (!config.IsUserConfigurable)
                                            {
                                                <br><small class="text-danger"><i class="fas fa-lock"></i> System Only</small>
                                            }
                                        </td>
                                        <td>
                                            @if (config.DataType == "Password" && !string.IsNullOrEmpty(config.ConfigValue))
                                            {
                                                <code>••••••••••••</code>
                                            }
                                            else if (config.DataType == "Boolean")
                                            {
                                                if (config.ConfigValue.ToLower() == "true")
                                                {
                                                    <span class="badge bg-success">Yes</span>
                                                }
                                                else
                                                {
                                                    <span class="badge bg-secondary">No</span>
                                                }
                                            }
                                            else if (string.IsNullOrEmpty(config.ConfigValue))
                                            {
                                                <em class="text-muted">Not set</em>
                                            }
                                            else
                                            {
                                                <code>@config.ConfigValue</code>
                                            }
                                            
                                            @if (!string.IsNullOrEmpty(config.DefaultValue) && config.ConfigValue != config.DefaultValue)
                                            {
                                                <br><small class="text-muted">Default: @config.DefaultValue</small>
                                            }
                                        </td>
                                        <td>
                                            <span class="badge bg-info text-white">@config.DataType</span>
                                        </td>
                                        <td>
                                            @config.Description
                                            @if (!string.IsNullOrEmpty(config.ValidationRule))
                                            {
                                                <br><small class="text-warning"><i class="fas fa-exclamation-triangle"></i> @config.ValidationRule</small>
                                            }
                                        </td>
                                        <td>
                                            <div class="btn-group" role="group">
                                                <a asp-action="Details" asp-route-id="@config.ConfigID" class="btn btn-sm btn-info" title="Details">
                                                    <i class="fas fa-eye"></i>
                                                </a>
                                                <a asp-action="Edit" asp-route-id="@config.ConfigID" class="btn btn-sm btn-warning" title="Edit">
                                                    <i class="fas fa-edit"></i>
                                                </a>
                                                <a asp-action="Delete" asp-route-id="@config.ConfigID" class="btn btn-sm btn-danger" title="Delete" onclick="return confirm('Are you sure you want to delete this configuration?')">
                                                    <i class="fas fa-trash"></i>
                                                </a>
                                            </div>
                                        </td>
                                    </tr>
                                }
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        }
    }
    else
    {
        <div class="card shadow mb-4">
            <div class="card-body text-center">
                <i class="fas fa-cogs fa-3x text-gray-300 mb-3"></i>
                <h5>No Configurations Found</h5>
                <p class="text-muted">No system configurations match your search criteria.</p>
                <a asp-action="Create" class="btn btn-primary">Create First Configuration</a>
                <a asp-action="Reset" class="btn btn-success" onclick="return confirm('Load default configurations?')">Load Defaults</a>
            </div>
        </div>
    }
</div>

<!-- Import Modal -->
<div class="modal fade" id="importModal" tabindex="-1" aria-labelledby="importModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="importModalLabel">Import Configurations</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form asp-action="Import" method="post" enctype="multipart/form-data">
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="file" class="form-label">CSV File</label>
                        <input type="file" name="file" class="form-control" accept=".csv" required />
                        <div class="form-text">Select a CSV file with configuration data to import.</div>
                    </div>
                    
                    <div class="alert alert-info">
                        <h6><i class="fas fa-info-circle"></i> CSV Format</h6>
                        <p class="mb-2">The CSV file should contain the following columns:</p>
                        <small><code>Category,ConfigKey,ConfigValue,DataType,Description,IsUserConfigurable,ValidationRule,DefaultValue</code></small>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">Import</button>
                </div>
            </form>
        </div>
    </div>
</div>

@section Scripts {
    <script>
        $(document).ready(function() {
            // Initialize tooltips
            $('[title]').tooltip();
        });
    </script>
}