@model IEnumerable<dynamic>

@{
    ViewData["Title"] = "Configuration Categories";
}

<div class="container-fluid">
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">@ViewData["Title"]</h1>
        <div>
            <a asp-action="Create" class="btn btn-sm btn-primary shadow-sm">
                <i class="fas fa-plus fa-sm text-white-50"></i> New Configuration
            </a>
            <a asp-action="Index" class="btn btn-sm btn-secondary shadow-sm">
                <i class="fas fa-arrow-left fa-sm text-white-50"></i> Back to List
            </a>
        </div>
    </div>

    <div class="row">
        @foreach (var category in Model)
        {
            <div class="col-xl-4 col-md-6 mb-4">
                <div class="card border-left-primary shadow h-100">
                    <div class="card-body">
                        <div class="row no-gutters align-items-center">
                            <div class="col mr-2">
                                <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                    @category.Category
                                </div>
                                <div class="h5 mb-0 font-weight-bold text-gray-800 mb-2">@category.Count Configurations</div>
                                
                                <div class="row">
                                    <div class="col-6">
                                        <small class="text-muted">User Configurable:</small><br>
                                        <span class="badge bg-success">@category.UserConfigurable</span>
                                    </div>
                                    <div class="col-6">
                                        <small class="text-muted">System Only:</small><br>
                                        <span class="badge bg-warning">@(category.Count - category.UserConfigurable)</span>
                                    </div>
                                </div>
                                
                                <div class="mt-2">
                                    <small class="text-muted">Last Updated:</small><br>
                                    <small>@category.LastUpdated.ToString("dd/MM/yyyy HH:mm")</small>
                                </div>
                            </div>
                            <div class="col-auto">
                                <i class="fas fa-@(GetCategoryIcon(category.Category)) fa-2x text-gray-300"></i>
                            </div>
                        </div>
                        
                        <div class="mt-3">
                            <div class="btn-group w-100" role="group">
                                <a asp-action="Index" asp-route-category="@category.Category" class="btn btn-sm btn-primary">
                                    <i class="fas fa-eye"></i> View
                                </a>
                                <a asp-action="BulkEdit" asp-route-category="@category.Category" class="btn btn-sm btn-warning">
                                    <i class="fas fa-edit"></i> Edit
                                </a>
                                <a asp-action="Export" asp-route-category="@category.Category" class="btn btn-sm btn-success">
                                    <i class="fas fa-download"></i> Export
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        }
    </div>

    <!-- Category Descriptions -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">Category Descriptions</h6>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-6">
                    <dl>
                        <dt><i class="fas fa-building text-primary"></i> Company</dt>
                        <dd class="mb-3">Basic company information like name, address, contact details</dd>
                        
                        <dt><i class="fas fa-cog text-primary"></i> System</dt>
                        <dd class="mb-3">Core system settings including language, timezone, session timeout</dd>
                        
                        <dt><i class="fas fa-envelope text-primary"></i> Email</dt>
                        <dd class="mb-3">SMTP configuration for sending emails and notifications</dd>
                        
                        <dt><i class="fas fa-boxes text-primary"></i> Inventory</dt>
                        <dd class="mb-3">Inventory management settings like stock thresholds and reorder points</dd>
                    </dl>
                </div>
                <div class="col-md-6">
                    <dl>
                        <dt><i class="fas fa-shopping-cart text-primary"></i> Sales</dt>
                        <dd class="mb-3">Sales process settings including payment terms and order approval</dd>
                        
                        <dt><i class="fas fa-shipping-fast text-primary"></i> Shipping</dt>
                        <dd class="mb-3">Shipping configuration including methods, thresholds, and packaging</dd>
                        
                        <dt><i class="fas fa-shield-alt text-primary"></i> Security</dt>
                        <dd class="mb-3">Security policies including password rules and access controls</dd>
                        
                        <dt><i class="fas fa-plug text-primary"></i> Integration</dt>
                        <dd class="mb-3">API keys and external system integration settings</dd>
                    </dl>
                </div>
            </div>
        </div>
    </div>
</div>

@section Styles {
    <style>
        .card.border-left-primary {
            border-left: 0.25rem solid #4e73df !important;
        }
        
        .btn-group .btn {
            flex: 1;
        }
    </style>
}

@functions {
    string GetCategoryIcon(string category)
    {
        return category switch
        {
            "Company" => "building",
            "System" => "cog",
            "Email" => "envelope",
            "Inventory" => "boxes",
            "Sales" => "shopping-cart",
            "Shipping" => "shipping-fast",
            "Security" => "shield-alt",
            "Integration" => "plug",
            _ => "cogs"
        };
    }
}