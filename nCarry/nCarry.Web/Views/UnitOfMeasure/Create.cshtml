@model nCarry.Web.Models.Products.UnitOfMeasure

@{
    ViewData["Title"] = "Create Unit of Measure";
}

<div class="page-header">
    <div class="row align-items-center">
        <div class="col">
            <h3 class="page-title">@ViewData["Title"]</h3>
            <ul class="breadcrumb">
                <li class="breadcrumb-item"><a asp-area="" asp-controller="Dashboard" asp-action="Index">Dashboard</a></li>
                <li class="breadcrumb-item"><a asp-action="Index">Units of Measure</a></li>
                <li class="breadcrumb-item active">Create</li>
            </ul>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-md-12">
        <div class="card">
            <div class="card-body">
                <form asp-action="Create">
                    <div asp-validation-summary="ModelOnly" class="text-danger"></div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label asp-for="UOMCode" class="control-label">UOM Code</label>
                                <input asp-for="UOMCode" class="form-control" />
                                <span asp-validation-for="UOMCode" class="text-danger"></span>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label asp-for="UOMName" class="control-label">UOM Name</label>
                                <input asp-for="UOMName" class="form-control" />
                                <span asp-validation-for="UOMName" class="text-danger"></span>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label asp-for="UOMType" class="control-label">UOM Type</label>
                                <select asp-for="UOMType" class="form-control">
                                    <option value="">-- Select Type --</option>
                                    <option value="Count">Count</option>
                                    <option value="Weight">Weight</option>
                                    <option value="Volume">Volume</option>
                                    <option value="Length">Length</option>
                                    <option value="Time">Time</option>
                                </select>
                                <span asp-validation-for="UOMType" class="text-danger"></span>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label asp-for="Symbol" class="control-label">Symbol</label>
                                <input asp-for="Symbol" class="form-control" />
                                <span asp-validation-for="Symbol" class="text-danger"></span>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label asp-for="ConversionFactor" class="control-label">Conversion Factor</label>
                                <input asp-for="ConversionFactor" class="form-control" value="1" />
                                <span asp-validation-for="ConversionFactor" class="text-danger"></span>
                                <small class="form-text text-muted">Factor to convert to base unit</small>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label asp-for="DecimalPlaces" class="control-label">Decimal Places</label>
                                <input asp-for="DecimalPlaces" class="form-control" type="number" min="0" max="6" value="2" />
                                <span asp-validation-for="DecimalPlaces" class="text-danger"></span>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <div class="form-check">
                                    <input asp-for="BaseUnit" class="form-check-input" />
                                    <label asp-for="BaseUnit" class="form-check-label">Base Unit</label>
                                </div>
                                <small class="form-text text-muted">Is this the base unit for its type?</small>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <div class="form-check">
                                    <input asp-for="IsActive" class="form-check-input" checked />
                                    <label asp-for="IsActive" class="form-check-label">Active</label>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="form-group mt-3">
                        <button type="submit" class="btn btn-primary">Create</button>
                        <a asp-action="Index" class="btn btn-secondary">Cancel</a>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}
}