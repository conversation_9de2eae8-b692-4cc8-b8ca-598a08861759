@model IEnumerable<nCarry.Web.Models.Products.UnitOfMeasure>

@{
    ViewData["Title"] = "Units of Measure";
}

<div class="page-header">
    <div class="row align-items-center">
        <div class="col">
            <h3 class="page-title">@ViewData["Title"]</h3>
            <ul class="breadcrumb">
                <li class="breadcrumb-item"><a asp-area="" asp-controller="Dashboard" asp-action="Index">Dashboard</a></li>
                <li class="breadcrumb-item active">Units of Measure</li>
            </ul>
        </div>
        <div class="col-auto">
            <a asp-action="Create" class="btn btn-primary">
                <i class="fas fa-plus"></i> New Unit
            </a>
        </div>
    </div>
</div>

@if (TempData["Success"] != null)
{
    <div class="alert alert-success alert-dismissible fade show" role="alert">
        @TempData["Success"]
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    </div>
}

@if (TempData["Error"] != null)
{
    <div class="alert alert-danger alert-dismissible fade show" role="alert">
        @TempData["Error"]
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    </div>
}

<div class="row">
    <div class="col-sm-12">
        <div class="card">
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-stripped">
                        <thead>
                            <tr>
                                <th>Code</th>
                                <th>Name</th>
                                <th>Symbol</th>
                                <th>Type</th>
                                <th>Base Unit</th>
                                <th>Conversion Factor</th>
                                <th>Decimal Places</th>
                                <th>Status</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach (var item in Model)
                            {
                                <tr>
                                    <td>@item.UOMCode</td>
                                    <td>@item.UOMName</td>
                                    <td>@(item.Symbol ?? "-")</td>
                                    <td>
                                        <span class="badge bg-info">@item.UOMType</span>
                                    </td>
                                    <td>
                                        @if (item.BaseUnit)
                                        {
                                            <span class="badge bg-success">Yes</span>
                                        }
                                        else
                                        {
                                            <span class="badge bg-secondary">No</span>
                                        }
                                    </td>
                                    <td>@item.ConversionFactor</td>
                                    <td>@item.DecimalPlaces</td>
                                    <td>
                                        @if (item.IsActive)
                                        {
                                            <span class="badge bg-success">Active</span>
                                        }
                                        else
                                        {
                                            <span class="badge bg-danger">Inactive</span>
                                        }
                                    </td>
                                    <td>
                                        <div class="actions">
                                            <a asp-action="Details" asp-route-id="@item.UOMID" class="btn btn-sm bg-info-light me-2">
                                                <i class="far fa-eye"></i>
                                            </a>
                                            <a asp-action="Edit" asp-route-id="@item.UOMID" class="btn btn-sm bg-warning-light me-2">
                                                <i class="far fa-edit"></i>
                                            </a>
                                            <a asp-action="Delete" asp-route-id="@item.UOMID" class="btn btn-sm bg-danger-light">
                                                <i class="far fa-trash-alt"></i>
                                            </a>
                                        </div>
                                    </td>
                                </tr>
                            }
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>