@model nCarry.Web.Models.Products.UnitOfMeasure

@{
    ViewData["Title"] = "Unit of Measure Details";
}

<div class="page-header">
    <div class="row align-items-center">
        <div class="col">
            <h3 class="page-title">@ViewData["Title"]</h3>
            <ul class="breadcrumb">
                <li class="breadcrumb-item"><a asp-area="" asp-controller="Dashboard" asp-action="Index">Dashboard</a></li>
                <li class="breadcrumb-item"><a asp-action="Index">Units of Measure</a></li>
                <li class="breadcrumb-item active">Details</li>
            </ul>
        </div>
        <div class="col-auto">
            <a asp-action="Edit" asp-route-id="@Model?.UOMID" class="btn btn-primary">
                <i class="fas fa-edit"></i> Edit
            </a>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-md-12">
        <div class="card">
            <div class="card-body">
                <h4>Unit Information</h4>
                <hr />
                <dl class="row">
                    <dt class="col-sm-2">UOM Code</dt>
                    <dd class="col-sm-10">@Model.UOMCode</dd>

                    <dt class="col-sm-2">UOM Name</dt>
                    <dd class="col-sm-10">@Model.UOMName</dd>

                    <dt class="col-sm-2">Symbol</dt>
                    <dd class="col-sm-10">@(Model?.Symbol ?? "N/A")</dd>

                    <dt class="col-sm-2">Type</dt>
                    <dd class="col-sm-10">
                        <span class="badge bg-info">@Model.UOMType</span>
                    </dd>

                    <dt class="col-sm-2">Base Unit</dt>
                    <dd class="col-sm-10">
                        @if (Model.BaseUnit)
                        {
                            <span class="badge bg-success">Yes</span>
                        }
                        else
                        {
                            <span class="badge bg-secondary">No</span>
                        }
                    </dd>

                    <dt class="col-sm-2">Conversion Factor</dt>
                    <dd class="col-sm-10">@Model.ConversionFactor</dd>

                    <dt class="col-sm-2">Decimal Places</dt>
                    <dd class="col-sm-10">@Model.DecimalPlaces</dd>

                    <dt class="col-sm-2">Status</dt>
                    <dd class="col-sm-10">
                        @if (Model.IsActive)
                        {
                            <span class="badge bg-success">Active</span>
                        }
                        else
                        {
                            <span class="badge bg-danger">Inactive</span>
                        }
                    </dd>

                    <dt class="col-sm-2">Created Date</dt>
                    <dd class="col-sm-10">@Model.CreatedDate.ToString("yyyy-MM-dd HH:mm")</dd>

                    <dt class="col-sm-2">Updated Date</dt>
                    <dd class="col-sm-10">@Model.UpdatedDate.ToString("yyyy-MM-dd HH:mm")</dd>
                </dl>

                <div class="mt-3">
                    <a asp-action="Index" class="btn btn-secondary">Back to List</a>
                </div>
            </div>
        </div>
    </div>
</div>