@model nCarry.Web.Models.Products.UnitOfMeasure

@{
    ViewData["Title"] = "Delete Unit of Measure";
}

<div class="page-header">
    <div class="row align-items-center">
        <div class="col">
            <h3 class="page-title">@ViewData["Title"]</h3>
            <ul class="breadcrumb">
                <li class="breadcrumb-item"><a asp-area="" asp-controller="Dashboard" asp-action="Index">Dashboard</a></li>
                <li class="breadcrumb-item"><a asp-action="Index">Units of Measure</a></li>
                <li class="breadcrumb-item active">Delete</li>
            </ul>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-md-12">
        <div class="card">
            <div class="card-body">
                <div class="alert alert-danger">
                    <h4>Are you sure you want to delete this unit of measure?</h4>
                    <p>This action cannot be undone.</p>
                </div>

                <dl class="row">
                    <dt class="col-sm-2">UOM Code</dt>
                    <dd class="col-sm-10">@Model.UOMCode</dd>

                    <dt class="col-sm-2">UOM Name</dt>
                    <dd class="col-sm-10">@Model.UOMName</dd>

                    <dt class="col-sm-2">Symbol</dt>
                    <dd class="col-sm-10">@(Model.Symbol ?? "N/A")</dd>

                    <dt class="col-sm-2">Type</dt>
                    <dd class="col-sm-10">
                        <span class="badge bg-info">@Model.UOMType</span>
                    </dd>

                    <dt class="col-sm-2">Base Unit</dt>
                    <dd class="col-sm-10">
                        @if (Model.BaseUnit)
                        {
                            <span class="badge bg-success">Yes</span>
                        }
                        else
                        {
                            <span class="badge bg-secondary">No</span>
                        }
                    </dd>

                    <dt class="col-sm-2">Conversion Factor</dt>
                    <dd class="col-sm-10">@Model.ConversionFactor</dd>
                </dl>

                <form asp-action="Delete" class="mt-3">
                    <input type="hidden" asp-for="UOMID" />
                    <button type="submit" class="btn btn-danger">Delete</button>
                    <a asp-action="Index" class="btn btn-secondary">Cancel</a>
                </form>
            </div>
        </div>
    </div>
</div>