@model nCarry.Web.Models.ViewModels.SalesOrderEditViewModel

@{
    ViewData["Title"] = "Edit Sales Order";
}

<div class="container-fluid">
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">@ViewData["Title"]</h1>
    </div>

    <div class="row">
        <div class="col-lg-12">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Order Information - @Model.OrderNumber</h6>
                </div>
                <div class="card-body">
                    <form asp-action="Edit">
                        <div asp-validation-summary="ModelOnly" class="text-danger"></div>
                        <input type="hidden" asp-for="OrderID" />
                        <input type="hidden" asp-for="OrderNumber" />
                        
                        <div class="row">
                            <!-- Customer Information -->
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label asp-for="CustomerID" class="control-label"></label>
                                    <select asp-for="CustomerID" class="form-control" asp-items="Model.Customers">
                                        <option value="">-- Select Customer --</option>
                                    </select>
                                    <span asp-validation-for="CustomerID" class="text-danger"></span>
                                </div>

                                <div class="form-group">
                                    <label asp-for="CustomerPO" class="control-label"></label>
                                    <input asp-for="CustomerPO" class="form-control" />
                                    <span asp-validation-for="CustomerPO" class="text-danger"></span>
                                </div>

                                <div class="form-group">
                                    <label asp-for="QuoteID" class="control-label"></label>
                                    <select asp-for="QuoteID" class="form-control" asp-items="Model.Quotes">
                                        <option value="">-- Select Quote (Optional) --</option>
                                    </select>
                                    <span asp-validation-for="QuoteID" class="text-danger"></span>
                                </div>

                                <div class="form-group">
                                    <label asp-for="SalesRepID" class="control-label"></label>
                                    <select asp-for="SalesRepID" class="form-control" asp-items="Model.SalesReps">
                                        <option value="">-- Select Sales Rep (Optional) --</option>
                                    </select>
                                    <span asp-validation-for="SalesRepID" class="text-danger"></span>
                                </div>
                            </div>

                            <!-- Order Details -->
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label asp-for="OrderStatus" class="control-label"></label>
                                    <select asp-for="OrderStatus" class="form-control">
                                        <option value="Pending">Pending</option>
                                        <option value="Confirmed">Confirmed</option>
                                        <option value="Processing">Processing</option>
                                        <option value="Shipped">Shipped</option>
                                        <option value="Delivered">Delivered</option>
                                        <option value="Cancelled">Cancelled</option>
                                        <option value="On-Hold">On Hold</option>
                                    </select>
                                    <span asp-validation-for="OrderStatus" class="text-danger"></span>
                                </div>

                                <div class="form-group">
                                    <label asp-for="OrderType" class="control-label"></label>
                                    <select asp-for="OrderType" class="form-control">
                                        <option value="Standard">Standard</option>
                                        <option value="Rush">Rush</option>
                                        <option value="Backorder">Backorder</option>
                                        <option value="Pre-order">Pre-order</option>
                                    </select>
                                    <span asp-validation-for="OrderType" class="text-danger"></span>
                                </div>

                                <div class="form-group">
                                    <label asp-for="RequestedDate" class="control-label"></label>
                                    <input asp-for="RequestedDate" class="form-control" type="date" />
                                    <span asp-validation-for="RequestedDate" class="text-danger"></span>
                                </div>

                                <div class="form-group">
                                    <label asp-for="PromisedDate" class="control-label"></label>
                                    <input asp-for="PromisedDate" class="form-control" type="date" />
                                    <span asp-validation-for="PromisedDate" class="text-danger"></span>
                                </div>
                            </div>
                        </div>

                        <!-- Shipping Information -->
                        <div class="row">
                            <div class="col-md-12">
                                <h5 class="text-primary mt-4 mb-3">Shipping Information</h5>
                            </div>
                            
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label asp-for="BillingAddressID" class="control-label"></label>
                                    <select asp-for="BillingAddressID" class="form-control" asp-items="Model.CustomerAddresses">
                                        <option value="">-- Select Billing Address (Optional) --</option>
                                    </select>
                                    <span asp-validation-for="BillingAddressID" class="text-danger"></span>
                                </div>

                                <div class="form-group">
                                    <label asp-for="ShippingMethod" class="control-label"></label>
                                    <input asp-for="ShippingMethod" class="form-control" />
                                    <span asp-validation-for="ShippingMethod" class="text-danger"></span>
                                </div>

                                <div class="form-group">
                                    <label asp-for="TrackingNumber" class="control-label"></label>
                                    <input asp-for="TrackingNumber" class="form-control" />
                                    <span asp-validation-for="TrackingNumber" class="text-danger"></span>
                                </div>
                            </div>

                            <div class="col-md-6">
                                <div class="form-group">
                                    <label asp-for="ShippingAddressID" class="control-label"></label>
                                    <select asp-for="ShippingAddressID" class="form-control" asp-items="Model.CustomerAddresses">
                                        <option value="">-- Select Shipping Address (Optional) --</option>
                                    </select>
                                    <span asp-validation-for="ShippingAddressID" class="text-danger"></span>
                                </div>

                                <div class="form-group">
                                    <label asp-for="FreightTerms" class="control-label"></label>
                                    <input asp-for="FreightTerms" class="form-control" />
                                    <span asp-validation-for="FreightTerms" class="text-danger"></span>
                                </div>

                                <div class="form-group">
                                    <label asp-for="WarehouseID" class="control-label"></label>
                                    <select asp-for="WarehouseID" class="form-control" asp-items="Model.Warehouses">
                                        <option value="">-- Select Warehouse (Optional) --</option>
                                    </select>
                                    <span asp-validation-for="WarehouseID" class="text-danger"></span>
                                </div>
                            </div>

                            <div class="col-md-12">
                                <div class="form-group">
                                    <label asp-for="DeliveryInstructions" class="control-label"></label>
                                    <textarea asp-for="DeliveryInstructions" class="form-control" rows="3"></textarea>
                                    <span asp-validation-for="DeliveryInstructions" class="text-danger"></span>
                                </div>
                            </div>
                        </div>

                        <!-- Financial Information -->
                        <div class="row">
                            <div class="col-md-12">
                                <h5 class="text-primary mt-4 mb-3">Financial Information</h5>
                            </div>
                            
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label asp-for="Currency" class="control-label"></label>
                                    <select asp-for="Currency" class="form-control">
                                        <option value="GBP">GBP - British Pound</option>
                                        <option value="USD">USD - US Dollar</option>
                                        <option value="EUR">EUR - Euro</option>
                                    </select>
                                    <span asp-validation-for="Currency" class="text-danger"></span>
                                </div>
                            </div>

                            <div class="col-md-3">
                                <div class="form-group">
                                    <label asp-for="ExchangeRate" class="control-label"></label>
                                    <input asp-for="ExchangeRate" class="form-control" step="0.000001" />
                                    <span asp-validation-for="ExchangeRate" class="text-danger"></span>
                                </div>
                            </div>

                            <div class="col-md-3">
                                <div class="form-group">
                                    <label asp-for="PaymentTerms" class="control-label"></label>
                                    <input asp-for="PaymentTerms" class="form-control" placeholder="e.g., Net 30" />
                                    <span asp-validation-for="PaymentTerms" class="text-danger"></span>
                                </div>
                            </div>

                            <div class="col-md-3">
                                <div class="form-group">
                                    <label asp-for="PaymentDueDate" class="control-label"></label>
                                    <input asp-for="PaymentDueDate" class="form-control" type="date" />
                                    <span asp-validation-for="PaymentDueDate" class="text-danger"></span>
                                </div>
                            </div>
                        </div>

                        <!-- Additional Information -->
                        <div class="row">
                            <div class="col-md-12">
                                <h5 class="text-primary mt-4 mb-3">Additional Information</h5>
                            </div>
                            
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label asp-for="PickingPriority" class="control-label"></label>
                                    <input asp-for="PickingPriority" class="form-control" />
                                    <span asp-validation-for="PickingPriority" class="text-danger"></span>
                                    <small class="form-text text-muted">Lower numbers indicate higher priority (1 = highest priority)</small>
                                </div>

                                <div class="form-group">
                                    <label asp-for="Notes" class="control-label"></label>
                                    <textarea asp-for="Notes" class="form-control" rows="3"></textarea>
                                    <span asp-validation-for="Notes" class="text-danger"></span>
                                </div>
                            </div>

                            <div class="col-md-6">
                                <div class="form-group">
                                    <label asp-for="InternalNotes" class="control-label"></label>
                                    <textarea asp-for="InternalNotes" class="form-control" rows="3"></textarea>
                                    <span asp-validation-for="InternalNotes" class="text-danger"></span>
                                    <small class="form-text text-muted">Internal notes (not visible to customer)</small>
                                </div>
                            </div>
                        </div>

                        <!-- Order Items Section -->
                        <div class="row">
                            <div class="col-md-12">
                                <h5 class="text-primary mt-4 mb-3">Order Items</h5>
                                
                                <div id="orderItems">
                                    @for (int i = 0; i < Model.Items.Count; i++)
                                    {
                                        <div class="card mb-3 order-item" data-index="@i">
                                            <div class="card-body">
                                                <div class="row">
                                                    <input type="hidden" asp-for="Items[@i].OrderItemID" />
                                                    <input type="hidden" asp-for="Items[@i].OrderID" />
                                                    <input type="hidden" asp-for="Items[@i].LineNumber" />
                                                    
                                                    <div class="col-md-3">
                                                        <div class="form-group">
                                                            <label asp-for="Items[@i].ProductID">Product</label>
                                                            <select asp-for="Items[@i].ProductID" class="form-control product-select" asp-items="Model.Products">
                                                                <option value="">-- Select Product --</option>
                                                            </select>
                                                        </div>
                                                    </div>
                                                    
                                                    <div class="col-md-2">
                                                        <div class="form-group">
                                                            <label asp-for="Items[@i].OrderedQuantity">Quantity</label>
                                                            <input asp-for="Items[@i].OrderedQuantity" class="form-control quantity-input" step="0.001" />
                                                        </div>
                                                    </div>
                                                    
                                                    <div class="col-md-2">
                                                        <div class="form-group">
                                                            <label asp-for="Items[@i].UOMID">Unit</label>
                                                            <select asp-for="Items[@i].UOMID" class="form-control" asp-items="Model.UnitOfMeasures">
                                                                <option value="">-- Select Unit --</option>
                                                            </select>
                                                        </div>
                                                    </div>
                                                    
                                                    <div class="col-md-2">
                                                        <div class="form-group">
                                                            <label asp-for="Items[@i].UnitPrice">Unit Price</label>
                                                            <input asp-for="Items[@i].UnitPrice" class="form-control price-input" step="0.01" />
                                                        </div>
                                                    </div>
                                                    
                                                    <div class="col-md-2">
                                                        <div class="form-group">
                                                            <label asp-for="Items[@i].TaxRate">Tax %</label>
                                                            <input asp-for="Items[@i].TaxRate" class="form-control tax-input" step="0.01" />
                                                        </div>
                                                    </div>
                                                    
                                                    <div class="col-md-1">
                                                        <div class="form-group">
                                                            <label>&nbsp;</label>
                                                            <button type="button" class="btn btn-danger btn-sm form-control remove-item">Remove</button>
                                                        </div>
                                                    </div>
                                                </div>
                                                
                                                <div class="row">
                                                    <div class="col-md-6">
                                                        <div class="form-group">
                                                            <label asp-for="Items[@i].Description">Description</label>
                                                            <input asp-for="Items[@i].Description" class="form-control" />
                                                        </div>
                                                    </div>
                                                    
                                                    <div class="col-md-3">
                                                        <div class="form-group">
                                                            <label>Line Total (Read-only)</label>
                                                            <input type="text" class="form-control line-total" readonly />
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    }
                                </div>
                                
                                <button type="button" id="addItem" class="btn btn-primary mb-3">Add New Item</button>
                                
                                <!-- Order Totals -->
                                <div class="card">
                                    <div class="card-body">
                                        <div class="row">
                                            <div class="col-md-8"></div>
                                            <div class="col-md-4">
                                                <table class="table">
                                                    <tr>
                                                        <td><strong>Subtotal:</strong></td>
                                                        <td id="orderSubtotal">$0.00</td>
                                                    </tr>
                                                    <tr>
                                                        <td><strong>Tax:</strong></td>
                                                        <td id="orderTax">$0.00</td>
                                                    </tr>
                                                    <tr>
                                                        <td><strong>Total:</strong></td>
                                                        <td><strong id="orderTotal">$0.00</strong></td>
                                                    </tr>
                                                </table>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="form-group mt-4">
                            <input type="submit" value="Update Order" class="btn btn-success" />
                            <a asp-action="Details" asp-route-id="@Model.OrderID" class="btn btn-info">View Details</a>
                            <a asp-action="Index" class="btn btn-secondary">Back to List</a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}
    
    <script>
        $(document).ready(function() {
            var itemIndex = @Model.Items.Count;
            
            // Store product and UOM options as JSON for dynamic use
            var productOptions = [
                { value: '', text: '-- Select Product --' }
                @if(Model.Products != null)
                {
                    foreach(var product in Model.Products)
                    {
                        <text>, { value: '@product.Value', text: '@Html.Raw(product.Text.Replace("'", "\\'"))' }</text>
                    }
                }
            ];
            
            var uomOptions = [
                { value: '', text: '-- Select Unit --' }
                @if(Model.UnitOfMeasures != null)
                {
                    foreach(var uom in Model.UnitOfMeasures)
                    {
                        <text>, { value: '@uom.Value', text: '@Html.Raw(uom.Text.Replace("'", "\\'"))' }</text>
                    }
                }
            ];
            
            // When customer changes, load customer addresses
            $('#CustomerID').change(function() {
                var customerId = $(this).val();
                if (customerId) {
                    // In a real implementation, you would make an AJAX call here
                    // to load customer addresses for the selected customer
                    console.log('Selected customer:', customerId);
                } else {
                    $('#BillingAddressID').empty().append('<option value="">-- Select Billing Address (Optional) --</option>');
                    $('#ShippingAddressID').empty().append('<option value="">-- Select Shipping Address (Optional) --</option>');
                }
            });

            // Add new item
            $('#addItem').click(function() {
                // Build product options HTML
                var productOptionsHtml = '';
                productOptions.forEach(function(option) {
                    productOptionsHtml += `<option value="${option.value}">${option.text}</option>`;
                });
                
                // Build UOM options HTML  
                var uomOptionsHtml = '';
                uomOptions.forEach(function(option) {
                    uomOptionsHtml += `<option value="${option.value}">${option.text}</option>`;
                });
                
                var newItem = `
                    <div class="card mb-3 order-item" data-index="${itemIndex}">
                        <div class="card-body">
                            <div class="row">
                                <input type="hidden" name="Items[${itemIndex}].OrderItemID" value="" />
                                <input type="hidden" name="Items[${itemIndex}].OrderID" value="@Model.OrderID" />
                                <input type="hidden" name="Items[${itemIndex}].LineNumber" value="${itemIndex + 1}" />
                                
                                <div class="col-md-3">
                                    <div class="form-group">
                                        <label>Product</label>
                                        <select name="Items[${itemIndex}].ProductID" class="form-control product-select">
                                            ${productOptionsHtml}
                                        </select>
                                    </div>
                                </div>
                                
                                <div class="col-md-2">
                                    <div class="form-group">
                                        <label>Quantity</label>
                                        <input name="Items[${itemIndex}].OrderedQuantity" class="form-control quantity-input" type="number" step="0.001" value="1" />
                                    </div>
                                </div>
                                
                                <div class="col-md-2">
                                    <div class="form-group">
                                        <label>Unit</label>
                                        <select name="Items[${itemIndex}].UOMID" class="form-control">
                                            ${uomOptionsHtml}
                                        </select>
                                    </div>
                                </div>
                                
                                <div class="col-md-2">
                                    <div class="form-group">
                                        <label>Unit Price</label>
                                        <input name="Items[${itemIndex}].UnitPrice" class="form-control price-input" type="number" step="0.01" value="0" />
                                    </div>
                                </div>
                                
                                <div class="col-md-2">
                                    <div class="form-group">
                                        <label>Tax %</label>
                                        <input name="Items[${itemIndex}].TaxRate" class="form-control tax-input" type="number" step="0.01" value="20" />
                                    </div>
                                </div>
                                
                                <div class="col-md-1">
                                    <div class="form-group">
                                        <label>&nbsp;</label>
                                        <button type="button" class="btn btn-danger btn-sm form-control remove-item">Remove</button>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label>Description</label>
                                        <input name="Items[${itemIndex}].Description" class="form-control" />
                                    </div>
                                </div>
                                
                                <div class="col-md-3">
                                    <div class="form-group">
                                        <label>Line Total (Read-only)</label>
                                        <input type="text" class="form-control line-total" readonly value="$0.00" />
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>`;
                
                $('#orderItems').append(newItem);
                itemIndex++;
                calculateTotals();
            });

            // Remove item
            $(document).on('click', '.remove-item', function() {
                $(this).closest('.order-item').remove();
                renumberItems();
                calculateTotals();
            });

            // Calculate line totals when inputs change
            $(document).on('input', '.quantity-input, .price-input, .tax-input', function() {
                calculateLineTotal($(this).closest('.order-item'));
                calculateTotals();
            });

            // When product is selected, load its price
            $(document).on('change', '.product-select', function() {
                var productId = $(this).val();
                var customerId = $('#CustomerID').val();
                var $item = $(this).closest('.order-item');
                
                if (productId && customerId) {
                    // Load product price (this would be an AJAX call in production)
                    // For now, just set a default price
                    $item.find('.price-input').val('0.00');
                    calculateLineTotal($item);
                    calculateTotals();
                }
            });

            function calculateLineTotal($item) {
                var quantity = parseFloat($item.find('.quantity-input').val()) || 0;
                var unitPrice = parseFloat($item.find('.price-input').val()) || 0;
                var taxRate = parseFloat($item.find('.tax-input').val()) || 0;
                
                var subtotal = quantity * unitPrice;
                var taxAmount = subtotal * (taxRate / 100);
                var total = subtotal + taxAmount;
                
                $item.find('.line-total').val('$' + total.toFixed(2));
            }

            function calculateTotals() {
                var subtotal = 0;
                var totalTax = 0;
                
                $('.order-item').each(function() {
                    var quantity = parseFloat($(this).find('.quantity-input').val()) || 0;
                    var unitPrice = parseFloat($(this).find('.price-input').val()) || 0;
                    var taxRate = parseFloat($(this).find('.tax-input').val()) || 0;
                    
                    var lineSubtotal = quantity * unitPrice;
                    var lineTax = lineSubtotal * (taxRate / 100);
                    
                    subtotal += lineSubtotal;
                    totalTax += lineTax;
                });
                
                var total = subtotal + totalTax;
                
                $('#orderSubtotal').text('$' + subtotal.toFixed(2));
                $('#orderTax').text('$' + totalTax.toFixed(2));
                $('#orderTotal').text('$' + total.toFixed(2));
            }

            function renumberItems() {
                $('.order-item').each(function(index) {
                    $(this).attr('data-index', index);
                    $(this).find('input[name*="LineNumber"]').val(index + 1);
                    
                    // Update all name attributes
                    $(this).find('input, select').each(function() {
                        var name = $(this).attr('name');
                        if (name && name.includes('Items[')) {
                            var newName = name.replace(/Items\[\d+\]/, 'Items[' + index + ']');
                            $(this).attr('name', newName);
                        }
                    });
                });
                itemIndex = $('.order-item').length;
            }

            // Initial calculation
            $('.order-item').each(function() {
                calculateLineTotal($(this));
            });
            calculateTotals();
        });
    </script>
}