﻿<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>@ViewData["Title"] - nCarry.Web</title>
    <link rel="stylesheet" href="~/lib/bootstrap/dist/css/bootstrap.min.css" />
    <link rel="stylesheet" href="~/css/site.css" asp-append-version="true" />
    <link rel="stylesheet" href="~/nCarry.Web.styles.css" asp-append-version="true" />
    @await RenderSectionAsync("Styles", required: false)
</head>
<body>
    <header>
        <nav class="navbar navbar-expand-sm navbar-toggleable-sm navbar-light bg-white border-bottom box-shadow mb-3">
            <div class="container-fluid">
                <a class="navbar-brand" asp-area="" asp-controller="Home" asp-action="Index">nCarry.Web</a>
                <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target=".navbar-collapse" aria-controls="navbarSupportedContent"
                        aria-expanded="false" aria-label="Toggle navigation">
                    <span class="navbar-toggler-icon"></span>
                </button>
                <div class="navbar-collapse collapse d-sm-inline-flex justify-content-between">
                    <ul class="navbar-nav flex-grow-1">
                        @if (!(User.Identity?.IsAuthenticated ?? false))
                        {
                            <li class="nav-item">
                                <a class="nav-link text-dark" asp-area="" asp-controller="Home" asp-action="Index">Home</a>
                            </li>
                        }
                        @if (User.Identity?.IsAuthenticated ?? false)
                        {
                            <li class="nav-item">
                                <a class="nav-link text-dark" asp-area="" asp-controller="Dashboard" asp-action="Index">Dashboard</a>
                            </li>
                            <li class="nav-item dropdown">
                                <a class="nav-link dropdown-toggle text-dark" href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                                    Management
                                </a>
                                <ul class="dropdown-menu" aria-labelledby="navbarDropdown">
                                    <li><a class="dropdown-item" asp-area="" asp-controller="Customer" asp-action="Index">Customers</a></li>
                                    <li><a class="dropdown-item" asp-area="" asp-controller="CustomerPriceList" asp-action="Index">Customer Price Lists</a></li>
                                    <li><a class="dropdown-item" asp-area="" asp-controller="Supplier" asp-action="Index">Suppliers</a></li>
                                    <li><a class="dropdown-item" asp-area="" asp-controller="ProductCategory" asp-action="Index">Product Categories</a></li>
                                    <li><a class="dropdown-item" asp-area="" asp-controller="UnitOfMeasure" asp-action="Index">Units of Measure</a></li>
                                    <li><a class="dropdown-item" asp-area="" asp-controller="Product" asp-action="Index">Products</a></li>
                                    <li><hr class="dropdown-divider"></li>
                                    <li><a class="dropdown-item" asp-area="" asp-controller="SalesOrder" asp-action="Index">Sales Orders</a></li>
                                    <li><a class="dropdown-item" asp-area="" asp-controller="SalesInvoice" asp-action="Index">Sales Invoices</a></li>
                                    <li><a class="dropdown-item" asp-area="" asp-controller="PurchaseOrder" asp-action="Index">Purchase Orders</a></li>
                                    <li><a class="dropdown-item" asp-area="" asp-controller="GoodsReceipt" asp-action="Index">Goods Receipts</a></li>
                                    <li><a class="dropdown-item" asp-area="" asp-controller="SupplierInvoice" asp-action="Index">Supplier Invoices</a></li>
                                    <li><hr class="dropdown-divider"></li>
                                    <li><a class="dropdown-item" asp-area="" asp-controller="Warehouse" asp-action="Index">Warehouses</a></li>
                                    <li><a class="dropdown-item" asp-area="" asp-controller="Inventory" asp-action="Index">Inventory</a></li>
                                    <li><hr class="dropdown-divider"></li>
                                    <li><a class="dropdown-item" asp-area="" asp-controller="Shipment" asp-action="Index">Shipments</a></li>
                                    <li><a class="dropdown-item" asp-area="" asp-controller="PickingList" asp-action="Index">Picking Lists</a></li>
                                    <li><a class="dropdown-item" asp-area="" asp-controller="PackingList" asp-action="Index">Packing Slips</a></li>
                                    <li><a class="dropdown-item" asp-area="" asp-controller="Shipment" asp-action="Track">Track Shipment</a></li>
                                    <li><hr class="dropdown-divider"></li>
                                    <li><a class="dropdown-item" asp-area="" asp-controller="Company" asp-action="Index">Companies</a></li>
                                    <li><a class="dropdown-item" asp-area="" asp-controller="Branch" asp-action="Index">Branches</a></li>
                                    <li><hr class="dropdown-divider"></li>
                                    <li><a class="dropdown-item" asp-area="" asp-controller="User" asp-action="Index">Users</a></li>
                                    <li><a class="dropdown-item" asp-area="" asp-controller="Role" asp-action="Index">Roles</a></li>
                                    <li><hr class="dropdown-divider"></li>
                                    <li><a class="dropdown-item" asp-area="" asp-controller="Payment" asp-action="Index">Payments</a></li>
                                </ul>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link text-dark" asp-area="" asp-controller="Reports" asp-action="Index">Reports</a>
                            </li>
                        }
                    </ul>
                    <partial name="_LoginPartial" />
                </div>
            </div>
        </nav>
    </header>
    <div class="container">
        <main role="main" class="pb-3">
            @RenderBody()
        </main>
    </div>

    <footer class="border-top footer text-muted">
        <div class="container">
            &copy; 2025 - nCarry.Web - <a asp-area="" asp-controller="Home" asp-action="Privacy">Privacy</a>
        </div>
    </footer>
    <script src="~/lib/jquery/dist/jquery.min.js"></script>
    <script src="~/lib/bootstrap/dist/js/bootstrap.bundle.min.js"></script>
    <script src="~/js/site.js" asp-append-version="true"></script>
    @await RenderSectionAsync("Scripts", required: false)
</body>
</html>
