@model nCarry.Web.Models.ViewModels.SalesInvoiceEditViewModel

@{
    ViewData["Title"] = "Edit Sales Invoice";
}

<div class="container-fluid">
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">@ViewData["Title"]</h1>
    </div>

    <form asp-action="Edit" method="post">
        <div asp-validation-summary="ModelOnly" class="alert alert-danger" role="alert"></div>
        <input type="hidden" asp-for="InvoiceID" />
        <input type="hidden" asp-for="CustomerID" />
        <input type="hidden" asp-for="OrderID" />
        
        <div class="row">
            <!-- Invoice Header -->
            <div class="col-lg-8">
                <div class="card shadow mb-4">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-primary">Invoice Information</h6>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label asp-for="InvoiceNumber" class="control-label">Invoice Number</label>
                                    <input asp-for="InvoiceNumber" class="form-control" readonly />
                                    <span asp-validation-for="InvoiceNumber" class="text-danger"></span>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label asp-for="InvoiceDate" class="control-label">Invoice Date</label>
                                    <input asp-for="InvoiceDate" type="date" class="form-control" />
                                    <span asp-validation-for="InvoiceDate" class="text-danger"></span>
                                </div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label asp-for="CustomerName" class="control-label">Customer</label>
                                    <input asp-for="CustomerName" class="form-control" readonly />
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label asp-for="DueDate" class="control-label">Due Date</label>
                                    <input asp-for="DueDate" type="date" class="form-control" />
                                    <span asp-validation-for="DueDate" class="text-danger"></span>
                                </div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label asp-for="CustomerPO" class="control-label">Customer PO</label>
                                    <input asp-for="CustomerPO" class="form-control" />
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label asp-for="PaymentTerms" class="control-label">Payment Terms</label>
                                    <input asp-for="PaymentTerms" class="form-control" />
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Invoice Summary -->
            <div class="col-lg-4">
                <div class="card shadow mb-4 bg-primary text-white">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-white">Invoice Summary</h6>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-6">Subtotal:</div>
                            <div class="col-6 text-right" id="subtotal">£0.00</div>
                        </div>
                        <div class="row">
                            <div class="col-6">Tax:</div>
                            <div class="col-6 text-right" id="taxtotal">£0.00</div>
                        </div>
                        <hr class="bg-white">
                        <div class="row font-weight-bold">
                            <div class="col-6">Total:</div>
                            <div class="col-6 text-right" id="grandtotal">£0.00</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Invoice Items -->
        <div class="card shadow mb-4">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">Invoice Items</h6>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-bordered" id="itemsTable">
                        <thead>
                            <tr>
                                <th width="20%">Product Code</th>
                                <th width="30%">Product Name</th>
                                <th width="10%">Quantity</th>
                                <th width="10%">UOM</th>
                                <th width="10%">Unit Price</th>
                                <th width="10%">Tax Rate</th>
                                <th width="10%">Line Total</th>
                            </tr>
                        </thead>
                        <tbody>
                            @if (Model.Items != null && Model.Items.Any())
                            {
                                for (int i = 0; i < Model.Items.Count; i++)
                                {
                                    <tr>
                                        <td>
                                            <input type="hidden" name="Items[@i].ProductID" value="@Model.Items[i].ProductID" />
                                            <input type="hidden" name="Items[@i].UOMID" value="@Model.Items[i].UOMID" />
                                            <input type="hidden" name="Items[@i].ProductCode" value="@Model.Items[i].ProductCode" />
                                            <input type="hidden" name="Items[@i].ProductName" value="@Model.Items[i].ProductName" />
                                            @Model.Items[i].ProductCode
                                        </td>
                                        <td>@Model.Items[i].ProductName</td>
                                        <td>
                                            <input type="number" name="Items[@i].Quantity" value="@Model.Items[i].Quantity" 
                                                   class="form-control form-control-sm quantity" step="0.001" />
                                        </td>
                                        <td>@Model.Items[i].UOMCode</td>
                                        <td>
                                            <input type="number" name="Items[@i].UnitPrice" value="@Model.Items[i].UnitPrice" 
                                                   class="form-control form-control-sm unit-price" step="0.01" />
                                        </td>
                                        <td>
                                            <input type="number" name="Items[@i].TaxRate" value="@Model.Items[i].TaxRate" 
                                                   class="form-control form-control-sm tax-rate" step="0.01" />
                                        </td>
                                        <td class="line-total text-right">£@Model.Items[i].LineTotal.ToString("N2")</td>
                                    </tr>
                                }
                            }
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
        
        <!-- Notes -->
        <div class="card shadow mb-4">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">Additional Information</h6>
            </div>
            <div class="card-body">
                <div class="form-group">
                    <label asp-for="Notes" class="control-label">Notes</label>
                    <textarea asp-for="Notes" class="form-control" rows="3"></textarea>
                </div>
                <div class="form-group">
                    <label asp-for="InternalNotes" class="control-label">Internal Notes</label>
                    <textarea asp-for="InternalNotes" class="form-control" rows="3"></textarea>
                </div>
            </div>
        </div>
        
        <!-- Action Buttons -->
        <div class="form-group">
            <input type="submit" value="Save Changes" class="btn btn-primary" />
            <a asp-action="Details" asp-route-id="@Model.InvoiceID" class="btn btn-secondary">Cancel</a>
        </div>
    </form>
</div>

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}
    <script>
        $(document).ready(function() {
            calculateTotals();
            
            // Recalculate totals when quantity, price, or tax rate changes
            $(document).on('input', '.quantity, .unit-price, .tax-rate', function() {
                calculateTotals();
            });
            
            function calculateTotals() {
                var subtotal = 0;
                var taxtotal = 0;
                
                $('#itemsTable tbody tr').each(function() {
                    var row = $(this);
                    var quantity = parseFloat(row.find('.quantity').val()) || 0;
                    var unitPrice = parseFloat(row.find('.unit-price').val()) || 0;
                    var taxRate = parseFloat(row.find('.tax-rate').val()) || 0;
                    
                    var lineSubtotal = quantity * unitPrice;
                    var lineTax = lineSubtotal * taxRate / 100;
                    var lineTotal = lineSubtotal + lineTax;
                    
                    row.find('.line-total').text('£' + lineTotal.toFixed(2));
                    
                    subtotal += lineSubtotal;
                    taxtotal += lineTax;
                });
                
                var grandtotal = subtotal + taxtotal;
                
                $('#subtotal').text('£' + subtotal.toFixed(2));
                $('#taxtotal').text('£' + taxtotal.toFixed(2));
                $('#grandtotal').text('£' + grandtotal.toFixed(2));
            }
        });
    </script>
}