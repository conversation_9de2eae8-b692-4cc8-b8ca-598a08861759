@model nCarry.Web.Models.Sales.SalesInvoice

@{
    ViewData["Title"] = "Delete Sales Invoice";
}

<div class="container-fluid">
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">@ViewData["Title"]</h1>
    </div>

    <div class="alert alert-danger" role="alert">
        <h4 class="alert-heading"><i class="fas fa-exclamation-triangle"></i> Warning!</h4>
        <p>You are about to delete this sales invoice. This action cannot be undone.</p>
        <hr>
        <p class="mb-0">Only draft invoices can be deleted. Posted invoices must be credited instead.</p>
    </div>

    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">Invoice Information</h6>
        </div>
        <div class="card-body">
            <dl class="row">
                <dt class="col-sm-3">Invoice Number</dt>
                <dd class="col-sm-9">@Model.InvoiceNumber</dd>
                
                <dt class="col-sm-3">Invoice Date</dt>
                <dd class="col-sm-9">@Model.InvoiceDate.ToString("dd/MM/yyyy")</dd>
                
                <dt class="col-sm-3">Customer</dt>
                <dd class="col-sm-9">@Model.Customer.CustomerName</dd>
                
                <dt class="col-sm-3">Total Amount</dt>
                <dd class="col-sm-9">@Model.Currency @Model.TotalAmount.ToString("N2")</dd>
                
                <dt class="col-sm-3">Status</dt>
                <dd class="col-sm-9">
                    @if (Model.InvoiceStatus == "Draft")
                    {
                        <span class="badge badge-warning">@Model.InvoiceStatus</span>
                    }
                    else
                    {
                        <span class="badge badge-success">@Model.InvoiceStatus</span>
                    }
                </dd>
                
                <dt class="col-sm-3">Payment Status</dt>
                <dd class="col-sm-9">
                    @switch (Model.PaymentStatus)
                    {
                        case "Paid":
                            <span class="badge badge-success">@Model.PaymentStatus</span>
                            break;
                        case "Partial":
                            <span class="badge badge-warning">@Model.PaymentStatus</span>
                            break;
                        default:
                            <span class="badge badge-danger">@Model.PaymentStatus</span>
                            break;
                    }
                </dd>
            </dl>
        </div>
    </div>

    <form asp-action="Delete" method="post">
        <input type="hidden" asp-for="InvoiceID" />
        
        <div class="form-group">
            <input type="submit" value="Delete" class="btn btn-danger" />
            <a asp-action="Index" class="btn btn-secondary">Cancel</a>
        </div>
    </form>
</div>

@section Scripts {
    <script>
        $(document).ready(function() {
            // Add confirmation dialog
            $('form').on('submit', function(e) {
                if (!confirm('Are you sure you want to delete this invoice? This action cannot be undone.')) {
                    e.preventDefault();
                }
            });
        });
    </script>
}