@model nCarry.Web.Models.ViewModels.SalesInvoiceCreateViewModel

@{
    ViewData["Title"] = "Create Sales Invoice";
}

<div class="container-fluid">
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">@ViewData["Title"]</h1>
    </div>

    <form asp-action="Create" method="post">
        <div asp-validation-summary="ModelOnly" class="alert alert-danger" role="alert"></div>
        
        <div class="row">
            <!-- Invoice Header -->
            <div class="col-lg-8">
                <div class="card shadow mb-4">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-primary">Invoice Information</h6>
                    </div>
                    <div class="card-body">
                        <input type="hidden" asp-for="OrderID" />
                        <input type="hidden" asp-for="CustomerID" />
                        <input type="hidden" asp-for="InvoiceType" />
                        <input type="hidden" asp-for="Currency" />
                        <input type="hidden" asp-for="ExchangeRate" />
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label asp-for="InvoiceNumber" class="control-label">Invoice Number</label>
                                    <input asp-for="InvoiceNumber" class="form-control" readonly />
                                    <span asp-validation-for="InvoiceNumber" class="text-danger"></span>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label asp-for="InvoiceDate" class="control-label">Invoice Date</label>
                                    <input asp-for="InvoiceDate" type="date" class="form-control" />
                                    <span asp-validation-for="InvoiceDate" class="text-danger"></span>
                                </div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label asp-for="CustomerID" class="control-label">Customer</label>
                                    @if (Model.OrderID.HasValue)
                                    {
                                        <input asp-for="CustomerName" class="form-control" readonly />
                                    }
                                    else
                                    {
                                        <select asp-for="CustomerID" class="form-control" asp-items="ViewBag.Customers">
                                            <option value="">-- Select Customer --</option>
                                        </select>
                                        <span asp-validation-for="CustomerID" class="text-danger"></span>
                                    }
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label asp-for="DueDate" class="control-label">Due Date</label>
                                    <input asp-for="DueDate" type="date" class="form-control" />
                                    <span asp-validation-for="DueDate" class="text-danger"></span>
                                </div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label asp-for="CustomerPO" class="control-label">Customer PO</label>
                                    <input asp-for="CustomerPO" class="form-control" />
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label asp-for="PaymentTerms" class="control-label">Payment Terms</label>
                                    <input asp-for="PaymentTerms" class="form-control" />
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Invoice Summary -->
            <div class="col-lg-4">
                <div class="card shadow mb-4 bg-primary text-white">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-white">Invoice Summary</h6>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-6">Subtotal:</div>
                            <div class="col-6 text-right" id="subtotal">£0.00</div>
                        </div>
                        <div class="row">
                            <div class="col-6">Tax:</div>
                            <div class="col-6 text-right" id="taxtotal">£0.00</div>
                        </div>
                        <hr class="bg-white">
                        <div class="row font-weight-bold">
                            <div class="col-6">Total:</div>
                            <div class="col-6 text-right" id="grandtotal">£0.00</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Invoice Items -->
        <div class="card shadow mb-4">
            <div class="card-header py-3 d-flex justify-content-between align-items-center">
                <h6 class="m-0 font-weight-bold text-primary">Invoice Items</h6>
                @if (!Model.OrderID.HasValue)
                {
                    <button type="button" class="btn btn-sm btn-primary" id="addItemBtn">
                        <i class="fas fa-plus"></i> Add Item
                    </button>
                }
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-bordered" id="itemsTable">
                        <thead>
                            <tr>
                                <th width="20%">Product Code</th>
                                <th width="30%">Product Name</th>
                                <th width="10%">Quantity</th>
                                <th width="10%">UOM</th>
                                <th width="10%">Unit Price</th>
                                <th width="10%">Tax Rate</th>
                                <th width="10%">Line Total</th>
                            </tr>
                        </thead>
                        <tbody>
                            @if (Model.Items != null && Model.Items.Any())
                            {
                                for (int i = 0; i < Model.Items.Count; i++)
                                {
                                    <tr>
                                        <td>
                                            <input type="hidden" name="Items[@i].ProductID" value="@Model.Items[i].ProductID" />
                                            <input type="hidden" name="Items[@i].UOMID" value="@Model.Items[i].UOMID" />
                                            <input type="hidden" name="Items[@i].ProductCode" value="@Model.Items[i].ProductCode" />
                                            <input type="hidden" name="Items[@i].ProductName" value="@Model.Items[i].ProductName" />
                                            <input type="hidden" name="Items[@i].UOMCode" value="@Model.Items[i].UOMCode" />
                                            @Model.Items[i].ProductCode
                                        </td>
                                        <td>@Model.Items[i].ProductName</td>
                                        <td>
                                            <input type="number" name="Items[@i].Quantity" value="@Model.Items[i].Quantity" 
                                                   class="form-control form-control-sm quantity" step="0.001" />
                                        </td>
                                        <td>@Model.Items[i].UOMCode</td>
                                        <td>
                                            <input type="number" name="Items[@i].UnitPrice" value="@Model.Items[i].UnitPrice" 
                                                   class="form-control form-control-sm unit-price" step="0.01" />
                                        </td>
                                        <td>
                                            <input type="number" name="Items[@i].TaxRate" value="@Model.Items[i].TaxRate" 
                                                   class="form-control form-control-sm tax-rate" step="0.01" />
                                        </td>
                                        <td class="line-total text-right">£0.00</td>
                                    </tr>
                                }
                            }
                            else if (!Model.OrderID.HasValue)
                            {
                                <tr>
                                    <td colspan="7" class="text-center text-muted">
                                        <i class="fas fa-info-circle"></i> No items added. Please add products to create an invoice.
                                    </td>
                                </tr>
                            }
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
        
        <!-- Notes -->
        <div class="card shadow mb-4">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">Additional Information</h6>
            </div>
            <div class="card-body">
                <div class="form-group">
                    <label asp-for="Notes" class="control-label">Notes</label>
                    <textarea asp-for="Notes" class="form-control" rows="3"></textarea>
                </div>
            </div>
        </div>
        
        <!-- Action Buttons -->
        <div class="form-group">
            <input type="submit" value="Create Invoice" class="btn btn-primary" />
            <a asp-action="Index" class="btn btn-secondary">Cancel</a>
        </div>
    </form>
</div>

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}
    <script>
        $(document).ready(function() {
            calculateTotals();
            
            var itemIndex = @(Model.Items?.Count ?? 0);
            
            // Add new item row
            $('#addItemBtn').click(function() {
                var newRow = `
                    <tr>
                        <td>
                            <input type="hidden" name="Items[${itemIndex}].ProductID" value="19" />
                            <input type="hidden" name="Items[${itemIndex}].UOMID" value="33" />
                            <input type="text" name="Items[${itemIndex}].ProductCode" class="form-control form-control-sm" placeholder="Product Code" />
                        </td>
                        <td>
                            <input type="text" name="Items[${itemIndex}].ProductName" class="form-control form-control-sm" placeholder="Product Name" required />
                        </td>
                        <td>
                            <input type="number" name="Items[${itemIndex}].Quantity" value="1" 
                                   class="form-control form-control-sm quantity" step="0.001" required />
                        </td>
                        <td>
                            <select name="Items[${itemIndex}].UOMCode" class="form-control form-control-sm">
                                <option value="EA">EA</option>
                                <option value="BOX">BOX</option>
                                <option value="KG">KG</option>
                            </select>
                        </td>
                        <td>
                            <input type="number" name="Items[${itemIndex}].UnitPrice" value="0" 
                                   class="form-control form-control-sm unit-price" step="0.01" required />
                        </td>
                        <td>
                            <input type="number" name="Items[${itemIndex}].TaxRate" value="20" 
                                   class="form-control form-control-sm tax-rate" step="0.01" />
                        </td>
                        <td class="line-total text-right">£0.00</td>
                    </tr>
                `;
                
                // Remove the "No items" message if it exists
                $('#itemsTable tbody tr:has(td[colspan="7"])').remove();
                
                $('#itemsTable tbody').append(newRow);
                itemIndex++;
                calculateTotals();
            });
            
            // Recalculate totals when quantity, price, or tax rate changes
            $(document).on('input', '.quantity, .unit-price, .tax-rate', function() {
                calculateTotals();
            });
            
            // Validate form before submission
            $('form').submit(function(e) {
                var hasItems = false;
                $('#itemsTable tbody tr').each(function() {
                    var quantity = parseFloat($(this).find('.quantity').val()) || 0;
                    if (quantity > 0) {
                        hasItems = true;
                        return false; // break the loop
                    }
                });
                
                if (!hasItems && !@(Model.OrderID.HasValue ? "true" : "false")) {
                    e.preventDefault();
                    alert('Please add at least one item to the invoice.');
                    return false;
                }
                
                // Ensure CustomerID is set
                var customerID = $('#CustomerID').val();
                if (!customerID && !@(Model.OrderID.HasValue ? "true" : "false")) {
                    e.preventDefault();
                    alert('Please select a customer.');
                    return false;
                }
            });
            
            function calculateTotals() {
                var subtotal = 0;
                var taxtotal = 0;
                
                $('#itemsTable tbody tr').each(function() {
                    var row = $(this);
                    // Skip rows with colspan (no items message)
                    if (row.find('td[colspan]').length > 0) return;
                    
                    var quantity = parseFloat(row.find('.quantity').val()) || 0;
                    var unitPrice = parseFloat(row.find('.unit-price').val()) || 0;
                    var taxRate = parseFloat(row.find('.tax-rate').val()) || 0;
                    
                    var lineSubtotal = quantity * unitPrice;
                    var lineTax = lineSubtotal * taxRate / 100;
                    var lineTotal = lineSubtotal + lineTax;
                    
                    row.find('.line-total').text('£' + lineTotal.toFixed(2));
                    
                    subtotal += lineSubtotal;
                    taxtotal += lineTax;
                });
                
                var grandtotal = subtotal + taxtotal;
                
                $('#subtotal').text('£' + subtotal.toFixed(2));
                $('#taxtotal').text('£' + taxtotal.toFixed(2));
                $('#grandtotal').text('£' + grandtotal.toFixed(2));
            }
        });
    </script>
}