@{
    ViewData["Title"] = "Track Shipment";
}

<div class="container-fluid">
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">@ViewData["Title"]</h1>
        <a asp-action="Index" class="btn btn-sm btn-secondary shadow-sm">
            <i class="fas fa-arrow-left fa-sm text-white-50"></i> Back to Shipments
        </a>
    </div>

    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Enter Tracking Information</h6>
                </div>
                <div class="card-body">
                    @if (ViewBag.Error != null)
                    {
                        <div class="alert alert-danger">
                            <i class="fas fa-exclamation-circle"></i> @ViewBag.Error
                        </div>
                    }

                    <form method="post">
                        <div class="form-group mb-4">
                            <label for="trackingNumber" class="form-label">Tracking Number</label>
                            <div class="input-group input-group-lg">
                                <input type="text" name="trackingNumber" class="form-control" id="trackingNumber" 
                                       placeholder="Enter shipment or tracking number" required />
                                <button class="btn btn-primary" type="submit">
                                    <i class="fas fa-search"></i> Track
                                </button>
                            </div>
                            <small class="form-text text-muted">
                                Enter your shipment number (e.g., SH20240629001) or carrier tracking number
                            </small>
                        </div>
                    </form>

                    <div class="text-center mt-4">
                        <h6 class="text-muted">Sample Tracking Numbers</h6>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="card border-left-primary shadow-sm mb-3">
                                    <div class="card-body py-2">
                                        <small class="text-primary font-weight-bold">Shipment Number Format</small><br>
                                        <code>SH20240629001</code>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="card border-left-success shadow-sm mb-3">
                                    <div class="card-body py-2">
                                        <small class="text-success font-weight-bold">Tracking Number Format</small><br>
                                        <code>1Z999AA1234567890</code>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Quick Tracking Form -->
            <div class="card shadow mt-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Quick Track Multiple Shipments</h6>
                </div>
                <div class="card-body">
                    <form id="multiTrackForm">
                        <div class="form-group mb-3">
                            <label for="multipleNumbers">Multiple Tracking Numbers</label>
                            <textarea class="form-control" id="multipleNumbers" rows="4" 
                                      placeholder="Enter multiple tracking numbers, one per line"></textarea>
                            <small class="form-text text-muted">Enter one tracking number per line</small>
                        </div>
                        <button type="button" class="btn btn-success" onclick="trackMultiple()">
                            <i class="fas fa-search"></i> Track All
                        </button>
                    </form>
                    
                    <div id="multiTrackResults" class="mt-3" style="display: none;">
                        <h6>Tracking Results:</h6>
                        <div id="resultsContainer"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script>
        $(document).ready(function() {
            $('#trackingNumber').focus();
            
            // Allow Enter key to submit form
            $('#trackingNumber').keypress(function(e) {
                if (e.which == 13) {
                    $(this).closest('form').submit();
                }
            });
        });

        function trackMultiple() {
            var numbers = $('#multipleNumbers').val().split('\n').filter(n => n.trim() !== '');
            
            if (numbers.length === 0) {
                alert('Please enter at least one tracking number.');
                return;
            }

            $('#multiTrackResults').show();
            var container = $('#resultsContainer');
            container.html('<div class="text-center"><i class="fas fa-spinner fa-spin"></i> Tracking shipments...</div>');

            var results = [];
            var completed = 0;

            numbers.forEach(function(number, index) {
                $.post('@Url.Action("Track")', { trackingNumber: number.trim() }, function(data) {
                    var result = {
                        number: number.trim(),
                        found: data.indexOf('alert-danger') === -1,
                        html: data
                    };
                    results[index] = result;
                    completed++;

                    if (completed === numbers.length) {
                        displayMultiTrackResults(results);
                    }
                }).fail(function() {
                    results[index] = {
                        number: number.trim(),
                        found: false,
                        html: '<div class="alert alert-danger">Error tracking this shipment</div>'
                    };
                    completed++;

                    if (completed === numbers.length) {
                        displayMultiTrackResults(results);
                    }
                });
            });
        }

        function displayMultiTrackResults(results) {
            var html = '';
            
            results.forEach(function(result) {
                var statusClass = result.found ? 'success' : 'danger';
                var statusIcon = result.found ? 'check' : 'times';
                
                html += `
                    <div class="card mb-2">
                        <div class="card-header py-2">
                            <small class="font-weight-bold">
                                <i class="fas fa-${statusIcon} text-${statusClass}"></i>
                                ${result.number}
                            </small>
                        </div>
                        <div class="card-body py-2">
                            ${result.found ? 
                                '<small class="text-success">Shipment found - click number to view details</small>' : 
                                '<small class="text-danger">Shipment not found</small>'}
                        </div>
                    </div>
                `;
            });

            $('#resultsContainer').html(html);
        }
    </script>
}