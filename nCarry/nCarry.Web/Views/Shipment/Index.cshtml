@model IEnumerable<nCarry.Web.Models.Logistics.Shipment>

@{
    ViewData["Title"] = "Shipments";
}

<div class="container-fluid">
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">@ViewData["Title"]</h1>
        <div>
            <a asp-action="Create" asp-route-branchId="@ViewBag.SelectedBranchId" class="btn btn-sm btn-primary shadow-sm">
                <i class="fas fa-plus fa-sm text-white-50"></i> New Shipment
            </a>
            <a asp-action="BulkUpdate" class="btn btn-sm btn-warning shadow-sm">
                <i class="fas fa-edit fa-sm text-white-50"></i> Bulk Update
            </a>
            <a asp-action="Track" class="btn btn-sm btn-info shadow-sm">
                <i class="fas fa-search fa-sm text-white-50"></i> Track Shipment
            </a>
        </div>
    </div>

    @if (TempData["Success"] != null)
    {
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            @TempData["Success"]
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    }

    @if (TempData["Error"] != null)
    {
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            @TempData["Error"]
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    }

    <!-- Filter Card -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">Filter Shipments</h6>
        </div>
        <div class="card-body">
            <form method="get">
                <div class="row">
                    <div class="col-md-2">
                        <div class="form-group">
                            <label for="branchId">Branch</label>
                            <select name="branchId" class="form-control">
                                <option value="">All Branches</option>
                                @foreach (var branch in (ViewBag.Branches as SelectList)!)
                                {
                                    @if (branch.Selected)
                                    {
                                        <option value="@branch.Value" selected>@branch.Text</option>
                                    }
                                    else
                                    {
                                        <option value="@branch.Value">@branch.Text</option>
                                    }
                                }
                            </select>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="form-group">
                            <label for="status">Status</label>
                            <select name="status" class="form-control">
                                @foreach (var status in (ViewBag.StatusList as List<SelectListItem>)!)
                                {
                                    @if (status.Selected)
                                    {
                                        <option value="@status.Value" selected>@status.Text</option>
                                    }
                                    else
                                    {
                                        <option value="@status.Value">@status.Text</option>
                                    }
                                }
                            </select>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="form-group">
                            <label for="carrierId">Carrier</label>
                            <select name="carrierId" class="form-control">
                                <option value="">All Carriers</option>
                                @foreach (var carrier in (ViewBag.Carriers as SelectList)!)
                                {
                                    @if (carrier.Selected)
                                    {
                                        <option value="@carrier.Value" selected>@carrier.Text</option>
                                    }
                                    else
                                    {
                                        <option value="@carrier.Value">@carrier.Text</option>
                                    }
                                }
                            </select>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="form-group">
                            <label for="fromDate">From Date</label>
                            <input type="date" name="fromDate" class="form-control" value="@ViewBag.FromDate?.ToString("yyyy-MM-dd")" />
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="form-group">
                            <label for="toDate">To Date</label>
                            <input type="date" name="toDate" class="form-control" value="@ViewBag.ToDate?.ToString("yyyy-MM-dd")" />
                        </div>
                    </div>
                    <div class="col-md-2">
                        <label>&nbsp;</label><br>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-filter"></i> Filter
                        </button>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- Summary Cards -->
    <div class="row">
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-primary shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                Total Shipments</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">@Model.Count()</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-boxes fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-warning shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                Pending</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">@Model.Count(s => s.Status == "Pending")</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-clock fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-info shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                In Transit</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">@Model.Count(s => s.Status == "InTransit" || s.Status == "OutForDelivery")</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-truck fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-success shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                Delivered</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">@Model.Count(s => s.Status == "Delivered")</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-check fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">Shipment List</h6>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-bordered" id="dataTable" width="100%" cellspacing="0">
                    <thead>
                        <tr>
                            <th>Shipment #</th>
                            <th>Date</th>
                            <th>Customer</th>
                            <th>Recipient</th>
                            <th>Carrier</th>
                            <th>Weight</th>
                            <th>COD</th>
                            <th>Priority</th>
                            <th>Status</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach (var item in Model)
                        {
                            <tr>
                                <td>
                                    <strong>@Html.DisplayFor(modelItem => item.ShipmentNumber)</strong>
                                    @if (!string.IsNullOrEmpty(item.TrackingNumber))
                                    {
                                        <br><small class="text-muted">Track: @item.TrackingNumber</small>
                                    }
                                </td>
                                <td>@item.ShipmentDate.ToString("dd/MM/yyyy")</td>
                                <td>
                                    @if (!string.IsNullOrEmpty(item.ShipToName))
                                    {
                                        @item.ShipToName
                                    }
                                    else
                                    {
                                        <text>-</text>
                                    }
                                </td>
                                <td>
                                    @Html.DisplayFor(modelItem => item.RecipientName)
                                    @if (!string.IsNullOrEmpty(item.RecipientPhone))
                                    {
                                        <br><small class="text-muted">@item.RecipientPhone</small>
                                    }
                                </td>
                                <td>
                                    @if (item.Carrier != null)
                                    {
                                        @Html.DisplayFor(modelItem => item.Carrier.CarrierName)
                                        @if (item.Service != null)
                                        {
                                            <br><small class="text-muted">@item.Service.ServiceName</small>
                                        }
                                    }
                                    else
                                    {
                                        <text>-</text>
                                    }
                                </td>
                                <td>
                                    @if (item.Weight > 0)
                                    {
                                        @item.Weight.ToString("F2") <small>kg</small>
                                    }
                                    else
                                    {
                                        <text>-</text>
                                    }
                                </td>
                                <td>
                                    @if (item.CODAmount > 0)
                                    {
                                        <span class="badge bg-warning text-dark">$@item.CODAmount.ToString("F2")</span>
                                    }
                                    else
                                    {
                                        <text>-</text>
                                    }
                                </td>
                                <td>
                                    <span class="badge bg-@(item.Priority == "High" || item.Priority == "Urgent" ? "danger" : item.Priority == "Medium" ? "warning" : "secondary")">
                                        @Html.DisplayFor(modelItem => item.Priority)
                                    </span>
                                </td>
                                <td>
                                    <span class="badge bg-@(GetStatusColor(item.Status))">
                                        @GetStatusText(item.Status)
                                    </span>
                                </td>
                                <td>
                                    <div class="btn-group" role="group">
                                        <a asp-action="Details" asp-route-id="@item.ShipmentID" class="btn btn-sm btn-info" title="Details">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <a asp-action="Edit" asp-route-id="@item.ShipmentID" class="btn btn-sm btn-warning" title="Edit">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <button type="button" class="btn btn-sm btn-success" onclick="quickStatusUpdate(@item.ShipmentID)" title="Quick Update">
                                            <i class="fas fa-sync"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                        }
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<!-- Quick Status Update Modal -->
<div class="modal fade" id="quickUpdateModal" tabindex="-1" aria-labelledby="quickUpdateModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="quickUpdateModalLabel">Quick Status Update</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="quickUpdateForm">
                    <input type="hidden" id="shipmentId" name="shipmentId" />
                    <div class="mb-3">
                        <label for="status" class="form-label">Status</label>
                        <select class="form-control" id="status" name="status" required>
                            <option value="Picked">Picked</option>
                            <option value="InTransit">In Transit</option>
                            <option value="OutForDelivery">Out for Delivery</option>
                            <option value="Delivered">Delivered</option>
                            <option value="Failed">Failed</option>
                            <option value="Returned">Returned</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label for="location" class="form-label">Location</label>
                        <input type="text" class="form-control" id="location" name="location" />
                    </div>
                    <div class="mb-3">
                        <label for="description" class="form-label">Description</label>
                        <textarea class="form-control" id="description" name="description" rows="2"></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary" onclick="updateStatus()">Update Status</button>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script>
        $(document).ready(function() {
            $('#dataTable').DataTable({
                "order": [[1, "desc"]],
                "pageLength": 25
            });
        });

        function quickStatusUpdate(shipmentId) {
            $('#shipmentId').val(shipmentId);
            $('#quickUpdateModal').modal('show');
        }

        function updateStatus() {
            var formData = {
                shipmentId: $('#shipmentId').val(),
                status: $('#status').val(),
                location: $('#location').val(),
                description: $('#description').val()
            };

            $.post('@Url.Action("UpdateStatus")', formData, function(data) {
                if (data.success) {
                    $('#quickUpdateModal').modal('hide');
                    location.reload();
                } else {
                    alert('Error: ' + data.message);
                }
            });
        }
    </script>
}

@functions {
    string GetStatusColor(string status)
    {
        return status switch
        {
            "Pending" => "warning",
            "Picked" => "info",
            "InTransit" => "primary",
            "OutForDelivery" => "info",
            "Delivered" => "success",
            "Failed" => "danger",
            "Returned" => "secondary",
            _ => "secondary"
        };
    }

    string GetStatusText(string status)
    {
        return status switch
        {
            "InTransit" => "In Transit",
            "OutForDelivery" => "Out for Delivery",
            _ => status
        };
    }
}