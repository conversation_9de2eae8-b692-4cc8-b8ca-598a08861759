@model nCarry.Web.Models.Logistics.Shipment

@{
    ViewData["Title"] = "Print Shipping Label";
    Layout = null; // Print layout
}

<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8" />
    <title>Shipping Label - @Model.ShipmentNumber</title>
    <style>
        @@media print {
            body {
                margin: 0;
                padding: 0;
            }
            .no-print {
                display: none;
            }
            .page-break {
                page-break-after: always;
            }
        }
        
        body {
            font-family: Arial, sans-serif;
            font-size: 12px;
            line-height: 1.4;
        }
        
        .label-container {
            width: 4in;
            height: 6in;
            border: 2px solid #000;
            padding: 10px;
            margin: 20px auto;
            position: relative;
        }
        
        .header {
            border-bottom: 2px solid #000;
            padding-bottom: 10px;
            margin-bottom: 10px;
        }
        
        .company-logo {
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 5px;
        }
        
        .tracking-barcode {
            text-align: center;
            margin: 15px 0;
            padding: 10px;
            border: 1px solid #000;
        }
        
        .barcode {
            font-family: 'Libre Barcode 128', monospace;
            font-size: 48px;
            line-height: 1;
        }
        
        .tracking-number {
            font-size: 14px;
            font-weight: bold;
            margin-top: 5px;
        }
        
        .address-section {
            margin: 10px 0;
            padding: 10px;
            border: 1px solid #ccc;
        }
        
        .address-label {
            font-weight: bold;
            margin-bottom: 5px;
            text-transform: uppercase;
            font-size: 10px;
        }
        
        .address-content {
            font-size: 14px;
        }
        
        .service-info {
            background: #000;
            color: #fff;
            padding: 5px;
            text-align: center;
            font-weight: bold;
            margin: 10px 0;
        }
        
        .footer-info {
            position: absolute;
            bottom: 10px;
            left: 10px;
            right: 10px;
            font-size: 10px;
        }
        
        .weight-dims {
            display: flex;
            justify-content: space-between;
            margin-top: 10px;
        }
        
        .print-controls {
            text-align: center;
            margin: 20px;
        }
        
        .print-controls button {
            padding: 10px 20px;
            margin: 0 10px;
            font-size: 16px;
            cursor: pointer;
        }
    </style>
    <link href="https://fonts.googleapis.com/css2?family=Libre+Barcode+128&display=swap" rel="stylesheet">
</head>
<body>
    <div class="print-controls no-print">
        <button onclick="window.print()">Print Label</button>
        <button onclick="window.close()">Close</button>
    </div>
    
    <div class="label-container">
        <div class="header">
            <div class="company-logo">nCarry</div>
            <div>@Model.ShipFromWarehouse?.WarehouseName</div>
            <div>Shipment Date: @Model.ShipmentDate.ToString("dd/MM/yyyy")</div>
        </div>
        
        @if (!string.IsNullOrEmpty(Model.TrackingNumber))
        {
            <div class="tracking-barcode">
                <div class="barcode">@Model.TrackingNumber</div>
                <div class="tracking-number">@Model.TrackingNumber</div>
            </div>
        }
        else
        {
            <div class="tracking-barcode">
                <div class="tracking-number">@Model.ShipmentNumber</div>
            </div>
        }
        
        <div class="address-section">
            <div class="address-label">FROM:</div>
            <div class="address-content">
                @Model.ShipFromWarehouse?.WarehouseName<br />
                @Model.ShipFromWarehouse?.Address1<br />
                @if (!string.IsNullOrEmpty(Model.ShipFromWarehouse?.Address2))
                {
                    @Model.ShipFromWarehouse.Address2<br />
                }
                @Model.ShipFromWarehouse?.City, @Model.ShipFromWarehouse?.State @Model.ShipFromWarehouse?.PostCode<br />
                @Model.ShipFromWarehouse?.Country
            </div>
        </div>
        
        <div class="address-section">
            <div class="address-label">TO:</div>
            <div class="address-content">
                <strong>@Model.ShipToName</strong><br />
                @Model.ShipToAddress1<br />
                @if (!string.IsNullOrEmpty(Model.ShipToAddress2))
                {
                    @Model.ShipToAddress2<br />
                }
                @Model.ShipToCity, @Model.ShipToState @Model.ShipToPostCode<br />
                @Model.ShipToCountry
            </div>
        </div>
        
        @if (Model.Carrier != null || Model.Service != null)
        {
            <div class="service-info">
                @(Model.Carrier?.CarrierName ?? "Standard Delivery")
                @if (Model.Service != null)
                {
                    <span> - @Model.Service.ServiceName</span>
                }
            </div>
        }
        
        <div class="footer-info">
            <div class="weight-dims">
                <div>Weight: @Model.TotalWeight.ToString("N2") kg</div>
                <div>Packages: @Model.TotalPackages</div>
            </div>
            @if (!string.IsNullOrEmpty(Model.SpecialHandling))
            {
                <div style="margin-top: 5px; font-weight: bold;">
                    SPECIAL HANDLING: @Model.SpecialHandling
                </div>
            }
            @if (Model.CODAmount > 0)
            {
                <div style="margin-top: 5px; font-weight: bold;">
                    COD: £@Model.CODAmount.ToString("N2")
                </div>
            }
        </div>
    </div>
    
    @if (Model.Items != null && Model.Items.Any())
    {
        <div class="page-break"></div>
        <div style="padding: 20px;">
            <h2>Package Details - @Model.ShipmentNumber</h2>
            <table style="width: 100%; border-collapse: collapse;">
                <thead>
                    <tr style="border-bottom: 2px solid #000;">
                        <th style="text-align: left; padding: 5px;">Package Number</th>
                        <th style="text-align: right; padding: 5px;">Weight (kg)</th>
                        <th style="text-align: right; padding: 5px;">Dimensions (cm)</th>
                    </tr>
                </thead>
                <tbody>
                    @if (Model.Items.Any())
                    {
                        @foreach (var item in Model.Items)
                        {
                            <tr style="border-bottom: 1px solid #ccc;">
                                <td style="padding: 5px;">@(item.PackageNumber ?? $"PKG-{Model.Items.ToList().IndexOf(item) + 1}")</td>
                                <td style="text-align: right; padding: 5px;">@(item.PackageWeight?.ToString("N2") ?? "N/A")</td>
                                <td style="text-align: right; padding: 5px;">
                                    @if (item.PackageLength.HasValue && item.PackageWidth.HasValue && item.PackageHeight.HasValue)
                                    {
                                        @($"{item.PackageLength:N0} x {item.PackageWidth:N0} x {item.PackageHeight:N0}")
                                    }
                                    else
                                    {
                                        <text>N/A</text>
                                    }
                                </td>
                            </tr>
                        }
                    }
                    else
                    {
                        <tr>
                            <td colspan="3" style="padding: 10px; text-align: center;">No package details available</td>
                        </tr>
                    }
                </tbody>
                <tfoot>
                    <tr style="border-top: 2px solid #000;">
                        <td style="padding: 5px; font-weight: bold;">Total Packages:</td>
                        <td colspan="2" style="text-align: right; padding: 5px; font-weight: bold;">
                            @Model.TotalPackages
                        </td>
                    </tr>
                </tfoot>
            </table>
        </div>
    }
    
    <script>
        // Auto-print on load if requested
        if (window.location.search.includes('autoprint=true')) {
            window.print();
        }
    </script>
</body>
</html>