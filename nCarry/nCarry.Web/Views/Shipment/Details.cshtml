@model nCarry.Web.Models.Logistics.Shipment

@{
    ViewData["Title"] = "Shipment Details";
}

<div class="page-header">
    <div class="row align-items-center">
        <div class="col">
            <h3 class="page-title">Shipment Details</h3>
            <ul class="breadcrumb">
                <li class="breadcrumb-item"><a asp-action="Index">Shipments</a></li>
                <li class="breadcrumb-item active">Details</li>
            </ul>
        </div>
        <div class="col-auto">
            <a asp-action="Edit" asp-route-id="@Model.ShipmentID" class="btn btn-primary">
                <i class="fas fa-edit"></i> Edit
            </a>
            <a asp-action="PrintLabel" asp-route-id="@Model.ShipmentID" class="btn btn-secondary" target="_blank">
                <i class="fas fa-print"></i> Print Label
            </a>
            <a asp-action="Track" asp-route-id="@Model.ShipmentID" class="btn btn-info">
                <i class="fas fa-map-marker-alt"></i> Track
            </a>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h4 class="card-title">Shipment Information</h4>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <dl class="row">
                            <dt class="col-sm-5">Shipment Number:</dt>
                            <dd class="col-sm-7">@Model.ShipmentNumber</dd>
                            
                            <dt class="col-sm-5">Shipment Date:</dt>
                            <dd class="col-sm-7">@Model.ShipmentDate.ToString("dd/MM/yyyy")</dd>
                            
                            <dt class="col-sm-5">Type:</dt>
                            <dd class="col-sm-7">@Model.ShipmentType</dd>
                            
                            <dt class="col-sm-5">Priority:</dt>
                            <dd class="col-sm-7">
                                @if (Model.Priority == "Urgent")
                                {
                                    <span class="badge bg-danger">@Model.Priority</span>
                                }
                                else if (Model.Priority == "High")
                                {
                                    <span class="badge bg-warning">@Model.Priority</span>
                                }
                                else
                                {
                                    <span class="badge bg-info">@Model.Priority</span>
                                }
                            </dd>
                            
                            <dt class="col-sm-5">Status:</dt>
                            <dd class="col-sm-7">
                                @if (Model.Status == "Delivered")
                                {
                                    <span class="badge bg-success">@Model.Status</span>
                                }
                                else if (Model.Status == "InTransit" || Model.Status == "OutForDelivery")
                                {
                                    <span class="badge bg-primary">@Model.Status</span>
                                }
                                else if (Model.Status == "Failed" || Model.Status == "Returned")
                                {
                                    <span class="badge bg-danger">@Model.Status</span>
                                }
                                else
                                {
                                    <span class="badge bg-secondary">@Model.Status</span>
                                }
                            </dd>
                        </dl>
                    </div>
                    <div class="col-md-6">
                        <dl class="row">
                            <dt class="col-sm-5">Ship To:</dt>
                            <dd class="col-sm-7">@Model.ShipToName</dd>
                            
                            <dt class="col-sm-5">Warehouse:</dt>
                            <dd class="col-sm-7">@Model.ShipFromWarehouse?.WarehouseName</dd>
                            
                            <dt class="col-sm-5">Tracking Number:</dt>
                            <dd class="col-sm-7">@(Model.TrackingNumber ?? "N/A")</dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>

        <div class="card mt-3">
            <div class="card-header">
                <h4 class="card-title">Shipping Details</h4>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <dl class="row">
                            <dt class="col-sm-5">Carrier:</dt>
                            <dd class="col-sm-7">@(Model.Carrier?.CarrierName ?? "N/A")</dd>
                            
                            <dt class="col-sm-5">Service:</dt>
                            <dd class="col-sm-7">@(Model.Service?.ServiceName ?? "N/A")</dd>
                            
                            <dt class="col-sm-5">Route:</dt>
                            <dd class="col-sm-7">@(Model.Route?.RouteName ?? "N/A")</dd>
                            
                            <dt class="col-sm-5">Freight Cost:</dt>
                            <dd class="col-sm-7">£@Model.FreightCost.ToString("N2")</dd>
                        </dl>
                    </div>
                    <div class="col-md-6">
                        <dl class="row">
                            <dt class="col-sm-5">Total Packages:</dt>
                            <dd class="col-sm-7">@Model.TotalPackages</dd>
                            
                            <dt class="col-sm-5">Weight:</dt>
                            <dd class="col-sm-7">@Model.TotalWeight.ToString("N2") kg</dd>
                            
                            <dt class="col-sm-5">Volume:</dt>
                            <dd class="col-sm-7">@Model.TotalVolume.ToString("N3") m³</dd>
                            
                            <dt class="col-sm-5">Freight Cost:</dt>
                            <dd class="col-sm-7">£@Model.FreightCost.ToString("N2")</dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>

        <div class="card mt-3">
            <div class="card-header">
                <h4 class="card-title">Recipient Information</h4>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <dl class="row">
                            <dt class="col-sm-5">Name:</dt>
                            <dd class="col-sm-7">@Model.ShipToName</dd>
                            
                            <dt class="col-sm-5">Phone:</dt>
                            <dd class="col-sm-7">@(Model.ShipToPhone ?? "N/A")</dd>
                            
                            <dt class="col-sm-5">Email:</dt>
                            <dd class="col-sm-7">@(Model.ShipToEmail ?? "N/A")</dd>
                        </dl>
                    </div>
                    <div class="col-md-6">
                        <dl class="row">
                            <dt class="col-sm-5">Address:</dt>
                            <dd class="col-sm-7">
                                @Model.ShipToAddress1<br />
                                @if (!string.IsNullOrEmpty(Model.ShipToAddress2))
                                {
                                    @Model.ShipToAddress2<br />
                                }
                                @Model.ShipToCity, @Model.ShipToState @Model.ShipToPostCode<br />
                                @Model.ShipToCountry
                            </dd>
                        </dl>
                    </div>
                </div>
                
                @if (!string.IsNullOrEmpty(Model.DeliveryNotes))
                {
                    <div class="row mt-3">
                        <div class="col-12">
                            <dt>Delivery Instructions:</dt>
                            <dd>@Model.DeliveryNotes</dd>
                        </div>
                    </div>
                }
            </div>
        </div>

        <div class="card mt-3">
            <div class="card-header">
                <h4 class="card-title">Delivery Information</h4>
            </div>
            <div class="card-body">
                <dl class="row">
                    <dt class="col-sm-3">Estimated Delivery:</dt>
                    <dd class="col-sm-9">@(Model.EstimatedDeliveryDate?.ToString("dd/MM/yyyy HH:mm") ?? "N/A")</dd>
                    
                    <dt class="col-sm-3">Actual Delivery:</dt>
                    <dd class="col-sm-9">@(Model.ActualDeliveryDate?.ToString("dd/MM/yyyy HH:mm") ?? "N/A")</dd>
                    
                    <dt class="col-sm-3">Delivered To:</dt>
                    <dd class="col-sm-9">@(Model.DeliveredToName ?? "N/A")</dd>
                    
                    @if (!string.IsNullOrEmpty(Model.DeliverySignature))
                    {
                        <dt class="col-sm-3">Signature:</dt>
                        <dd class="col-sm-9">@Model.DeliverySignature</dd>
                    }
                </dl>
            </div>
        </div>
    </div>

    <div class="col-lg-4">
        <div class="card">
            <div class="card-header">
                <h4 class="card-title">Quick Actions</h4>
            </div>
            <div class="card-body">
                <button type="button" class="btn btn-success btn-block mb-2" data-bs-toggle="modal" data-bs-target="#updateStatusModal">
                    <i class="fas fa-exchange-alt"></i> Update Status
                </button>
                <a asp-action="CreatePickingList" asp-route-shipmentId="@Model.ShipmentID" class="btn btn-primary btn-block mb-2">
                    <i class="fas fa-clipboard-list"></i> Create Picking List
                </a>
                <a asp-action="CreatePackingSlip" asp-route-shipmentId="@Model.ShipmentID" class="btn btn-info btn-block mb-2">
                    <i class="fas fa-box"></i> Create Packing Slip
                </a>
                @if (Model.Status != "Delivered" && Model.Status != "Cancelled")
                {
                    <button type="button" class="btn btn-danger btn-block" onclick="cancelShipment(@Model.ShipmentID)">
                        <i class="fas fa-times"></i> Cancel Shipment
                    </button>
                }
            </div>
        </div>

        <div class="card mt-3">
            <div class="card-header">
                <h4 class="card-title">Additional Information</h4>
            </div>
            <div class="card-body">
                <dl class="row">
                    <dt class="col-sm-6">Insurance Value:</dt>
                    <dd class="col-sm-6">£@Model.InsuranceValue.ToString("N2")</dd>
                    
                    <dt class="col-sm-6">COD Amount:</dt>
                    <dd class="col-sm-6">£@Model.CODAmount.ToString("N2")</dd>
                    
                    @if (!string.IsNullOrEmpty(Model.SpecialHandling))
                    {
                        <dt class="col-sm-6">Special Handling:</dt>
                        <dd class="col-sm-6">@Model.SpecialHandling</dd>
                    }
                </dl>
            </div>
        </div>

        <div class="card mt-3">
            <div class="card-header">
                <h4 class="card-title">Tracking History</h4>
            </div>
            <div class="card-body">
                @if (Model.TrackingHistory != null && Model.TrackingHistory.Any())
                {
                    <div class="activity">
                        @foreach (var tracking in Model.TrackingHistory.OrderByDescending(t => t.TrackingDate))
                        {
                            <div class="activity-item">
                                <div class="activity-date">@tracking.TrackingDate.ToString("dd/MM/yyyy HH:mm")</div>
                                <div class="activity-content">
                                    <strong>@tracking.Status</strong>
                                    @if (!string.IsNullOrEmpty(tracking.Location))
                                    {
                                        <span> - @tracking.Location</span>
                                    }
                                    @if (!string.IsNullOrEmpty(tracking.Description))
                                    {
                                        <p class="mb-0 text-muted">@tracking.Description</p>
                                    }
                                </div>
                            </div>
                        }
                    </div>
                }
                else
                {
                    <p class="text-muted">No tracking history available.</p>
                }
            </div>
        </div>
    </div>
</div>

<!-- Update Status Modal -->
<div class="modal fade" id="updateStatusModal" tabindex="-1" aria-labelledby="updateStatusModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="updateStatusModalLabel">Update Shipment Status</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form id="updateStatusForm">
                <div class="modal-body">
                    <input type="hidden" id="shipmentId" value="@Model.ShipmentID" />
                    <div class="form-group">
                        <label for="status">Status</label>
                        <select id="status" name="status" class="form-control" required>
                            <option value="">Select Status</option>
                            <option value="Picked">Picked</option>
                            <option value="InTransit">In Transit</option>
                            <option value="OutForDelivery">Out for Delivery</option>
                            <option value="Delivered">Delivered</option>
                            <option value="Failed">Failed</option>
                            <option value="Returned">Returned</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="location">Location</label>
                        <input type="text" id="location" name="location" class="form-control" />
                    </div>
                    <div class="form-group">
                        <label for="description">Description</label>
                        <textarea id="description" name="description" class="form-control" rows="3"></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">Update Status</button>
                </div>
            </form>
        </div>
    </div>
</div>

@section Scripts {
    <script>
        $(document).ready(function() {
            $('#updateStatusForm').submit(function(e) {
                e.preventDefault();
                
                var formData = {
                    shipmentId: $('#shipmentId').val(),
                    status: $('#status').val(),
                    location: $('#location').val(),
                    description: $('#description').val()
                };
                
                $.post('@Url.Action("UpdateStatus", "Shipment")', formData, function(response) {
                    if (response.success) {
                        location.reload();
                    } else {
                        alert('Error: ' + response.message);
                    }
                });
            });
        });
        
        function cancelShipment(id) {
            if (confirm('Are you sure you want to cancel this shipment?')) {
                $.post('@Url.Action("UpdateStatus", "Shipment")', {
                    shipmentId: id,
                    status: 'Cancelled',
                    location: 'System',
                    description: 'Shipment cancelled by user'
                }, function(response) {
                    if (response.success) {
                        location.reload();
                    } else {
                        alert('Error: ' + response.message);
                    }
                });
            }
        }
    </script>
}