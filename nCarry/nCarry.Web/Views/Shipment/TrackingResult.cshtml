@model nCarry.Web.Models.Logistics.Shipment

@{
    ViewData["Title"] = "Tracking Result";
}

<div class="container-fluid">
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">@ViewData["Title"]</h1>
        <div>
            <a asp-action="Track" class="btn btn-sm btn-primary shadow-sm">
                <i class="fas fa-search fa-sm text-white-50"></i> Track Another
            </a>
            <button onclick="window.print()" class="btn btn-sm btn-secondary shadow-sm">
                <i class="fas fa-print fa-sm text-white-50"></i> Print
            </button>
        </div>
    </div>

    <!-- Shipment Header -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <div class="row align-items-center">
                <div class="col">
                    <h6 class="m-0 font-weight-bold text-primary">
                        Shipment: @Model.ShipmentNumber
                        @if (!string.IsNullOrEmpty(Model.TrackingNumber))
                        {
                            <small class="text-muted">| Tracking: @Model.TrackingNumber</small>
                        }
                    </h6>
                </div>
                <div class="col-auto">
                    <span class="badge badge-lg bg-@(GetStatusColor(Model.Status)) text-white">
                        @GetStatusText(Model.Status)
                    </span>
                </div>
            </div>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-6">
                    <h6 class="font-weight-bold text-primary">Shipment Details</h6>
                    <dl class="row">
                        <dt class="col-sm-4">Date:</dt>
                        <dd class="col-sm-8">@Model.ShipmentDate.ToString("dd/MM/yyyy")</dd>
                        
                        <dt class="col-sm-4">Ship To:</dt>
                        <dd class="col-sm-8">@Model.ShipToName</dd>
                        
                        <dt class="col-sm-4">Carrier:</dt>
                        <dd class="col-sm-8">
                            @(Model.Carrier?.CarrierName ?? "N/A")
                            @if (Model.Service != null)
                            {
                                <br><small>@Model.Service.ServiceName</small>
                            }
                        </dd>
                        
                        <dt class="col-sm-4">Priority:</dt>
                        <dd class="col-sm-8">
                            <span class="badge bg-@(Model.Priority == "High" || Model.Priority == "Urgent" ? "danger" : Model.Priority == "Medium" ? "warning" : "secondary")">
                                @Model.Priority
                            </span>
                        </dd>
                    </dl>
                </div>
                <div class="col-md-6">
                    <h6 class="font-weight-bold text-primary">Delivery Information</h6>
                    <dl class="row">
                        <dt class="col-sm-4">Recipient:</dt>
                        <dd class="col-sm-8">@Model.RecipientName</dd>
                        
                        <dt class="col-sm-4">Phone:</dt>
                        <dd class="col-sm-8">@(Model.RecipientPhone ?? "N/A")</dd>
                        
                        <dt class="col-sm-4">Address:</dt>
                        <dd class="col-sm-8">
                            @Model.DeliveryAddress<br>
                            @Model.DeliveryCity, @Model.DeliveryPostalCode<br>
                            @Model.DeliveryCountry
                        </dd>
                        
                        @if (Model.CODAmount > 0)
                        {
                            <dt class="col-sm-4">COD Amount:</dt>
                            <dd class="col-sm-8">
                                <span class="badge bg-warning text-dark">$@Model.CODAmount.ToString("F2")</span>
                            </dd>
                        }
                    </dl>
                </div>
            </div>
        </div>
    </div>

    <!-- Tracking Timeline -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">Tracking Timeline</h6>
        </div>
        <div class="card-body">
            @if (Model.TrackingHistory != null && Model.TrackingHistory.Any())
            {
                <div class="timeline">
                    @foreach (var trackingEvent in Model.TrackingHistory.OrderByDescending(te => te.TrackingDate))
                    {
                        <div class="timeline-item">
                            <div class="timeline-marker bg-@(GetStatusColor(trackingEvent.Status))">
                                <i class="fas fa-@(GetStatusIcon(trackingEvent.Status)) text-white"></i>
                            </div>
                            <div class="timeline-content">
                                <div class="timeline-header">
                                    <h6 class="timeline-title">@GetStatusText(trackingEvent.Status)</h6>
                                    <small class="text-muted">
                                        @trackingEvent.TrackingDate.ToString("dd/MM/yyyy HH:mm")
                                        @if (!string.IsNullOrEmpty(trackingEvent.Location))
                                        {
                                            <span class="mx-1">•</span> @trackingEvent.Location
                                        }
                                    </small>
                                </div>
                                @if (!string.IsNullOrEmpty(trackingEvent.Description))
                                {
                                    <p class="timeline-description">@trackingEvent.Description</p>
                                }
                                @if (!string.IsNullOrEmpty(trackingEvent.Notes))
                                {
                                    <p class="timeline-notes"><small><em>@trackingEvent.Notes</em></small></p>
                                }
                            </div>
                        </div>
                    }
                </div>
            }
            else
            {
                <div class="alert alert-info">
                    <i class="fas fa-info-circle"></i> No tracking events available for this shipment.
                </div>
            }
        </div>
    </div>

    <!-- Package Information -->
    @if (Model.Weight > 0 || Model.Volume > 0 || Model.DeclaredValue > 0)
    {
        <div class="card shadow mb-4">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">Package Information</h6>
            </div>
            <div class="card-body">
                <div class="row">
                    @if (Model.Weight > 0)
                    {
                        <div class="col-md-4">
                            <div class="text-center">
                                <i class="fas fa-weight fa-2x text-gray-400 mb-2"></i>
                                <h5>@Model.Weight.ToString("F2") kg</h5>
                                <small class="text-muted">Weight</small>
                            </div>
                        </div>
                    }
                    @if (Model.Volume > 0)
                    {
                        <div class="col-md-4">
                            <div class="text-center">
                                <i class="fas fa-cube fa-2x text-gray-400 mb-2"></i>
                                <h5>@Model.Volume.ToString("F2") m³</h5>
                                <small class="text-muted">Volume</small>
                            </div>
                        </div>
                    }
                    @if (Model.DeclaredValue > 0)
                    {
                        <div class="col-md-4">
                            <div class="text-center">
                                <i class="fas fa-dollar-sign fa-2x text-gray-400 mb-2"></i>
                                <h5>$@Model.DeclaredValue.ToString("F2")</h5>
                                <small class="text-muted">Declared Value</small>
                            </div>
                        </div>
                    }
                </div>
            </div>
        </div>
    }

    <!-- Special Instructions -->
    @if (!string.IsNullOrEmpty(Model.DeliveryInstructions) || !string.IsNullOrEmpty(Model.SpecialHandling))
    {
        <div class="card shadow mb-4">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">Special Instructions</h6>
            </div>
            <div class="card-body">
                @if (!string.IsNullOrEmpty(Model.DeliveryInstructions))
                {
                    <h6>Delivery Instructions:</h6>
                    <p>@Model.DeliveryInstructions</p>
                }
                @if (!string.IsNullOrEmpty(Model.SpecialHandling))
                {
                    <h6>Special Handling:</h6>
                    <p>@Model.SpecialHandling</p>
                }
            </div>
        </div>
    }
</div>

@section Styles {
    <style>
        .timeline {
            position: relative;
            padding-left: 2rem;
        }
        
        .timeline::before {
            content: '';
            position: absolute;
            left: 1rem;
            top: 0;
            bottom: 0;
            width: 2px;
            background: #e3e6f0;
        }
        
        .timeline-item {
            position: relative;
            margin-bottom: 2rem;
        }
        
        .timeline-marker {
            position: absolute;
            left: -1.5rem;
            top: 0;
            width: 2rem;
            height: 2rem;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            border: 3px solid white;
            box-shadow: 0 0 0 3px #e3e6f0;
        }
        
        .timeline-content {
            margin-left: 1rem;
            background: white;
            border: 1px solid #e3e6f0;
            border-radius: 0.5rem;
            padding: 1rem;
            box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15);
        }
        
        .timeline-title {
            margin-bottom: 0.25rem;
            font-size: 1rem;
        }
        
        .timeline-description {
            margin-bottom: 0.5rem;
        }
        
        .timeline-notes {
            margin-bottom: 0;
            color: #6c757d;
        }
        
        .badge-lg {
            font-size: 0.9rem;
            padding: 0.5rem 1rem;
        }
        
        @@media print {
            .btn, .card-header { display: none !important; }
            .card { border: none !important; box-shadow: none !important; }
        }
    </style>
}

@functions {
    string GetStatusColor(string status)
    {
        return status switch
        {
            "Pending" => "warning",
            "Picked" => "info",
            "InTransit" => "primary",
            "OutForDelivery" => "info",
            "Delivered" => "success",
            "Failed" => "danger",
            "Returned" => "secondary",
            _ => "secondary"
        };
    }

    string GetStatusText(string status)
    {
        return status switch
        {
            "InTransit" => "In Transit",
            "OutForDelivery" => "Out for Delivery",
            _ => status
        };
    }

    string GetStatusIcon(string status)
    {
        return status switch
        {
            "Pending" => "clock",
            "Picked" => "hand-paper",
            "InTransit" => "truck",
            "OutForDelivery" => "shipping-fast",
            "Delivered" => "check",
            "Failed" => "times",
            "Returned" => "undo",
            _ => "circle"
        };
    }
}