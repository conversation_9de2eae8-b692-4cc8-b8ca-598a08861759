@model nCarry.Web.Models.Logistics.Shipment

@{
    ViewData["Title"] = "Create Shipment";
}

<div class="page-header">
    <div class="row align-items-center">
        <div class="col">
            <h3 class="page-title">Create Shipment</h3>
            <ul class="breadcrumb">
                <li class="breadcrumb-item"><a asp-action="Index">Shipments</a></li>
                <li class="breadcrumb-item active">Create</li>
            </ul>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-lg-12">
        <div class="card">
            <div class="card-body">
                <form asp-action="Create">
                    <div asp-validation-summary="ModelOnly" class="alert alert-danger"></div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label asp-for="ShipmentNumber" class="control-label"></label>
                                <input asp-for="ShipmentNumber" class="form-control" readonly />
                                <span asp-validation-for="ShipmentNumber" class="text-danger"></span>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label asp-for="ShipmentDate" class="control-label"></label>
                                <input asp-for="ShipmentDate" type="date" class="form-control" />
                                <span asp-validation-for="ShipmentDate" class="text-danger"></span>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-4">
                            <div class="form-group">
                                <label asp-for="ShipmentType" class="control-label"></label>
                                <select asp-for="ShipmentType" class="form-control">
                                    <option value="Delivery">Delivery</option>
                                    <option value="Pickup">Pickup</option>
                                    <option value="Transfer">Transfer</option>
                                </select>
                                <span asp-validation-for="ShipmentType" class="text-danger"></span>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-group">
                                <label asp-for="Priority" class="control-label"></label>
                                <select asp-for="Priority" asp-items="ViewBag.PriorityList" class="form-control">
                                    <option value="">Select Priority</option>
                                </select>
                                <span asp-validation-for="Priority" class="text-danger"></span>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-group">
                                <label asp-for="Status" class="control-label"></label>
                                <select asp-for="Status" asp-items="ViewBag.StatusList" class="form-control">
                                </select>
                                <span asp-validation-for="Status" class="text-danger"></span>
                            </div>
                        </div>
                    </div>

                    <hr />
                    <h5>Warehouse & Customer Information</h5>
                    
                    <div class="row">
                        <div class="col-md-4">
                            <div class="form-group">
                                <label asp-for="ShipFromWarehouseID" class="control-label">Ship From Warehouse</label>
                                <select asp-for="ShipFromWarehouseID" asp-items="ViewBag.Warehouses" class="form-control">
                                    <option value="">Select Warehouse</option>
                                </select>
                                <span asp-validation-for="ShipFromWarehouseID" class="text-danger"></span>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-group">
                                <label asp-for="BranchID" class="control-label">Branch</label>
                                <select asp-for="BranchID" asp-items="ViewBag.Branches" class="form-control">
                                    <option value="">Select Branch</option>
                                </select>
                                <span asp-validation-for="BranchID" class="text-danger"></span>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-group">
                                <label asp-for="CustomerID" class="control-label">Customer</label>
                                <select asp-for="CustomerID" asp-items="ViewBag.Customers" class="form-control">
                                    <option value="">Select Customer</option>
                                </select>
                                <span asp-validation-for="CustomerID" class="text-danger"></span>
                            </div>
                        </div>
                    </div>

                    <hr />
                    <h5>Shipping Information</h5>
                    
                    <div class="row">
                        <div class="col-md-4">
                            <div class="form-group">
                                <label asp-for="CarrierID" class="control-label">Carrier</label>
                                <select asp-for="CarrierID" asp-items="ViewBag.Carriers" class="form-control">
                                    <option value="">Select Carrier</option>
                                </select>
                                <span asp-validation-for="CarrierID" class="text-danger"></span>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-group">
                                <label asp-for="ServiceID" class="control-label">Service</label>
                                <select asp-for="ServiceID" asp-items="ViewBag.Services" class="form-control">
                                    <option value="">Select Service</option>
                                </select>
                                <span asp-validation-for="ServiceID" class="text-danger"></span>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-group">
                                <label asp-for="TrackingNumber" class="control-label"></label>
                                <input asp-for="TrackingNumber" class="form-control" />
                                <span asp-validation-for="TrackingNumber" class="text-danger"></span>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label asp-for="RouteID" class="control-label">Route</label>
                                <select asp-for="RouteID" asp-items="ViewBag.Routes" class="form-control">
                                    <option value="">Select Route</option>
                                </select>
                                <span asp-validation-for="RouteID" class="text-danger"></span>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label asp-for="DeliveryZoneID" class="control-label">Delivery Zone</label>
                                <select asp-for="DeliveryZoneID" asp-items="ViewBag.DeliveryZones" class="form-control">
                                    <option value="">Select Zone</option>
                                </select>
                                <span asp-validation-for="DeliveryZoneID" class="text-danger"></span>
                            </div>
                        </div>
                    </div>

                    <hr />
                    <h5>Recipient Information</h5>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label asp-for="ShipToName" class="control-label">Recipient Name</label>
                                <input asp-for="ShipToName" class="form-control" />
                                <span asp-validation-for="ShipToName" class="text-danger"></span>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="form-group">
                                <label asp-for="ShipToPhone" class="control-label">Phone</label>
                                <input asp-for="ShipToPhone" class="form-control" />
                                <span asp-validation-for="ShipToPhone" class="text-danger"></span>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="form-group">
                                <label asp-for="ShipToEmail" class="control-label">Email</label>
                                <input asp-for="ShipToEmail" class="form-control" />
                                <span asp-validation-for="ShipToEmail" class="text-danger"></span>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label asp-for="ShipToAddress1" class="control-label">Address Line 1</label>
                                <input asp-for="ShipToAddress1" class="form-control" />
                                <span asp-validation-for="ShipToAddress1" class="text-danger"></span>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label asp-for="ShipToAddress2" class="control-label">Address Line 2</label>
                                <input asp-for="ShipToAddress2" class="form-control" />
                                <span asp-validation-for="ShipToAddress2" class="text-danger"></span>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-3">
                            <div class="form-group">
                                <label asp-for="ShipToCity" class="control-label">City</label>
                                <input asp-for="ShipToCity" class="form-control" />
                                <span asp-validation-for="ShipToCity" class="text-danger"></span>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="form-group">
                                <label asp-for="ShipToState" class="control-label">State/Province</label>
                                <input asp-for="ShipToState" class="form-control" />
                                <span asp-validation-for="ShipToState" class="text-danger"></span>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="form-group">
                                <label asp-for="ShipToPostCode" class="control-label">Postal Code</label>
                                <input asp-for="ShipToPostCode" class="form-control" />
                                <span asp-validation-for="ShipToPostCode" class="text-danger"></span>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="form-group">
                                <label asp-for="ShipToCountry" class="control-label">Country</label>
                                <input asp-for="ShipToCountry" class="form-control" />
                                <span asp-validation-for="ShipToCountry" class="text-danger"></span>
                            </div>
                        </div>
                    </div>

                    <hr />
                    <h5>Package Information</h5>
                    
                    <div class="row">
                        <div class="col-md-3">
                            <div class="form-group">
                                <label asp-for="TotalPackages" class="control-label">Total Packages</label>
                                <input asp-for="TotalPackages" type="number" class="form-control" />
                                <span asp-validation-for="TotalPackages" class="text-danger"></span>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="form-group">
                                <label asp-for="TotalWeight" class="control-label">Weight (kg)</label>
                                <input asp-for="TotalWeight" type="number" step="0.01" class="form-control" />
                                <span asp-validation-for="TotalWeight" class="text-danger"></span>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="form-group">
                                <label asp-for="TotalVolume" class="control-label">Volume (m³)</label>
                                <input asp-for="TotalVolume" type="number" step="0.001" class="form-control" />
                                <span asp-validation-for="TotalVolume" class="text-danger"></span>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="form-group">
                                <label asp-for="FreightCost" class="control-label">Freight Cost</label>
                                <input asp-for="FreightCost" type="number" step="0.01" class="form-control" />
                                <span asp-validation-for="FreightCost" class="text-danger"></span>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-3">
                            <div class="form-group">
                                <label asp-for="InsuranceValue" class="control-label">Insurance Value</label>
                                <input asp-for="InsuranceValue" type="number" step="0.01" class="form-control" />
                                <span asp-validation-for="InsuranceValue" class="text-danger"></span>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="form-group">
                                <label asp-for="CODAmount" class="control-label">COD Amount</label>
                                <input asp-for="CODAmount" type="number" step="0.01" class="form-control" />
                                <span asp-validation-for="CODAmount" class="text-danger"></span>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label asp-for="SpecialHandling" class="control-label">Special Handling</label>
                                <input asp-for="SpecialHandling" class="form-control" />
                                <span asp-validation-for="SpecialHandling" class="text-danger"></span>
                            </div>
                        </div>
                    </div>

                    <div class="form-group">
                        <label asp-for="DeliveryNotes" class="control-label">Delivery Instructions</label>
                        <textarea asp-for="DeliveryNotes" class="form-control" rows="3"></textarea>
                        <span asp-validation-for="DeliveryNotes" class="text-danger"></span>
                    </div>

                    <div class="form-group mt-3">
                        <button type="submit" class="btn btn-primary">Create Shipment</button>
                        <a asp-action="Index" class="btn btn-secondary">Cancel</a>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}
    
    <script>
        $(document).ready(function() {
            // Auto-populate customer address when customer is selected
            $('#CustomerID').change(function() {
                var customerId = $(this).val();
                if (customerId) {
                    $.get('@Url.Action("GetCustomerDetails", "Customer")', { id: customerId }, function(data) {
                        if (data) {
                            $('#ShipToName').val(data.customerName);
                            $('#ShipToPhone').val(data.phone);
                            $('#ShipToEmail').val(data.email);
                            $('#ShipToAddress1').val(data.address1);
                            $('#ShipToAddress2').val(data.address2);
                            $('#ShipToCity').val(data.city);
                            $('#ShipToState').val(data.state);
                            $('#ShipToPostCode').val(data.postCode);
                            $('#ShipToCountry').val(data.country || 'United Kingdom');
                        }
                    });
                }
            });

            // Filter services based on selected carrier
            $('#CarrierID').change(function() {
                var carrierId = $(this).val();
                $('#ServiceID').empty().append('<option value="">Select Service</option>');
                
                if (carrierId) {
                    $.get('@Url.Action("GetCarrierServices", "Carrier")', { carrierId: carrierId }, function(services) {
                        $.each(services, function(i, service) {
                            $('#ServiceID').append($('<option></option>').val(service.serviceID).text(service.serviceName));
                        });
                    });
                }
            });
        });
    </script>
}