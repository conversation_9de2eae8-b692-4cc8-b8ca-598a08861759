@{
    ViewData["Title"] = "Bulk Update Shipments";
}

<div class="page-header">
    <div class="row align-items-center">
        <div class="col">
            <h3 class="page-title">Bulk Update Shipments</h3>
            <ul class="breadcrumb">
                <li class="breadcrumb-item"><a asp-action="Index">Shipments</a></li>
                <li class="breadcrumb-item active">Bulk Update</li>
            </ul>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-lg-12">
        <div class="card">
            <div class="card-body">
                <form asp-action="BulkUpdate" method="post">
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle"></i> Enter shipment numbers or tracking numbers, one per line. You can paste from Excel or other sources.
                    </div>
                    
                    <div class="form-group">
                        <label for="shipmentNumbers">Shipment/Tracking Numbers</label>
                        <textarea id="shipmentNumbers" name="shipmentNumbers" class="form-control" rows="10" required 
                                  placeholder="Enter shipment or tracking numbers, one per line..."></textarea>
                        <small class="form-text text-muted">Example: SH20240115001, SH20240115002, etc.</small>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-4">
                            <div class="form-group">
                                <label for="status">New Status</label>
                                <select id="status" name="status" asp-items="ViewBag.StatusList" class="form-control" required>
                                    <option value="">Select Status</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-group">
                                <label for="location">Location</label>
                                <input type="text" id="location" name="location" class="form-control" 
                                       placeholder="e.g., Distribution Center, In Transit" />
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-group">
                                <label for="description">Description</label>
                                <input type="text" id="description" name="description" class="form-control" 
                                       placeholder="Optional description" />
                            </div>
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-sync"></i> Update Shipments
                        </button>
                        <a asp-action="Index" class="btn btn-secondary">Cancel</a>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script>
        $(document).ready(function() {
            // Count lines and display
            $('#shipmentNumbers').on('input', function() {
                var lines = $(this).val().split('\n').filter(line => line.trim() !== '');
                var count = lines.length;
                $('.shipment-count').remove();
                if (count > 0) {
                    $(this).after('<small class="form-text text-muted shipment-count">' + count + ' shipment(s) to update</small>');
                }
            });
            
            // Auto-format pasted content
            $('#shipmentNumbers').on('paste', function(e) {
                setTimeout(() => {
                    var text = $(this).val();
                    // Replace various delimiters with newlines
                    text = text.replace(/[,;|\t]/g, '\n');
                    // Remove extra whitespace and empty lines
                    text = text.split('\n')
                        .map(line => line.trim())
                        .filter(line => line !== '')
                        .join('\n');
                    $(this).val(text);
                    $(this).trigger('input');
                }, 100);
            });
        });
    </script>
}