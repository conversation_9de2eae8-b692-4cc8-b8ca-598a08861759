{"permissions": {"allow": ["<PERSON><PERSON>(mkdir:*)", "Bash(git add:*)", "Bash(git commit:*)", "Bash(git push:*)", "Bash(git pull:*)", "Bash(dotnet new:*)", "Bash(dotnet add package:*)", "Bash(dotnet tool list:*)", "Bash(dotnet ef migrations:*)", "Bash(dotnet ef database:*)", "Bash(dotnet tool update:*)", "Bash(dotnet restore:*)", "Bash(export PATH=\"$PATH:/Users/<USER>/.dotnet/tools\")", "Bash(dotnet run)", "Bash(pip install:*)", "<PERSON><PERSON>(python:*)", "Bash(brew install:*)", "Bash(dotnet build)", "Bash(dotnet tool install:*)", "Bash(grep:*)", "Bash(find:*)", "Bash(dotnet clean:*)", "Bash(ls:*)", "Bash(sqlcmd:*)", "Bash(tree:*)", "Bash(rg:*)", "Bash(dotnet run:*)", "<PERSON><PERSON>(pkill:*)", "<PERSON><PERSON>(true)", "Bash(dotnet build:*)", "<PERSON><PERSON>(curl:*)", "<PERSON><PERSON>(timeout:*)", "Bash(for:*)", "Bash(rm:*)", "<PERSON><PERSON>(chmod:*)", "Bash(/tmp/test_dashboard.sh:*)", "<PERSON><PERSON>(echo:*)", "Bash(open http://localhost:5298/Shipment)", "Bash(kill:*)", "Bash(open http://localhost:5299/Account/Login)", "<PERSON><PERSON>(cat:*)", "mcp__playwright__browser_navigate", "mcp__playwright__browser_click", "mcp__playwright__browser_take_screenshot", "mcp__playwright__browser_close", "mcp__playwright__browser_select_option", "mcp__playwright__browser_type", "mcp__playwright__browser_navigate_back", "mcp__playwright__browser_evaluate", "**", "mcp__playwright__browser_wait_for", "mcp__playwright__browser_snapshot", "mcp__playwright__browser_hover", "mcp__playwright__browser_tab_new", "mcp__playwright__browser_network_requests", "mcp__playwright__browser_console_messages", "mcp__playwright__browser_tab_select", "mcp__playwright__browser_press_key", "mcp__playwright__browser_install", "mcp__playwright__browser_tab_close"], "deny": []}}